import request from "@/utils/request";

export class LiveRulesVo {
  times: number | string = ""; // 次数
  ruleKey: string = ""; // 规则标识(如:gift_coefficient)
  points: number | string = ""; // 积分/人气值
  type: number | string = ""; // 积分/人气值
}

export class LiveRulesDto {
  times: string = ""; // 次数
  ruleKey: string = ""; // 规则标识(如:gift_coefficient)
  points: string = ""; // 积分/人气值
  type: string = "";

  constructor(e: LiveRulesVo) {
    if (e) {
      this.times = String(e.times || "");
      this.ruleKey = e.ruleKey;
      this.points = String(e.points || "0");
      this.type = String(e.type);
    }
  }
}

export interface LiveListRulesVo {
  // 人气规则
  popularityRules: LiveRulesVo[];
  // 积分规则
  pointsRules: LiveRulesVo[];
}

const LiveConfigApi = {
  /** 获取*/
  get() {
    return request<any, LiveListRulesVo>({
      url: `/tenant/live/config/rules`,
      method: "get",
    });
  },
  /**
   * 保存
   * @param data 表单数据
   */
  save(data: LiveRulesDto[]) {
    return request({
      url: `/tenant/live/config/save/rules`,
      method: "put",
      data: { list: data },
    });
  },
};

export default LiveConfigApi;
