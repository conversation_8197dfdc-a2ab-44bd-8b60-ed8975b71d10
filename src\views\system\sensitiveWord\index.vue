<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item prop="word" label="敏感词">
          <el-input
            v-model="queryParams.word"
            placeholder="请输入敏感词"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="isEnable" label="校验状态">
          <el-select v-model="queryParams.isEnable" style="min-width: 100px" placeholder="请选择">
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          <el-button icon="refresh" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          v-hasPerm="['sys:sensitive:add']"
          type="success"
          icon="plus"
          :loading="loading"
          @click="handleOpenDialog()"
        >
          新增
        </el-button>
        <el-button
          v-hasPerm="['sys:sensitive:del']"
          type="danger"
          :disabled="ids.length === 0 || loading"
          :loading="loading"
          icon="delete"
          @click="handleDelete()"
        >
          删除
        </el-button>
        <el-button
          v-hasPerm="['sys:sensitive:refresh']"
          type="primary"
          icon="refresh"
          :loading="loading"
          @click="handleRefreshSensitiveWord"
        >
          刷新敏感词缓存
        </el-button>
        <el-button
          v-hasPerm="['sys:sensitive:refresh:tenant']"
          type="warning"
          icon="refresh"
          :loading="loading"
          @click="handleRefreshSensitiveWordByTenant"
        >
          刷新敏感词缓存
        </el-button>
        <el-button
          v-hasPerm="['sys:sensitive:import']"
          type="primary"
          icon="upload"
          :loading="loading"
          @click="handleImportClick"
        >
          导入
        </el-button>
        <el-button
          v-hasPerm="['sys:sensitive:import']"
          type="info"
          icon="download"
          :loading="downloadLoading"
          @click="handleDownloadTemplate"
        >
          下载模板
        </el-button>
        <input
          ref="importFileInput"
          type="file"
          accept=".xls,.xlsx"
          style="display: none"
          @change="handleImportChange"
        />
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="sensitiveWordList"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="敏感词" prop="word" min-width="120" />
        <!-- <el-table-column label="是否敏感词" prop="isSensitive" min-width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.isSensitive === 1" type="danger">敏感词</el-tag>
            <el-tag v-else type="success">白名单</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="校验状态" prop="isEnable" min-width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.isEnable"
              :disabled="!checkPerms(['sys:sensitive:edit']) || enableLoading"
              :loading="enableLoading && scope.row.id === currentEnableId"
              :active-value="1"
              :inactive-value="0"
              @change="(val) => handleEnableChange(scope.row, val)"
            />
          </template>
        </el-table-column>
        <el-table-column label="添加时间" prop="createdAt" min-width="160" />
        <!--        <el-table-column label="最后修改时间" prop="updatedAt" min-width="160" />-->
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button
              v-hasPerm="['sys:sensitive:edit']"
              type="primary"
              size="small"
              link
              icon="edit"
              :loading="loading"
              @click="handleOpenDialog(scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
              v-hasPerm="['sys:sensitive:del']"
              type="danger"
              size="small"
              link
              icon="delete"
              :loading="loading"
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNumber"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 敏感词表单弹窗 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="500px"
      @close="handleCloseDialog"
    >
      <el-form ref="sensitiveWordFormRef" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="敏感词" prop="word">
          <el-input v-model="formData.word" placeholder="请输入敏感词" />
        </el-form-item>

        <el-form-item label="校验状态" prop="isSensitive">
          <el-select v-model="formData.isEnable" style="width: 120px" placeholder="请选择">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="loading" @click="handleSubmit">确 定</el-button>
          <el-button :disabled="loading" @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { checkPerms } from "@/directive/permission";

defineOptions({
  name: "SensitiveWord",
  inheritAttrs: false,
});

import SensitiveWordAPI, {
  SensitiveWordPageQuery,
  SensitiveWordVo,
} from "@/api/system/sensitiveWord";

const queryFormRef = ref();
const sensitiveWordFormRef = ref();
const loading = ref(false);
const downloadLoading = ref(false);
const enableLoading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<SensitiveWordPageQuery>({
  pageNumber: 1,
  pageSize: 10,
  word: "",
  tags: "",
  isSensitive: "",
});

const sensitiveWordList = ref<SensitiveWordVo[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});
// 敏感词表单
const formData = reactive<SensitiveWordVo>({
  id: undefined,
  word: "",
  tags: "",
  isSensitive: 1,
  isEnable: 1,
  createdAt: "",
  updatedAt: "",
});

const rules = reactive({
  word: [{ required: true, message: "请输入敏感词", trigger: "blur" }],
  isSensitive: [{ required: true, message: "请选择是否敏感词", trigger: "blur" }],
});

const tagList = ref<string[]>([]);

const emptySensitiveWord: SensitiveWordVo = {
  id: undefined,
  word: "",
  tags: "",
  isSensitive: 0,
  isEnable: 1,
  createdAt: "",
  updatedAt: "",
};

const currentEnableId = ref<number | undefined>(undefined);

// 查询
function handleQuery() {
  loading.value = true;
  SensitiveWordAPI.getSensitiveWordPage(queryParams)
    .then((res: any) => {
      sensitiveWordList.value = res.list;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 重置查询
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.pageNumber = 1;
  handleQuery();
}

// 行复选框选中
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

// 打开敏感词弹窗
function handleOpenDialog(sensitiveWordId?: number) {
  dialog.visible = true;
  if (sensitiveWordId) {
    dialog.title = "修改敏感词";
    SensitiveWordAPI.getSensitiveWordById(sensitiveWordId).then((safeData: any) => {
      safeData = (safeData || { ...emptySensitiveWord }) as SensitiveWordVo;
      Object.assign(formData, safeData);
      formData.isSensitive = Number(safeData.isSensitive) || 1;
      tagList.value = safeData.tags ? safeData.tags.split(",") : [];
    });
  } else {
    dialog.title = "新增敏感词";
    tagList.value = [];
    formData.isSensitive = 1;
  }
}

// 提交敏感词表单
function handleSubmit() {
  sensitiveWordFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const sensitiveWordId = formData.id;
      const submitData = {
        ...formData,
        tags: tagList.value.join(","),
      };
      if (sensitiveWordId) {
        SensitiveWordAPI.updateSensitiveWord(submitData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        SensitiveWordAPI.addSensitiveWord(submitData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

// 关闭弹窗
function handleCloseDialog() {
  dialog.visible = false;

  sensitiveWordFormRef.value.resetFields();
  sensitiveWordFormRef.value.clearValidate();

  formData.id = undefined;
  formData.isSensitive = 0;
}

// 删除敏感词
function handleDelete(sensitiveWordId?: number) {
  let idsToDelete: number[] = [];
  if (sensitiveWordId) {
    idsToDelete = [sensitiveWordId];
  } else if (ids.value.length > 0) {
    idsToDelete = ids.value;
  }
  if (idsToDelete.length === 0) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    async () => {
      loading.value = true;
      try {
        for (const id of idsToDelete) {
          await SensitiveWordAPI.deleteSensitiveWordById(id);
        }
        ElMessage.success("删除成功");
        handleResetQuery();
      } finally {
        loading.value = false;
      }
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

function handleRefreshSensitiveWord() {
  loading.value = true;
  SensitiveWordAPI.refreshSensitiveWord()
    .then(() => {
      ElMessage.success("刷新成功");
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleRefreshSensitiveWordByTenant() {
  loading.value = true;
  SensitiveWordAPI.refreshSensitiveWordByTenant()
    .then(() => {
      ElMessage.success("敏感词库缓存刷新成功");
    })
    .catch(() => {
      // ElMessage.error("租户敏感词库刷新失败");
    })
    .finally(() => {
      loading.value = false;
    });
}

const importFileInput = ref<HTMLInputElement | null>(null);

function handleImportClick() {
  if (importFileInput.value) {
    importFileInput.value.click();
  }
}

function handleImportChange(e: Event) {
  const files = (e.target as HTMLInputElement).files;
  if (!files || files.length === 0) return;
  const file = files[0];
  loading.value = true;
  SensitiveWordAPI.importSensitiveWords(file)
    .then(() => {
      ElMessage.success("导入成功");
      handleResetQuery();
    })
    .catch(() => {
      // ElMessage.error("导入失败");
    })
    .finally(() => {
      loading.value = false;
      if (importFileInput.value) importFileInput.value.value = "";
    });
}

function handleDownloadTemplate() {
  downloadLoading.value = true;
  SensitiveWordAPI.downloadSensitiveWordTemplate()
    .then((response: any) => {
      const blob = response instanceof Blob ? response : response.data;
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "敏感词导入模板.xlsx";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    })
    .catch(() => {
      ElMessage.error("模板下载失败");
    })
    .finally(() => {
      downloadLoading.value = false;
    });
}

function handleEnableChange(row: any, val: string | number | boolean) {
  if (enableLoading.value) {
    // 如果正在处理其他请求，回滚状态
    row.isEnable = Number(val) === 1 ? 0 : 1;
    return;
  }

  enableLoading.value = true;
  currentEnableId.value = row.id;

  const enableValue = Number(val);

  SensitiveWordAPI.enableSensitiveWord({
    id: row.id,
    word: row.word,
    isSensitive: row.isSensitive,
    isEnable: enableValue,
  })
    .then(() => {
      ElMessage.success(enableValue === 1 ? "已启用" : "已禁用");
    })
    .catch(() => {
      // ElMessage.error("操作失败");
      row.isEnable = enableValue === 1 ? 0 : 1; // 回滚
    })
    .finally(() => {
      enableLoading.value = false;
      currentEnableId.value = undefined;
    });
}

onMounted(() => {
  handleQuery();
});
</script>
