import{d as e,S as s,r as t,e as a,f as i,w as l,m as r,C as o,i as d,F as n,$ as p,g as m,P as u,Q as f,h as g}from"./index.Dk5pbsTU.js";import{E as v}from"./el-drawer.Df_TzNjH.js";import"./el-overlay.DpVCS8zG.js";import{b as h,d as c,E as y}from"./el-main.CclDHmVj.js";import{E as b,a as _}from"./el-descriptions-item.BlvmJIy_.js";/* empty css               */import{E as x}from"./el-image-viewer.BH897zgF.js";import{a as w}from"./liveSession.RdnykuzD.js";import j from"./commentList.DWUu4lMl.js";import{E as S}from"./index.L2DVy5yq.js";const I={style:{display:"flex","align-items":"center"}},E={style:{"margin-left":"5px"}},k=e({__name:"lookUserInfo",props:{sessionId:{type:String,default:""}},setup(e,{expose:k}){const J=s({visible:!1,title:"发言详情"}),L=t({}),N=t({});return k({open:e=>{w.watchingUserInfo(e.userId).then((e=>{N.value=e})),L.value=JSON.parse(JSON.stringify(e)),J.visible=!0}}),(s,t)=>{const w=x,k=_,U=S,V=b,z=c,H=y,O=h,P=v;return i(),a(P,{modelValue:d(J).visible,"onUpdate:modelValue":t[0]||(t[0]=e=>d(J).visible=e),title:d(J).title,size:"80vw"},{default:l((()=>[r(O,{style:{height:"100%"}},{default:l((()=>[r(z,{style:{padding:"0 0 10px 0",height:"auto"}},{default:l((()=>[r(V,{"label-width":"80",border:"",column:4},{default:l((()=>[r(k,{"label-width":"0px"},{default:l((()=>[o("div",I,[r(w,{style:{width:"30px",height:"30px","border-radius":"30px"},"preview-src-list":[d(L).avatar],"preview-teleported":"",src:d(L).avatar},null,8,["preview-src-list","src"]),o("span",E,n(d(L).nickname),1)])])),_:1}),r(k,{label:"当前等级"},{default:l((()=>[p(n(d(N).currentLevel||"-"),1)])),_:1}),r(k,{label:"本场累计时长"},{default:l((()=>[p(n(d(L).durationStr),1)])),_:1}),r(k,{label:"当前积分"},{default:l((()=>[p(n(d(N).currentPoints||"-"),1)])),_:1}),r(k,{span:4},{default:l((()=>[(i(!0),m(u,null,f(d(L).tagList,((e,s)=>(i(),a(U,{key:s,round:"",effect:"light",size:"small",style:{"margin-right":"5px"},type:"primary"},{default:l((()=>[p(n(e),1)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1}),r(H,{style:{padding:"0",height:"100%"}},{default:l((()=>[d(J).visible?(i(),a(j,{key:0,hideHead:"","user-id":String(d(L).userId),"session-id":Number(e.sessionId)},null,8,["user-id","session-id"])):g("",!0)])),_:1})])),_:1})])),_:1},8,["modelValue","title"])}}});export{k as _};
