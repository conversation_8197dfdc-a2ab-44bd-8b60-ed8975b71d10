import{d as e,r as a,S as l,e as t,f as s,w as o,m as i,C as n,$ as r,az as u}from"./index.Dk5pbsTU.js";import{E as m}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as d}from"./el-button.CXI119n4.js";import{E as f,a as p}from"./el-form-item.Bw6Zyv_7.js";import{E as c}from"./el-input.DiGatoux.js";import{T as g}from"./tags.B175GojK.js";const v={class:"dialog-footer"},_=e({name:"EditTag",__name:"edit",emits:["success"],setup(e,{expose:_,emit:h}){const j=h,b=a(!1),y=a(!1),w=a(),x=l({id:"",name:"",status:1}),V=l({name:[{required:!0,message:"请输入标签名称",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]});function E(){C()}function k(){var e;null==(e=w.value)||e.validate((e=>{if(e)if(y.value=!0,x.id)g.update(x).then((()=>{u.success("操作成功"),j("success"),C()})).finally((()=>{y.value=!1}));else{const e=x.name.split(",").map((e=>e.trim())).filter((e=>e));if(0===e.length)return u.warning("请输入有效的标签名称"),void(y.value=!1);const a={tagNames:e.join(",")};g.batchAdd(a).then((()=>{u.success("操作成功"),j("success"),C()})).finally((()=>{y.value=!1}))}}))}function C(){b.value=!1,x.name="",x.status=1,x.id=""}return _({open:function(e){C(),e&&Object.assign(x,e),b.value=!0}}),(e,a)=>{const l=c,u=p,g=f,_=d,h=m;return s(),t(h,{title:x.id?"编辑标签":"新增标签",modelValue:b.value,"onUpdate:modelValue":a[1]||(a[1]=e=>b.value=e),width:"500px","append-to-body":""},{footer:o((()=>[n("div",v,[i(_,{type:"primary",onClick:k,loading:y.value},{default:o((()=>a[2]||(a[2]=[r("确 定")]))),_:1,__:[2]},8,["loading"]),i(_,{onClick:E},{default:o((()=>a[3]||(a[3]=[r("取 消")]))),_:1,__:[3]})])])),default:o((()=>[i(g,{ref_key:"formRef",ref:w,model:x,"label-position":"top",rules:V,"label-width":"80px"},{default:o((()=>[i(u,{label:"标签名称",prop:"name"},{default:o((()=>[i(l,{modelValue:x.name,"onUpdate:modelValue":a[0]||(a[0]=e=>x.name=e),placeholder:x.id?"请输入标签名称":"请输入标签名称，多个标签用英文逗号分隔",maxlength:11,"show-word-limit":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"])}}});export{_};
