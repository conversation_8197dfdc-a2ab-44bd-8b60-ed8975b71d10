import{aT as e}from"./index.Dk5pbsTU.js";const t="/api/v1/config",a={getPage:a=>e({url:`${t}/page`,method:"get",params:a}),getValueByKey:a=>e({url:`${t}/form/${a}`,method:"get"}),getFormData:a=>e({url:`${t}/${a}/form`,method:"get"}),add:a=>e({url:`${t}`,method:"post",data:a}),update:(a,r)=>e({url:`${t}/${a}`,method:"put",data:r}),deleteById:a=>e({url:`${t}/${a}`,method:"delete"}),refreshCache:()=>e({url:`${t}/refresh`,method:"PUT"})};export{a as C};
