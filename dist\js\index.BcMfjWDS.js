import{a0 as e,K as o,_ as n,d as t,ap as a,e as s,f as l,w as r,V as i,m as c,C as u,n as d,j as p,k as f,g as m,h as v,D as g,F as b,Z as h,l as y,$ as C,X as E,T as x,bT as w,E as B,bU as k,c as I,r as M,S as T,bV as L,bh as S,bW as R,I as _,o as z,a6 as A,bX as V,br as j,L as P,H as $,bE as O,bY as H,O as K,aa as D,bm as U,a5 as q}from"./index.Dk5pbsTU.js";import{E as F}from"./el-button.CXI119n4.js";import{E as W}from"./el-input.DiGatoux.js";import{E as X,b as Z,c as Y}from"./el-overlay.DpVCS8zG.js";import{o as G,E as J}from"./index.C6NthMtN.js";import{i as N}from"./validator.HGn2BZtD.js";import{u as Q}from"./index.D6CER_Ot.js";import{u as ee}from"./index.DFyomGhz.js";const oe="_trap-focus-children",ne=[],te=e=>{if(0===ne.length)return;const n=ne[ne.length-1][oe];if(n.length>0&&e.code===o.tab){if(1===n.length)return e.preventDefault(),void(document.activeElement!==n[0]&&n[0].focus());const o=e.shiftKey,t=e.target===n[0],a=e.target===n[n.length-1];t&&o&&(e.preventDefault(),n[n.length-1].focus()),a&&!o&&(e.preventDefault(),n[0].focus())}};var ae=n(t({name:"ElMessageBox",directives:{TrapFocus:{beforeMount(e){e[oe]=G(e),ne.push(e),ne.length<=1&&document.addEventListener("keydown",te)},updated(o){e((()=>{o[oe]=G(o)}))},unmounted(){ne.shift(),0===ne.length&&document.removeEventListener("keydown",te)}}},components:{ElButton:F,ElFocusTrap:J,ElInput:W,ElOverlay:X,ElIcon:B,...w},inheritAttrs:!1,props:{buttonSize:{type:String,validator:N},modal:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},showClose:{type:Boolean,default:!0},closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},closeOnHashChange:{type:Boolean,default:!0},center:Boolean,draggable:Boolean,overflow:Boolean,roundButton:{default:!1,type:Boolean},container:{type:String,default:"body"},boxType:{type:String,default:""}},emits:["vanish","action"],setup(o,{emit:n}){const{locale:t,zIndex:a,ns:s,size:l}=k("message-box",I((()=>o.buttonSize))),{t:r}=t,{nextZIndex:i}=a,c=M(!1),u=T({autofocus:!0,beforeClose:null,callback:null,cancelButtonText:"",cancelButtonClass:"",confirmButtonText:"",confirmButtonClass:"",customClass:"",customStyle:{},dangerouslyUseHTMLString:!1,distinguishCancelAndClose:!1,icon:"",closeIcon:"",inputPattern:null,inputPlaceholder:"",inputType:"text",inputValue:"",inputValidator:void 0,inputErrorMessage:"",message:"",modalFade:!0,modalClass:"",showCancelButton:!1,showConfirmButton:!0,type:"",title:void 0,showInput:!1,action:"",confirmButtonLoading:!1,cancelButtonLoading:!1,confirmButtonLoadingIcon:L(S),cancelButtonLoadingIcon:L(S),confirmButtonDisabled:!1,editorErrorMessage:"",validateError:!1,zIndex:i()}),d=I((()=>{const e=u.type;return{[s.bm("icon",e)]:e&&R[e]}})),p=Q(),f=Q(),m=I((()=>{const e=u.type;return u.icon||e&&R[e]||""})),v=I((()=>!!u.message)),g=M(),b=M(),h=M(),y=M(),C=M(),E=I((()=>u.confirmButtonClass));_((()=>u.inputValue),(async n=>{await e(),"prompt"===o.boxType&&n&&K()}),{immediate:!0}),_((()=>c.value),(n=>{var t,a;n&&("prompt"!==o.boxType&&(u.autofocus?h.value=null!=(a=null==(t=C.value)?void 0:t.$el)?a:g.value:h.value=g.value),u.zIndex=i()),"prompt"===o.boxType&&(n?e().then((()=>{var e;y.value&&y.value.$el&&(u.autofocus?h.value=null!=(e=D())?e:g.value:h.value=g.value)})):(u.editorErrorMessage="",u.validateError=!1))}));const x=I((()=>o.draggable)),w=I((()=>o.overflow));function B(){c.value&&(c.value=!1,e((()=>{u.action&&n("action",u.action)})))}ee(g,b,x,w),z((async()=>{await e(),o.closeOnHashChange&&window.addEventListener("hashchange",B)})),A((()=>{o.closeOnHashChange&&window.removeEventListener("hashchange",B)}));const $=()=>{o.closeOnClickModal&&H(u.distinguishCancelAndClose?"close":"cancel")},O=Y($),H=e=>{var n;("prompt"!==o.boxType||"confirm"!==e||K())&&(u.action=e,u.beforeClose?null==(n=u.beforeClose)||n.call(u,e,u,B):B())},K=()=>{if("prompt"===o.boxType){const e=u.inputPattern;if(e&&!e.test(u.inputValue||""))return u.editorErrorMessage=u.inputErrorMessage||r("el.messagebox.error"),u.validateError=!0,!1;const o=u.inputValidator;if(j(o)){const e=o(u.inputValue);if(!1===e)return u.editorErrorMessage=u.inputErrorMessage||r("el.messagebox.error"),u.validateError=!0,!1;if(P(e))return u.editorErrorMessage=e,u.validateError=!0,!1}}return u.editorErrorMessage="",u.validateError=!1,!0},D=()=>{var e,o;const n=null==(e=y.value)?void 0:e.$refs;return null!=(o=null==n?void 0:n.input)?o:null==n?void 0:n.textarea},U=()=>{H("close")};return o.lockScroll&&Z(c),{...V(u),ns:s,overlayEvent:O,visible:c,hasMessage:v,typeClass:d,contentId:p,inputId:f,btnSize:l,iconComponent:m,confirmButtonClasses:E,rootRef:g,focusStartRef:h,headerRef:b,inputRef:y,confirmRef:C,doClose:B,handleClose:U,onCloseRequested:()=>{o.closeOnPressEscape&&U()},handleWrapperClick:$,handleInputEnter:e=>{if("textarea"!==u.inputType)return e.preventDefault(),H("confirm")},handleAction:H,t:r}}}),[["render",function(e,o,n,t,w,B){const k=a("el-icon"),I=a("el-input"),M=a("el-button"),T=a("el-focus-trap"),L=a("el-overlay");return l(),s(x,{name:"fade-in-linear",onAfterLeave:o=>e.$emit("vanish"),persisted:""},{default:r((()=>[i(c(L,{"z-index":e.zIndex,"overlay-class":[e.ns.is("message-box"),e.modalClass],mask:e.modal},{default:r((()=>[u("div",{role:"dialog","aria-label":e.title,"aria-modal":"true","aria-describedby":e.showInput?void 0:e.contentId,class:d(`${e.ns.namespace.value}-overlay-message-box`),onClick:e.overlayEvent.onClick,onMousedown:e.overlayEvent.onMousedown,onMouseup:e.overlayEvent.onMouseup},[c(T,{loop:"",trapped:e.visible,"focus-trap-el":e.rootRef,"focus-start-el":e.focusStartRef,onReleaseRequested:e.onCloseRequested},{default:r((()=>[u("div",{ref:"rootRef",class:d([e.ns.b(),e.customClass,e.ns.is("draggable",e.draggable),{[e.ns.m("center")]:e.center}]),style:f(e.customStyle),tabindex:"-1",onClick:p((()=>{}),["stop"])},[null!==e.title&&void 0!==e.title?(l(),m("div",{key:0,ref:"headerRef",class:d([e.ns.e("header"),{"show-close":e.showClose}])},[u("div",{class:d(e.ns.e("title"))},[e.iconComponent&&e.center?(l(),s(k,{key:0,class:d([e.ns.e("status"),e.typeClass])},{default:r((()=>[(l(),s(g(e.iconComponent)))])),_:1},8,["class"])):v("v-if",!0),u("span",null,b(e.title),1)],2),e.showClose?(l(),m("button",{key:0,type:"button",class:d(e.ns.e("headerbtn")),"aria-label":e.t("el.messagebox.close"),onClick:o=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel"),onKeydown:h(p((o=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel")),["prevent"]),["enter"])},[c(k,{class:d(e.ns.e("close"))},{default:r((()=>[(l(),s(g(e.closeIcon||"close")))])),_:1},8,["class"])],42,["aria-label","onClick","onKeydown"])):v("v-if",!0)],2)):v("v-if",!0),u("div",{id:e.contentId,class:d(e.ns.e("content"))},[u("div",{class:d(e.ns.e("container"))},[e.iconComponent&&!e.center&&e.hasMessage?(l(),s(k,{key:0,class:d([e.ns.e("status"),e.typeClass])},{default:r((()=>[(l(),s(g(e.iconComponent)))])),_:1},8,["class"])):v("v-if",!0),e.hasMessage?(l(),m("div",{key:1,class:d(e.ns.e("message"))},[y(e.$slots,"default",{},(()=>[e.dangerouslyUseHTMLString?(l(),s(g(e.showInput?"label":"p"),{key:1,for:e.showInput?e.inputId:void 0,innerHTML:e.message},null,8,["for","innerHTML"])):(l(),s(g(e.showInput?"label":"p"),{key:0,for:e.showInput?e.inputId:void 0},{default:r((()=>[C(b(e.dangerouslyUseHTMLString?"":e.message),1)])),_:1},8,["for"]))]))],2)):v("v-if",!0)],2),i(u("div",{class:d(e.ns.e("input"))},[c(I,{id:e.inputId,ref:"inputRef",modelValue:e.inputValue,"onUpdate:modelValue":o=>e.inputValue=o,type:e.inputType,placeholder:e.inputPlaceholder,"aria-invalid":e.validateError,class:d({invalid:e.validateError}),onKeydown:h(e.handleInputEnter,["enter"])},null,8,["id","modelValue","onUpdate:modelValue","type","placeholder","aria-invalid","class","onKeydown"]),u("div",{class:d(e.ns.e("errormsg")),style:f({visibility:e.editorErrorMessage?"visible":"hidden"})},b(e.editorErrorMessage),7)],2),[[E,e.showInput]])],10,["id"]),u("div",{class:d(e.ns.e("btns"))},[e.showCancelButton?(l(),s(M,{key:0,loading:e.cancelButtonLoading,"loading-icon":e.cancelButtonLoadingIcon,class:d([e.cancelButtonClass]),round:e.roundButton,size:e.btnSize,onClick:o=>e.handleAction("cancel"),onKeydown:h(p((o=>e.handleAction("cancel")),["prevent"]),["enter"])},{default:r((()=>[C(b(e.cancelButtonText||e.t("el.messagebox.cancel")),1)])),_:1},8,["loading","loading-icon","class","round","size","onClick","onKeydown"])):v("v-if",!0),i(c(M,{ref:"confirmRef",type:"primary",loading:e.confirmButtonLoading,"loading-icon":e.confirmButtonLoadingIcon,class:d([e.confirmButtonClasses]),round:e.roundButton,disabled:e.confirmButtonDisabled,size:e.btnSize,onClick:o=>e.handleAction("confirm"),onKeydown:h(p((o=>e.handleAction("confirm")),["prevent"]),["enter"])},{default:r((()=>[C(b(e.confirmButtonText||e.t("el.messagebox.confirm")),1)])),_:1},8,["loading","loading-icon","class","round","disabled","size","onClick","onKeydown"]),[[E,e.showConfirmButton]])],2)],14,["onClick"])])),_:3},8,["trapped","focus-trap-el","focus-start-el","onReleaseRequested"])],42,["aria-label","aria-describedby","onClick","onMousedown","onMouseup"])])),_:3},8,["z-index","overlay-class","mask"]),[[E,e.visible]])])),_:3},8,["onAfterLeave"])}],["__file","index.vue"]]);const se=new Map,le=(e,o,n=null)=>{const t=c(ae,e,j(e.message)||O(e.message)?{default:j(e.message)?e.message:()=>e.message}:null);return t.appContext=n,H(t,o),(e=>{let o=document.body;return e.appendTo&&(P(e.appendTo)&&(o=document.querySelector(e.appendTo)),U(e.appendTo)&&(o=e.appendTo),U(o)||(o=document.body)),o})(e).appendChild(o.firstElementChild),t.component},re=(e,o)=>{const n=document.createElement("div");e.onVanish=()=>{H(null,n),se.delete(a)},e.onAction=o=>{const n=se.get(a);let s;s=e.showInput?{value:a.inputValue,action:o}:o,e.callback?e.callback(s,t.proxy):"cancel"===o||"close"===o?e.distinguishCancelAndClose&&"cancel"!==o?n.reject("close"):n.reject("cancel"):n.resolve(s)};const t=le(e,n,o),a=t.proxy;for(const s in e)K(e,s)&&!K(a.$props,s)&&("closeIcon"===s&&D(e[s])?a[s]=L(e[s]):a[s]=e[s]);return a.visible=!0,a};function ie(e,o=null){if(!$)return Promise.reject();let n;return P(e)||O(e)?e={message:e}:n=e.callback,new Promise(((t,a)=>{const s=re(e,null!=o?o:ie._context);se.set(s,{options:e,callback:n,resolve:t,reject:a})}))}const ce={alert:{closeOnPressEscape:!1,closeOnClickModal:!1},confirm:{showCancelButton:!0},prompt:{showCancelButton:!0,showInput:!0}};["alert","confirm","prompt"].forEach((e=>{ie[e]=function(e){return(o,n,t,a)=>{let s="";return D(n)?(t=n,s=""):s=q(n)?"":n,ie(Object.assign({title:s,message:o,type:"",...ce[e]},t,{boxType:e}),a)}}(e)})),ie.close=()=>{se.forEach(((e,o)=>{o.doClose()})),se.clear()},ie._context=null;const ue=ie;ue.install=e=>{ue._context=e._context,e.config.globalProperties.$msgbox=ue,e.config.globalProperties.$messageBox=ue,e.config.globalProperties.$alert=ue.alert,e.config.globalProperties.$confirm=ue.confirm,e.config.globalProperties.$prompt=ue.prompt};const de=ue;export{de as E};
