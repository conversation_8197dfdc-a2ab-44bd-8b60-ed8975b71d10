import{_ as e,d as t,b9 as s,b as a,c as r,g as n,f as i,l as o,n as l,i as c,k as u,q as d,G as h}from"./index.Dk5pbsTU.js";const f=t({name:"ElContainer"});var p=e(t({...f,props:{direction:{type:String}},setup(e){const t=e,u=s(),d=a("container"),h=r((()=>{if("vertical"===t.direction)return!0;if("horizontal"===t.direction)return!1;if(u&&u.default){return u.default().some((e=>{const t=e.type.name;return"ElHeader"===t||"ElFooter"===t}))}return!1}));return(e,t)=>(i(),n("section",{class:l([c(d).b(),c(d).is("vertical",c(h))])},[o(e.$slots,"default")],2))}}),[["__file","container.vue"]]);const g=t({name:"ElAside"});var m=e(t({...g,props:{width:{type:String,default:null}},setup(e){const t=e,s=a("aside"),d=r((()=>t.width?s.cssVarBlock({width:t.width}):{}));return(e,t)=>(i(),n("aside",{class:l(c(s).b()),style:u(c(d))},[o(e.$slots,"default")],6))}}),[["__file","aside.vue"]]);const v=t({name:"ElFooter"});var _=e(t({...v,props:{height:{type:String,default:null}},setup(e){const t=e,s=a("footer"),d=r((()=>t.height?s.cssVarBlock({height:t.height}):{}));return(e,t)=>(i(),n("footer",{class:l(c(s).b()),style:u(c(d))},[o(e.$slots,"default")],6))}}),[["__file","footer.vue"]]);const b=t({name:"ElHeader"});var y=e(t({...b,props:{height:{type:String,default:null}},setup(e){const t=e,s=a("header"),d=r((()=>t.height?s.cssVarBlock({height:t.height}):{}));return(e,t)=>(i(),n("header",{class:l(c(s).b()),style:u(c(d))},[o(e.$slots,"default")],6))}}),[["__file","header.vue"]]);const E=t({name:"ElMain"});var $=e(t({...E,setup(e){const t=a("main");return(e,s)=>(i(),n("main",{class:l(c(t).b())},[o(e.$slots,"default")],2))}}),[["__file","main.vue"]]);const k=d(p,{Aside:m,Footer:_,Header:y,Main:$}),w=h(m),S=h(_),B=h(y),F=h($);export{F as E,S as a,k as b,w as c,B as d};
