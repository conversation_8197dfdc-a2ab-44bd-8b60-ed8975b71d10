<!-- 马甲管理 -->
<template>
  <el-dialog
    v-model="visible"
    title="马甲管理"
    width="1000px"
    append-to-body
    destroy-on-close
    @closed="handleClose"
  >
    <div class="vest-container">
      <!-- 左侧分组列表 -->
      <div class="group-list">
        <div class="group-header">
          <el-input
            v-model="groupQuery.groupName"
            placeholder="搜索分组"
            clearable
            @keyup.enter="getGroupPage"
          >
            <template #prefix>
              <el-icon><search /></el-icon>
            </template>
          </el-input>
        </div>
        <el-scrollbar>
          <ul class="group-items" v-loading="groupLoading">
            <li
              v-for="group in groupList"
              :key="group.id"
              :class="{ active: selectedGroup?.id === group.id }"
              @click="handleGroupSelect(group)"
            >
              <span class="group-name">{{ group.groupName }}</span>
              <el-button
                v-hasPerm="['live:vest']"
                type="danger"
                link
                icon="delete"
                @click.stop="handleDeleteGroup(group)"
              />
            </li>
            <li v-if="!groupLoading && (!groupList || groupList.length === 0)" class="no-data">
              暂无分组数据
            </li>
          </ul>
        </el-scrollbar>
        <div class="group-footer">
          <el-input
            v-model="newGroupName"
            placeholder="输入分组名称"
            @keyup.enter="handleAddGroup"
          >
            <template #append>
              <el-button @click="handleAddGroup">
                <el-icon><plus /></el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 右侧马甲管理 -->
      <div class="vest-panel">
        <div class="panel-header">
          <span>{{ selectedGroup ? selectedGroup.groupName : '请选择分组' }}</span>
          <el-button
            v-hasPerm="['live:vest']"
            type="primary"
            link
            icon="plus"
            :disabled="!selectedGroup"
            @click="handleAddVest"
          >
            新增马甲
          </el-button>
        </div>
        <div class="search-bar">
          <el-input
            v-model="vestQuery.nickname"
            placeholder="马甲昵称"
            clearable
            @keyup.enter="getVestPage"
          />
          <el-button type="primary" icon="search" @click="getVestPage">搜索</el-button>
        </div>
        <el-table ref="vestTableRef" v-loading="vestLoading" :data="vestList" highlight-current-row border @row-click="row => console.log('Row clicked:', row)">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column label="头像" width="70" align="center">
            <template #default="{ row }">
              <el-avatar :size="40" :src="row.avatar" :fit="'cover'" />
            </template>
          </el-table-column>
          <el-table-column label="昵称" prop="nickname" min-width="120" show-overflow-tooltip />
          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'info'">
                {{ row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createdAt" width="160" align="center" show-overflow-tooltip />
          <el-table-column fixed="right" label="操作" width="120" align="center">
            <template #default="scope">
              <el-button
                v-hasPerm="['live:vest']"
                type="primary"
                link
                icon="edit"
                @click="handleEditVest(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                v-hasPerm="['live:vest']"
                type="danger"
                link
                icon="delete"
                @click="handleDeleteVest(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="vestTotal"
          v-model:total="vestTotal"
          v-model:page="vestQuery.pageNum"
          v-model:limit="vestQuery.pageSize"
          @pagination="getVestPage"
        />
      </div>
    </div>

    <!-- 马甲表单 -->
    <el-dialog
      v-model="vestDialog.visible"
      :title="vestDialog.title"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="vestFormRef"
        :model="vestForm"
        :rules="vestRules"
        label-width="100px"
      >
        <el-form-item label="马甲昵称" prop="nickname">
          <el-input v-model="vestForm.nickname" placeholder="请输入马甲昵称" />
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <single-image-upload v-model="vestForm.avatar" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="vestForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitVestForm">确 定</el-button>
          <el-button @click="vestDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import VestApi, {
  VestGroupPageDto,
  VestGroupVo,
  VestGroupDto,
  VestPageDto,
  VestVo,
  VestDto
} from '@/api/live/vest'

const props = defineProps<{
  roomId: number
}>()

const emit = defineEmits(['success'])

// Dialog visibility
const visible = ref(false)

// Group management
const groupQuery = reactive<VestGroupPageDto>({
  roomId: props.roomId,
  pageNum: 1,
  pageSize: 999, // 加载所有分组
  groupName: ''
})
const groupList = ref<VestGroupVo[]>([])
const groupLoading = ref(false)
const selectedGroup = ref<VestGroupVo>()
const newGroupName = ref('')

// Vest management
const vestQuery = reactive<VestPageDto>({
  pageNum: 1,
  pageSize: 10,
  nickname: ''
})
const vestList = ref<VestVo[]>([])
const vestTotal = ref(0)
const vestLoading = ref(false)

// Vest form
const vestDialog = reactive({
  visible: false,
  title: ''
})
const vestFormRef = ref<FormInstance>()
const vestForm = reactive<VestDto>({
  nickname: '',
  avatar: '',
  status: 1,
  roomId: 0,
  groupId: 0
})
const vestRules: FormRules = {
  nickname: [{ required: true, message: '请输入马甲昵称', trigger: 'blur' }],
  avatar: [{ required: true, message: '请上传头像', trigger: 'change' }]
}

// Response types
interface PageResult<T> {
  records: T[]
  pageNumber: number
  pageSize: number
  totalPage: number
  totalRow: number
}

// Watch roomId changes
watch(() => props.roomId, (newRoomId) => {
  if (newRoomId) {
    groupQuery.roomId = newRoomId
    getGroupPage()
  }
}, { immediate: true })

// Methods
function open() {
  visible.value = true
  getGroupPage()
}

function handleClose() {
  selectedGroup.value = undefined
  vestList.value = []
  vestTotal.value = 0
  newGroupName.value = ''
}

async function getGroupPage() {
  if (!props.roomId) return
  groupLoading.value = true
  try {
    const response = (await VestApi.groupPage(groupQuery)) as unknown as PageResult<VestGroupVo>
    groupList.value = response.records || []
  } catch (error) {
    console.error('Failed to fetch groups:', error)
    groupList.value = []
  } finally {
    groupLoading.value = false
  }
}

async function getVestPage() {
  if (!selectedGroup.value) return
  vestQuery.groupId = selectedGroup.value.id
  vestLoading.value = true
  try {
    const response = (await VestApi.page(vestQuery)) as unknown as PageResult<VestVo>
    console.log('Vest list data:', response)
    vestList.value = [...(response.records || [])]
    vestTotal.value = response.totalRow || 0
    console.log('Updated vestList:', vestList.value)
  } catch (error) {
    console.error('Failed to fetch vests:', error)
    vestList.value = []
    vestTotal.value = 0
  } finally {
    vestLoading.value = false
  }
}

// 监听vestList变化
watch(vestList, (newVal) => {
  console.log('vestList changed:', newVal)
}, { deep: true })

function handleGroupSelect(row: VestGroupVo) {
  selectedGroup.value = row
  vestQuery.pageNum = 1
  getVestPage()
}

async function handleAddGroup() {
  if (!newGroupName.value) {
    ElMessage.warning('请输入分组名称')
    return
  }
  await VestApi.saveGroup({
    groupName: newGroupName.value,
    roomId: props.roomId
  })
  ElMessage.success('新增成功')
  newGroupName.value = ''
  getGroupPage()
}

async function handleDeleteGroup(row: VestGroupVo) {
  try {
    await ElMessageBox.confirm(`确认删除分组"${row.groupName}"吗？`, '提示', {
      type: 'warning'
    })
    await VestApi.removeGroup(row.id)
    ElMessage.success('删除成功')
    if (selectedGroup.value?.id === row.id) {
      selectedGroup.value = undefined
      vestList.value = []
      vestTotal.value = 0
    }
    getGroupPage()
  } catch (error) {
    if (error !== 'cancel') console.error(error)
  }
}

function handleAddVest() {
  if (!selectedGroup.value) return
  vestDialog.title = '新增马甲'
  vestForm.id = undefined
  vestForm.nickname = ''
  vestForm.avatar = ''
  vestForm.status = 1
  vestForm.roomId = props.roomId
  vestForm.groupId = selectedGroup.value.id
  vestDialog.visible = true
}

function handleEditVest(row: VestVo) {
  vestDialog.title = '编辑马甲'
  vestForm.id = row.id
  vestForm.nickname = row.nickname
  vestForm.avatar = row.avatar
  vestForm.status = row.status
  vestForm.roomId = props.roomId
  vestForm.groupId = row.groupId
  vestDialog.visible = true
}

async function handleDeleteVest(row: VestVo) {
  try {
    await ElMessageBox.confirm(`确认删除马甲"${row.nickname}"吗？`, '提示', {
      type: 'warning'
    })
    await VestApi.remove(row.id)
    ElMessage.success('删除成功')
    getVestPage()
  } catch (error) {
    if (error !== 'cancel') console.error(error)
  }
}

async function submitVestForm() {
  if (!vestFormRef.value) return
  await vestFormRef.value.validate()
  await VestApi.save(vestForm)
  ElMessage.success(vestForm.id ? '修改成功' : '新增成功')
  vestDialog.visible = false
  getVestPage()
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.vest-container {
  display: flex;
  gap: 20px;
  height: 600px;

  .group-list {
    width: 200px;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    overflow: hidden;

    .group-header {
      padding: 12px;
      border-bottom: 1px solid var(--el-border-color);
      background-color: var(--el-fill-color-light);
    }

    .group-items {
      flex: 1;
      overflow: auto;
      list-style: none;
      margin: 0;
      padding: 0;
      min-height: 100px;

      .no-data {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100px;
        color: var(--el-text-color-secondary);
        font-size: 14px;
      }

      li {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 12px;
        cursor: pointer;
        transition: all 0.2s;
        border-bottom: 1px solid var(--el-border-color-light);

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: var(--el-fill-color-light);
        }

        &.active {
          background-color: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
        }

        .group-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
        }

        .el-button {
          padding: 2px;
          opacity: 0;
          transition: opacity 0.2s;
        }

        &:hover .el-button {
          opacity: 1;
        }
      }
    }

    .group-footer {
      padding: 12px;
      border-top: 1px solid var(--el-border-color);
      background-color: var(--el-fill-color-light);
    }
  }

  .vest-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    padding: 16px;
    min-width: 0;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      span {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .search-bar {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
    }

    .el-table {
      min-height: 400px;
    }

    :deep(.el-table__inner-wrapper) {
      height: 100%;
    }

    :deep(.el-table__body-wrapper) {
      overflow-y: auto;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style> 