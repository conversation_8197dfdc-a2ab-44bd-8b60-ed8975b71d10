import{L as e,b6 as a,bb as l,t as o,N as s,r as i,A as d,c as r,c9 as u,_ as n,d as t,b as m,g as p,f as c,C as v,V as b,ct as f,i as y,j as g,n as V,ak as k,l as B,$ as S,F as h,a0 as x,k as C,o as R,y as _,S as j,bX as G,I as E,G as I,q as U}from"./index.Dk5pbsTU.js";import{C as z,U as F}from"./event.BwRzfsZt.js";import{u as w,a as K}from"./use-form-common-props.CQPDkY7k.js";import{u as N}from"./index.DuiNpp1i.js";import{u as $}from"./index.C9UdVphc.js";import{u as L}from"./index.D6CER_Ot.js";import{u as A,a as q}from"./use-form-item.DzRJVC1I.js";import{d as O}from"./error.D_Dr4eZ1.js";const P=o({modelValue:{type:[String,Number,Boolean],default:void 0},size:s,disabled:Boolean,label:{type:[String,Number,Boolean],default:void 0},value:{type:[String,Number,Boolean],default:void 0},name:{type:String,default:void 0}}),X=o({...P,border:Boolean}),D={[F]:o=>e(o)||a(o)||l(o),[z]:o=>e(o)||a(o)||l(o)},H=Symbol("radioGroupKey"),J=(e,a)=>{const l=i(),o=d(H,void 0),s=r((()=>!!o)),n=r((()=>u(e.value)?e.label:e.value)),t=r({get:()=>s.value?o.modelValue:e.modelValue,set(i){s.value?o.changeEvent(i):a&&a(F,i),l.value.checked=e.modelValue===n.value}}),m=w(r((()=>null==o?void 0:o.size))),p=K(r((()=>null==o?void 0:o.disabled))),c=i(!1),v=r((()=>p.value||s.value&&t.value!==n.value?-1:0));return N({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-radio",ref:"https://element-plus.org/en-US/component/radio.html"},r((()=>s.value&&u(e.value)))),{radioRef:l,isGroup:s,radioGroup:o,focus:c,size:m,disabled:p,tabIndex:v,modelValue:t,actualValue:n}},M=t({name:"ElRadio"});var Q=n(t({...M,props:X,emits:D,setup(e,{emit:a}){const l=e,o=m("radio"),{radioRef:s,radioGroup:i,focus:d,size:r,disabled:u,modelValue:n,actualValue:t}=J(l,a);function C(){x((()=>a(z,n.value)))}return(e,a)=>{var l;return c(),p("label",{class:V([y(o).b(),y(o).is("disabled",y(u)),y(o).is("focus",y(d)),y(o).is("bordered",e.border),y(o).is("checked",y(n)===y(t)),y(o).m(y(r))])},[v("span",{class:V([y(o).e("input"),y(o).is("disabled",y(u)),y(o).is("checked",y(n)===y(t))])},[b(v("input",{ref_key:"radioRef",ref:s,"onUpdate:modelValue":e=>k(n)?n.value=e:null,class:V(y(o).e("original")),value:y(t),name:e.name||(null==(l=y(i))?void 0:l.name),disabled:y(u),checked:y(n)===y(t),type:"radio",onFocus:e=>d.value=!0,onBlur:e=>d.value=!1,onChange:C,onClick:g((()=>{}),["stop"])},null,42,["onUpdate:modelValue","value","name","disabled","checked","onFocus","onBlur","onClick"]),[[f,y(n)]]),v("span",{class:V(y(o).e("inner"))},null,2)],2),v("span",{class:V(y(o).e("label")),onKeydown:g((()=>{}),["stop"])},[B(e.$slots,"default",{},(()=>[S(h(e.label),1)]))],42,["onKeydown"])],2)}}}),[["__file","radio.vue"]]);const T=o({...P}),W=t({name:"ElRadioButton"});var Y=n(t({...W,props:T,setup(e){const a=e,l=m("radio"),{radioRef:o,focus:s,size:i,disabled:d,modelValue:u,radioGroup:n,actualValue:t}=J(a),x=r((()=>({backgroundColor:(null==n?void 0:n.fill)||"",borderColor:(null==n?void 0:n.fill)||"",boxShadow:(null==n?void 0:n.fill)?`-1px 0 0 0 ${n.fill}`:"",color:(null==n?void 0:n.textColor)||""})));return(e,a)=>{var r;return c(),p("label",{class:V([y(l).b("button"),y(l).is("active",y(u)===y(t)),y(l).is("disabled",y(d)),y(l).is("focus",y(s)),y(l).bm("button",y(i))])},[b(v("input",{ref_key:"radioRef",ref:o,"onUpdate:modelValue":e=>k(u)?u.value=e:null,class:V(y(l).be("button","original-radio")),value:y(t),type:"radio",name:e.name||(null==(r=y(n))?void 0:r.name),disabled:y(d),onFocus:e=>s.value=!0,onBlur:e=>s.value=!1,onClick:g((()=>{}),["stop"])},null,42,["onUpdate:modelValue","value","name","disabled","onFocus","onBlur","onClick"]),[[f,y(u)]]),v("span",{class:V(y(l).be("button","inner")),style:C(y(u)===y(t)?y(x):{}),onKeydown:g((()=>{}),["stop"])},[B(e.$slots,"default",{},(()=>[S(h(e.label),1)]))],46,["onKeydown"])],2)}}}),[["__file","radio-button.vue"]]);const Z=o({id:{type:String,default:void 0},size:s,disabled:Boolean,modelValue:{type:[String,Number,Boolean],default:void 0},fill:{type:String,default:""},textColor:{type:String,default:""},name:{type:String,default:void 0},validateEvent:{type:Boolean,default:!0},...$(["ariaLabel"])}),ee=D,ae=t({name:"ElRadioGroup"});var le=n(t({...ae,props:Z,emits:ee,setup(e,{emit:a}){const l=e,o=m("radio"),s=L(),d=i(),{formItem:u}=A(),{inputId:n,isLabeledByFormItem:t}=q(l,{formItemContext:u});R((()=>{const e=d.value.querySelectorAll("[type=radio]"),a=e[0];!Array.from(e).some((e=>e.checked))&&a&&(a.tabIndex=0)}));const v=r((()=>l.name||s.value));return _(H,j({...G(l),changeEvent:e=>{a(F,e),x((()=>a(z,e)))},name:v})),E((()=>l.modelValue),(()=>{l.validateEvent&&(null==u||u.validate("change").catch((e=>O())))})),(e,a)=>(c(),p("div",{id:y(n),ref_key:"radioGroupRef",ref:d,class:V(y(o).b("group")),role:"radiogroup","aria-label":y(t)?void 0:e.ariaLabel||"radio-group","aria-labelledby":y(t)?y(u).labelId:void 0},[B(e.$slots,"default")],10,["id","aria-label","aria-labelledby"]))}}),[["__file","radio-group.vue"]]);const oe=U(Q,{RadioButton:Y,RadioGroup:le}),se=I(le);I(Y);export{se as E,oe as a};
