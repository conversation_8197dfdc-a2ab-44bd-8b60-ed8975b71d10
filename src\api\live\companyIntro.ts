import request from '@/utils/request'

/**
 * Get company introduction
 */
export const getCompanyIntro = () => {
  return request.get('/api/basic/introduction')
}

/**
 * Save company introduction
 */
export const saveCompanyIntro = (content: string) => {
  return request.post('/api/basic/introduction', { content })
}

export interface CompanyIntroVo {
  id: number
  tenantId: number
  content: string
  createdAt: string
  updatedAt: string
} 