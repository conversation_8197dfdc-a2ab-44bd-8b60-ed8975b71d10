import request from "@/utils/request";

// 分页查询
export class WebmasterPageQuery implements PageQuery {
  pageNum = 1;
  pageSize = 10;
  // 关键字
  keywords: string = "";
  // 状态
  status: string = "";
  // 身份
  identity: string = "";

  constructor(e: string) {
    this.identity = e;
  }
}

// 分页返回内容
export interface WebmasterPageVO {
  // 主键ID
  id: number;
  // 租户用户ID
  tenantUserId: number;
  // 手机号
  mobile: string;
  // 姓名
  userName: string;
  // 昵称
  nickName: string;
  // 用户头像
  avatar: string;
  // 是否自主开播 0-否 1-是
  isAutonomous: number;
  // 状态 0-禁用 1-启用
  status: number;
  // 身份 0-主播 1-助手
  identity: number;
  // 租户ID
  tenantId: number;
  // 租户名称
  tenantName: string;
  // 创建时间
  createdAt: string;
  // 操作loading
  loading: boolean;
  // 主播介绍
  introduction: string;
  // 账号
  account: string;
  // 密码
  password: string;
}

// 保存对象
export class SaveWebmasterDto {
  id?: number; // 主播ID，新增时为空
  mobile: string = ""; // 手机号（用户名）
  userName: string = ""; // 姓名
  nickName: string = ""; // 昵称
  avatar: string = ""; // 用户头像
  isAutonomous: number = 0; // 是否自主开播 0-否 1-是
  introduction: string = ""; // 主播介绍
  account: string = ""; // 账号
  password: string = ""; // 密码

  constructor(data?: WebmasterPageVO) {
    if (data) {
      this.account = data.account;
      this.password = data.password;
      this.id = data.id;
      this.mobile = data.mobile;
      this.userName = data.userName;
      this.nickName = data.nickName;
      this.avatar = data.avatar;
      this.isAutonomous = data.isAutonomous;
      this.introduction = data.introduction;
    }
  }
}

// 修改状态
export class EditStatusWebmasterDto {
  id: number = 0; // 主播ID
  status: number = 0; // 状态 0-禁用 1-启用
  constructor(data?: WebmasterPageVO) {
    if (data) {
      this.id = data.id;
      this.status = data.status;
    }
  }
}

const WebmasterApi = {
  /** 分页 */
  page(queryParams?: WebmasterPageQuery) {
    return request<any, PageResult<WebmasterPageVO[]>>({
      url: `/webcaster/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 删除
   *
   * @param id ID
   */
  remove(id: number) {
    return request<any, null>({
      url: `/webcaster/remove/${id}`,
      method: "delete",
    });
  },

  /**
   * 保存
   * @param data 表单数据
   */
  save(data: SaveWebmasterDto) {
    return request({
      url: `/webcaster/save`,
      method: "post",
      data: data,
    });
  },
  /**
   * 修改状态
   * @param data 表单数据
   */
  editStatus(data: EditStatusWebmasterDto) {
    return request({
      url: `/webcaster/editStatus`,
      method: "post",
      data: data,
    });
  },
};

export default WebmasterApi;
