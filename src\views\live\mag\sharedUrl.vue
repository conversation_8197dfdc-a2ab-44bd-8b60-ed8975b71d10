<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px">
    <div class="p-2" style="border: 1px solid var(--el-border-color)">
      <el-descriptions title="直播间H5链接" :column="2">
        <el-descriptions-item label="直播名称" :span="2">
          {{ info.title }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <div class="flex" style="align-items: center">
            {{ sharedUrlInfo.h5Url }}
            <el-icon
              class="cursor-pointer ml-4 color-primary"
              @click="reasoningCopy(sharedUrlInfo.h5Url)"
            >
              <DocumentCopy />
            </el-icon>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="p-2 m-t-2" style="border: 1px solid var(--el-border-color)">
      <el-descriptions title="小程序码" :column="2">
        <el-descriptions-item :span="1">
          <el-image style="width: 60px; height: 60px" :src="sharedUrlInfo.miniProgramUrl" />
        </el-descriptions-item>
        <el-descriptions-item :span="1">
          <el-button type="primary" @click="reasoningCopy(sharedUrlInfo.miniProgramUrl)">
            复 制
          </el-button>
          <el-button type="warning" @click="downMiniQr">下 载</el-button>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-if="info.isSecret" class="p-2 m-t-2" style="border: 1px solid var(--el-border-color)">
      <el-descriptions title="登录口令" :column="2">
        <el-descriptions-item :span="2">
          <div class="flex" style="align-items: center">
            {{ info.secretKey }}
            <el-icon
              class="cursor-pointer ml-4 color-primary"
              @click="reasoningCopy(info.secretKey)"
            >
              <DocumentCopy />
            </el-icon>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import useClipboard from "vue-clipboard3";
import LiveSessionApi, { LiveSessionPageVo, LiveSharedUrlVo } from "@/api/live/liveSession";

const { toClipboard } = useClipboard();
const dialog = reactive({
  visible: false,
  title: "",
});

const info = ref<LiveSessionPageVo>({} as any);
const sharedUrlInfo = ref<LiveSharedUrlVo>({} as any);

function getSharedUrl() {
  LiveSessionApi.sharedUrl(info.value.id).then((res) => {
    sharedUrlInfo.value = res;
  });
}

// 下载小程序码
function downMiniQr() {
  // 创建一个<a></a>标签
  const a = document.createElement("a");
  a.href = sharedUrlInfo.value.miniProgramUrl;
  a.download = "小程序码.png";
  // 障眼法藏起来a标签
  a.style.display = "none";
  // 将a标签追加到文档对象中
  document.body.appendChild(a);
  // 模拟点击了<a>标签,会触发<a>标签的href的读取,浏览器就会自动下载了
  a.click();
  // 一次性的,用完就删除a标签
  a.remove();
}

// 思考内容复制
function reasoningCopy(_text: string) {
  toClipboard(_text)
    .then(() => {
      ElMessage.success("复制成功");
    })
    .catch(() => {
      ElMessage.error("复制失败");
    });
}

defineExpose({
  open: (_row?: LiveSessionPageVo) => {
    info.value = JSON.parse(JSON.stringify(_row));
    getSharedUrl();
    dialog.visible = true;
    dialog.title = "直播间信息分享";
  },
});
</script>
