<template>
  <el-drawer v-model="dialog.visible" :title="dialog.title" size="80vw">
    <el-form ref="editFormRef" :model="formData" :rules="rules" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="文件名" prop="fileName">
            <el-input
              v-model="formData.fileName"
              :maxlength="50"
              show-word-limit
              placeholder="请输入文件名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文件类型" prop="typeId">
            <el-select v-model="formData.typeId" style="width: 200px">
              <el-option
                v-for="item in sendInfo.data"
                :key="item.id"
                :label="item.name"
                :value="String(item.id)"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <wang-editor v-model="formData.content" />
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import KnowledgeTypeApi, {
  AiKnowledgeDto,
  KnowledgePageVo,
  KnowledgeTypePageQueryDto,
  KnowledgeTypePageVo,
} from "@/api/system/knowledgeType";
import { useApi } from "@/utils/commonSetup";

const dialog = reactive({
  visible: false,
  title: "",
});
const { sendInfo, onSend } = useApi<KnowledgeTypePageQueryDto, KnowledgeTypePageVo[]>(
  KnowledgeTypeApi.allKnowledgeType as any,
  new KnowledgeTypePageQueryDto()
);
sendInfo.params!.status = "1";
const formData = ref(new AiKnowledgeDto());
const editFormRef = ref();
const emits = defineEmits(["success"]);
const loading = ref(false);
const rules = {
  fileName: [{ required: true, message: "请输入文件名称", trigger: "blur" }],
  typeId: [{ required: true, message: "请选择文件类型", trigger: "blur" }],
};

function handleSubmit() {
  editFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      KnowledgeTypeApi.aiKnowledgeSave(formData.value)
        .then(() => {
          ElMessage.success("保存成功");
          dialog.visible = false;
          emits("success");
        })
        .finally(() => (loading.value = false));
    }
  });
}

defineExpose({
  open: (_row?: KnowledgePageVo) => {
    dialog.visible = true;
    dialog.title = _row?.id ? "编辑知识库文件" : "新增知识库文件";
    formData.value = new AiKnowledgeDto(_row);
    onSend();
  },
});
</script>
