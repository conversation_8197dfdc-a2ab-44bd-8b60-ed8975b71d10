import{d as e,r as t,o,g as n,f as s,C as a,h as r,m as i,w as l,$ as c,j as u,i as d,az as m}from"./index.Dk5pbsTU.js";import{E as p}from"./el-button.CXI119n4.js";import{F as f}from"./index.WzKGworL.js";import{_ as h}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.DuiNpp1i.js";import"./use-form-item.DzRJVC1I.js";import"./use-form-common-props.CQPDkY7k.js";import"./index.D6CER_Ot.js";const v={class:"canvas-dom"},g=["src"],_=h(e({__name:"signature",setup(e){const h=t(""),_=t();let w,y=!1;const j=e=>{let t;if(e.offsetX){const{offsetX:o,offsetY:n}=e;t=[o,n]}else{const{top:o,left:n}=_.value.getBoundingClientRect();t=[e.touches[0].clientX-n,e.touches[0].clientY-o]}return t};let k=0,x=0;const C=e=>{[k,x]=j(e),y=!0},R=e=>{if(y){const[t,o]=j(e);!function(e,t,o,n,s){s.beginPath(),s.globalAlpha=1,s.lineWidth=2,s.strokeStyle="#000",s.moveTo(e,t),s.lineTo(o,n),s.closePath(),s.stroke()}(k,x,t,o,w),k=t,x=o}},E=()=>{y&&(y=!1)};o((()=>{w=_.value.getContext("2d")}));const M=async()=>{if(U(_.value))return void m({type:"warning",message:"当前签名文件为空"});const e=D(_.value.toDataURL(),"签名.png");if(!e)return;const t=await f.uploadFile(e);T(),h.value=t.url},T=()=>{w.clearRect(0,0,_.value.width,_.value.height)},U=e=>{const t=document.createElement("canvas");return t.width=e.width,t.height=e.height,e.toDataURL()==t.toDataURL()},b=()=>{if(U(_.value))return void m({type:"warning",message:"当前签名文件为空"});const e=document.createElement("a");e.href=_.value.toDataURL(),e.download="签名";const t=new MouseEvent("click");e.dispatchEvent(t)},D=(e,t)=>{const o=e.split(",");if(!o.length)return;const n=o[0].match(/:(.*?);/);if(n){const e=atob(o[1]);let s=e.length;const a=new Uint8Array(s);for(;s--;)a[s]=e.charCodeAt(s);return new File([a],t,{type:n[1]})}};return(e,t)=>{const o=p;return s(),n("div",v,[t[3]||(t[3]=a("h3",null,"基于canvas实现的签名组件",-1)),a("header",null,[i(o,{type:"primary",onClick:b},{default:l((()=>t[0]||(t[0]=[c("保存为图片")]))),_:1,__:[0]}),i(o,{onClick:M},{default:l((()=>t[1]||(t[1]=[c("保存到后端")]))),_:1,__:[1]}),i(o,{onClick:T},{default:l((()=>t[2]||(t[2]=[c("清空签名")]))),_:1,__:[2]})]),a("canvas",{ref_key:"canvas",ref:_,height:"200",width:"500",onMousedown:C,onMousemove:u(R,["stop","prevent"]),onMouseup:E,onTouchstart:C,onTouchmove:u(R,["stop","prevent"]),onTouchend:E},null,544),d(h)?(s(),n("img",{key:0,src:d(h),alt:"签名"},null,8,g)):r("",!0)])}}}),[["__scopeId","data-v-59c27848"]]);export{_ as default};
