import{aT as e}from"./index.Dk5pbsTU.js";const s={groupPage:s=>e.get("/system/vest/group/list",{params:s}),saveGroup:s=>e.post("/system/vest/group",s),removeGroup:s=>e.delete(`/system/vest/group/${s}`),page:s=>e.get("/system/vest/list",{params:s}),save:s=>e.post("/system/vest",s),remove:s=>e({url:`/live/vest/${s}`,method:"delete"}),getAllGroups:s=>e({url:"/system/vest/group/all",method:"get",params:{roomId:s}}),getVestsByGroupId:s=>e({url:`/system/vest/byGroup/${s}`,method:"get"})};export{s as V};
