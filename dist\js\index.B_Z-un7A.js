import{d as s,b2 as e,r as _,b0 as t,ai as o,c as a,cW as i,o as l,ap as n,g as r,f as c,k as p,m,i as u,w as E,C as v,ak as g,P as d,Q as j,n as V,E as L,e as I,D,l as O,h as R,j as f,cX as A}from"./index.Dk5pbsTU.js";import{E as P}from"./el-popper.Dbn4MgsT.js";import{E as T}from"./el-popover.Bo2lPKkO.js";import{E as h,a as b}from"./el-tab-pane.CXnN_Izo.js";/* empty css                     */import"./el-tooltip.l0sNRNKZ.js";import{E as k}from"./el-input.DiGatoux.js";import{E as w}from"./index.ybpLT-bz.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";const C={class:"icon-grid"},x=["onClick"],q={class:"icon-grid"},S=["onClick"],$=y(s({__name:"index",props:e({modelValue:{type:String,default:""},width:{type:String,default:"500px"}},{modelValue:{type:String,required:!0,default:""},modelModifiers:{}}),emits:e(["update:modelValue"],["update:modelValue"]),setup(s,{emit:e}){const y=s,$=e,U=_(),W=_(),z=_(!1),G=_("svg"),J=_([]),K=_(Object.keys(t)),M=o(s,"modelValue"),Q=_(""),X=_([]),Y=_(K.value),Z=a((()=>M.value&&M.value.startsWith("el-icon")));function B(s){G.value=s.props.name,F()}function F(){"svg"===G.value?X.value=Q.value?J.value.filter((s=>s.toLowerCase().includes(Q.value.toLowerCase()))):J.value:Y.value=Q.value?K.value.filter((s=>s.toLowerCase().includes(Q.value.toLowerCase()))):K.value}function H(s){const e="element"===G.value?"el-icon-"+s:s;$("update:modelValue",e),z.value=!1}function N(){z.value=!z.value}function ss(){M.value=""}return i(U,(()=>z.value=!1),{ignore:[W]}),l((()=>{!function(){const s=Object.assign({"../../assets/icons/aibot.svg":()=>A((()=>import("./aibot.PWJIieBK.js")),[]),"../../assets/icons/api.svg":()=>A((()=>import("./api.B2FJMEEY.js")),[]),"../../assets/icons/backtop.svg":()=>A((()=>import("./backtop.C5bGCvZX.js")),[]),"../../assets/icons/bilibili.svg":()=>A((()=>import("./bilibili.BDqZjNm-.js")),[]),"../../assets/icons/browser.svg":()=>A((()=>import("./browser.BEqBigRi.js")),[]),"../../assets/icons/captcha.svg":()=>A((()=>import("./captcha.KixZeTLs.js")),[]),"../../assets/icons/cascader.svg":()=>A((()=>import("./cascader.Bxy7lIuJ.js")),[]),"../../assets/icons/client.svg":()=>A((()=>import("./client.fwafuIJ_.js")),[]),"../../assets/icons/close.svg":()=>A((()=>import("./close.3A3CTE27.js")),[]),"../../assets/icons/close_all.svg":()=>A((()=>import("./close_all.CpAPdWg9.js")),[]),"../../assets/icons/close_left.svg":()=>A((()=>import("./close_left.FPCWsnfT.js")),[]),"../../assets/icons/close_other.svg":()=>A((()=>import("./close_other.CtMgS35C.js")),[]),"../../assets/icons/close_right.svg":()=>A((()=>import("./close_right.WYLOaSAD.js")),[]),"../../assets/icons/cnblogs.svg":()=>A((()=>import("./cnblogs.B2Y7s_5c.js")),[]),"../../assets/icons/code.svg":()=>A((()=>import("./code.DbEIL5pZ.js")),[]),"../../assets/icons/collapse.svg":()=>A((()=>import("./collapse.ROHmwAah.js")),[]),"../../assets/icons/csdn.svg":()=>A((()=>import("./csdn.DUJUJ9Ed.js")),[]),"../../assets/icons/dict.svg":()=>A((()=>import("./dict.BlxtrVRf.js")),[]),"../../assets/icons/document.svg":()=>A((()=>import("./document.DqID0E9P.js")),[]),"../../assets/icons/down.svg":()=>A((()=>import("./down.By8c2p9v.js")),[]),"../../assets/icons/download.svg":()=>A((()=>import("./download.BcBrriOr.js")),[]),"../../assets/icons/enter.svg":()=>A((()=>import("./enter.BqL7pZcd.js")),[]),"../../assets/icons/esc.svg":()=>A((()=>import("./esc.eVgXU48X.js")),[]),"../../assets/icons/file.svg":()=>A((()=>import("./file.LSCgjjDT.js")),[]),"../../assets/icons/fullscreen-exit.svg":()=>A((()=>import("./fullscreen-exit.DXwCcaMo.js")),[]),"../../assets/icons/fullscreen.svg":()=>A((()=>import("./fullscreen.dczNRBeD.js")),[]),"../../assets/icons/gitcode.svg":()=>A((()=>import("./gitcode.8kuXv4pf.js")),[]),"../../assets/icons/gitee.svg":()=>A((()=>import("./gitee.C7LaIEZ_.js")),[]),"../../assets/icons/github.svg":()=>A((()=>import("./github.DLO2QQQy.js")),[]),"../../assets/icons/homepage.svg":()=>A((()=>import("./homepage.BcyYp1IG.js")),[]),"../../assets/icons/java.svg":()=>A((()=>import("./java.CUWOGSYw.js")),[]),"../../assets/icons/juejin.svg":()=>A((()=>import("./juejin.BBqoLiLP.js")),[]),"../../assets/icons/language.svg":()=>A((()=>import("./language.k0ZPy50U.js")),[]),"../../assets/icons/mask.svg":()=>A((()=>import("./mask.DJOxI8Sy.js")),[]),"../../assets/icons/menu.svg":()=>A((()=>import("./menu.CSAxtYMZ.js")),[]),"../../assets/icons/message.svg":()=>A((()=>import("./message.CTraJOIE.js")),[]),"../../assets/icons/monitor.svg":()=>A((()=>import("./monitor.Cm5WDh5_.js")),[]),"../../assets/icons/project.svg":()=>A((()=>import("./project.CAkqf0A3.js")),[]),"../../assets/icons/qq.svg":()=>A((()=>import("./qq.X0Hffnhw.js")),[]),"../../assets/icons/refresh.svg":()=>A((()=>import("./refresh.B5ExwFoE.js")),[]),"../../assets/icons/role.svg":()=>A((()=>import("./role.DDpGZwDf.js")),[]),"../../assets/icons/rule.svg":()=>A((()=>import("./rule.8kg6a1pP.js")),[]),"../../assets/icons/search.svg":()=>A((()=>import("./search.BuhcqAYw.js")),[]),"../../assets/icons/setting.svg":()=>A((()=>import("./setting.CfydkD5J.js")),[]),"../../assets/icons/size.svg":()=>A((()=>import("./size.C48ZYpz_.js")),[]),"../../assets/icons/system.svg":()=>A((()=>import("./system.BjyKXCM4.js")),[]),"../../assets/icons/table.svg":()=>A((()=>import("./table.C6Xt0YCE.js")),[]),"../../assets/icons/todo.svg":()=>A((()=>import("./todo.C6Ti5UL0.js")),[]),"../../assets/icons/tree.svg":()=>A((()=>import("./tree.ByH7Oq4A.js")),[]),"../../assets/icons/typescript.svg":()=>A((()=>import("./typescript.C8n9I--t.js")),[]),"../../assets/icons/up.svg":()=>A((()=>import("./up.RRIFBrWs.js")),[]),"../../assets/icons/user.svg":()=>A((()=>import("./user.rrOxQVjB.js")),[]),"../../assets/icons/visitor.svg":()=>A((()=>import("./visitor.C-ADb4dr.js")),[]),"../../assets/icons/vue.svg":()=>A((()=>import("./vue.BjDXW0c2.js")),[]),"../../assets/icons/wechat.svg":()=>A((()=>import("./wechat.ca-m1rk6.js")),[]),"../../assets/icons/xml.svg":()=>A((()=>import("./xml.DDPJ6DAu.js")),[])});for(const e in s){const s=e.replace(/.*\/(.*)\.svg$/,"$1");J.value.push(s)}X.value=J.value}(),M.value&&(K.value.includes(M.value.replace("el-icon-",""))?G.value="element":G.value="svg")})),(s,e)=>{const _=L,t=n("CircleClose"),o=n("ArrowDown"),a=k,i=P,l=w,A=b,$=h,J=T;return c(),r("div",{ref_key:"iconSelectRef",ref:U,style:p({width:y.width})},[m(J,{visible:u(z),width:y.width,placement:"bottom-end"},{reference:E((()=>[v("div",{onClick:e[1]||(e[1]=s=>z.value=!u(z))},[O(s.$slots,"default",{},(()=>[m(a,{modelValue:M.value,"onUpdate:modelValue":e[0]||(e[0]=s=>M.value=s),readonly:"",placeholder:"点击选择图标",class:"reference"},{prepend:E((()=>[u(Z)?(c(),I(_,{key:0},{default:E((()=>[(c(),I(D(M.value.replace("el-icon-",""))))])),_:1})):(c(),r("div",{key:1,class:V(`i-svg:${M.value}`)},null,2))])),suffix:E((()=>[M.value?(c(),I(_,{key:0,style:{"margin-right":"8px"},onClick:f(ss,["stop"])},{default:E((()=>[m(t)])),_:1})):R("",!0),m(_,{style:p({transform:u(z)?"rotate(180deg)":"rotate(0)",transition:"transform .5s"})},{default:E((()=>[m(o,{onClick:f(N,["stop"])})])),_:1},8,["style"])])),_:1},8,["modelValue"])]),!0)])])),default:E((()=>[v("div",{ref_key:"popoverContentRef",ref:W},[m(a,{modelValue:u(Q),"onUpdate:modelValue":e[2]||(e[2]=s=>g(Q)?Q.value=s:null),placeholder:"搜索图标",clearable:"",onInput:F},null,8,["modelValue"]),m($,{modelValue:u(G),"onUpdate:modelValue":e[3]||(e[3]=s=>g(G)?G.value=s:null),onTabClick:B},{default:E((()=>[m(A,{label:"SVG 图标",name:"svg"},{default:E((()=>[m(l,{height:"300px"},{default:E((()=>[v("ul",C,[(c(!0),r(d,null,j(u(X),(s=>(c(),r("li",{key:"svg-"+s,class:"icon-grid-item",onClick:e=>H(s)},[m(i,{content:s,placement:"bottom",effect:"light"},{default:E((()=>[v("div",{class:V(`i-svg:${s}`)},null,2)])),_:2},1032,["content"])],8,x)))),128))])])),_:1})])),_:1}),m(A,{label:"Element 图标",name:"element"},{default:E((()=>[m(l,{height:"300px"},{default:E((()=>[v("ul",q,[(c(!0),r(d,null,j(u(Y),(s=>(c(),r("li",{key:s,class:"icon-grid-item",onClick:e=>H(s)},[m(_,null,{default:E((()=>[(c(),I(D(s)))])),_:2},1024)],8,S)))),128))])])),_:1})])),_:1})])),_:1},8,["modelValue"])],512)])),_:3},8,["visible","width"])],4)}}}),[["__scopeId","data-v-3a2d6a32"]]);export{$ as _};
