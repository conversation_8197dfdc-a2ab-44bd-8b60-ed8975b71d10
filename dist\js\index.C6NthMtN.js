import{o as e,a6 as t,r as n,bm as o,H as s,K as a,_ as r,d as u,l as d,I as c,i,M as l,a0 as f,L as v,y as p,t as m,z as E,e as b,f as y,bB as L,q as h}from"./index.Dk5pbsTU.js";const w=e=>Array.from(e.querySelectorAll('a[href],button:not([disabled]),button:not([hidden]),:not([tabindex="-1"]),input:not([disabled]),input:not([type="hidden"]),select:not([disabled]),textarea:not([disabled])')).filter((e=>T(e)&&(e=>"fixed"!==getComputedStyle(e).position&&null!==e.offsetParent)(e))),T=e=>{if(e.tabIndex>0||0===e.tabIndex&&null!==e.getAttribute("tabIndex"))return!0;if(e.tabIndex<0||e.hasAttribute("disabled")||"true"===e.getAttribute("aria-disabled"))return!1;switch(e.nodeName){case"A":return!!e.href&&"ignore"!==e.rel;case"INPUT":return!("hidden"===e.type||"file"===e.type);case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}},g=function(e,t,...n){let o;o=t.includes("mouse")||t.includes("click")?"MouseEvents":t.includes("key")?"KeyboardEvent":"HTMLEvents";const s=document.createEvent(o);return s.initEvent(t,...n),e.dispatchEvent(s),e},A=e=>!e.getAttribute("aria-owns"),x=(e,t,n)=>{const{parentNode:o}=e;if(!o)return null;const s=o.querySelectorAll(n);return s[Array.prototype.indexOf.call(s,e)+t]||null},k=e=>{e&&(e.focus(),!A(e)&&e.click())},S="focus-trap.focus-after-trapped",I="focus-trap.focus-after-released",N={cancelable:!0,bubbles:!1},R={cancelable:!0,bubbles:!1},F="focusAfterTrapped",K="focusAfterReleased",P=Symbol("elFocusTrap"),_=n(),C=n(0),O=n(0);let q=0;const B=e=>{const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0||e===document.activeElement?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t},M=(e,t)=>{for(const n of e)if(!j(n,t))return n},j=(e,t)=>{if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1},H=(e,t)=>{if(e&&e.focus){const n=document.activeElement;let s=!1;!o(e)||T(e)||e.getAttribute("tabindex")||(e.setAttribute("tabindex","-1"),s=!0),e.focus({preventScroll:!0}),O.value=window.performance.now(),e!==n&&(e=>e instanceof HTMLInputElement&&"select"in e)(e)&&t&&e.select(),o(e)&&s&&e.removeAttribute("tabindex")}};function U(e,t){const n=[...e],o=e.indexOf(t);return-1!==o&&n.splice(o,1),n}const D=(()=>{let e=[];return{push:t=>{const n=e[0];n&&t!==n&&n.pause(),e=U(e,t),e.unshift(t)},remove:t=>{var n,o;e=U(e,t),null==(o=null==(n=e[0])?void 0:n.resume)||o.call(n)}}})(),$=()=>{_.value="pointer",C.value=window.performance.now()},W=()=>{_.value="keyboard",C.value=window.performance.now()},z=e=>new CustomEvent("focus-trap.focusout-prevented",{...R,detail:e});let X=[];const G=e=>{e.code===a.esc&&X.forEach((t=>t(e)))};var J=r(u({name:"ElFocusTrap",inheritAttrs:!1,props:{loop:Boolean,trapped:Boolean,focusTrapEl:Object,focusStartEl:{type:[Object,String],default:"first"}},emits:[F,K,"focusin","focusout","focusout-prevented","release-requested"],setup(o,{emit:r}){const u=n();let d,m;const{focusReason:E}=(e((()=>{0===q&&(document.addEventListener("mousedown",$),document.addEventListener("touchstart",$),document.addEventListener("keydown",W)),q++})),t((()=>{q--,q<=0&&(document.removeEventListener("mousedown",$),document.removeEventListener("touchstart",$),document.removeEventListener("keydown",W))})),{focusReason:_,lastUserFocusTimestamp:C,lastAutomatedFocusTimestamp:O});var b;b=e=>{o.trapped&&!y.paused&&r("release-requested",e)},e((()=>{0===X.length&&document.addEventListener("keydown",G),s&&X.push(b)})),t((()=>{X=X.filter((e=>e!==b)),0===X.length&&s&&document.removeEventListener("keydown",G)}));const y={paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}},L=e=>{if(!o.loop&&!o.trapped)return;if(y.paused)return;const{code:t,altKey:n,ctrlKey:s,metaKey:u,currentTarget:d,shiftKey:c}=e,{loop:i}=o,l=t===a.tab&&!n&&!s&&!u,f=document.activeElement;if(l&&f){const t=d,[n,o]=(e=>{const t=B(e);return[M(t,e),M(t.reverse(),e)]})(t);if(n&&o)if(c||f!==o){if(c&&[n,t].includes(f)){const t=z({focusReason:E.value});r("focusout-prevented",t),t.defaultPrevented||(e.preventDefault(),i&&H(o,!0))}}else{const t=z({focusReason:E.value});r("focusout-prevented",t),t.defaultPrevented||(e.preventDefault(),i&&H(n,!0))}else if(f===t){const t=z({focusReason:E.value});r("focusout-prevented",t),t.defaultPrevented||e.preventDefault()}}};p(P,{focusTrapRef:u,onKeydown:L}),c((()=>o.focusTrapEl),(e=>{e&&(u.value=e)}),{immediate:!0}),c([u],(([e],[t])=>{e&&(e.addEventListener("keydown",L),e.addEventListener("focusin",T),e.addEventListener("focusout",g)),t&&(t.removeEventListener("keydown",L),t.removeEventListener("focusin",T),t.removeEventListener("focusout",g))}));const h=e=>{r(F,e)},w=e=>r(K,e),T=e=>{const t=i(u);if(!t)return;const n=e.target,s=e.relatedTarget,a=n&&t.contains(n);if(!o.trapped){s&&t.contains(s)||(d=s)}a&&r("focusin",e),y.paused||o.trapped&&(a?m=n:H(m,!0))},g=e=>{const t=i(u);if(!y.paused&&t)if(o.trapped){const n=e.relatedTarget;l(n)||t.contains(n)||setTimeout((()=>{if(!y.paused&&o.trapped){const e=z({focusReason:E.value});r("focusout-prevented",e),e.defaultPrevented||H(m,!0)}}),0)}else{const n=e.target;n&&t.contains(n)||r("focusout",e)}};async function A(){await f();const e=i(u);if(e){D.push(y);const t=e.contains(document.activeElement)?d:document.activeElement;d=t;if(!e.contains(t)){const n=new Event(S,N);e.addEventListener(S,h),e.dispatchEvent(n),n.defaultPrevented||f((()=>{let n=o.focusStartEl;v(n)||(H(n),document.activeElement!==n&&(n="first")),"first"===n&&((e,t=!1)=>{const n=document.activeElement;for(const o of e)if(H(o,t),document.activeElement!==n)return})(B(e),!0),document.activeElement!==t&&"container"!==n||H(e)}))}}}function x(){const e=i(u);if(e){e.removeEventListener(S,h);const t=new CustomEvent(I,{...N,detail:{focusReason:E.value}});e.addEventListener(I,w),e.dispatchEvent(t),t.defaultPrevented||"keyboard"!=E.value&&C.value>O.value&&!e.contains(document.activeElement)||H(null!=d?d:document.body),e.removeEventListener(I,w),D.remove(y)}}return e((()=>{o.trapped&&A(),c((()=>o.trapped),(e=>{e?A():x()}))})),t((()=>{o.trapped&&x(),u.value&&(u.value.removeEventListener("keydown",L),u.value.removeEventListener("focusin",T),u.value.removeEventListener("focusout",g),u.value=void 0)})),{onKeydown:L}}}),[["render",function(e,t,n,o,s,a){return d(e.$slots,"default",{handleKeydown:e.onKeydown})}],["__file","focus-trap.vue"]]);const Q=m({to:{type:E([String,Object]),required:!0},disabled:Boolean});const V=h(r(u({__name:"teleport",props:Q,setup:e=>(e,t)=>e.disabled?d(e.$slots,"default",{key:0}):(y(),b(L,{key:1,to:e.to},[d(e.$slots,"default")],8,["to"]))}),[["__file","teleport.vue"]]));export{J as E,P as F,V as a,Q as b,H as c,A as d,k as f,x as g,T as i,w as o,g as t};
