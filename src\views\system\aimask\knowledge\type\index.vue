<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="keywords" label="">
          <el-input
            v-model="page.query.keyWord"
            placeholder="类型名称检索"
            clearable
            @keyup.enter="getPage"
          />
        </el-form-item>
        <el-form-item prop="status" label="启用状态">
          <dict v-model="page.query.status" code="disable_status" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          v-hasPerm="['sys:aimask:knowledge:type:addOrEdit']"
          type="success"
          icon="plus"
          @click="editModelRef?.open()"
        >
          新增
        </el-button>
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="类型名称" align="center" prop="name" min-width="120" />
        <el-table-column label="启用状态" align="center" width="120">
          <template #default="{ row }">
            <el-switch
              v-if="hasAuth('sys:aimask:knowledge:type:addOrEdit')"
              v-model="row.status"
              inline-prompt
              active-text="是"
              inactive-text="否"
              :inactive-value="0"
              :active-value="1"
              :loading="row.statusLoading"
              :before-change="() => editStatus(row)"
            />
            <dict-label v-else v-model="row.status" code="disable_status" />
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="180">
          <template #default="scope">
            <el-button
              v-hasPerm="['sys:aimask:knowledge:type:addOrEdit']"
              type="warning"
              size="small"
              link
              :loading="scope.row.loading"
              @click="editModelRef?.open(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="!scope.row.isChoose && hasAuth('sys:aimask:knowledge:type:delete')"
              type="danger"
              size="small"
              link
              :loading="scope.row.loading"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
    <edit-model ref="editModelRef" @success="resetQuery" />
  </div>
</template>

<script setup lang="ts">
import EditModel from "./edit.vue";

defineOptions({ name: "MaskKnowledgeType" });
import { usePage } from "@/utils/commonSetup";
import KnowledgeTypeApi, {
  KnowledgeTypePageQueryDto,
  KnowledgeTypePageVo,
  SaveOrEditKnowledgeTypeDto,
} from "@/api/system/knowledgeType";
import { hasAuth } from "@/plugins/permission";

const editModelRef = ref<InstanceType<typeof EditModel>>();
const { page, getPage, resetQuery } = usePage<KnowledgeTypePageQueryDto, KnowledgeTypePageVo>(
  new KnowledgeTypePageQueryDto(),
  KnowledgeTypeApi.page
);

function handleDelete(_row: KnowledgeTypePageVo) {
  ElMessageBox.confirm(`确定删除知识库类型《${_row.name}》吗？`, `删除`, {
    confirmButtonText: "确定删除",
    cancelButtonText: "取消",
    confirmButtonClass: "el-button--danger",
    type: "error",
  })
    .then(() => {
      _row.loading = true;
      KnowledgeTypeApi.remove(_row.id)
        .then(() => {
          ElMessage.success("删除成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}

function editStatus(_row: KnowledgeTypePageVo) {
  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(
      `确定要${_row.status ? "停用" : "启用"}知识库类型《${_row.name}》吗？`,
      `${_row.status ? "停用" : "启用"}知识库类型`,
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }
    )
      .then(() => {
        const formData = new SaveOrEditKnowledgeTypeDto(_row);
        if (formData.status) formData.status = 0;
        else formData.status = 1;
        _row.statusLoading = true;
        KnowledgeTypeApi.saveOrEdit(formData)
          .then(() => {
            ElMessage.success("操作成功");
            resolve(false);
            resetQuery();
          })
          .finally(() => {
            _row.statusLoading = false;
            reject(false);
          });
      })
      .catch(() => {
        ElMessage.info("已取消");
        reject(false);
      });
  });
}
</script>
