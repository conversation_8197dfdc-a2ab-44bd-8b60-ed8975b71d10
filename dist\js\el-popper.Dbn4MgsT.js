import{t as e,_ as t,d as n,r as o,c as r,l as a,y as i,b as s,A as l,a6 as u,g as p,f,k as c,n as d,i as v,z as m,ba as g,V as h,bj as b,aa as y,P as w,bk as x,bl as O,m as R,o as A,I as E,bm as T,e as C,h as S,w as k,U as j,bn as M,M as B,H as P,s as F,bo as L,be as D,b6 as _,q as I,bp as H,K as $,bq as W,B as q,br as z,bb as U,a9 as N,ae as V,aG as K,bs as Z,bt as X,T as Y,X as G,bu as J,aK as Q,bv as ee,F as te}from"./index.Dk5pbsTU.js";import{u as ne}from"./index.C9UdVphc.js";import{i as oe,E as re,b as ae,a as ie,c as se}from"./index.C6NthMtN.js";import{a as le,u as ue}from"./index.D6CER_Ot.js";import{i as pe}from"./isUndefined.DgmxjSXK.js";import{f as fe}from"./use-form-common-props.CQPDkY7k.js";const ce=Symbol("popper"),de=Symbol("popperContent"),ve=["dialog","grid","group","listbox","menu","navigation","tooltip","tree"],me=e({role:{type:String,values:ve,default:"tooltip"}}),ge=n({name:"ElPopper",inheritAttrs:!1});var he=t(n({...ge,props:me,setup(e,{expose:t}){const n=e,s={triggerRef:o(),popperInstanceRef:o(),contentRef:o(),referenceRef:o(),role:r((()=>n.role))};return t(s),i(ce,s),(e,t)=>a(e.$slots,"default")}}),[["__file","popper.vue"]]);const be=n({name:"ElPopperArrow",inheritAttrs:!1});var ye=t(n({...be,setup(e,{expose:t}){const n=s("popper"),{arrowRef:o,arrowStyle:r}=l(de,void 0);return u((()=>{o.value=void 0})),t({arrowRef:o}),(e,t)=>(f(),p("span",{ref_key:"arrowRef",ref:o,class:d(v(n).e("arrow")),style:c(v(r)),"data-popper-arrow":""},null,6))}}),[["__file","arrow.vue"]]);const we=e({virtualRef:{type:m(Object)},virtualTriggering:Boolean,onMouseenter:{type:m(Function)},onMouseleave:{type:m(Function)},onClick:{type:m(Function)},onKeydown:{type:m(Function)},onFocus:{type:m(Function)},onBlur:{type:m(Function)},onContextmenu:{type:m(Function)},id:String,open:Boolean}),xe=Symbol("elForwardRef"),Oe=n({name:"ElOnlyChild",setup(e,{slots:t,attrs:n}){var o;const r=l(xe),a=(i=null!=(o=null==r?void 0:r.setForwardRef)?o:g,{mounted(e){i(e)},updated(e){i(e)},unmounted(){i(null)}});var i;return()=>{var e;const o=null==(e=t.default)?void 0:e.call(t,n);if(!o)return null;if(o.length>1)return null;const r=Re(o);return r?h(b(r,n),[[a]]):null}}});function Re(e){if(!e)return null;const t=e;for(const n of t){if(y(n))switch(n.type){case O:continue;case x:case"svg":return Ae(n);case w:return Re(n.children);default:return n}return Ae(n)}return null}function Ae(e){const t=s("only-child");return R("span",{class:t.e("content")},[e])}const Ee=n({name:"ElPopperTrigger",inheritAttrs:!1});var Te=t(n({...Ee,props:we,setup(e,{expose:t}){const n=e,{role:o,triggerRef:s}=l(ce,void 0);var p;p=s,i(xe,{setForwardRef:e=>{p.value=e}});const c=r((()=>m.value?n.id:void 0)),d=r((()=>{if(o&&"tooltip"===o.value)return n.open&&n.id?n.id:void 0})),m=r((()=>{if(o&&"tooltip"!==o.value)return o.value})),g=r((()=>m.value?`${n.open}`:void 0));let h;const b=["onMouseenter","onMouseleave","onClick","onKeydown","onFocus","onBlur","onContextmenu"];return A((()=>{E((()=>n.virtualRef),(e=>{e&&(s.value=M(e))}),{immediate:!0}),E(s,((e,t)=>{null==h||h(),h=void 0,T(e)&&(b.forEach((o=>{var r;const a=n[o];a&&(e.addEventListener(o.slice(2).toLowerCase(),a),null==(r=null==t?void 0:t.removeEventListener)||r.call(t,o.slice(2).toLowerCase(),a))})),oe(e)&&(h=E([c,d,m,g],(t=>{["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach(((n,o)=>{B(t[o])?e.removeAttribute(n):e.setAttribute(n,t[o])}))}),{immediate:!0}))),T(t)&&oe(t)&&["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach((e=>t.removeAttribute(e)))}),{immediate:!0})})),u((()=>{if(null==h||h(),h=void 0,s.value&&T(s.value)){const e=s.value;b.forEach((t=>{const o=n[t];o&&e.removeEventListener(t.slice(2).toLowerCase(),o)})),s.value=void 0}})),t({triggerRef:s}),(e,t)=>e.virtualTriggering?S("v-if",!0):(f(),C(v(Oe),j({key:0},e.$attrs,{"aria-controls":v(c),"aria-describedby":v(d),"aria-expanded":v(g),"aria-haspopup":v(m)}),{default:k((()=>[a(e.$slots,"default")])),_:3},16,["aria-controls","aria-describedby","aria-expanded","aria-haspopup"]))}}),[["__file","trigger.vue"]]),Ce="top",Se="bottom",ke="right",je="left",Me="auto",Be=[Ce,Se,ke,je],Pe="start",Fe="end",Le="viewport",De="popper",_e=Be.reduce((function(e,t){return e.concat([t+"-"+Pe,t+"-"+Fe])}),[]),Ie=[].concat(Be,[Me]).reduce((function(e,t){return e.concat([t,t+"-"+Pe,t+"-"+Fe])}),[]),He=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function $e(e){return e?(e.nodeName||"").toLowerCase():null}function We(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function qe(e){return e instanceof We(e).Element||e instanceof Element}function ze(e){return e instanceof We(e).HTMLElement||e instanceof HTMLElement}function Ue(e){return"undefined"!=typeof ShadowRoot&&(e instanceof We(e).ShadowRoot||e instanceof ShadowRoot)}var Ne={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},o=t.attributes[e]||{},r=t.elements[e];!ze(r)||!$e(r)||(Object.assign(r.style,n),Object.keys(o).forEach((function(e){var t=o[e];!1===t?r.removeAttribute(e):r.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var o=t.elements[e],r=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});!ze(o)||!$e(o)||(Object.assign(o.style,a),Object.keys(r).forEach((function(e){o.removeAttribute(e)})))}))}},requires:["computeStyles"]};function Ve(e){return e.split("-")[0]}var Ke=Math.max,Ze=Math.min,Xe=Math.round;function Ye(e,t){void 0===t&&(t=!1);var n=e.getBoundingClientRect(),o=1,r=1;if(ze(e)&&t){var a=e.offsetHeight,i=e.offsetWidth;i>0&&(o=Xe(n.width)/i||1),a>0&&(r=Xe(n.height)/a||1)}return{width:n.width/o,height:n.height/r,top:n.top/r,right:n.right/o,bottom:n.bottom/r,left:n.left/o,x:n.left/o,y:n.top/r}}function Ge(e){var t=Ye(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function Je(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Ue(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function Qe(e){return We(e).getComputedStyle(e)}function et(e){return["table","td","th"].indexOf($e(e))>=0}function tt(e){return((qe(e)?e.ownerDocument:e.document)||window.document).documentElement}function nt(e){return"html"===$e(e)?e:e.assignedSlot||e.parentNode||(Ue(e)?e.host:null)||tt(e)}function ot(e){return ze(e)&&"fixed"!==Qe(e).position?e.offsetParent:null}function rt(e){for(var t=We(e),n=ot(e);n&&et(n)&&"static"===Qe(n).position;)n=ot(n);return n&&("html"===$e(n)||"body"===$e(n)&&"static"===Qe(n).position)?t:n||function(e){var t=-1!==navigator.userAgent.toLowerCase().indexOf("firefox");if(-1!==navigator.userAgent.indexOf("Trident")&&ze(e)&&"fixed"===Qe(e).position)return null;var n=nt(e);for(Ue(n)&&(n=n.host);ze(n)&&["html","body"].indexOf($e(n))<0;){var o=Qe(n);if("none"!==o.transform||"none"!==o.perspective||"paint"===o.contain||-1!==["transform","perspective"].indexOf(o.willChange)||t&&"filter"===o.willChange||t&&o.filter&&"none"!==o.filter)return n;n=n.parentNode}return null}(e)||t}function at(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function it(e,t,n){return Ke(e,Ze(t,n))}function st(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function lt(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}var ut={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,r=e.options,a=n.elements.arrow,i=n.modifiersData.popperOffsets,s=Ve(n.placement),l=at(s),u=[je,ke].indexOf(s)>=0?"height":"width";if(a&&i){var p=function(e,t){return st("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:lt(e,Be))}(r.padding,n),f=Ge(a),c="y"===l?Ce:je,d="y"===l?Se:ke,v=n.rects.reference[u]+n.rects.reference[l]-i[l]-n.rects.popper[u],m=i[l]-n.rects.reference[l],g=rt(a),h=g?"y"===l?g.clientHeight||0:g.clientWidth||0:0,b=v/2-m/2,y=p[c],w=h-f[u]-p[d],x=h/2-f[u]/2+b,O=it(y,x,w),R=l;n.modifiersData[o]=((t={})[R]=O,t.centerOffset=O-x,t)}},effect:function(e){var t=e.state,n=e.options.element,o=void 0===n?"[data-popper-arrow]":n;null!=o&&("string"==typeof o&&!(o=t.elements.popper.querySelector(o))||!Je(t.elements.popper,o)||(t.elements.arrow=o))},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function pt(e){return e.split("-")[1]}var ft={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ct(e){var t,n=e.popper,o=e.popperRect,r=e.placement,a=e.variation,i=e.offsets,s=e.position,l=e.gpuAcceleration,u=e.adaptive,p=e.roundOffsets,f=e.isFixed,c=i.x,d=void 0===c?0:c,v=i.y,m=void 0===v?0:v,g="function"==typeof p?p({x:d,y:m}):{x:d,y:m};d=g.x,m=g.y;var h=i.hasOwnProperty("x"),b=i.hasOwnProperty("y"),y=je,w=Ce,x=window;if(u){var O=rt(n),R="clientHeight",A="clientWidth";if(O===We(n)&&("static"!==Qe(O=tt(n)).position&&"absolute"===s&&(R="scrollHeight",A="scrollWidth")),r===Ce||(r===je||r===ke)&&a===Fe)w=Se,m-=(f&&O===x&&x.visualViewport?x.visualViewport.height:O[R])-o.height,m*=l?1:-1;if(r===je||(r===Ce||r===Se)&&a===Fe)y=ke,d-=(f&&O===x&&x.visualViewport?x.visualViewport.width:O[A])-o.width,d*=l?1:-1}var E,T=Object.assign({position:s},u&&ft),C=!0===p?function(e){var t=e.x,n=e.y,o=window.devicePixelRatio||1;return{x:Xe(t*o)/o||0,y:Xe(n*o)/o||0}}({x:d,y:m}):{x:d,y:m};return d=C.x,m=C.y,l?Object.assign({},T,((E={})[w]=b?"0":"",E[y]=h?"0":"",E.transform=(x.devicePixelRatio||1)<=1?"translate("+d+"px, "+m+"px)":"translate3d("+d+"px, "+m+"px, 0)",E)):Object.assign({},T,((t={})[w]=b?m+"px":"",t[y]=h?d+"px":"",t.transform="",t))}var dt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,o=n.gpuAcceleration,r=void 0===o||o,a=n.adaptive,i=void 0===a||a,s=n.roundOffsets,l=void 0===s||s,u={placement:Ve(t.placement),variation:pt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ct(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ct(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},vt={passive:!0};var mt={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,o=e.options,r=o.scroll,a=void 0===r||r,i=o.resize,s=void 0===i||i,l=We(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&u.forEach((function(e){e.addEventListener("scroll",n.update,vt)})),s&&l.addEventListener("resize",n.update,vt),function(){a&&u.forEach((function(e){e.removeEventListener("scroll",n.update,vt)})),s&&l.removeEventListener("resize",n.update,vt)}},data:{}},gt={left:"right",right:"left",bottom:"top",top:"bottom"};function ht(e){return e.replace(/left|right|bottom|top/g,(function(e){return gt[e]}))}var bt={start:"end",end:"start"};function yt(e){return e.replace(/start|end/g,(function(e){return bt[e]}))}function wt(e){var t=We(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function xt(e){return Ye(tt(e)).left+wt(e).scrollLeft}function Ot(e){var t=Qe(e),n=t.overflow,o=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+o)}function Rt(e){return["html","body","#document"].indexOf($e(e))>=0?e.ownerDocument.body:ze(e)&&Ot(e)?e:Rt(nt(e))}function At(e,t){var n;void 0===t&&(t=[]);var o=Rt(e),r=o===(null==(n=e.ownerDocument)?void 0:n.body),a=We(o),i=r?[a].concat(a.visualViewport||[],Ot(o)?o:[]):o,s=t.concat(i);return r?s:s.concat(At(nt(i)))}function Et(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Tt(e,t){return t===Le?Et(function(e){var t=We(e),n=tt(e),o=t.visualViewport,r=n.clientWidth,a=n.clientHeight,i=0,s=0;return o&&(r=o.width,a=o.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(i=o.offsetLeft,s=o.offsetTop)),{width:r,height:a,x:i+xt(e),y:s}}(e)):qe(t)?function(e){var t=Ye(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}(t):Et(function(e){var t,n=tt(e),o=wt(e),r=null==(t=e.ownerDocument)?void 0:t.body,a=Ke(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),i=Ke(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),s=-o.scrollLeft+xt(e),l=-o.scrollTop;return"rtl"===Qe(r||n).direction&&(s+=Ke(n.clientWidth,r?r.clientWidth:0)-a),{width:a,height:i,x:s,y:l}}(tt(e)))}function Ct(e,t,n){var o="clippingParents"===t?function(e){var t=At(nt(e)),n=["absolute","fixed"].indexOf(Qe(e).position)>=0&&ze(e)?rt(e):e;return qe(n)?t.filter((function(e){return qe(e)&&Je(e,n)&&"body"!==$e(e)})):[]}(e):[].concat(t),r=[].concat(o,[n]),a=r[0],i=r.reduce((function(t,n){var o=Tt(e,n);return t.top=Ke(o.top,t.top),t.right=Ze(o.right,t.right),t.bottom=Ze(o.bottom,t.bottom),t.left=Ke(o.left,t.left),t}),Tt(e,a));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function St(e){var t,n=e.reference,o=e.element,r=e.placement,a=r?Ve(r):null,i=r?pt(r):null,s=n.x+n.width/2-o.width/2,l=n.y+n.height/2-o.height/2;switch(a){case Ce:t={x:s,y:n.y-o.height};break;case Se:t={x:s,y:n.y+n.height};break;case ke:t={x:n.x+n.width,y:l};break;case je:t={x:n.x-o.width,y:l};break;default:t={x:n.x,y:n.y}}var u=a?at(a):null;if(null!=u){var p="y"===u?"height":"width";switch(i){case Pe:t[u]=t[u]-(n[p]/2-o[p]/2);break;case Fe:t[u]=t[u]+(n[p]/2-o[p]/2)}}return t}function kt(e,t){void 0===t&&(t={});var n=t,o=n.placement,r=void 0===o?e.placement:o,a=n.boundary,i=void 0===a?"clippingParents":a,s=n.rootBoundary,l=void 0===s?Le:s,u=n.elementContext,p=void 0===u?De:u,f=n.altBoundary,c=void 0!==f&&f,d=n.padding,v=void 0===d?0:d,m=st("number"!=typeof v?v:lt(v,Be)),g=p===De?"reference":De,h=e.rects.popper,b=e.elements[c?g:p],y=Ct(qe(b)?b:b.contextElement||tt(e.elements.popper),i,l),w=Ye(e.elements.reference),x=St({reference:w,element:h,placement:r}),O=Et(Object.assign({},h,x)),R=p===De?O:w,A={top:y.top-R.top+m.top,bottom:R.bottom-y.bottom+m.bottom,left:y.left-R.left+m.left,right:R.right-y.right+m.right},E=e.modifiersData.offset;if(p===De&&E){var T=E[r];Object.keys(A).forEach((function(e){var t=[ke,Se].indexOf(e)>=0?1:-1,n=[Ce,Se].indexOf(e)>=0?"y":"x";A[e]+=T[n]*t}))}return A}var jt={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var r=n.mainAxis,a=void 0===r||r,i=n.altAxis,s=void 0===i||i,l=n.fallbackPlacements,u=n.padding,p=n.boundary,f=n.rootBoundary,c=n.altBoundary,d=n.flipVariations,v=void 0===d||d,m=n.allowedAutoPlacements,g=t.options.placement,h=Ve(g),b=l||(h===g||!v?[ht(g)]:function(e){if(Ve(e)===Me)return[];var t=ht(e);return[yt(e),t,yt(t)]}(g)),y=[g].concat(b).reduce((function(e,n){return e.concat(Ve(n)===Me?function(e,t){void 0===t&&(t={});var n=t,o=n.placement,r=n.boundary,a=n.rootBoundary,i=n.padding,s=n.flipVariations,l=n.allowedAutoPlacements,u=void 0===l?Ie:l,p=pt(o),f=p?s?_e:_e.filter((function(e){return pt(e)===p})):Be,c=f.filter((function(e){return u.indexOf(e)>=0}));0===c.length&&(c=f);var d=c.reduce((function(t,n){return t[n]=kt(e,{placement:n,boundary:r,rootBoundary:a,padding:i})[Ve(n)],t}),{});return Object.keys(d).sort((function(e,t){return d[e]-d[t]}))}(t,{placement:n,boundary:p,rootBoundary:f,padding:u,flipVariations:v,allowedAutoPlacements:m}):n)}),[]),w=t.rects.reference,x=t.rects.popper,O=new Map,R=!0,A=y[0],E=0;E<y.length;E++){var T=y[E],C=Ve(T),S=pt(T)===Pe,k=[Ce,Se].indexOf(C)>=0,j=k?"width":"height",M=kt(t,{placement:T,boundary:p,rootBoundary:f,altBoundary:c,padding:u}),B=k?S?ke:je:S?Se:Ce;w[j]>x[j]&&(B=ht(B));var P=ht(B),F=[];if(a&&F.push(M[C]<=0),s&&F.push(M[B]<=0,M[P]<=0),F.every((function(e){return e}))){A=T,R=!1;break}O.set(T,F)}if(R)for(var L=function(e){var t=y.find((function(t){var n=O.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return A=t,"break"},D=v?3:1;D>0;D--){if("break"===L(D))break}t.placement!==A&&(t.modifiersData[o]._skip=!0,t.placement=A,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Mt(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Bt(e){return[Ce,ke,Se,je].some((function(t){return e[t]>=0}))}var Pt={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,o=t.rects.reference,r=t.rects.popper,a=t.modifiersData.preventOverflow,i=kt(t,{elementContext:"reference"}),s=kt(t,{altBoundary:!0}),l=Mt(i,o),u=Mt(s,r,a),p=Bt(l),f=Bt(u);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:p,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":f})}};var Ft={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,o=e.name,r=n.offset,a=void 0===r?[0,0]:r,i=Ie.reduce((function(e,n){return e[n]=function(e,t,n){var o=Ve(e),r=[je,Ce].indexOf(o)>=0?-1:1,a="function"==typeof n?n(Object.assign({},t,{placement:e})):n,i=a[0],s=a[1];return i=i||0,s=(s||0)*r,[je,ke].indexOf(o)>=0?{x:s,y:i}:{x:i,y:s}}(n,t.rects,a),e}),{}),s=i[t.placement],l=s.x,u=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[o]=i}};var Lt={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=St({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}};var Dt={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name,r=n.mainAxis,a=void 0===r||r,i=n.altAxis,s=void 0!==i&&i,l=n.boundary,u=n.rootBoundary,p=n.altBoundary,f=n.padding,c=n.tether,d=void 0===c||c,v=n.tetherOffset,m=void 0===v?0:v,g=kt(t,{boundary:l,rootBoundary:u,padding:f,altBoundary:p}),h=Ve(t.placement),b=pt(t.placement),y=!b,w=at(h),x=function(e){return"x"===e?"y":"x"}(w),O=t.modifiersData.popperOffsets,R=t.rects.reference,A=t.rects.popper,E="function"==typeof m?m(Object.assign({},t.rects,{placement:t.placement})):m,T="number"==typeof E?{mainAxis:E,altAxis:E}:Object.assign({mainAxis:0,altAxis:0},E),C=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,S={x:0,y:0};if(O){if(a){var k,j="y"===w?Ce:je,M="y"===w?Se:ke,B="y"===w?"height":"width",P=O[w],F=P+g[j],L=P-g[M],D=d?-A[B]/2:0,_=b===Pe?R[B]:A[B],I=b===Pe?-A[B]:-R[B],H=t.elements.arrow,$=d&&H?Ge(H):{width:0,height:0},W=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},q=W[j],z=W[M],U=it(0,R[B],$[B]),N=y?R[B]/2-D-U-q-T.mainAxis:_-U-q-T.mainAxis,V=y?-R[B]/2+D+U+z+T.mainAxis:I+U+z+T.mainAxis,K=t.elements.arrow&&rt(t.elements.arrow),Z=K?"y"===w?K.clientTop||0:K.clientLeft||0:0,X=null!=(k=null==C?void 0:C[w])?k:0,Y=P+V-X,G=it(d?Ze(F,P+N-X-Z):F,P,d?Ke(L,Y):L);O[w]=G,S[w]=G-P}if(s){var J,Q="x"===w?Ce:je,ee="x"===w?Se:ke,te=O[x],ne="y"===x?"height":"width",oe=te+g[Q],re=te-g[ee],ae=-1!==[Ce,je].indexOf(h),ie=null!=(J=null==C?void 0:C[x])?J:0,se=ae?oe:te-R[ne]-A[ne]-ie+T.altAxis,le=ae?te+R[ne]+A[ne]-ie-T.altAxis:re,ue=d&&ae?function(e,t,n){var o=it(e,t,n);return o>n?n:o}(se,te,le):it(d?se:oe,te,d?le:re);O[x]=ue,S[x]=ue-te}t.modifiersData[o]=S}},requiresIfExists:["offset"]};function _t(e,t,n){void 0===n&&(n=!1);var o=ze(t),r=ze(t)&&function(e){var t=e.getBoundingClientRect(),n=Xe(t.width)/e.offsetWidth||1,o=Xe(t.height)/e.offsetHeight||1;return 1!==n||1!==o}(t),a=tt(t),i=Ye(e,r),s={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(o||!o&&!n)&&(("body"!==$e(t)||Ot(a))&&(s=function(e){return e!==We(e)&&ze(e)?function(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}(e):wt(e)}(t)),ze(t)?((l=Ye(t,!0)).x+=t.clientLeft,l.y+=t.clientTop):a&&(l.x=xt(a))),{x:i.left+s.scrollLeft-l.x,y:i.top+s.scrollTop-l.y,width:i.width,height:i.height}}function It(e){var t=new Map,n=new Set,o=[];function r(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var o=t.get(e);o&&r(o)}})),o.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||r(e)})),o}function Ht(e){var t;return function(){return t||(t=new Promise((function(n){Promise.resolve().then((function(){t=void 0,n(e())}))}))),t}}var $t={placement:"bottom",modifiers:[],strategy:"absolute"};function Wt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function qt(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,o=void 0===n?[]:n,r=t.defaultOptions,a=void 0===r?$t:r;return function(e,t,n){void 0===n&&(n=a);var r={placement:"bottom",orderedModifiers:[],options:Object.assign({},$t,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},i=[],s=!1,l={state:r,setOptions:function(n){var s="function"==typeof n?n(r.options):n;u(),r.options=Object.assign({},a,r.options,s),r.scrollParents={reference:qe(e)?At(e):e.contextElement?At(e.contextElement):[],popper:At(t)};var p=function(e){var t=It(e);return He.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}(function(e){var t=e.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{});return Object.keys(t).map((function(e){return t[e]}))}([].concat(o,r.options.modifiers)));return r.orderedModifiers=p.filter((function(e){return e.enabled})),r.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,o=void 0===n?{}:n,a=e.effect;if("function"==typeof a){var s=a({state:r,name:t,instance:l,options:o}),u=function(){};i.push(s||u)}})),l.update()},forceUpdate:function(){if(!s){var e=r.elements,t=e.reference,n=e.popper;if(Wt(t,n)){r.rects={reference:_t(t,rt(n),"fixed"===r.options.strategy),popper:Ge(n)},r.reset=!1,r.placement=r.options.placement,r.orderedModifiers.forEach((function(e){return r.modifiersData[e.name]=Object.assign({},e.data)}));for(var o=0;o<r.orderedModifiers.length;o++)if(!0!==r.reset){var a=r.orderedModifiers[o],i=a.fn,u=a.options,p=void 0===u?{}:u,f=a.name;"function"==typeof i&&(r=i({state:r,options:p,name:f,instance:l})||r)}else r.reset=!1,o=-1}}},update:Ht((function(){return new Promise((function(e){l.forceUpdate(),e(r)}))})),destroy:function(){u(),s=!0}};if(!Wt(e,t))return l;function u(){i.forEach((function(e){return e()})),i=[]}return l.setOptions(n).then((function(e){!s&&n.onFirstUpdate&&n.onFirstUpdate(e)})),l}}qt(),qt({defaultModifiers:[mt,Lt,dt,Ne]});var zt=qt({defaultModifiers:[mt,Lt,dt,Ne,Ft,jt,Dt,ut,Pt]});const Ut=e({arrowOffset:{type:Number,default:5}}),Nt=e({boundariesPadding:{type:Number,default:0},fallbackPlacements:{type:m(Array),default:void 0},gpuAcceleration:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:String,values:Ie,default:"bottom"},popperOptions:{type:m(Object),default:()=>({})},strategy:{type:String,values:["fixed","absolute"],default:"absolute"}}),Vt=e({...Nt,...Ut,id:String,style:{type:m([String,Array,Object])},className:{type:m([String,Array,Object])},effect:{type:m(String),default:"dark"},visible:Boolean,enterable:{type:Boolean,default:!0},pure:Boolean,focusOnShow:{type:Boolean,default:!1},trapping:{type:Boolean,default:!1},popperClass:{type:m([String,Array,Object])},popperStyle:{type:m([String,Array,Object])},referenceEl:{type:m(Object)},triggerTargetEl:{type:m(Object)},stopPopperMouseEvent:{type:Boolean,default:!0},virtualTriggering:Boolean,zIndex:Number,...ne(["ariaLabel"])}),Kt={mouseenter:e=>e instanceof MouseEvent,mouseleave:e=>e instanceof MouseEvent,focus:()=>!0,blur:()=>!0,close:()=>!0},Zt=(e,t=[])=>{const{placement:n,strategy:o,popperOptions:r}=e,a={placement:n,strategy:o,...r,modifiers:[...Xt(e),...t]};return function(e,t){t&&(e.modifiers=[...e.modifiers,...null!=t?t:[]])}(a,null==r?void 0:r.modifiers),a};function Xt(e){const{offset:t,gpuAcceleration:n,fallbackPlacements:o}=e;return[{name:"offset",options:{offset:[0,null!=t?t:12]}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5,fallbackPlacements:o}},{name:"computeStyles",options:{gpuAcceleration:n}}]}const Yt=(e,t,n={})=>{const a={name:"updateState",enabled:!0,phase:"write",fn:({state:e})=>{const t=function(e){const t=Object.keys(e.elements),n=L(t.map((t=>[t,e.styles[t]||{}]))),o=L(t.map((t=>[t,e.attributes[t]])));return{styles:n,attributes:o}}(e);Object.assign(l.value,t)},requires:["computeStyles"]},i=r((()=>{const{onFirstUpdate:e,placement:t,strategy:o,modifiers:r}=v(n);return{onFirstUpdate:e,placement:t||"bottom",strategy:o||"absolute",modifiers:[...r||[],a,{name:"applyStyles",enabled:!1}]}})),s=F(),l=o({styles:{popper:{position:v(i).strategy,left:"0",top:"0"},arrow:{position:"absolute"}},attributes:{}}),p=()=>{s.value&&(s.value.destroy(),s.value=void 0)};return E(i,(e=>{const t=v(s);t&&t.setOptions(e)}),{deep:!0}),E([e,t],(([e,t])=>{p(),e&&t&&(s.value=zt(e,t,v(i)))})),u((()=>{p()})),{state:r((()=>{var e;return{...(null==(e=v(s))?void 0:e.state)||{}}})),styles:r((()=>v(l).styles)),attributes:r((()=>v(l).attributes)),update:()=>{var e;return null==(e=v(s))?void 0:e.update()},forceUpdate:()=>{var e;return null==(e=v(s))?void 0:e.forceUpdate()},instanceRef:r((()=>v(s)))}};const Gt=e=>{const{popperInstanceRef:t,contentRef:n,triggerRef:a,role:i}=l(ce,void 0),s=o(),u=r((()=>e.arrowOffset)),p=r((()=>({name:"eventListeners",enabled:!!e.visible}))),f=r((()=>{var e;const t=v(s),n=null!=(e=v(u))?e:0;return{name:"arrow",enabled:!pe(t),options:{element:t,padding:n}}})),c=r((()=>({onFirstUpdate:()=>{b()},...Zt(e,[v(f),v(p)])}))),d=r((()=>(e=>{if(P)return M(e)})(e.referenceEl)||v(a))),{attributes:m,state:g,styles:h,update:b,forceUpdate:y,instanceRef:w}=Yt(d,n,c);return E(w,(e=>t.value=e),{flush:"sync"}),A((()=>{E((()=>{var e;return null==(e=v(d))?void 0:e.getBoundingClientRect()}),(()=>{b()}))})),{attributes:m,arrowRef:s,contentRef:n,instanceRef:w,state:g,styles:h,role:i,forceUpdate:y,update:b}},Jt=n({name:"ElPopperContent"});var Qt=t(n({...Jt,props:Vt,emits:Kt,setup(e,{expose:t,emit:n}){const c=e,{focusStartRef:d,trapped:m,onFocusAfterReleased:h,onFocusAfterTrapped:b,onFocusInTrap:y,onFocusoutPrevented:w,onReleaseRequested:x}=((e,t)=>{const n=o(!1),r=o();return{focusStartRef:r,trapped:n,onFocusAfterReleased:e=>{var n;"pointer"!==(null==(n=e.detail)?void 0:n.focusReason)&&(r.value="first",t("blur"))},onFocusAfterTrapped:()=>{t("focus")},onFocusInTrap:t=>{e.visible&&!n.value&&(t.target&&(r.value=t.target),n.value=!0)},onFocusoutPrevented:t=>{e.trapping||("pointer"===t.detail.focusReason&&t.preventDefault(),n.value=!1)},onReleaseRequested:()=>{n.value=!1,t("close")}}})(c,n),{attributes:O,arrowRef:C,contentRef:S,styles:M,instanceRef:P,role:F,update:L}=Gt(c),{ariaModal:I,arrowStyle:H,contentAttrs:$,contentClass:W,contentStyle:q,updateZIndex:z}=((e,{attributes:t,styles:n,role:a})=>{const{nextZIndex:i}=D(),l=s("popper"),u=r((()=>v(t).popper)),p=o(_(e.zIndex)?e.zIndex:i()),f=r((()=>[l.b(),l.is("pure",e.pure),l.is(e.effect),e.popperClass])),c=r((()=>[{zIndex:v(p)},v(n).popper,e.popperStyle||{}]));return{ariaModal:r((()=>"dialog"===a.value?"false":void 0)),arrowStyle:r((()=>v(n).arrow||{})),contentAttrs:u,contentClass:f,contentStyle:c,contentZIndex:p,updateZIndex:()=>{p.value=_(e.zIndex)?e.zIndex:i()}}})(c,{styles:M,attributes:O,role:F}),U=l(fe,void 0);let N;i(de,{arrowStyle:H,arrowRef:C}),U&&i(fe,{...U,addInputId:g,removeInputId:g});const V=(e=!0)=>{L(),e&&z()},K=()=>{V(!1),c.visible&&c.focusOnShow?m.value=!0:!1===c.visible&&(m.value=!1)};return A((()=>{E((()=>c.triggerTargetEl),((e,t)=>{null==N||N(),N=void 0;const n=v(e||S.value),o=v(t||S.value);T(n)&&(N=E([F,()=>c.ariaLabel,I,()=>c.id],(e=>{["role","aria-label","aria-modal","id"].forEach(((t,o)=>{B(e[o])?n.removeAttribute(t):n.setAttribute(t,e[o])}))}),{immediate:!0})),o!==n&&T(o)&&["role","aria-label","aria-modal","id"].forEach((e=>{o.removeAttribute(e)}))}),{immediate:!0}),E((()=>c.visible),K,{immediate:!0})})),u((()=>{null==N||N(),N=void 0})),t({popperContentRef:S,popperInstanceRef:P,updatePopper:V,contentStyle:q}),(e,t)=>(f(),p("div",j({ref_key:"contentRef",ref:S},v($),{style:v(q),class:v(W),tabindex:"-1",onMouseenter:t=>e.$emit("mouseenter",t),onMouseleave:t=>e.$emit("mouseleave",t)}),[R(v(re),{trapped:v(m),"trap-on-focus-in":!0,"focus-trap-el":v(S),"focus-start-el":v(d),onFocusAfterTrapped:v(b),onFocusAfterReleased:v(h),onFocusin:v(y),onFocusoutPrevented:v(w),onReleaseRequested:v(x)},{default:k((()=>[a(e.$slots,"default")])),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusin","onFocusoutPrevented","onReleaseRequested"])],16,["onMouseenter","onMouseleave"]))}}),[["__file","content.vue"]]);const en=I(he),tn=Symbol("elTooltip");function nn(){let e;const t=()=>window.clearTimeout(e);return H((()=>t())),{registerTimeout:(n,o)=>{t(),e=window.setTimeout(n,o)},cancelTimeout:t}}const on=e({showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0}}),rn=e({...on,...Vt,appendTo:{type:ae.to.type},content:{type:String,default:""},rawContent:Boolean,persistent:Boolean,visible:{type:m(Boolean),default:null},transition:String,teleported:{type:Boolean,default:!0},disabled:Boolean,...ne(["ariaLabel"])}),an=e({...we,disabled:Boolean,trigger:{type:m([String,Array]),default:"hover"},triggerKeys:{type:m(Array),default:()=>[$.enter,$.numpadEnter,$.space]}}),sn=W({type:m(Boolean),default:null}),ln=W({type:m(Function)}),{useModelToggleProps:un,useModelToggleEmits:pn,useModelToggle:fn}=(e=>{const t=`update:${e}`,n=`onUpdate:${e}`,o=[t];return{useModelToggle:({indicator:o,toggleReason:a,shouldHideWhenRouteChanges:i,shouldProceed:s,onShow:l,onHide:u})=>{const p=q(),{emit:f}=p,c=p.props,d=r((()=>z(c[n]))),v=r((()=>null===c[e])),m=e=>{!0!==o.value&&(o.value=!0,a&&(a.value=e),z(l)&&l(e))},g=e=>{!1!==o.value&&(o.value=!1,a&&(a.value=e),z(u)&&u(e))},h=e=>{if(!0===c.disabled||z(s)&&!s())return;const n=d.value&&P;n&&f(t,!0),!v.value&&n||m(e)},b=e=>{if(!0===c.disabled||!P)return;const n=d.value&&P;n&&f(t,!1),!v.value&&n||g(e)},y=e=>{U(e)&&(c.disabled&&e?d.value&&f(t,!1):o.value!==e&&(e?m():g()))};return E((()=>c[e]),y),i&&void 0!==p.appContext.config.globalProperties.$route&&E((()=>({...p.proxy.$route})),(()=>{i.value&&o.value&&b()})),A((()=>{y(c[e])})),{hide:b,show:h,toggle:()=>{o.value?b():h()},hasUpdateHandler:d}},useModelToggleProps:{[e]:sn,[n]:ln},useModelToggleEmits:o}})("visible"),cn=e({...me,...un,...rn,...an,...Ut,showArrow:{type:Boolean,default:!0}}),dn=[...pn,"before-show","before-hide","show","hide","open","close"],vn=(e,t,n)=>o=>{((e,t)=>N(e)?e.includes(t):e===t)(v(e),t)&&n(o)},mn=(e,t,{checkForDefaultPrevented:n=!0}={})=>o=>{const r=null==e?void 0:e(o);if(!1===n||!r)return null==t?void 0:t(o)},gn=e=>t=>"mouse"===t.pointerType?e(t):void 0,hn=n({name:"ElTooltipTrigger"});var bn=t(n({...hn,props:an,setup(e,{expose:t}){const n=e,r=s("tooltip"),{controlled:i,id:u,open:p,onOpen:c,onClose:m,onToggle:g}=l(tn,void 0),h=o(null),b=()=>{if(v(i)||n.disabled)return!0},y=V(n,"trigger"),w=mn(b,vn(y,"hover",c)),x=mn(b,vn(y,"hover",m)),O=mn(b,vn(y,"click",(e=>{0===e.button&&g(e)}))),R=mn(b,vn(y,"focus",c)),A=mn(b,vn(y,"focus",m)),E=mn(b,vn(y,"contextmenu",(e=>{e.preventDefault(),g(e)}))),T=mn(b,(e=>{const{code:t}=e;n.triggerKeys.includes(t)&&(e.preventDefault(),g(e))}));return t({triggerRef:h}),(e,t)=>(f(),C(v(Te),{id:v(u),"virtual-ref":e.virtualRef,open:v(p),"virtual-triggering":e.virtualTriggering,class:d(v(r).e("trigger")),onBlur:v(A),onClick:v(O),onContextmenu:v(E),onFocus:v(R),onMouseenter:v(w),onMouseleave:v(x),onKeydown:v(T)},{default:k((()=>[a(e.$slots,"default")])),_:3},8,["id","virtual-ref","open","virtual-triggering","class","onBlur","onClick","onContextmenu","onFocus","onMouseenter","onMouseleave","onKeydown"]))}}),[["__file","trigger.vue"]]);const yn=()=>{const e=Z(),t=le(),n=r((()=>`${e.value}-popper-container-${t.prefix}`)),o=r((()=>`#${n.value}`));return{id:n,selector:o}},wn=()=>{const{id:e,selector:t}=yn();return K((()=>{P&&(document.body.querySelector(t.value)||(e=>{const t=document.createElement("div");t.id=e,document.body.appendChild(t)})(e.value))})),{id:e,selector:t}},xn=n({name:"ElTooltipContent",inheritAttrs:!1});var On=t(n({...xn,props:rn,setup(e,{expose:t}){const n=e,{selector:i}=yn(),p=s("tooltip"),c=o(),d=X((()=>{var e;return null==(e=c.value)?void 0:e.popperContentRef}));let m;const{controlled:g,id:b,open:y,trigger:w,onClose:x,onOpen:O,onShow:A,onHide:T,onBeforeShow:M,onBeforeHide:B}=l(tn,void 0),P=r((()=>n.transition||`${p.namespace.value}-fade-in-linear`)),F=r((()=>n.persistent));u((()=>{null==m||m()}));const L=r((()=>!!v(F)||v(y))),D=r((()=>!n.disabled&&v(y))),_=r((()=>n.appendTo||i.value)),I=r((()=>{var e;return null!=(e=n.style)?e:{}})),H=o(!0),$=()=>{T(),Z()&&se(document.body),H.value=!0},W=()=>{if(v(g))return!0},q=mn(W,(()=>{n.enterable&&"hover"===v(w)&&O()})),z=mn(W,(()=>{"hover"===v(w)&&x()})),U=()=>{var e,t;null==(t=null==(e=c.value)?void 0:e.updatePopper)||t.call(e),null==M||M()},N=()=>{null==B||B()},V=()=>{A()},K=()=>{n.virtualTriggering||x()},Z=e=>{var t;const n=null==(t=c.value)?void 0:t.popperContentRef,o=(null==e?void 0:e.relatedTarget)||document.activeElement;return null==n?void 0:n.contains(o)};return E((()=>v(y)),(e=>{e?(H.value=!1,m=J(d,(()=>{if(v(g))return;"hover"!==v(w)&&x()}))):null==m||m()}),{flush:"post"}),E((()=>n.content),(()=>{var e,t;null==(t=null==(e=c.value)?void 0:e.updatePopper)||t.call(e)})),t({contentRef:c,isFocusInsideContent:Z}),(e,t)=>(f(),C(v(ie),{disabled:!e.teleported,to:v(_)},{default:k((()=>[R(Y,{name:v(P),onAfterLeave:$,onBeforeEnter:U,onAfterEnter:V,onBeforeLeave:N},{default:k((()=>[v(L)?h((f(),C(v(Qt),j({key:0,id:v(b),ref_key:"contentRef",ref:c},e.$attrs,{"aria-label":e.ariaLabel,"aria-hidden":H.value,"boundaries-padding":e.boundariesPadding,"fallback-placements":e.fallbackPlacements,"gpu-acceleration":e.gpuAcceleration,offset:e.offset,placement:e.placement,"popper-options":e.popperOptions,"arrow-offset":e.arrowOffset,strategy:e.strategy,effect:e.effect,enterable:e.enterable,pure:e.pure,"popper-class":e.popperClass,"popper-style":[e.popperStyle,v(I)],"reference-el":e.referenceEl,"trigger-target-el":e.triggerTargetEl,visible:v(D),"z-index":e.zIndex,onMouseenter:v(q),onMouseleave:v(z),onBlur:K,onClose:v(x)}),{default:k((()=>[a(e.$slots,"default")])),_:3},16,["id","aria-label","aria-hidden","boundaries-padding","fallback-placements","gpu-acceleration","offset","placement","popper-options","arrow-offset","strategy","effect","enterable","pure","popper-class","popper-style","reference-el","trigger-target-el","visible","z-index","onMouseenter","onMouseleave","onClose"])),[[G,v(D)]]):S("v-if",!0)])),_:3},8,["name"])])),_:3},8,["disabled","to"]))}}),[["__file","content.vue"]]);const Rn=n({name:"ElTooltip"});const An=I(t(n({...Rn,props:cn,emits:dn,setup(e,{expose:t,emit:n}){const l=e;wn();const u=s("tooltip"),c=ue(),d=o(),m=o(),g=()=>{var e;const t=v(d);t&&(null==(e=t.popperInstanceRef)||e.update())},h=o(!1),b=o(),{show:y,hide:w,hasUpdateHandler:x}=fn({indicator:h,toggleReason:b}),{onOpen:O,onClose:A}=(({showAfter:e,hideAfter:t,autoClose:n,open:o,close:r})=>{const{registerTimeout:a}=nn(),{registerTimeout:i,cancelTimeout:s}=nn();return{onOpen:t=>{a((()=>{o(t);const e=v(n);_(e)&&e>0&&i((()=>{r(t)}),e)}),v(e))},onClose:e=>{s(),a((()=>{r(e)}),v(t))}}})({showAfter:V(l,"showAfter"),hideAfter:V(l,"hideAfter"),autoClose:V(l,"autoClose"),open:y,close:w}),T=r((()=>U(l.visible)&&!x.value)),j=r((()=>[u.b(),l.popperClass]));i(tn,{controlled:T,id:c,open:Q(h),trigger:V(l,"trigger"),onOpen:e=>{O(e)},onClose:e=>{A(e)},onToggle:e=>{v(h)?A(e):O(e)},onShow:()=>{n("show",b.value)},onHide:()=>{n("hide",b.value)},onBeforeShow:()=>{n("before-show",b.value)},onBeforeHide:()=>{n("before-hide",b.value)},updatePopper:g}),E((()=>l.disabled),(e=>{e&&h.value&&(h.value=!1)}));return ee((()=>h.value&&w())),t({popperRef:d,contentRef:m,isFocusInsideContent:e=>{var t;return null==(t=m.value)?void 0:t.isFocusInsideContent(e)},updatePopper:g,onOpen:O,onClose:A,hide:w}),(e,t)=>(f(),C(v(en),{ref_key:"popperRef",ref:d,role:e.role},{default:k((()=>[R(bn,{disabled:e.disabled,trigger:e.trigger,"trigger-keys":e.triggerKeys,"virtual-ref":e.virtualRef,"virtual-triggering":e.virtualTriggering},{default:k((()=>[e.$slots.default?a(e.$slots,"default",{key:0}):S("v-if",!0)])),_:3},8,["disabled","trigger","trigger-keys","virtual-ref","virtual-triggering"]),R(On,{ref_key:"contentRef",ref:m,"aria-label":e.ariaLabel,"boundaries-padding":e.boundariesPadding,content:e.content,disabled:e.disabled,effect:e.effect,enterable:e.enterable,"fallback-placements":e.fallbackPlacements,"hide-after":e.hideAfter,"gpu-acceleration":e.gpuAcceleration,offset:e.offset,persistent:e.persistent,"popper-class":v(j),"popper-style":e.popperStyle,placement:e.placement,"popper-options":e.popperOptions,"arrow-offset":e.arrowOffset,pure:e.pure,"raw-content":e.rawContent,"reference-el":e.referenceEl,"trigger-target-el":e.triggerTargetEl,"show-after":e.showAfter,strategy:e.strategy,teleported:e.teleported,transition:e.transition,"virtual-triggering":e.virtualTriggering,"z-index":e.zIndex,"append-to":e.appendTo},{default:k((()=>[a(e.$slots,"content",{},(()=>[e.rawContent?(f(),p("span",{key:0,innerHTML:e.content},null,8,["innerHTML"])):(f(),p("span",{key:1},te(e.content),1))])),e.showArrow?(f(),C(v(ye),{key:0})):S("v-if",!0)])),_:3},8,["aria-label","boundaries-padding","content","disabled","effect","enterable","fallback-placements","hide-after","gpu-acceleration","offset","persistent","popper-class","popper-style","placement","popper-options","arrow-offset","pure","raw-content","reference-el","trigger-target-el","show-after","strategy","teleported","transition","virtual-triggering","z-index","append-to"])])),_:3},8,["role"]))}}),[["__file","tooltip.vue"]]));export{An as E,Oe as O,tn as T,an as a,Ie as b,mn as c,ve as r,rn as u,gn as w};
