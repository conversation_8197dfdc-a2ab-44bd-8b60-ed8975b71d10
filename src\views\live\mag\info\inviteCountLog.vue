<template>
  <el-dialog v-model="isShow" :width="800" title="引流数总览" @close="info = {} as any">
    <el-container>
      <el-header style="height: auto; padding: 0 0 10px 0">
        <el-descriptions border label-width="65px" :column="3">
          <el-descriptions-item label="引流人">{{ info?.nickName }}</el-descriptions-item>
          <el-descriptions-item label="转发数">{{ info?.sharedCount }}</el-descriptions-item>
          <el-descriptions-item label="引流数">{{ info?.inviteCount }}</el-descriptions-item>
        </el-descriptions>
      </el-header>
      <el-main style="height: 500px; padding: 0">
        <look-user
          v-if="info?.id"
          :inviter-id="info?.userId"
          :inviterType="info?.userType"
          :session-id="sessionId"
        />
      </el-main>
    </el-container>
  </el-dialog>
</template>
<script setup lang="ts">
import { PageInviteRecordVo } from "@/api/live/liveSession";
import LookUser from "@/views/live/mag/info/lookUser.vue";

defineProps({
  sessionId: {
    type: Number,
    default: -1,
  },
});
const isShow = ref(false);
const info = ref<PageInviteRecordVo>();
defineExpose({
  open: (data: PageInviteRecordVo) => {
    info.value = data;
    isShow.value = true;
  },
});
</script>
