<script setup lang="ts">
import { useApi } from "@/utils/commonSetup";
import livePreview, { PreviewDataStatsVO } from "@/api/live/livePreview";
import { dayjs } from "element-plus";
import DrainageList from "@/views/live/livePreview/components/DrainageList.vue";
import SpeechesRanking from "@/views/live/livePreview/components/SpeechesRanking.vue";
import AllUserList from "@/views/live/livePreview/components/AllUserList.vue";

const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
});
const activeName = ref("yl");
const { sendInfo, onSend } = useApi<number, Array<PreviewDataStatsVO>>(livePreview.stats as any);
let stsTimer: any = null;
watch(
  () => props.info.id,
  (_newVal: number) => {
    if (_newVal) {
      if (stsTimer) {
        clearInterval(stsTimer);
      }
      activeName.value = "yl";
      sendInfo.params = props.info.id;
      onSend();
      stsTimer = setInterval(
        () => {
          onSend();
        },
        1000 * 60 * 5
      );
    }
  },
  { deep: true }
);
const stsOptions = computed(() => {
  return {
    color: ["#FF657A", "#2DCA7B", "#4080FF"],
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: [
        {
          name: "真实观众",
          textStyle: {
            color: "#FF657A",
          },
        },
        {
          name: "弹幕数量",
          textStyle: {
            color: "#2DCA7B",
          },
        },
        {
          name: "Ai助理弹幕数",
          textStyle: {
            color: "#4080FF",
          },
        },
      ],
      itemWidth: 7,
      itemHeight: 7,
      icon: "circle",
      left: 20,
      top: 20,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: sendInfo.data?.map((item) => dayjs(item.createdAt).format("HH:mm:ss")) || [],
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "真实观众",
        type: "line",
        data: sendInfo.data?.map((item) => item.onlineCount) || [],
        smooth: true,
      },
      {
        name: "弹幕数量",
        type: "line",
        data: sendInfo.data?.map((item) => item.commentCount) || [],
        smooth: true,
      },
      {
        name: "Ai助理弹幕数",
        type: "line",
        data: sendInfo.data?.map((item) => item.aiCommentCount) || [],
        smooth: true,
      },
    ],
  };
});
</script>

<template>
  <el-container style="height: 100%; width: 100%; padding: 0" class="info">
    <el-main style="height: 100%; width: 100%; padding: 0">
      <el-container style="height: 100%; width: 100%; padding: 0">
        <el-main style="height: 100%; width: 100%; padding: 0">
          <el-tabs v-model="activeName" class="my-tabs">
            <el-tab-pane label="引流排行榜" name="yl">
              <drainage-list :info="info" />
            </el-tab-pane>
            <el-tab-pane label="发言排行榜" name="fy">
              <speeches-ranking :info="info" />
            </el-tab-pane>
            <el-tab-pane label="所有观众" name="all">
              <all-user-list :info="info" />
            </el-tab-pane>
          </el-tabs>
        </el-main>
      </el-container>
    </el-main>
    <el-footer class="sts">
      <e-charts :options="stsOptions" width="100%" height="100%" />
    </el-footer>
  </el-container>
</template>

<style scoped lang="scss">
.info {
  :deep(.my-tabs) {
    height: 100%;

    .el-tab-pane {
      height: 100%;
    }

    .el-tabs__header {
      margin-bottom: 10px;
    }

    .el-tabs__nav-wrap {
      margin-left: 20px;

      &:after {
        display: none;
      }
    }
  }
}

.sts {
  height: 285px;
  padding: 0;
  border-top: 1px solid var(--el-border-color);
}
</style>
