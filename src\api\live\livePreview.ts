import request from "@/utils/request";

export interface PreviewDataStatsVO {
  /*id*/
  id: number;
  /*直播场次id*/
  liveSessionId: number;
  /*在线数*/
  onlineCount: number;
  /*发言数*/
  commentCount: number;
  /*ai发言数*/
  aiCommentCount: number;
  /*	直播间状态 1直播中 3暂停中*/
  sessionStatus: number;
  /*创建时间*/
  createdAt: string;
}

export interface PreviewSessionInfoVO {
  /*直播场次ID*/
  id: number;
  /*助理*/
  assist: Array<string>;
  /*实际开始时间*/
  actualStartTime: string;
  /*直播间观众*/
  watchCount: number;
  /*销售列表*/
  saleList: Array<string>;
  /*播放地址*/
  h5Url: string;
}

export class PageInviterListQueryDto {
  pageNum = 1;
  pageSize = 30;
  /*场次id*/
  sessionId: string = "";
}

export interface PageInviterListVO {
  /*邀请记录ID*/
  id: number;
  /*邀请id*/
  inviterId: number;
  /*邀请人类型 0 普通用户 1 主播 2助理 3:销售*/
  inviterType: number;
  /*邀请人数*/
  inviteCount: number;
  /*邀请人名称*/
  inviterName: string;
}

export interface PagePreviewSessionVO {
  /*场次ID*/
  id: number;
  /*直播标题*/
  title: string;
  /*主播名称*/
  anchorName: string;
}

export class PageSessionUserDto {
  pageNum = 1;
  pageSize = 10;
  sessionId: string = "";
  /*引流人id*/
  inviterId: string = "";
  /*类型*/
  type: string = "";
}

export interface UserTagsVo {
  userId: number;
  tagId: number;
  name: string;
}

export interface SessionUserVo {
  /*id*/
  id: number;
  /*用户id*/
  userId: number;
  /*用户类型 3:销售 2:助理 其余为普通用户*/
  userType: number;
  /*昵称*/
  nickname: string;
  /*头像*/
  avatar: string;
  /*观看时长 (秒)*/
  duration: string;
  /*	ip地址*/
  ipAddress: string;
  /*国 不为中国的情况下,不会有省和市*/
  country: string;
  /*省*/
  province: string;
  /*市*/
  city: string;
  /*	封禁类型-1为正常 0:普通封禁 1:永久封禁 2:拉黑*/
  banType: number;
  /*封禁时长(秒) 封禁类型0:普通封禁时使用*/
  banDuration: number;
  /*标签数组*/
  tagIds: Array<UserTagsVo>;
  /*发言数*/
  commentCount: number;
  /*是否在线 1:在线 0:离线*/
  isOnline: number;
  /*邀请人名称 为空就是没被邀请*/
  inviterName: string;
  /*邀请人id 为空就是没被邀请*/
  inviterId: number;
}

const LivePreviewApi = {
  /*数据统计*/
  stats(liveSessionId: number) {
    return request<any, Array<PreviewDataStatsVO>>({
      url: `/tenant/live/preview/data/stats?sessionId=${liveSessionId}`,
      method: "get",
    });
  },
  /*直播场次信息*/
  info(liveSessionId: number) {
    return request<any, PreviewSessionInfoVO>({
      url: `/tenant/live/preview/info?sessionId=${liveSessionId}`,
      method: "get",
    });
  },
  /*引流人列表*/
  inviterList(query: PageInviterListQueryDto) {
    return request<any, PageResult<PageInviterListVO[]>>({
      url: `/tenant/live/preview/inviter/list`,
      method: "get",
      params: query,
    });
  },
  /*直播场次列表*/
  list(query: PageQuery) {
    return request<PageQuery, PageResult<PagePreviewSessionVO[]>>({
      url: `/tenant/live/preview/list`,
      method: "get",
      params: query,
    });
  },
  /*查看直播用户*/
  sessionUser(query: PageSessionUserDto) {
    return request<any, PageResult<SessionUserVo[]>>({
      url: `/tenant/live/preview/sessionUser`,
      method: "get",
      params: query,
    });
  },
};
export default LivePreviewApi;
