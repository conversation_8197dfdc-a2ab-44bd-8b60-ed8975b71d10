import{bH as r,bI as n,bJ as t,bK as a,bL as e,bM as u,bN as i,bO as o,t as l}from"./index.Dk5pbsTU.js";import{i as s,a as c,b as f}from"./_arrayPush.DSBJLlac.js";function v(r){return r}var b=Date.now;var h,g,p,d=r?function(n,t){return r(n,"toString",{configurable:!0,enumerable:!1,value:(a=t,function(){return a}),writable:!0});var a}:v,m=(h=d,g=0,p=0,function(){var r=b(),n=16-(r-p);if(p=r,n>0){if(++g>=800)return arguments[0]}else g=0;return h.apply(void 0,arguments)}),y=Math.max;function S(r,n,t){return n=y(void 0===n?r.length-1:n,0),function(){for(var a=arguments,e=-1,u=y(a.length-n,0),i=Array(u);++e<u;)i[e]=a[n+e];e=-1;for(var o=Array(n+1);++e<n;)o[e]=a[e];return o[n]=t(i),function(r,n,t){switch(t.length){case 0:return r.call(n);case 1:return r.call(n,t[0]);case 2:return r.call(n,t[0],t[1]);case 3:return r.call(n,t[0],t[1],t[2])}return r.apply(n,t)}(r,this,o)}}var j=n?n.isConcatSpreadable:void 0;function w(r){return t(r)||s(r)||!!(j&&r&&r[j])}function x(r,n,t,a,e){var u=-1,i=r.length;for(t||(t=w),e||(e=[]);++u<i;){var o=r[u];n>0&&t(o)?n>1?x(o,n-1,t,a,e):c(e,o):e[e.length]=o}return e}function O(r){return(null==r?0:r.length)?x(r,1):[]}function A(r,n){return null!=r&&n in Object(r)}function C(r,n){return null!=r&&function(r,n,i){for(var o=-1,l=(n=a(n,r)).length,c=!1;++o<l;){var v=e(n[o]);if(!(c=null!=r&&i(r,v)))break;r=r[v]}return c||++o!=l?c:!!(l=null==r?0:r.length)&&f(l)&&u(v,l)&&(t(r)||s(r))}(r,n,A)}function L(r,n){return function(r,n,t){for(var e=-1,u=n.length,l={};++e<u;){var s=n[e],c=i(r,s);t(c,s)&&o(l,a(s,r),c)}return l}(r,n,(function(n,t){return C(r,t)}))}var M=function(r){return m(S(r,void 0,O),r+"")}((function(r,n){return null==r?{}:L(r,n)}));const k=l({ariaLabel:String,ariaOrientation:{type:String,values:["horizontal","vertical","undefined"]},ariaControls:String}),z=r=>M(k,r);export{x as b,O as f,C as h,v as i,S as o,M as p,m as s,z as u};
