<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px">
    <el-form ref="editFormRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="formData.userName"
          :maxlength="25"
          show-word-limit
          placeholder="请输入姓名"
        />
      </el-form-item>
      <el-form-item label="昵称" prop="nickName">
        <el-input
          v-model="formData.nickName"
          :maxlength="25"
          show-word-limit
          placeholder="请输入昵称"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="formData.mobile"
          :maxlength="11"
          show-word-limit
          placeholder="请输入手机号"
        />
      </el-form-item>
      <el-form-item label="头像" prop="avatar">
        <SingleImageUpload v-model="formData.avatar" :style="{ width: '100px', height: '100px' }" />
      </el-form-item>
      <el-form-item label="自主开播" prop="isAutonomous">
        <el-switch v-model="formData.isAutonomous" :active-value="1" :inactive-value="0" />
      </el-form-item>
      <el-form-item label="登录账号" prop="account">
        <el-input
          v-model="formData.account"
          :maxlength="25"
          show-word-limit
          placeholder="请输入账号"
        />
      </el-form-item>
      <el-form-item label="登录密码" :prop="!formData.id ? 'password' : ''">
        <el-input
          v-model="formData.password"
          :maxlength="25"
          show-word-limit
          placeholder="如需修改，请输入即可"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="handleSubmit">确 定</el-button>
        <el-button :loading="loading" @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { formValidator, isPhone } from "@/utils/validate";
import WebmasterApi, {
  SaveWebmasterDto,
  WebmasterPageVO,
} from "@/api/webcasterAndAssistant/webcaster";

const dialog = reactive({
  visible: false,
  title: "",
});
const formData = ref(new SaveWebmasterDto());
const editFormRef = ref();
const emits = defineEmits(["success"]);
const loading = ref(false);
const rules = {
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
  account: [{ required: true, message: "请输入账号", trigger: "blur" }],
  userName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  nickName: [{ required: true, message: "请输入昵称", trigger: "blur" }],
  avatar: [{ required: true, message: "请上传头像", trigger: "blur" }],
  mobile: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    formValidator(isPhone, "手机号错误"),
  ]
};

function handleSubmit() {
  editFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      WebmasterApi.save(formData.value)
        .then(() => {
          ElMessage.success("保存成功");
          dialog.visible = false;
          emits("success");
        })
        .finally(() => (loading.value = false));
    }
  });
}

defineExpose({
  open: (_row?: WebmasterPageVO) => {
    dialog.visible = true;
    dialog.title = _row?.id ? "编辑主播信息" : "新增主播";
    formData.value = new SaveWebmasterDto(_row);
  },
});
</script>
