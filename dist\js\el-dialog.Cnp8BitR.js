import{_ as e,d as s,x as o,A as a,c as l,g as t,f as r,C as n,h as d,l as i,n as c,i as f,F as u,m as p,w as v,e as b,D as g,bZ as m,E as y,k as h,b9 as C,b as k,r as R,T as w,V as $,U as A,aL as _,X as x,y as F,q as E}from"./index.Dk5pbsTU.js";import{e as I,f as L,d as P,a as j,u as M,E as T,c as q}from"./el-overlay.DpVCS8zG.js";import{F as D,E as z,a as B}from"./index.C6NthMtN.js";import{u as S}from"./index.DFyomGhz.js";import{c as U}from"./refs.BzVvuxps.js";import{u as H}from"./index.DuiNpp1i.js";const K=Symbol("dialogInjectionKey"),O=s({name:"ElDialogContent"});var V=e(s({...O,props:L,emits:I,setup(e,{expose:s}){const C=e,{t:k}=o(),{Close:R}=m,{dialogRef:w,headerRef:$,bodyId:A,ns:_,style:x}=a(K),{focusTrapRef:F}=a(D),E=l((()=>[_.b(),_.is("fullscreen",C.fullscreen),_.is("draggable",C.draggable),_.is("align-center",C.alignCenter),{[_.m("center")]:C.center}])),I=U(F,w),L=l((()=>C.draggable)),P=l((()=>C.overflow)),{resetPosition:j,updatePosition:M}=S(w,$,L,P);return s({resetPosition:j,updatePosition:M}),(e,s)=>(r(),t("div",{ref:f(I),class:c(f(E)),style:h(f(x)),tabindex:"-1"},[n("header",{ref_key:"headerRef",ref:$,class:c([f(_).e("header"),e.headerClass,{"show-close":e.showClose}])},[i(e.$slots,"header",{},(()=>[n("span",{role:"heading","aria-level":e.ariaLevel,class:c(f(_).e("title"))},u(e.title),11,["aria-level"])])),e.showClose?(r(),t("button",{key:0,"aria-label":f(k)("el.dialog.close"),class:c(f(_).e("headerbtn")),type:"button",onClick:s=>e.$emit("close")},[p(f(y),{class:c(f(_).e("close"))},{default:v((()=>[(r(),b(g(e.closeIcon||f(R))))])),_:1},8,["class"])],10,["aria-label","onClick"])):d("v-if",!0)],2),n("div",{id:f(A),class:c([f(_).e("body"),e.bodyClass])},[i(e.$slots,"default")],10,["id"]),e.$slots.footer?(r(),t("footer",{key:0,class:c([f(_).e("footer"),e.footerClass])},[i(e.$slots,"footer")],2)):d("v-if",!0)],6))}}),[["__file","dialog-content.vue"]]);const X=s({name:"ElDialog",inheritAttrs:!1});const Z=E(e(s({...X,props:j,emits:P,setup(e,{expose:s}){const o=e,a=C();H({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},l((()=>!!a.title)));const t=k("dialog"),u=R(),g=R(),m=R(),{visible:y,titleId:E,bodyId:I,style:L,overlayDialogStyle:P,rendered:j,zIndex:D,afterEnter:S,afterLeave:U,beforeLeave:O,handleClose:X,onModalClick:Z,onOpenAutoFocus:G,onCloseAutoFocus:J,onCloseRequested:N,onFocusoutPrevented:Q}=M(o,u);F(K,{dialogRef:u,headerRef:g,bodyId:I,ns:t,rendered:j,style:L});const W=q(Z),Y=l((()=>o.draggable&&!o.fullscreen));return s({visible:y,dialogContentRef:m,resetPosition:()=>{var e;null==(e=m.value)||e.resetPosition()},handleClose:X}),(e,s)=>(r(),b(f(B),{to:e.appendTo,disabled:"body"===e.appendTo&&!e.appendToBody},{default:v((()=>[p(w,{name:"dialog-fade",onAfterEnter:f(S),onAfterLeave:f(U),onBeforeLeave:f(O),persisted:""},{default:v((()=>[$(p(f(T),{"custom-mask-event":"",mask:e.modal,"overlay-class":e.modalClass,"z-index":f(D)},{default:v((()=>[n("div",{role:"dialog","aria-modal":"true","aria-label":e.title||void 0,"aria-labelledby":e.title?void 0:f(E),"aria-describedby":f(I),class:c(`${f(t).namespace.value}-overlay-dialog`),style:h(f(P)),onClick:f(W).onClick,onMousedown:f(W).onMousedown,onMouseup:f(W).onMouseup},[p(f(z),{loop:"",trapped:f(y),"focus-start-el":"container",onFocusAfterTrapped:f(G),onFocusAfterReleased:f(J),onFocusoutPrevented:f(Q),onReleaseRequested:f(N)},{default:v((()=>[f(j)?(r(),b(V,A({key:0,ref_key:"dialogContentRef",ref:m},e.$attrs,{center:e.center,"align-center":e.alignCenter,"close-icon":e.closeIcon,draggable:f(Y),overflow:e.overflow,fullscreen:e.fullscreen,"header-class":e.headerClass,"body-class":e.bodyClass,"footer-class":e.footerClass,"show-close":e.showClose,title:e.title,"aria-level":e.headerAriaLevel,onClose:f(X)}),_({header:v((()=>[e.$slots.title?i(e.$slots,"title",{key:1}):i(e.$slots,"header",{key:0,close:f(X),titleId:f(E),titleClass:f(t).e("title")})])),default:v((()=>[i(e.$slots,"default")])),_:2},[e.$slots.footer?{name:"footer",fn:v((()=>[i(e.$slots,"footer")]))}:void 0]),1040,["center","align-center","close-icon","draggable","overflow","fullscreen","header-class","body-class","footer-class","show-close","title","aria-level","onClose"])):d("v-if",!0)])),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,["aria-label","aria-labelledby","aria-describedby","onClick","onMousedown","onMouseup"])])),_:3},8,["mask","overlay-class","z-index"]),[[x,f(y)]])])),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])])),_:3},8,["to","disabled"]))}}),[["__file","dialog.vue"]]));export{Z as E};
