var e=Object.defineProperty,a=(a,l,t)=>((a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t)(a,"symbol"!=typeof l?l+"":l,t);import{aT as l,d as t,S as o,r as i,e as s,f as d,w as r,m as p,i as u,h as n,C as m,$ as v,az as c}from"./index.Dk5pbsTU.js";import{E as f}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as h}from"./el-button.CXI119n4.js";import{E as U,a as _}from"./el-form-item.Bw6Zyv_7.js";import{_ as b}from"./FileUpload.DqYUzwLQ.js";import{E as g}from"./el-switch.kQ5v4arH.js";import{_ as y}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as w}from"./el-input.DiGatoux.js";import{f as V,a as C}from"./validate.Bicq6Mu8.js";class T{constructor(){a(this,"pageNum",1),a(this,"pageSize",10),a(this,"versionName",""),a(this,"deviceType",""),a(this,"status",""),a(this,"platformType","")}}class j{constructor(e){a(this,"id"),a(this,"versionName",""),a(this,"deviceType",0),a(this,"platformType",0),a(this,"isHotUpdate",0),a(this,"isForceUpdate",0),a(this,"downloadUrl",""),a(this,"downloadUrls",[]),a(this,"updateContent",""),e&&(this.id=e.id,this.versionName=e.versionName,this.deviceType=e.deviceType,this.platformType=e.platformType,this.isHotUpdate=e.isHotUpdate,this.isForceUpdate=e.isForceUpdate,this.downloadUrl=e.downloadUrl,this.downloadUrls=[e.downloadUrl],this.updateContent=e.updateContent)}}const x={page:e=>l({url:"/adminConfig/version/page",method:"post",data:e}),remove:e=>l({url:`/adminConfig/version/remove/${e}`,method:"post"}),save:e=>l({url:"/adminConfig/version/saveOrUpdate",method:"post",data:e}),publish:e=>l({url:`/adminConfig/version/publish/${e}`,method:"post"})},F={class:"dialog-footer"},N=t({__name:"edit",emits:["success"],setup(e,{expose:a,emit:l}){const t=o({visible:!1,title:""}),T=i(new j),N=i(),H=l,E=i(!1),k={versionName:[{required:!0,message:"请输入版本号",trigger:"blur"},V(C,"版本号错误")],deviceType:[{required:!0,message:"请选择客户端",trigger:"blur"}],updateContent:[{required:!0,message:"请输入更新内容",trigger:"blur"}],downloadUrls:[{required:!0,message:"请上传安装包",trigger:"blur"}]};function q(){N.value.validate((e=>{e&&(T.value.downloadUrl=T.value.downloadUrls[0],E.value=!0,x.save(T.value).then((()=>{c.success("保存成功"),t.visible=!1,H("success")})).finally((()=>E.value=!1)))}))}return a({open:e=>{t.visible=!0,t.title=(null==e?void 0:e.id)?"编辑版本":"新增版本",T.value=new j(e)}}),(e,a)=>{const l=w,o=_,i=y,c=g,V=b,C=U,j=h,x=f;return d(),s(x,{modelValue:u(t).visible,"onUpdate:modelValue":a[7]||(a[7]=e=>u(t).visible=e),title:u(t).title,width:"500px"},{footer:r((()=>[m("div",F,[p(j,{type:"primary",onClick:q},{default:r((()=>a[8]||(a[8]=[v("确 定")]))),_:1,__:[8]}),p(j,{onClick:a[6]||(a[6]=e=>u(t).visible=!1)},{default:r((()=>a[9]||(a[9]=[v("取 消")]))),_:1,__:[9]})])])),default:r((()=>[p(C,{ref_key:"editFormRef",ref:N,model:u(T),rules:k,"label-width":"100px"},{default:r((()=>[p(o,{label:"版本号",prop:"versionName"},{default:r((()=>[p(l,{modelValue:u(T).versionName,"onUpdate:modelValue":a[0]||(a[0]=e=>u(T).versionName=e),maxlength:50,"show-word-limit":"",placeholder:"请输入版本号"},null,8,["modelValue"])])),_:1}),p(o,{label:"操作系统",prop:"deviceType"},{default:r((()=>[p(i,{modelValue:u(T).deviceType,"onUpdate:modelValue":a[1]||(a[1]=e=>u(T).deviceType=e),type:"radio",code:"version_device_type"},null,8,["modelValue"])])),_:1}),p(o,{label:"是否热更新",prop:"isHotUpdate"},{default:r((()=>[p(c,{modelValue:u(T).isHotUpdate,"onUpdate:modelValue":a[2]||(a[2]=e=>u(T).isHotUpdate=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1}),p(o,{label:"是否强制更新",prop:"isForceUpdate"},{default:r((()=>[p(c,{modelValue:u(T).isForceUpdate,"onUpdate:modelValue":a[3]||(a[3]=e=>u(T).isForceUpdate=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1}),p(o,{label:"更新内容",prop:"updateContent"},{default:r((()=>[p(l,{modelValue:u(T).updateContent,"onUpdate:modelValue":a[4]||(a[4]=e=>u(T).updateContent=e),type:"textarea",rows:4},null,8,["modelValue"])])),_:1}),u(t).visible?(d(),s(o,{key:0,label:"安装包",prop:"downloadUrls"},{default:r((()=>[p(V,{modelValue:u(T).downloadUrls,"onUpdate:modelValue":a[5]||(a[5]=e=>u(T).downloadUrls=e),maxFileSize:300,limit:1},null,8,["modelValue"])])),_:1})):n("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}});export{T as A,x as V,N as _};
