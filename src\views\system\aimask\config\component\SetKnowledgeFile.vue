<script setup lang="ts">
import { useApi, usePage } from "@/utils/commonSetup";
import KnowledgeType<PERSON><PERSON>, {
  KnowledgePageQueryDto,
  KnowledgePageVo,
  KnowledgeTypePageQueryDto,
  KnowledgeTypePageVo,
} from "@/api/system/knowledgeType";
import { Close } from "@element-plus/icons-vue";

const dialog = reactive({
  title: "设置知识库文件",
  show: false,
});
const emits = defineEmits(["success"]);
const { sendInfo, onSend } = useApi<KnowledgeTypePageQueryDto, KnowledgeTypePageVo[]>(
  KnowledgeTypeApi.allKnowledgeType as any,
  new KnowledgeTypePageQueryDto()
);
sendInfo.params!.status = "1";
const { page, getPage } = usePage<KnowledgePageQueryDto, KnowledgePageVo>(
  new KnowledgePageQueryDto(),
  KnowledgeTypeApi.aiKnowledgePage
);
const selectList = ref<Array<KnowledgePageVo>>([]);
let selectTimer: any = null;

function getSelectList(_ids: number[]) {
  if (selectTimer) {
    clearTimeout(selectTimer);
  } else {
    selectTimer = setTimeout(() => {
      KnowledgeTypeApi.aiKnowledgePage({
        pageNum: 1,
        pageSize: 99999,
        includeIds: _ids,
      } as any).then((res: any) => {
        selectList.value = res.data.records;
      });
    }, 200);
  }
}

function deleteSelectData(_index: number) {
  selectList.value.splice(_index, 1);
  page.query.excludeIds = selectList.value.map((item) => item.id);
  page.query.pageNum = 1;
  getPage();
}

function onSuccess() {
  emits(
    "success",
    selectList.value.map((item) => item.id)
  );
  dialog.show = false;
}

function addSelect(row: KnowledgePageVo | boolean) {
  if (typeof row === "boolean") {
    selectList.value = selectList.value.concat(page.data.records);
  } else {
    selectList.value.push(row);
  }
  page.query.excludeIds = selectList.value.map((item) => item.id);
  page.query.pageNum = 1;
  getSelectList(page.query.excludeIds);
  getPage();
}

defineExpose({
  open: (_ids: Array<number> = []) => {
    onSend();
    getSelectList(_ids);
    page.query.status = "1";
    page.query.pageNum = 1;
    page.query.excludeIds = _ids;
    getPage();
    dialog.show = true;
  },
});
</script>

<template>
  <el-dialog v-model="dialog.show" width="700px" :title="dialog.title">
    <el-container style="border-bottom: 1px solid var(--el-border-color)">
      <el-header style="padding: 10px 0">
        <el-form ref="queryFormRef" :model="page.query" :inline="true">
          <el-form-item prop="keywords" label="">
            <el-input
              v-model="page.query.keyWord"
              placeholder="类型名称检索"
              clearable
              @keyup.enter="getPage"
            />
          </el-form-item>
          <el-form-item prop="fileType" label="文件类型">
            <el-select v-model="page.query.typeId" clearable style="width: 200px">
              <el-option
                v-for="item in sendInfo.data"
                :key="item.id"
                :label="item.name"
                :value="String(item.id)"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-header>
      <el-main style="padding: 0; height: 500px">
        <el-table
          ref="dataTableRef"
          v-loading="page.loading"
          :data="page.data.records"
          height="100%"
          empty-text="暂无可选数据"
          highlight-current-row
          border
        >
          <el-table-column width="55" align="center">
            <template #header>
              <el-link type="primary" @click="addSelect(true)">全选</el-link>
            </template>
            <template #default="{ row }">
              <div class="select-box" @click="addSelect(row)" />
            </template>
          </el-table-column>
          <el-table-column label="序号" align="center" width="55" type="index" />
          <el-table-column label="文件名称" align="center" prop="fileName" min-width="120" />
          <el-table-column label="文件类型" align="center" prop="typeName" min-width="120">
            <template #default="{ row }">
              {{ row.typeName }}
              <span v-if="!row.typeStatus">
                （
                <span style="color: var(--el-color-danger)">已停用</span>
                ）
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <pagination
          v-if="page.data.totalRow"
          v-model:total="page.data.totalRow"
          v-model:page="page.query.pageNum"
          v-model:limit="page.query.pageSize"
          @pagination="getPage"
        />
      </el-footer>
    </el-container>
    <template #footer>
      <div
        style="
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 13px;
        "
      >
        <div>
          已选
          <el-popover placement="top" :width="500" trigger="click">
            <template #reference>
              <span style="color: var(--el-color-danger); cursor: pointer; margin: 0 5px">
                {{ selectList.length }}
              </span>
            </template>
            <el-empty v-if="!selectList.length" title="暂无已选数据" />
            <scroll-view v-else :scroll-y="true" style="height: 300px">
              <div v-for="(item, index) in selectList" :key="index" class="select-item">
                <div class="select-item-name">{{ item.fileName }}</div>
                <el-icon
                  style="color: var(--el-color-info); cursor: pointer"
                  @click="deleteSelectData(index)"
                >
                  <close />
                </el-icon>
              </div>
            </scroll-view>
          </el-popover>
          个文件
        </div>
        <div>
          <el-button @click="dialog.show = false">取消</el-button>
          <el-button type="primary" @click="onSuccess">确定</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.select-box {
  border: 1px solid var(--el-border-color);
  width: 15px;
  height: 15px;
  border-radius: 2px;
  cursor: pointer;
}

.select-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 2px;
  border-bottom: 1px solid var(--el-border-color);

  &-name {
    margin-right: 10px;
    width: 0;
    flex: 1;
  }
}
</style>
