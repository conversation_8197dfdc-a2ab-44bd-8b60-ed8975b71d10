import{g as e,f as l}from"./vnode.Cbclzz8S.js";import{d as t,J as s,b as a,V as r,a7 as n,M as i,A as o,t as c,z as p,_ as d,g as u,i as b,f as y,C as h,P as m,Q as v,e as f,m as g,N as w,b9 as S,c as N,h as k,n as $,l as W,$ as x,F as D,y as E,q as A,G as _}from"./index.Dk5pbsTU.js";import{u as j}from"./use-form-common-props.CQPDkY7k.js";const z=Symbol("elDescriptions");var C=t({name:"ElDescriptionsCell",props:{cell:{type:Object},tag:{type:String,default:"td"},type:{type:String}},setup:()=>({descriptions:o(z,{})}),render(){var l;const t=e(this.cell),o=((null==(l=this.cell)?void 0:l.dirs)||[]).map((e=>{const{dir:l,arg:t,modifiers:s,value:a}=e;return[l,a,t,s]})),{border:c,direction:p}=this.descriptions,d="vertical"===p,u=()=>{var e,l,s;return(null==(s=null==(l=null==(e=this.cell)?void 0:e.children)?void 0:l.label)?void 0:s.call(l))||t.label},b=()=>{var e,l,t;return null==(t=null==(l=null==(e=this.cell)?void 0:e.children)?void 0:l.default)?void 0:t.call(l)},y=t.span,h=t.rowspan,m=t.align?`is-${t.align}`:"",v=t.labelAlign?`is-${t.labelAlign}`:m,f=t.className,g=t.labelClassName,w="label"===this.type&&(t.labelWidth||this.descriptions.labelWidth)||t.width,S={width:s(w),minWidth:s(t.minWidth)},N=a("descriptions");switch(this.type){case"label":return r(n(this.tag,{style:S,class:[N.e("cell"),N.e("label"),N.is("bordered-label",c),N.is("vertical-label",d),v,g],colSpan:d?y:1,rowspan:d?1:h},u()),o);case"content":return r(n(this.tag,{style:S,class:[N.e("cell"),N.e("content"),N.is("bordered-content",c),N.is("vertical-content",d),m,f],colSpan:d?y:2*y-1,rowspan:d?2*h-1:h},b()),o);default:{const e=u(),l={},a=s(t.labelWidth||this.descriptions.labelWidth);return a&&(l.width=a,l.display="inline-block"),r(n("td",{style:S,class:[N.e("cell"),m],colSpan:y,rowspan:h},[i(e)?void 0:n("span",{style:l,class:[N.e("label"),g]},e),n("span",{class:[N.e("content"),f]},b())]),o)}}}});const I=c({row:{type:p(Array),default:()=>[]}}),q=t({name:"ElDescriptionsRow"});var B=d(t({...q,props:I,setup(e){const l=o(z,{});return(e,t)=>"vertical"===b(l).direction?(y(),u(m,{key:0},[h("tr",null,[(y(!0),u(m,null,v(e.row,((e,l)=>(y(),f(b(C),{key:`tr1-${l}`,cell:e,tag:"th",type:"label"},null,8,["cell"])))),128))]),h("tr",null,[(y(!0),u(m,null,v(e.row,((e,l)=>(y(),f(b(C),{key:`tr2-${l}`,cell:e,tag:"td",type:"content"},null,8,["cell"])))),128))])],64)):(y(),u("tr",{key:1},[(y(!0),u(m,null,v(e.row,((e,t)=>(y(),u(m,{key:`tr3-${t}`},[b(l).border?(y(),u(m,{key:0},[g(b(C),{cell:e,tag:"td",type:"label"},null,8,["cell"]),g(b(C),{cell:e,tag:"td",type:"content"},null,8,["cell"])],64)):(y(),f(b(C),{key:1,cell:e,tag:"td",type:"both"},null,8,["cell"]))],64)))),128))]))}}),[["__file","descriptions-row.vue"]]);const F=c({border:Boolean,column:{type:Number,default:3},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},size:w,title:{type:String,default:""},extra:{type:String,default:""},labelWidth:{type:[String,Number],default:""}}),G="ElDescriptionsItem",J=t({name:"ElDescriptions"});var M=d(t({...J,props:F,setup(e){const t=e,s=a("descriptions"),r=j(),n=S();E(z,t);const i=N((()=>[s.b(),s.m(r.value)])),o=(e,l,t,s=!1)=>(e.props||(e.props={}),l>t&&(e.props.span=t),s&&(e.props.span=l),e),c=()=>{if(!n.default)return[];const e=l(n.default()).filter((e=>{var l;return(null==(l=null==e?void 0:e.type)?void 0:l.name)===G})),s=[];let a=[],r=t.column,i=0;const c=[];return e.forEach(((l,n)=>{var p,d,u;const b=(null==(p=l.props)?void 0:p.span)||1,y=(null==(d=l.props)?void 0:d.rowspan)||1,h=s.length;if(c[h]||(c[h]=0),y>1)for(let e=1;e<y;e++)c[u=h+e]||(c[u]=0),c[h+e]++,i++;if(c[h]>0&&(r-=c[h],c[h]=0),n<e.length-1&&(i+=b>r?r:b),n===e.length-1){const e=t.column-i%t.column;return a.push(o(l,e,r,!0)),void s.push(a)}b<r?(r-=b,a.push(l)):(a.push(o(l,b,r)),s.push(a),r=t.column,a=[])})),s};return(e,l)=>(y(),u("div",{class:$(b(i))},[e.title||e.extra||e.$slots.title||e.$slots.extra?(y(),u("div",{key:0,class:$(b(s).e("header"))},[h("div",{class:$(b(s).e("title"))},[W(e.$slots,"title",{},(()=>[x(D(e.title),1)]))],2),h("div",{class:$(b(s).e("extra"))},[W(e.$slots,"extra",{},(()=>[x(D(e.extra),1)]))],2)],2)):k("v-if",!0),h("div",{class:$(b(s).e("body"))},[h("table",{class:$([b(s).e("table"),b(s).is("bordered",e.border)])},[h("tbody",null,[(y(!0),u(m,null,v(c(),((e,l)=>(y(),f(B,{key:l,row:e},null,8,["row"])))),128))])],2)],2)],2))}}),[["__file","description.vue"]]);const O=["left","center","right"],P=c({label:{type:String,default:""},span:{type:Number,default:1},rowspan:{type:Number,default:1},width:{type:[String,Number],default:""},minWidth:{type:[String,Number],default:""},labelWidth:{type:[String,Number],default:""},align:{type:String,values:O,default:"left"},labelAlign:{type:String,values:O},className:{type:String,default:""},labelClassName:{type:String,default:""}}),Q=t({name:G,props:P}),R=A(M,{DescriptionsItem:Q}),V=_(Q);export{R as E,V as a};
