import{d as e,b4 as s,r as a,b5 as t,I as l,o as n,a6 as o,g as r,f as u,m as i,w as d,$ as p,ak as m,i as c,e as f,C as _,P as v,Q as b,n as y,F as j}from"./index.Dk5pbsTU.js";import{E as g,a as k}from"./el-form-item.Bw6Zyv_7.js";import{E as x}from"./el-card.DwLhVNHW.js";import{a as h,E as V}from"./el-col.Cfu8vZQ4.js";/* empty css               */import{E as I}from"./el-button.CXI119n4.js";import{E as w}from"./el-input.DiGatoux.js";import{E as C}from"./el-link.qHYW6llJ.js";import{u as U}from"./useStomp.9yPK3VZE.js";import{E}from"./index.L2DVy5yq.js";import{_ as S}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./error.D_Dr4eZ1.js";import"./index.D6CER_Ot.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./index.DuiNpp1i.js";import"./use-form-item.DzRJVC1I.js";import"./index.C9UdVphc.js";import"./event.BwRzfsZt.js";import"./index.DEKElSOG.js";import"./index.Vn8pbgQR.js";const A={class:"app-container"},O={class:"message-container"},P={key:0,class:"message-content"},q={class:"color-#333"},L={key:1},R=S(e({__name:"websocket",setup(e){const S=s(),R=a(void 0),T=a(!1),W=a([]),D=a("亲爱的朋友们，系统已恢复最新状态。"),F=a("Hi, "+S.userInfo.username+" 这里是点对点消息示例！"),H=a("root"),{isConnected:J,connect:K,subscribe:M,disconnect:N,client:Q}=U({brokerURL:R.value,token:t(),reconnectDelay:5e3,debug:!0});function $(){K()}function z(){N()}function B(){Q.value&&T.value&&(Q.value.publish({destination:"/topic/notice",body:D.value}),W.value.push({sender:S.userInfo.username,content:D.value}))}function G(){Q.value&&T.value&&(Q.value.publish({destination:"/app/sendToUser/"+H.value,body:F.value}),W.value.push({sender:S.userInfo.username,content:F.value}))}return l(J,(e=>{T.value=e,e?(M("/topic/notice",(e=>{W.value.push({sender:"Server",content:e.body})})),M("/user/queue/greeting",(e=>{const s=JSON.parse(e.body);W.value.push({sender:s.sender,content:s.content})})),W.value.push({sender:"Server",content:"Websocket 已连接",type:"tip"})):W.value.push({sender:"Server",content:"Websocket 已断开",type:"tip"})})),n((()=>{$()})),o((()=>{z()})),(e,s)=>{const a=C,t=w,l=I,n=h,o=E,U=V,J=x,K=k,M=g;return u(),r("div",A,[i(a,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/websocket.vue",type:"primary",target:"_blank",class:"mb-[20px]"},{default:d((()=>s[4]||(s[4]=[p(" 示例源码 请点击>>>> ")]))),_:1,__:[4]}),i(U,{gutter:10},{default:d((()=>[i(n,{span:12},{default:d((()=>[i(J,null,{default:d((()=>[i(U,null,{default:d((()=>[i(n,{span:16},{default:d((()=>[i(t,{modelValue:c(R),"onUpdate:modelValue":s[0]||(s[0]=e=>m(R)?R.value=e:null),class:"w-220px"},null,8,["modelValue"]),i(l,{type:"primary",class:"ml-5",disabled:c(T),onClick:$},{default:d((()=>s[5]||(s[5]=[p(" 连接 ")]))),_:1,__:[5]},8,["disabled"]),i(l,{type:"danger",disabled:!c(T),onClick:z},{default:d((()=>s[6]||(s[6]=[p(" 断开 ")]))),_:1,__:[6]},8,["disabled"])])),_:1}),i(n,{span:8,class:"text-right"},{default:d((()=>[s[9]||(s[9]=p(" 连接状态： ")),c(T)?(u(),f(o,{key:0,class:"ml-2",type:"success"},{default:d((()=>s[7]||(s[7]=[p("已连接")]))),_:1,__:[7]})):(u(),f(o,{key:1,class:"ml-2",type:"info"},{default:d((()=>s[8]||(s[8]=[p("已断开")]))),_:1,__:[8]}))])),_:1,__:[9]})])),_:1})])),_:1}),i(J,{class:"mt-5"},{default:d((()=>[i(M,{"label-width":"90px"},{default:d((()=>[i(K,{label:"消息内容"},{default:d((()=>[i(t,{modelValue:c(D),"onUpdate:modelValue":s[1]||(s[1]=e=>m(D)?D.value=e:null),type:"textarea"},null,8,["modelValue"])])),_:1}),i(K,null,{default:d((()=>[i(l,{type:"primary",onClick:B},{default:d((()=>s[10]||(s[10]=[p("发送广播")]))),_:1,__:[10]})])),_:1})])),_:1})])),_:1}),i(J,{class:"mt-5"},{default:d((()=>[i(M,{"label-width":"90px"},{default:d((()=>[i(K,{label:"消息内容"},{default:d((()=>[i(t,{modelValue:c(F),"onUpdate:modelValue":s[2]||(s[2]=e=>m(F)?F.value=e:null),type:"textarea"},null,8,["modelValue"])])),_:1}),i(K,{label:"消息接收人"},{default:d((()=>[i(t,{modelValue:c(H),"onUpdate:modelValue":s[3]||(s[3]=e=>m(H)?H.value=e:null)},null,8,["modelValue"])])),_:1}),i(K,null,{default:d((()=>[i(l,{type:"primary",onClick:G},{default:d((()=>s[11]||(s[11]=[p("发送点对点消息")]))),_:1,__:[11]})])),_:1})])),_:1})])),_:1})])),_:1}),i(n,{span:12},{default:d((()=>[i(J,null,{default:d((()=>[_("div",O,[(u(!0),r(v,null,b(c(W),((e,s)=>(u(),r("div",{key:s,class:y({"tip-message":"tip"===e.type,message:"tip"!==e.type,"message--sent":e.sender===c(S).userInfo.username,"message--received":e.sender!==c(S).userInfo.username})},["tip"!=e.type?(u(),r("div",P,[_("div",{class:y({"message-sender":e.sender===c(S).userInfo.username,"message-receiver":e.sender!==c(S).userInfo.username})},j(e.sender),3),_("div",q,j(e.content),1)])):(u(),r("div",L,j(e.content),1))],2)))),128))])])),_:1})])),_:1})])),_:1})])}}}),[["__scopeId","data-v-fa4bf913"]]);export{R as default};
