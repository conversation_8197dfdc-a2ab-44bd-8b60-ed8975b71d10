import{d as e,r as t,aQ as o,g as r,f as a,C as i,m as s,w as l,Z as n,i as p,$ as m,V as u,e as d,h as c,F as _,az as f}from"./index.Dk5pbsTU.js";import{v as j}from"./el-loading.Dqi-qL7c.js";import{E as g}from"./el-card.DwLhVNHW.js";import w from"./index.Cywy93e7.js";import{a as v,E as y}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{_ as b}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as h}from"./el-link.qHYW6llJ.js";import{E as k}from"./el-image-viewer.BH897zgF.js";/* empty css               */import{a as x,E as C}from"./el-form-item.Bw6Zyv_7.js";import{E as V}from"./el-button.CXI119n4.js";import{_ as U}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as q}from"./el-input.DiGatoux.js";import{_ as E}from"./edit.vue_vue_type_script_setup_true_lang.BJi511mG.js";import{_ as S}from"./editIntroduction.vue_vue_type_script_setup_true_lang.B1g6wN8C.js";import{_ as N}from"./info.vue_vue_type_script_setup_true_lang.BaZIr3y_.js";import{a as R,E as B,W as z}from"./webcaster.CYqw_lYq.js";import{u as I}from"./commonSetup.Dm-aByKQ.js";/* empty css                       */import"./el-overlay.DpVCS8zG.js";import{E as P}from"./index.BcMfjWDS.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./event.BwRzfsZt.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.DuiNpp1i.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./position.DfR5znly.js";import"./el-radio.w2rep3_A.js";import"./el-dialog.Cnp8BitR.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./el-switch.kQ5v4arH.js";import"./validator.HGn2BZtD.js";import"./SingleImageUpload.WGBxPB_4.js";import"./el-progress.BQBUwu9o.js";import"./cloneDeep.DcCMo0F4.js";import"./index.WzKGworL.js";import"./validate.Bicq6Mu8.js";import"./el-drawer.Df_TzNjH.js";import"./index.vue_vue_type_script_setup_true_lang.BOVTspW2.js";import"./el-descriptions-item.BlvmJIy_.js";const T={class:"app-container"},A={class:"search-bar"},$={class:"mb-10px"},K=e({name:"Webmaster",__name:"index",setup(e){const K=t(),M=t(),F=t(),{page:L,getPage:O,resetQuery:Q}=I(new z("0"),R.page);return(e,t)=>{const z=q,I=x,W=U,D=V,G=C,H=y,J=k,Z=h,Y=b,X=v,ee=w,te=g,oe=o("hasPerm"),re=j;return a(),r("div",T,[i("div",A,[s(G,{ref:"queryFormRef",model:p(L).query,inline:!0},{default:l((()=>[s(I,{prop:"keywords",label:""},{default:l((()=>[s(z,{modelValue:p(L).query.keywords,"onUpdate:modelValue":t[0]||(t[0]=e=>p(L).query.keywords=e),placeholder:"姓名、昵称检索",clearable:"",onKeyup:n(p(O),["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),s(I,{prop:"status",label:"账号状态"},{default:l((()=>[s(W,{modelValue:p(L).query.status,"onUpdate:modelValue":t[1]||(t[1]=e=>p(L).query.status=e),code:"disable_status"},null,8,["modelValue"])])),_:1}),s(I,null,{default:l((()=>[s(D,{type:"primary",icon:"search",onClick:p(O)},{default:l((()=>t[6]||(t[6]=[m("搜索")]))),_:1,__:[6]},8,["onClick"]),s(D,{icon:"refresh",onClick:p(Q)},{default:l((()=>t[7]||(t[7]=[m("重置")]))),_:1,__:[7]},8,["onClick"])])),_:1})])),_:1},8,["model"])]),s(te,{shadow:"never"},{default:l((()=>[i("div",$,[u((a(),d(D,{type:"success",icon:"plus",onClick:t[2]||(t[2]=e=>{var t;return null==(t=p(M))?void 0:t.open()})},{default:l((()=>t[8]||(t[8]=[m(" 新增 ")]))),_:1,__:[8]})),[[oe,["wa:webmaster:save"]]])]),u((a(),d(X,{ref:"dataTableRef",data:p(L).data.records,"highlight-current-row":"",border:""},{default:l((()=>[s(H,{label:"序号",align:"center",width:"55",type:"index"}),s(H,{label:"头像",align:"center",prop:"name",width:"100"},{default:l((({row:e})=>[s(J,{style:{width:"30px",height:"30px","border-radius":"30px"},"preview-src-list":[e.avatar],"preview-teleported":"",src:e.avatar},null,8,["preview-src-list","src"])])),_:1}),s(H,{label:"主播",align:"center",prop:"userName","min-width":"120"},{default:l((({row:e})=>[s(Z,{underline:!1,type:"primary",onClick:t=>{var o;return null==(o=p(F))?void 0:o.open(e)}},{default:l((()=>[m(_(e.userName),1)])),_:2},1032,["onClick"])])),_:1}),s(H,{label:"昵称",align:"center",prop:"nickName","min-width":"120"}),s(H,{label:"是否允许自主开播",align:"center",width:"80"},{default:l((({row:e})=>[s(Y,{modelValue:e.isAutonomous,"onUpdate:modelValue":t=>e.isAutonomous=t,code:"yes_or_no"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(H,{label:"手机号",align:"center","min-width":"100",prop:"mobile"}),s(H,{label:"账号状态",align:"center",width:"80"},{default:l((({row:e})=>[s(Y,{modelValue:e.status,"onUpdate:modelValue":t=>e.status=t,code:"disable_status"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(H,{label:"简介",align:"center","min-width":"100",prop:"introduction","show-overflow-tooltip":""}),s(H,{fixed:"right",label:"操作",width:"180"},{default:l((e=>[u((a(),d(D,{type:"primary",size:"small",link:"",icon:"edit",onClick:t=>{var o;return null==(o=p(M))?void 0:o.open(e.row)}},{default:l((()=>t[9]||(t[9]=[m(" 编辑 ")]))),_:2,__:[9]},1032,["onClick"])),[[oe,["wa:webmaster:save"]]]),u((a(),d(D,{type:"info",size:"small",link:"",onClick:t=>{var o;return null==(o=p(K))?void 0:o.open(e.row)}},{default:l((()=>t[10]||(t[10]=[m(" 主播介绍 ")]))),_:2,__:[10]},1032,["onClick"])),[[oe,["wa:webmaster:save"]]]),u((a(),d(D,{type:"danger",size:"small",link:"",icon:"delete",loading:e.row.loading,onClick:t=>{return o=e.row,void P.confirm(`确定删除主播《${o.nickName}》吗？`,"删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{o.loading=!0,R.remove(o.id).then((()=>{f.success("删除成功"),Q()})).finally((()=>o.loading=!1))})).catch((()=>f.info("已取消")));var o}},{default:l((()=>t[11]||(t[11]=[m(" 删除 ")]))),_:2,__:[11]},1032,["loading","onClick"])),[[oe,["wa:webmaster:delete"]]]),u((a(),d(D,{type:"warning",size:"small",link:"",loading:e.row.loading,onClick:t=>{return o=e.row,void P.confirm(`确定要${o.status?"停用":"启用"}主播《${o.nickName}》吗？`,(o.status?"停用":"启用")+"授权",{confirmButtonText:"确定",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{const e=new B(o);e.status?e.status=0:e.status=1,o.loading=!0,R.editStatus(e).then((()=>{f.success("操作成功"),Q()})).finally((()=>o.loading=!1))})).catch((()=>f.info("已取消")));var o}},{default:l((()=>[m(_(e.row.status?"停用":"启用"),1)])),_:2},1032,["loading","onClick"])),[[oe,["wa:webmaster:save"]]])])),_:1})])),_:1},8,["data"])),[[re,p(L).loading]]),p(L).data.totalRow?(a(),d(ee,{key:0,total:p(L).data.totalRow,"onUpdate:total":t[3]||(t[3]=e=>p(L).data.totalRow=e),page:p(L).query.pageNum,"onUpdate:page":t[4]||(t[4]=e=>p(L).query.pageNum=e),limit:p(L).query.pageSize,"onUpdate:limit":t[5]||(t[5]=e=>p(L).query.pageSize=e),onPagination:p(O)},null,8,["total","page","limit","onPagination"])):c("",!0)])),_:1}),s(E,{ref_key:"editModelRef",ref:M,onSuccess:p(Q)},null,8,["onSuccess"]),s(N,{ref_key:"infoModelRef",ref:F},null,512),s(S,{ref_key:"editIntroductionRef",ref:K,onSuccess:p(Q)},null,8,["onSuccess"])])}}});export{K as default};
