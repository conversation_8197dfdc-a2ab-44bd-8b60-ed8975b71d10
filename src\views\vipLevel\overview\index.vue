<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="status" label="最近登录时间">
          <date-range-picker
            v-model:start="page.query.startTime"
            v-model:end="page.query.endTime"
            is-split
            type="daterange"
          />
        </el-form-item>
        <el-form-item prop="status" label="会员等级">
          <el-select v-model="page.query.level" style="width: 250px">
            <el-option
              v-for="item in sendInfo.data"
              :key="item.id"
              :label="item.levelName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="等级名称" align="center" prop="currentLevel" min-width="100" />
        <el-table-column label="头像" align="center" width="100">
          <template #default="{ row }">
            <el-image
              style="width: 30px; height: 30px; border-radius: 30px"
              :preview-src-list="[row.avatar]"
              preview-teleported
              :src="row.avatar"
            />
          </template>
        </el-table-column>
        <el-table-column label="昵称" align="center" prop="nickname" min-width="100">
          <template #default="{ row }">
            <el-link type="primary" @click="pointsDetailsRef?.open(row)">
              {{ row.nickname }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="平台积分" align="center" prop="currentPoints" min-width="100" />
        <el-table-column label="标签" align="center" prop="currentPoints" min-width="100">
          <template #default="{ row }">
            {{ (row.tagList || []).join("、") }}
          </template>
        </el-table-column>
        <el-table-column label="最近登录时间" align="center" prop="updatedAt" min-width="100" />
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
  </div>
  <points-details ref="pointsDetailsRef" />
</template>

<script setup lang="ts">
import PointsDetails from "./pointsDetails.vue";

defineOptions({ name: "VipLevelOverview" });
import { useApi, usePage } from "@/utils/commonSetup";
import GradeApi, { GradeConfigVo, GradeUserPageQuery, GradeUserVo } from "@/api/system/grade";

const { page, getPage, resetQuery } = usePage<GradeUserPageQuery, GradeUserVo>(
  new GradeUserPageQuery(),
  GradeApi.userList
);
const pointsDetailsRef = ref<InstanceType<typeof PointsDetails>>();
const { sendInfo, onSend } = useApi<null, GradeConfigVo[]>(GradeApi.configList);
onSend();
</script>
