import{d as e,S as t,r as a,ap as o,e as l,f as r,w as i,m as s,i as p,Z as n,g as d,P as m,Q as u,$ as c,V as y,C as f,h as g,F as j,E as _,Y as h}from"./index.Dk5pbsTU.js";import{v as w}from"./el-loading.Dqi-qL7c.js";import{E as x}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import"./el-popper.Dbn4MgsT.js";import{E as v}from"./el-popover.Bo2lPKkO.js";import{E as b}from"./el-empty.Dee0wMKK.js";import{b as k,d as q,E,a as I}from"./el-main.CclDHmVj.js";import C from"./index.Cywy93e7.js";import{a as V,E as N}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";/* empty css                     *//* empty css               */import{E as S}from"./el-link.qHYW6llJ.js";import{E as K,a as U}from"./el-form-item.Bw6Zyv_7.js";import{E as P}from"./el-button.CXI119n4.js";import{E as T,a as R}from"./el-select.CRWkm-it.js";import{E as z}from"./el-input.DiGatoux.js";import{a as F,u as O}from"./commonSetup.Dm-aByKQ.js";import{K as W,a as A,b as H}from"./knowledgeType.DrndLWHa.js";import{_ as J}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./use-form-common-props.CQPDkY7k.js";import"./dropdown.B_OfpyL_.js";import"./el-pagination.C5FHY27u.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.ybpLT-bz.js";import"./index.L2DVy5yq.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";const L=["onClick"],M={key:0},Q={style:{width:"100%",display:"flex","align-items":"center","justify-content":"space-between","font-size":"13px"}},Y={style:{color:"var(--el-color-danger)",cursor:"pointer",margin:"0 5px"}},Z={class:"select-item-name"},$=J(e({__name:"SetKnowledgeFile",emits:["success"],setup(e,{expose:J,emit:$}){const G=t({title:"设置知识库文件",show:!1}),B=$,{sendInfo:D,onSend:X}=F(A.allKnowledgeType,new W);D.params.status="1";const{page:ee,getPage:te}=O(new H,A.aiKnowledgePage),ae=a([]);let oe=null;function le(e){oe?clearTimeout(oe):oe=setTimeout((()=>{A.aiKnowledgePage({pageNum:1,pageSize:99999,includeIds:e}).then((e=>{ae.value=e.data.records}))}),200)}function re(){B("success",ae.value.map((e=>e.id))),G.show=!1}function ie(e){"boolean"==typeof e?ae.value=ae.value.concat(ee.data.records):ae.value.push(e),ee.query.excludeIds=ae.value.map((e=>e.id)),ee.query.pageNum=1,le(ee.query.excludeIds),te()}return J({open:(e=[])=>{X(),le(e),ee.query.status="1",ee.query.pageNum=1,ee.query.excludeIds=e,te(),G.show=!0}}),(e,t)=>{const a=z,F=U,O=R,W=T,A=P,H=K,J=q,$=S,B=N,X=V,oe=E,le=C,se=I,pe=k,ne=b,de=_,me=o("scroll-view"),ue=v,ce=x,ye=w;return r(),l(ce,{modelValue:p(G).show,"onUpdate:modelValue":t[7]||(t[7]=e=>p(G).show=e),width:"700px",title:p(G).title},{footer:i((()=>[f("div",Q,[f("div",null,[t[11]||(t[11]=c(" 已选 ")),s(ue,{placement:"top",width:500,trigger:"click"},{reference:i((()=>[f("span",Y,j(p(ae).length),1)])),default:i((()=>[p(ae).length?(r(),l(me,{key:1,"scroll-y":!0,style:{height:"300px"}},{default:i((()=>[(r(!0),d(m,null,u(p(ae),((e,t)=>(r(),d("div",{key:t,class:"select-item"},[f("div",Z,j(e.fileName),1),s(de,{style:{color:"var(--el-color-info)",cursor:"pointer"},onClick:e=>{return a=t,ae.value.splice(a,1),ee.query.excludeIds=ae.value.map((e=>e.id)),ee.query.pageNum=1,void te();var a}},{default:i((()=>[s(p(h))])),_:2},1032,["onClick"])])))),128))])),_:1})):(r(),l(ne,{key:0,title:"暂无已选数据"}))])),_:1}),t[12]||(t[12]=c(" 个文件 "))]),f("div",null,[s(A,{onClick:t[6]||(t[6]=e=>p(G).show=!1)},{default:i((()=>t[13]||(t[13]=[c("取消")]))),_:1,__:[13]}),s(A,{type:"primary",onClick:re},{default:i((()=>t[14]||(t[14]=[c("确定")]))),_:1,__:[14]})])])])),default:i((()=>[s(pe,{style:{"border-bottom":"1px solid var(--el-border-color)"}},{default:i((()=>[s(J,{style:{padding:"10px 0"}},{default:i((()=>[s(H,{ref:"queryFormRef",model:p(ee).query,inline:!0},{default:i((()=>[s(F,{prop:"keywords",label:""},{default:i((()=>[s(a,{modelValue:p(ee).query.keyWord,"onUpdate:modelValue":t[0]||(t[0]=e=>p(ee).query.keyWord=e),placeholder:"类型名称检索",clearable:"",onKeyup:n(p(te),["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),s(F,{prop:"fileType",label:"文件类型"},{default:i((()=>[s(W,{modelValue:p(ee).query.typeId,"onUpdate:modelValue":t[1]||(t[1]=e=>p(ee).query.typeId=e),clearable:"",style:{width:"200px"}},{default:i((()=>[(r(!0),d(m,null,u(p(D).data,(e=>(r(),l(O,{key:e.id,label:e.name,value:String(e.id)},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),s(F,null,{default:i((()=>[s(A,{type:"primary",icon:"search",onClick:p(te)},{default:i((()=>t[8]||(t[8]=[c("搜索")]))),_:1,__:[8]},8,["onClick"])])),_:1})])),_:1},8,["model"])])),_:1}),s(oe,{style:{padding:"0",height:"500px"}},{default:i((()=>[y((r(),l(X,{ref:"dataTableRef",data:p(ee).data.records,height:"100%","empty-text":"暂无可选数据","highlight-current-row":"",border:""},{default:i((()=>[s(B,{width:"55",align:"center"},{header:i((()=>[s($,{type:"primary",onClick:t[2]||(t[2]=e=>ie(!0))},{default:i((()=>t[9]||(t[9]=[c("全选")]))),_:1,__:[9]})])),default:i((({row:e})=>[f("div",{class:"select-box",onClick:t=>ie(e)},null,8,L)])),_:1}),s(B,{label:"序号",align:"center",width:"55",type:"index"}),s(B,{label:"文件名称",align:"center",prop:"fileName","min-width":"120"}),s(B,{label:"文件类型",align:"center",prop:"typeName","min-width":"120"},{default:i((({row:e})=>[c(j(e.typeName)+" ",1),e.typeStatus?g("",!0):(r(),d("span",M,t[10]||(t[10]=[c(" （ "),f("span",{style:{color:"var(--el-color-danger)"}},"已停用",-1),c(" ） ")])))])),_:1})])),_:1},8,["data"])),[[ye,p(ee).loading]])])),_:1}),s(se,null,{default:i((()=>[p(ee).data.totalRow?(r(),l(le,{key:0,total:p(ee).data.totalRow,"onUpdate:total":t[3]||(t[3]=e=>p(ee).data.totalRow=e),page:p(ee).query.pageNum,"onUpdate:page":t[4]||(t[4]=e=>p(ee).query.pageNum=e),limit:p(ee).query.pageSize,"onUpdate:limit":t[5]||(t[5]=e=>p(ee).query.pageSize=e),onPagination:p(te)},null,8,["total","page","limit","onPagination"])):g("",!0)])),_:1})])),_:1})])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-26c2cfb2"]]);export{$ as default};
