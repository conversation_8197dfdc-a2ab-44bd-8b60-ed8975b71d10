<template>
  <el-container>
    <el-main class="p-0 m-0">
      <el-row :gutter="20">
        <el-col :span="12" :xs="24">
          <el-card shadow="never" title="直播间人气核算">
            <el-table
              v-loading="sendInfo.loading"
              :data="sendInfo.data?.popularityRules || []"
              highlight-current-row
              border
            >
              <el-table-column label="操作" align="center" width="100">
                <template #default="{ row }">
                  <dict-label v-model="row.ruleKey" code="live_rule_key" />
                </template>
              </el-table-column>
              <el-table-column label="核算类型" align="center" width="130">
                <template #default="{ row }">
                  <dict
                    v-model="row.type"
                    :clearable="false"
                    style="width: 100px"
                    code="live_room_accounting_type"
                  />
                </template>
              </el-table-column>
              <el-table-column label="人气值" align="center" prop="levelName" min-width="120">
                <template #default="{ row }">
                  <el-input v-model="row.points" oninput="value=value.replace(/[^\d]/g,'')" />
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-card shadow="never" title="直播间人用户积分设置">
            <el-table
              v-loading="sendInfo.loading"
              :data="sendInfo.data?.pointsRules || []"
              highlight-current-row
              border
            >
              <el-table-column label="操作" align="center" width="100">
                <template #default="{ row }">
                  <dict-label v-model="row.ruleKey" code="live_rule_key" />
                </template>
              </el-table-column>
              <el-table-column label="核算类型" align="center" width="130">
                <template #default="{ row }">
                  <dict
                    v-model="row.type"
                    style="width: 100px"
                    :clearable="false"
                    code="live_room_accounting_type"
                  />
                </template>
              </el-table-column>
              <el-table-column label="积分" align="center" prop="levelName" min-width="120">
                <template #default="{ row }">
                  <el-input v-model="row.points" oninput="value=value.replace(/[^\d]/g,'')" />
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </el-main>
    <el-footer class="text-align-center justify-center">
      <el-button type="primary" @click="onSubmit">保&nbsp;存</el-button>
    </el-footer>
  </el-container>
</template>

<script setup lang="ts">
import LiveConfigApi, { LiveListRulesVo, LiveRulesDto } from "@/api/live/liveConfig";
import { useApi } from "@/utils/commonSetup";

defineOptions({ name: "LiveBaseConfig" });

const { sendInfo, onSend } = useApi<null, LiveListRulesVo>(LiveConfigApi.get);
onSend();
const { sendInfo: saveInfo, onSend: onSave } = useApi<LiveRulesDto[], any>(
  LiveConfigApi.save as any
);

// 提交
function onSubmit() {
  const popularityRules = sendInfo.data?.popularityRules || [];
  const pointsRules = sendInfo.data?.pointsRules || [];
  if (popularityRules.find((f) => (!f.points && f.points != 0) || Number(f.points) < 0)) {
    ElMessage.error("人气值不能为空且最小为0");
    return;
  }
  if (pointsRules.find((f) => (!f.points && f.points != 0) || Number(f.points) < 0)) {
    ElMessage.error("积分值不能为空且最小为0");
    return;
  }
  saveInfo.params = [
    ...popularityRules.map((m) => new LiveRulesDto(m)),
    ...pointsRules.map((m) => new LiveRulesDto(m)),
  ];

  sendInfo.loading = true;
  onSave()
    .then(() => {
      onSend();
    })
    .finally(() => (sendInfo.loading = false));
}
</script>
