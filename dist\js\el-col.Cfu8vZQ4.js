import{t,ad as e,z as s,_ as a,d as u,A as l,c as r,b as p,b6 as n,aa as o,e as f,f as d,w as c,l as i,k as b,n as g,i as y,D as m,q as $,y as h}from"./index.Dk5pbsTU.js";const j=t({tag:{type:String,default:"div"},span:{type:Number,default:24},offset:{type:Number,default:0},pull:{type:Number,default:0},push:{type:Number,default:0},xs:{type:s([Number,Object]),default:()=>e({})},sm:{type:s([Number,Object]),default:()=>e({})},md:{type:s([Number,Object]),default:()=>e({})},lg:{type:s([Number,Object]),default:()=>e({})},xl:{type:s([Number,Object]),default:()=>e({})}}),v=Symbol("rowContextKey"),N=u({name:"ElCol"});const x=$(a(u({...N,props:j,setup(t){const e=t,{gutter:s}=l(v,{gutter:r((()=>0))}),a=p("col"),u=r((()=>{const t={};return s.value&&(t.paddingLeft=t.paddingRight=s.value/2+"px"),t})),$=r((()=>{const t=[];["span","offset","pull","push"].forEach((s=>{const u=e[s];n(u)&&("span"===s?t.push(a.b(`${e[s]}`)):u>0&&t.push(a.b(`${s}-${e[s]}`)))}));return["xs","sm","md","lg","xl"].forEach((s=>{n(e[s])?t.push(a.b(`${s}-${e[s]}`)):o(e[s])&&Object.entries(e[s]).forEach((([e,u])=>{t.push("span"!==e?a.b(`${s}-${e}-${u}`):a.b(`${s}-${u}`))}))})),s.value&&t.push(a.is("guttered")),[a.b(),t]}));return(t,e)=>(d(),f(m(t.tag),{class:g(y($)),style:b(y(u))},{default:c((()=>[i(t.$slots,"default")])),_:3},8,["class","style"]))}}),[["__file","col.vue"]])),_=t({tag:{type:String,default:"div"},gutter:{type:Number,default:0},justify:{type:String,values:["start","center","end","space-around","space-between","space-evenly"],default:"start"},align:{type:String,values:["top","middle","bottom"]}}),w=u({name:"ElRow"});const E=$(a(u({...w,props:_,setup(t){const e=t,s=p("row"),a=r((()=>e.gutter));h(v,{gutter:a});const u=r((()=>{const t={};return e.gutter?(t.marginRight=t.marginLeft=`-${e.gutter/2}px`,t):t})),l=r((()=>[s.b(),s.is(`justify-${e.justify}`,"start"!==e.justify),s.is(`align-${e.align}`,!!e.align)]));return(t,e)=>(d(),f(m(t.tag),{class:g(y(l)),style:b(y(u))},{default:c((()=>[i(t.$slots,"default")])),_:3},8,["class","style"]))}}),[["__file","row.vue"]]));export{E,x as a};
