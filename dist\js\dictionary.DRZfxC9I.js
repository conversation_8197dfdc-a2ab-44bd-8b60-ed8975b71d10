import{d as e,r as l,g as r,f as s,m as t,w as o,$ as a,ak as i,i as m}from"./index.Dk5pbsTU.js";import{a as n,E as u}from"./el-form-item.Bw6Zyv_7.js";import{_ as d}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as p}from"./el-link.qHYW6llJ.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./error.D_Dr4eZ1.js";import"./index.D6CER_Ot.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./el-checkbox.DDYarIkn.js";import"./index.C9UdVphc.js";import"./event.BwRzfsZt.js";import"./use-form-item.DzRJVC1I.js";import"./isEqual.C0S6DIiJ.js";import"./index.DuiNpp1i.js";import"./el-radio.w2rep3_A.js";/* empty css               */import"./el-select.CRWkm-it.js";import"./el-popper.Dbn4MgsT.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.ybpLT-bz.js";import"./index.L2DVy5yq.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./index.Vn8pbgQR.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";/* empty css                     */const c={class:"app-container"},j=e({__name:"dictionary",setup(e){const j=l("1"),_=l(1),f=l(["1","2"]);return(e,l)=>{const y=p,b=d,v=n,x=u;return s(),r("div",c,[t(y,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/dictionary.vue",type:"primary",target:"_blank",class:"mb-[20px]"},{default:o((()=>l[4]||(l[4]=[a(" 示例源码 请点击>>>> ")]))),_:1,__:[4]}),t(x,null,{default:o((()=>[t(v,{label:"性别"},{default:o((()=>[t(b,{modelValue:m(j),"onUpdate:modelValue":l[0]||(l[0]=e=>i(j)?j.value=e:null),code:"gender"},null,8,["modelValue"]),t(y,{underline:!1,type:"primary",class:"ml-5"},{default:o((()=>l[5]||(l[5]=[a(' 值为String: const value = ref("1"); ')]))),_:1,__:[5]})])),_:1}),t(v,{label:"性别"},{default:o((()=>[t(b,{modelValue:m(_),"onUpdate:modelValue":l[1]||(l[1]=e=>i(_)?_.value=e:null),code:"gender"},null,8,["modelValue"]),t(y,{underline:!1,type:"success",class:"ml-5"},{default:o((()=>l[6]||(l[6]=[a(" 值为Number: const value = ref(1); ")]))),_:1,__:[6]})])),_:1}),t(v,{label:"单选框字典"},{default:o((()=>[t(b,{modelValue:m(_),"onUpdate:modelValue":l[2]||(l[2]=e=>i(_)?_.value=e:null),type:"radio",code:"gender"},null,8,["modelValue"]),t(y,{underline:!1,type:"success",class:"ml-5"},{default:o((()=>l[7]||(l[7]=[a(" 值为Number: const value = ref(1); ")]))),_:1,__:[7]})])),_:1}),t(v,{label:"复选框字典"},{default:o((()=>[t(b,{modelValue:m(f),"onUpdate:modelValue":l[3]||(l[3]=e=>i(f)?f.value=e:null),type:"checkbox",code:"gender"},null,8,["modelValue"]),t(y,{underline:!1,type:"success",class:"ml-5"},{default:o((()=>l[8]||(l[8]=[a(' 值为Array: const value = ref(["1", "2"]); ')]))),_:1,__:[8]})])),_:1})])),_:1})])}}});export{j as default};
