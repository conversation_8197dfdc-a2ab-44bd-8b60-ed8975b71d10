import{s as e,r as l,c as t,ab as a,t as o,A as s,aa as n,ca as i,cu as r,I as u,B as p,_ as d,d as c,V as v,X as f,g as m,f as b,l as h,C as g,F as y,j as S,n as x,b as C,i as w,bX as O,S as V,a6 as I,a0 as T,h as E,k,o as R,x as B,cv as D,a9 as L,bx as M,a5 as $,cw as z,R as j,br as F,K as W,cx as _,H as K,b6 as N,L as P,cy as H,z as A,W as Q,v as U,bA as q,N as G,ap as X,aQ as Z,m as J,w as Y,e as ee,P as le,Q as te,$ as ae,Z as oe,b3 as se,D as ne,cz as ie,E as re,y as ue,cA as pe,bE as de,q as ce,G as ve}from"./index.Dk5pbsTU.js";import{u as fe,b as me,E as be}from"./el-popper.Dbn4MgsT.js";import{s as he,E as ge}from"./index.ybpLT-bz.js";import{t as ye,E as Se}from"./index.L2DVy5yq.js";import{s as xe,a as Ce}from"./token.DWNpOE8r.js";import{e as we}from"./strings.MqEQKtyI.js";import{t as Oe,d as Ve}from"./error.D_Dr4eZ1.js";import{c as Ie}from"./castArray.C4RhTg2c.js";import{i as Te}from"./isEqual.C0S6DIiJ.js";import{u as Ee}from"./index.D6CER_Ot.js";import{a as ke,u as Re}from"./index.Vn8pbgQR.js";import{u as Be,a as De}from"./use-form-item.DzRJVC1I.js";import{u as Le}from"./use-form-common-props.CQPDkY7k.js";import{U as Me,C as $e}from"./event.BwRzfsZt.js";import{s as ze}from"./scroll.CVc-P3_z.js";import{d as je}from"./debounce.DJJTSR8O.js";import{b as Fe}from"./_baseIteratee.CPRpgrLu.js";import{u as We}from"./index.C9UdVphc.js";import{C as _e}from"./index.wZTqlYZ6.js";import{f as Ke}from"./vnode.Cbclzz8S.js";const Ne="ElOption",Pe=o({value:{type:[String,Number,Boolean,Object],required:!0},label:{type:[String,Number]},created:Boolean,disabled:Boolean});var He=d(c({name:Ne,componentName:Ne,props:Pe,setup(e){const l=C("select"),a=Ee(),o=t((()=>[l.be("dropdown","item"),l.is("disabled",w(f)),l.is("selected",w(v)),l.is("hovering",w(y))])),d=V({index:-1,groupDisabled:!1,visible:!0,hover:!1}),{currentLabel:c,itemSelected:v,isDisabled:f,select:m,hoverItem:b,updateOption:h}=function(e,l){const a=s(xe);a||Oe(Ne,"usage: <el-select><el-option /></el-select/>");const o=s(Ce,{disabled:!1}),d=t((()=>h(Ie(a.props.modelValue),e.value))),c=t((()=>{var e;if(a.props.multiple){const l=Ie(null!=(e=a.props.modelValue)?e:[]);return!d.value&&l.length>=a.props.multipleLimit&&a.props.multipleLimit>0}return!1})),v=t((()=>e.label||(n(e.value)?"":e.value))),f=t((()=>e.value||e.label||"")),m=t((()=>e.disabled||l.groupDisabled||c.value)),b=p(),h=(l=[],t)=>{if(n(e.value)){const e=a.props.valueKey;return l&&l.some((l=>i(r(l,e))===r(t,e)))}return l&&l.includes(t)};return u((()=>v.value),(()=>{e.created||a.props.remote||a.setSelected()})),u((()=>e.value),((l,t)=>{const{remote:o,valueKey:s}=a.props;if((o?l!==t:!Te(l,t))&&(a.onOptionDestroy(t,b.proxy),a.onOptionCreate(b.proxy)),!e.created&&!o){if(s&&n(l)&&n(t)&&l[s]===t[s])return;a.setSelected()}})),u((()=>o.disabled),(()=>{l.groupDisabled=o.disabled}),{immediate:!0}),{select:a,currentLabel:v,currentValue:f,itemSelected:d,isDisabled:m,hoverItem:()=>{e.disabled||o.disabled||(a.states.hoveringIndex=a.optionsArray.indexOf(b.proxy))},updateOption:t=>{const a=new RegExp(we(t),"i");l.visible=a.test(String(v.value))||e.created}}}(e,d),{visible:g,hover:y}=O(d),S=p().proxy;return m.onOptionCreate(S),I((()=>{const e=S.value,{selected:l}=m.states,t=l.some((e=>e.value===S.value));T((()=>{m.states.cachedOptions.get(e)!==S||t||m.states.cachedOptions.delete(e)})),m.onOptionDestroy(e,S)})),{ns:l,id:a,containerKls:o,currentLabel:c,itemSelected:v,isDisabled:f,select:m,visible:g,hover:y,states:d,hoverItem:b,updateOption:h,selectOptionClick:function(){f.value||m.handleOptionSelect(S)}}}}),[["render",function(e,l){return v((b(),m("li",{id:e.id,class:x(e.containerKls),role:"option","aria-disabled":e.isDisabled||void 0,"aria-selected":e.itemSelected,onMousemove:e.hoverItem,onClick:S(e.selectOptionClick,["stop"])},[h(e.$slots,"default",{},(()=>[g("span",null,y(e.currentLabel),1)]))],42,["id","aria-disabled","aria-selected","onMousemove","onClick"])),[[f,e.visible]])}],["__file","option.vue"]]);var Ae=d(c({name:"ElSelectDropdown",componentName:"ElSelectDropdown",setup(){const e=s(xe),o=C("select"),n=t((()=>e.props.popperClass)),i=t((()=>e.props.multiple)),r=t((()=>e.props.fitInputWidth)),u=l("");function p(){var l;u.value=`${null==(l=e.selectRef)?void 0:l.offsetWidth}px`}return R((()=>{p(),a(e.selectRef,p)})),{ns:o,minWidth:u,popperClass:n,isMultiple:i,isFitInputWidth:r}}}),[["render",function(e,l,t,a,o,s){return b(),m("div",{class:x([e.ns.b("dropdown"),e.ns.is("multiple",e.isMultiple),e.popperClass]),style:k({[e.isFitInputWidth?"width":"minWidth"]:e.minWidth})},[e.$slots.header?(b(),m("div",{key:0,class:x(e.ns.be("dropdown","header"))},[h(e.$slots,"header")],2)):E("v-if",!0),h(e.$slots,"default"),e.$slots.footer?(b(),m("div",{key:1,class:x(e.ns.be("dropdown","footer"))},[h(e.$slots,"footer")],2)):E("v-if",!0)],6)}],["__file","select-dropdown.vue"]]);const Qe=(e,o)=>{const{t:s}=B(),i=Ee(),p=C("select"),d=C("input"),c=V({inputValue:"",options:new Map,cachedOptions:new Map,optionValues:[],selected:[],selectionWidth:0,collapseItemWidth:0,selectedLabel:"",hoveringIndex:-1,previousQuery:null,inputHovering:!1,menuVisibleOnFocus:!1,isBeforeHide:!1}),v=l(),f=l(),m=l(),b=l(),h=l(),g=l(),y=l(),S=l(),x=l(),w=l(),O=l(),{isComposing:I,handleCompositionStart:E,handleCompositionUpdate:k,handleCompositionEnd:P}=ke({afterComposition:e=>He(e)}),{wrapperRef:H,isFocused:A,handleBlur:Q}=Re(h,{beforeFocus:()=>ee.value,afterFocus(){e.automaticDropdown&&!U.value&&(U.value=!0,c.menuVisibleOnFocus=!0)},beforeBlur(e){var l,t;return(null==(l=m.value)?void 0:l.isFocusInsideContent(e))||(null==(t=b.value)?void 0:t.isFocusInsideContent(e))},afterBlur(){var l;U.value=!1,c.menuVisibleOnFocus=!1,e.validateEvent&&(null==(l=null==X?void 0:X.validate)||l.call(X,"blur").catch((e=>Ve())))}}),U=l(!1),q=l(),{form:G,formItem:X}=Be(),{inputId:Z}=De(e,{formItemContext:X}),{valueOnClear:J,isEmptyValue:Y}=D(e),ee=t((()=>e.disabled||(null==G?void 0:G.disabled))),le=t((()=>L(e.modelValue)?e.modelValue.length>0:!Y(e.modelValue))),te=t((()=>{var e;return null!=(e=null==G?void 0:G.statusIcon)&&e})),ae=t((()=>e.clearable&&!ee.value&&c.inputHovering&&le.value)),oe=t((()=>e.remote&&e.filterable&&!e.remoteShowSuffix?"":e.suffixIcon)),se=t((()=>p.is("reverse",!(!oe.value||!U.value)))),ne=t((()=>(null==X?void 0:X.validateState)||"")),ie=t((()=>ne.value&&M[ne.value])),re=t((()=>e.remote?300:0)),ue=t((()=>e.remote&&!c.inputValue&&0===c.options.size)),pe=t((()=>e.loading?e.loadingText||s("el.select.loading"):e.filterable&&c.inputValue&&c.options.size>0&&0===de.value?e.noMatchText||s("el.select.noMatch"):0===c.options.size?e.noDataText||s("el.select.noData"):null)),de=t((()=>ce.value.filter((e=>e.visible)).length)),ce=t((()=>{const e=Array.from(c.options.values()),l=[];return c.optionValues.forEach((t=>{const a=e.findIndex((e=>e.value===t));a>-1&&l.push(e[a])})),l.length>=e.length?l:e})),ve=t((()=>Array.from(c.cachedOptions.values()))),fe=t((()=>{const l=ce.value.filter((e=>!e.created)).some((e=>e.currentLabel===c.inputValue));return e.filterable&&e.allowCreate&&""!==c.inputValue&&!l})),me=()=>{e.filterable&&F(e.filterMethod)||e.filterable&&e.remote&&F(e.remoteMethod)||ce.value.forEach((e=>{var l;null==(l=e.updateOption)||l.call(e,c.inputValue)}))},be=Le(),he=t((()=>["small"].includes(be.value)?"small":"default")),ge=t({get:()=>U.value&&!ue.value,set(e){U.value=e}}),ye=t((()=>{if(e.multiple&&!$(e.modelValue))return 0===Ie(e.modelValue).length&&!c.inputValue;const l=L(e.modelValue)?e.modelValue[0]:e.modelValue;return!e.filterable&&!$(l)||!c.inputValue})),Se=t((()=>{var l;const t=null!=(l=e.placeholder)?l:s("el.select.placeholder");return e.multiple||!le.value?t:c.selectedLabel})),xe=t((()=>z?null:"mouseenter"));u((()=>e.modelValue),((l,t)=>{e.multiple&&e.filterable&&!e.reserveKeyword&&(c.inputValue="",Ce("")),Oe(),!Te(l,t)&&e.validateEvent&&(null==X||X.validate("change").catch((e=>Ve())))}),{flush:"post",deep:!0}),u((()=>U.value),(e=>{e?Ce(c.inputValue):(c.inputValue="",c.previousQuery=null,c.isBeforeHide=!0),o("visible-change",e)})),u((()=>c.options.entries()),(()=>{K&&(Oe(),e.defaultFirstOption&&(e.filterable||e.remote)&&de.value&&we())}),{flush:"post"}),u([()=>c.hoveringIndex,ce],(([e])=>{N(e)&&e>-1?q.value=ce.value[e]||{}:q.value={},ce.value.forEach((e=>{e.hover=q.value===e}))})),j((()=>{c.isBeforeHide||me()}));const Ce=l=>{c.previousQuery===l||I.value||(c.previousQuery=l,e.filterable&&F(e.filterMethod)?e.filterMethod(l):e.filterable&&e.remote&&F(e.remoteMethod)&&e.remoteMethod(l),e.defaultFirstOption&&(e.filterable||e.remote)&&de.value?T(we):T(_e))},we=()=>{const e=ce.value.filter((e=>e.visible&&!e.disabled&&!e.states.groupDisabled)),l=e.find((e=>e.created)),t=e[0],a=ce.value.map((e=>e.value));c.hoveringIndex=Xe(a,l||t)},Oe=()=>{if(!e.multiple){const l=L(e.modelValue)?e.modelValue[0]:e.modelValue,t=We(l);return c.selectedLabel=t.currentLabel,void(c.selected=[t])}c.selectedLabel="";const l=[];$(e.modelValue)||Ie(e.modelValue).forEach((e=>{l.push(We(e))})),c.selected=l},We=l=>{let t;const a=_(l);for(let o=c.cachedOptions.size-1;o>=0;o--){const s=ve.value[o];if(a?r(s.value,e.valueKey)===r(l,e.valueKey):s.value===l){t={value:l,currentLabel:s.currentLabel,get isDisabled(){return s.isDisabled}};break}}if(t)return t;return{value:l,currentLabel:a?l.label:null!=l?l:""}},_e=()=>{c.hoveringIndex=ce.value.findIndex((e=>c.selected.some((l=>ll(l)===ll(e)))))},Ke=()=>{var e,l;null==(l=null==(e=m.value)?void 0:e.updatePopper)||l.call(e)},Ne=()=>{var e,l;null==(l=null==(e=b.value)?void 0:e.updatePopper)||l.call(e)},Pe=()=>{c.inputValue.length>0&&!U.value&&(U.value=!0),Ce(c.inputValue)},He=l=>{if(c.inputValue=l.target.value,!e.remote)return Pe();Ae()},Ae=je((()=>{Pe()}),re.value),Qe=l=>{Te(e.modelValue,l)||o($e,l)},Ue=e=>function(e,l){var t=null==e?0:e.length;if(!t)return-1;var a=t-1;return function(e,l,t){e.length;for(var a=t+1;a--;)if(l(e[a],a,e))return a;return-1}(e,Fe(l),a)}(e,(e=>{const l=c.cachedOptions.get(e);return l&&!l.disabled&&!l.states.groupDisabled})),qe=l=>{l.stopPropagation();const t=e.multiple?[]:J.value;if(e.multiple)for(const e of c.selected)e.isDisabled&&t.push(e.value);o(Me,t),Qe(t),c.hoveringIndex=-1,U.value=!1,o("clear"),Ye()},Ge=l=>{var t;if(e.multiple){const a=Ie(null!=(t=e.modelValue)?t:[]).slice(),s=Xe(a,l);s>-1?a.splice(s,1):(e.multipleLimit<=0||a.length<e.multipleLimit)&&a.push(l.value),o(Me,a),Qe(a),l.created&&Ce(""),e.filterable&&!e.reserveKeyword&&(c.inputValue="")}else o(Me,l.value),Qe(l.value),U.value=!1;Ye(),U.value||T((()=>{Ze(l)}))},Xe=(l,t)=>$(t)?-1:n(t.value)?l.findIndex((l=>Te(r(l,e.valueKey),ll(t)))):l.indexOf(t.value),Ze=e=>{var l,t,a,o,s;const n=L(e)?e[0]:e;let i=null;if(null==n?void 0:n.value){const e=ce.value.filter((e=>e.value===n.value));e.length>0&&(i=e[0].$el)}if(m.value&&i){const e=null==(o=null==(a=null==(t=null==(l=m.value)?void 0:l.popperRef)?void 0:t.contentRef)?void 0:a.querySelector)?void 0:o.call(a,`.${p.be("dropdown","wrap")}`);e&&ze(e,i)}null==(s=O.value)||s.handleScroll()},Je=t((()=>{var e,l;return null==(l=null==(e=m.value)?void 0:e.popperRef)?void 0:l.contentRef})),Ye=()=>{var e;null==(e=h.value)||e.focus()},el=()=>{ee.value||(z&&(c.inputHovering=!0),c.menuVisibleOnFocus?c.menuVisibleOnFocus=!1:U.value=!U.value)},ll=l=>n(l.value)?r(l.value,e.valueKey):l.value,tl=t((()=>ce.value.filter((e=>e.visible)).every((e=>e.isDisabled)))),al=t((()=>e.multiple?e.collapseTags?c.selected.slice(0,e.maxCollapseTags):c.selected:[])),ol=t((()=>e.multiple&&e.collapseTags?c.selected.slice(e.maxCollapseTags):[])),sl=e=>{if(U.value){if(0!==c.options.size&&0!==de.value&&!I.value&&!tl.value){"next"===e?(c.hoveringIndex++,c.hoveringIndex===c.options.size&&(c.hoveringIndex=0)):"prev"===e&&(c.hoveringIndex--,c.hoveringIndex<0&&(c.hoveringIndex=c.options.size-1));const l=ce.value[c.hoveringIndex];!l.isDisabled&&l.visible||sl(e),T((()=>Ze(q.value)))}}else U.value=!0},nl=t((()=>{const l=(()=>{if(!f.value)return 0;const e=window.getComputedStyle(f.value);return Number.parseFloat(e.gap||"6px")})();return{maxWidth:`${w.value&&1===e.maxCollapseTags?c.selectionWidth-c.collapseItemWidth-l:c.selectionWidth}px`}})),il=t((()=>({maxWidth:`${c.selectionWidth}px`})));return a(f,(()=>{c.selectionWidth=Number.parseFloat(window.getComputedStyle(f.value).width)})),a(S,Ke),a(H,Ke),a(x,Ne),a(w,(()=>{c.collapseItemWidth=w.value.getBoundingClientRect().width})),R((()=>{Oe()})),{inputId:Z,contentId:i,nsSelect:p,nsInput:d,states:c,isFocused:A,expanded:U,optionsArray:ce,hoverOption:q,selectSize:be,filteredOptionsCount:de,updateTooltip:Ke,updateTagTooltip:Ne,debouncedOnInputChange:Ae,onInput:He,deletePrevTag:l=>{if(e.multiple&&l.code!==W.delete&&l.target.value.length<=0){const l=Ie(e.modelValue).slice(),t=Ue(l);if(t<0)return;const a=l[t];l.splice(t,1),o(Me,l),Qe(l),o("remove-tag",a)}},deleteTag:(l,t)=>{const a=c.selected.indexOf(t);if(a>-1&&!ee.value){const l=Ie(e.modelValue).slice();l.splice(a,1),o(Me,l),Qe(l),o("remove-tag",t.value)}l.stopPropagation(),Ye()},deleteSelected:qe,handleOptionSelect:Ge,scrollToOption:Ze,hasModelValue:le,shouldShowPlaceholder:ye,currentPlaceholder:Se,mouseEnterEventName:xe,needStatusIcon:te,showClose:ae,iconComponent:oe,iconReverse:se,validateState:ne,validateIcon:ie,showNewOption:fe,updateOptions:me,collapseTagSize:he,setSelected:Oe,selectDisabled:ee,emptyText:pe,handleCompositionStart:E,handleCompositionUpdate:k,handleCompositionEnd:P,onOptionCreate:e=>{c.options.set(e.value,e),c.cachedOptions.set(e.value,e)},onOptionDestroy:(e,l)=>{c.options.get(e)===l&&c.options.delete(e)},handleMenuEnter:()=>{c.isBeforeHide=!1,T((()=>{var e;null==(e=O.value)||e.update(),Ze(c.selected)}))},focus:Ye,blur:()=>{var e;if(U.value)return U.value=!1,void T((()=>{var e;return null==(e=h.value)?void 0:e.blur()}));null==(e=h.value)||e.blur()},handleClearClick:e=>{qe(e)},handleClickOutside:e=>{if(U.value=!1,A.value){const l=new FocusEvent("focus",e);T((()=>Q(l)))}},handleEsc:()=>{c.inputValue.length>0?c.inputValue="":U.value=!1},toggleMenu:el,selectOption:()=>{if(U.value){const e=ce.value[c.hoveringIndex];e&&!e.isDisabled&&Ge(e)}else el()},getValueKey:ll,navigateOptions:sl,dropdownMenuVisible:ge,showTagList:al,collapseTagList:ol,popupScroll:e=>{o("popup-scroll",e)},tagStyle:nl,collapseTagStyle:il,popperRef:Je,inputRef:h,tooltipRef:m,tagTooltipRef:b,prefixRef:g,suffixRef:y,selectRef:v,wrapperRef:H,selectionRef:f,scrollbarRef:O,menuRef:S,tagMenuRef:x,collapseItemRef:w}};var Ue=c({name:"ElOptions",setup(e,{slots:l}){const t=s(xe);let a=[];return()=>{var e,o;const s=null==(e=l.default)?void 0:e.call(l),n=[];return s.length&&function e(l){L(l)&&l.forEach((l=>{var t,a,o,s;const i=null==(t=(null==l?void 0:l.type)||{})?void 0:t.name;"ElOptionGroup"===i?e(P(l.children)||L(l.children)||!F(null==(a=l.children)?void 0:a.default)?l.children:null==(o=l.children)?void 0:o.default()):"ElOption"===i?n.push(null==(s=l.props)?void 0:s.value):L(l.children)&&e(l.children)}))}(null==(o=s[0])?void 0:o.children),Te(n,a)||(a=n,t&&(t.states.optionValues=n)),s}}});const qe=o({name:String,id:String,modelValue:{type:A([Array,String,Number,Boolean,Object]),default:void 0},autocomplete:{type:String,default:"off"},automaticDropdown:Boolean,size:G,effect:{type:A(String),default:"light"},disabled:Boolean,clearable:Boolean,filterable:Boolean,allowCreate:Boolean,loading:Boolean,popperClass:{type:String,default:""},popperOptions:{type:A(Object),default:()=>({})},remote:Boolean,loadingText:String,noMatchText:String,noDataText:String,remoteMethod:Function,filterMethod:Function,multiple:Boolean,multipleLimit:{type:Number,default:0},placeholder:{type:String},defaultFirstOption:Boolean,reserveKeyword:{type:Boolean,default:!0},valueKey:{type:String,default:"value"},collapseTags:Boolean,collapseTagsTooltip:Boolean,maxCollapseTags:{type:Number,default:1},teleported:fe.teleported,persistent:{type:Boolean,default:!0},clearIcon:{type:U,default:q},fitInputWidth:Boolean,suffixIcon:{type:U,default:Q},tagType:{...ye.type,default:"info"},tagEffect:{...ye.effect,default:"light"},validateEvent:{type:Boolean,default:!0},remoteShowSuffix:Boolean,showArrow:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:A(String),values:me,default:"bottom-start"},fallbackPlacements:{type:A(Array),default:["bottom-start","top-start","right","left"]},tabindex:{type:[String,Number],default:0},appendTo:fe.appendTo,...H,...We(["ariaLabel"])});he.scroll;const Ge="ElSelect";var Xe=d(c({name:Ge,componentName:Ge,components:{ElSelectMenu:Ae,ElOption:He,ElOptions:Ue,ElTag:Se,ElScrollbar:ge,ElTooltip:be,ElIcon:re},directives:{ClickOutside:_e},props:qe,emits:[Me,$e,"remove-tag","clear","visible-change","focus","blur","popup-scroll"],setup(o,{emit:s,slots:i}){const r=t((()=>{const{modelValue:e,multiple:l}=o,t=l?[]:void 0;return L(e)?l?e:t:l?t:e})),p=V({...O(o),modelValue:r}),d=Qe(p,s),{calculatorRef:c,inputStyle:v}=function(){const o=e(),s=l(0),n=t((()=>({minWidth:`${Math.max(s.value,11)}px`})));return a(o,(()=>{var e,l;s.value=null!=(l=null==(e=o.value)?void 0:e.getBoundingClientRect().width)?l:0})),{calculatorRef:o,calculatorWidth:s,inputStyle:n}}();u((()=>{var e;return null==(e=i.default)?void 0:e.call(i)}),(e=>{var l;l=e,!o.persistent&&l&&Ke(l).filter((e=>n(e)&&"ElOption"===e.type.name)).forEach((e=>{const l={...e.props};l.currentLabel=l.label||(n(l.value)?"":l.value),d.onOptionCreate(l)}))}),{immediate:!0}),ue(xe,V({props:p,states:d.states,selectRef:d.selectRef,optionsArray:d.optionsArray,setSelected:d.setSelected,handleOptionSelect:d.handleOptionSelect,onOptionCreate:d.onOptionCreate,onOptionDestroy:d.onOptionDestroy}));const f=t((()=>o.multiple?d.states.selected.map((e=>e.currentLabel)):d.states.selectedLabel));return{...d,modelValue:r,selectedLabel:f,calculatorRef:c,inputStyle:v}}}),[["render",function(e,l){const t=X("el-tag"),a=X("el-tooltip"),o=X("el-icon"),s=X("el-option"),n=X("el-options"),i=X("el-scrollbar"),r=X("el-select-menu"),u=Z("click-outside");return v((b(),m("div",{ref:"selectRef",class:x([e.nsSelect.b(),e.nsSelect.m(e.selectSize)]),[ie(e.mouseEnterEventName)]:l=>e.states.inputHovering=!0,onMouseleave:l=>e.states.inputHovering=!1},[J(a,{ref:"tooltipRef",visible:e.dropdownMenuVisible,placement:e.placement,teleported:e.teleported,"popper-class":[e.nsSelect.e("popper"),e.popperClass],"popper-options":e.popperOptions,"fallback-placements":e.fallbackPlacements,effect:e.effect,pure:"",trigger:"click",transition:`${e.nsSelect.namespace.value}-zoom-in-top`,"stop-popper-mouse-event":!1,"gpu-acceleration":!1,persistent:e.persistent,"append-to":e.appendTo,"show-arrow":e.showArrow,offset:e.offset,onBeforeShow:e.handleMenuEnter,onHide:l=>e.states.isBeforeHide=!1},{default:Y((()=>{var l;return[g("div",{ref:"wrapperRef",class:x([e.nsSelect.e("wrapper"),e.nsSelect.is("focused",e.isFocused),e.nsSelect.is("hovering",e.states.inputHovering),e.nsSelect.is("filterable",e.filterable),e.nsSelect.is("disabled",e.selectDisabled)]),onClick:S(e.toggleMenu,["prevent"])},[e.$slots.prefix?(b(),m("div",{key:0,ref:"prefixRef",class:x(e.nsSelect.e("prefix"))},[h(e.$slots,"prefix")],2)):E("v-if",!0),g("div",{ref:"selectionRef",class:x([e.nsSelect.e("selection"),e.nsSelect.is("near",e.multiple&&!e.$slots.prefix&&!!e.states.selected.length)])},[e.multiple?h(e.$slots,"tag",{key:0},(()=>[(b(!0),m(le,null,te(e.showTagList,(l=>(b(),m("div",{key:e.getValueKey(l),class:x(e.nsSelect.e("selected-item"))},[J(t,{closable:!e.selectDisabled&&!l.isDisabled,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",style:k(e.tagStyle),onClose:t=>e.deleteTag(t,l)},{default:Y((()=>[g("span",{class:x(e.nsSelect.e("tags-text"))},[h(e.$slots,"label",{label:l.currentLabel,value:l.value},(()=>[ae(y(l.currentLabel),1)]))],2)])),_:2},1032,["closable","size","type","effect","style","onClose"])],2)))),128)),e.collapseTags&&e.states.selected.length>e.maxCollapseTags?(b(),ee(a,{key:0,ref:"tagTooltipRef",disabled:e.dropdownMenuVisible||!e.collapseTagsTooltip,"fallback-placements":["bottom","top","right","left"],effect:e.effect,placement:"bottom",teleported:e.teleported},{default:Y((()=>[g("div",{ref:"collapseItemRef",class:x(e.nsSelect.e("selected-item"))},[J(t,{closable:!1,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",style:k(e.collapseTagStyle)},{default:Y((()=>[g("span",{class:x(e.nsSelect.e("tags-text"))}," + "+y(e.states.selected.length-e.maxCollapseTags),3)])),_:1},8,["size","type","effect","style"])],2)])),content:Y((()=>[g("div",{ref:"tagMenuRef",class:x(e.nsSelect.e("selection"))},[(b(!0),m(le,null,te(e.collapseTagList,(l=>(b(),m("div",{key:e.getValueKey(l),class:x(e.nsSelect.e("selected-item"))},[J(t,{class:"in-tooltip",closable:!e.selectDisabled&&!l.isDisabled,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",onClose:t=>e.deleteTag(t,l)},{default:Y((()=>[g("span",{class:x(e.nsSelect.e("tags-text"))},[h(e.$slots,"label",{label:l.currentLabel,value:l.value},(()=>[ae(y(l.currentLabel),1)]))],2)])),_:2},1032,["closable","size","type","effect","onClose"])],2)))),128))],2)])),_:3},8,["disabled","effect","teleported"])):E("v-if",!0)])):E("v-if",!0),g("div",{class:x([e.nsSelect.e("selected-item"),e.nsSelect.e("input-wrapper"),e.nsSelect.is("hidden",!e.filterable)])},[v(g("input",{id:e.inputId,ref:"inputRef","onUpdate:modelValue":l=>e.states.inputValue=l,type:"text",name:e.name,class:x([e.nsSelect.e("input"),e.nsSelect.is(e.selectSize)]),disabled:e.selectDisabled,autocomplete:e.autocomplete,style:k(e.inputStyle),tabindex:e.tabindex,role:"combobox",readonly:!e.filterable,spellcheck:"false","aria-activedescendant":(null==(l=e.hoverOption)?void 0:l.id)||"","aria-controls":e.contentId,"aria-expanded":e.dropdownMenuVisible,"aria-label":e.ariaLabel,"aria-autocomplete":"none","aria-haspopup":"listbox",onKeydown:[oe(S((l=>e.navigateOptions("next")),["stop","prevent"]),["down"]),oe(S((l=>e.navigateOptions("prev")),["stop","prevent"]),["up"]),oe(S(e.handleEsc,["stop","prevent"]),["esc"]),oe(S(e.selectOption,["stop","prevent"]),["enter"]),oe(S(e.deletePrevTag,["stop"]),["delete"])],onCompositionstart:e.handleCompositionStart,onCompositionupdate:e.handleCompositionUpdate,onCompositionend:e.handleCompositionEnd,onInput:e.onInput,onClick:S(e.toggleMenu,["stop"])},null,46,["id","onUpdate:modelValue","name","disabled","autocomplete","tabindex","readonly","aria-activedescendant","aria-controls","aria-expanded","aria-label","onKeydown","onCompositionstart","onCompositionupdate","onCompositionend","onInput","onClick"]),[[se,e.states.inputValue]]),e.filterable?(b(),m("span",{key:0,ref:"calculatorRef","aria-hidden":"true",class:x(e.nsSelect.e("input-calculator")),textContent:y(e.states.inputValue)},null,10,["textContent"])):E("v-if",!0)],2),e.shouldShowPlaceholder?(b(),m("div",{key:1,class:x([e.nsSelect.e("selected-item"),e.nsSelect.e("placeholder"),e.nsSelect.is("transparent",!e.hasModelValue||e.expanded&&!e.states.inputValue)])},[e.hasModelValue?h(e.$slots,"label",{key:0,label:e.currentPlaceholder,value:e.modelValue},(()=>[g("span",null,y(e.currentPlaceholder),1)])):(b(),m("span",{key:1},y(e.currentPlaceholder),1))],2)):E("v-if",!0)],2),g("div",{ref:"suffixRef",class:x(e.nsSelect.e("suffix"))},[e.iconComponent&&!e.showClose?(b(),ee(o,{key:0,class:x([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.iconReverse])},{default:Y((()=>[(b(),ee(ne(e.iconComponent)))])),_:1},8,["class"])):E("v-if",!0),e.showClose&&e.clearIcon?(b(),ee(o,{key:1,class:x([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.nsSelect.e("clear")]),onClick:e.handleClearClick},{default:Y((()=>[(b(),ee(ne(e.clearIcon)))])),_:1},8,["class","onClick"])):E("v-if",!0),e.validateState&&e.validateIcon&&e.needStatusIcon?(b(),ee(o,{key:2,class:x([e.nsInput.e("icon"),e.nsInput.e("validateIcon"),e.nsInput.is("loading","validating"===e.validateState)])},{default:Y((()=>[(b(),ee(ne(e.validateIcon)))])),_:1},8,["class"])):E("v-if",!0)],2)],10,["onClick"])]})),content:Y((()=>[J(r,{ref:"menuRef"},{default:Y((()=>[e.$slots.header?(b(),m("div",{key:0,class:x(e.nsSelect.be("dropdown","header")),onClick:S((()=>{}),["stop"])},[h(e.$slots,"header")],10,["onClick"])):E("v-if",!0),v(J(i,{id:e.contentId,ref:"scrollbarRef",tag:"ul","wrap-class":e.nsSelect.be("dropdown","wrap"),"view-class":e.nsSelect.be("dropdown","list"),class:x([e.nsSelect.is("empty",0===e.filteredOptionsCount)]),role:"listbox","aria-label":e.ariaLabel,"aria-orientation":"vertical",onScroll:e.popupScroll},{default:Y((()=>[e.showNewOption?(b(),ee(s,{key:0,value:e.states.inputValue,created:!0},null,8,["value"])):E("v-if",!0),J(n,null,{default:Y((()=>[h(e.$slots,"default")])),_:3})])),_:3},8,["id","wrap-class","view-class","class","aria-label","onScroll"]),[[f,e.states.options.size>0&&!e.loading]]),e.$slots.loading&&e.loading?(b(),m("div",{key:1,class:x(e.nsSelect.be("dropdown","loading"))},[h(e.$slots,"loading")],2)):e.loading||0===e.filteredOptionsCount?(b(),m("div",{key:2,class:x(e.nsSelect.be("dropdown","empty"))},[h(e.$slots,"empty",{},(()=>[g("span",null,y(e.emptyText),1)]))],2)):E("v-if",!0),e.$slots.footer?(b(),m("div",{key:3,class:x(e.nsSelect.be("dropdown","footer")),onClick:S((()=>{}),["stop"])},[h(e.$slots,"footer")],10,["onClick"])):E("v-if",!0)])),_:3},512)])),_:3},8,["visible","placement","teleported","popper-class","popper-options","fallback-placements","effect","transition","persistent","append-to","show-arrow","offset","onBeforeShow","onHide"])],16,["onMouseleave"])),[[u,e.handleClickOutside,e.popperRef]])}],["__file","select.vue"]]);var Ze=d(c({name:"ElOptionGroup",componentName:"ElOptionGroup",props:{label:String,disabled:Boolean},setup(e){const a=C("select"),o=l(),s=p(),n=l([]);ue(Ce,V({...O(e)}));const i=t((()=>n.value.some((e=>!0===e.visible)))),r=e=>{const l=Ie(e),t=[];return l.forEach((e=>{var l;de(e)&&((e=>{var l;return"ElOption"===e.type.name&&!!(null==(l=e.component)?void 0:l.proxy)})(e)?t.push(e.component.proxy):L(e.children)&&e.children.length?t.push(...r(e.children)):(null==(l=e.component)?void 0:l.subTree)&&t.push(...r(e.component.subTree)))})),t},u=()=>{n.value=r(s.subTree)};return R((()=>{u()})),pe(o,u,{attributes:!0,subtree:!0,childList:!0}),{groupRef:o,visible:i,ns:a}}}),[["render",function(e,l,t,a,o,s){return v((b(),m("ul",{ref:"groupRef",class:x(e.ns.be("group","wrap"))},[g("li",{class:x(e.ns.be("group","title"))},y(e.label),3),g("li",null,[g("ul",{class:x(e.ns.b("group"))},[h(e.$slots,"default")],2)])],2)),[[f,e.visible]])}],["__file","option-group.vue"]]);const Je=ce(Xe,{Option:He,OptionGroup:Ze}),Ye=ve(He);ve(Ze);export{Je as E,Ye as a};
