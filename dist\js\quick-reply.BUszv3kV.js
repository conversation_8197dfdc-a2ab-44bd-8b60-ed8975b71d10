import{d as e,S as i,r as l,e as s,f as o,w as t,m as r,i as a,C as p,g as m,h as n,P as d,Q as u,$ as c,E as f,c1 as y,az as j}from"./index.Dk5pbsTU.js";import{E as _}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as k,a as v}from"./el-form-item.Bw6Zyv_7.js";import{E as R}from"./el-input.DiGatoux.js";import{E as q}from"./el-divider.2VNIZioN.js";import{E as h}from"./el-button.CXI119n4.js";import{a as x}from"./liveRoom.B9NhOBdK.js";import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./index.C9UdVphc.js";import"./index.DEKElSOG.js";import"./use-form-item.DzRJVC1I.js";import"./index.Vn8pbgQR.js";const b={class:"quick-reply-list"},C={key:0,class:"empty-list"},w={class:"dialog-footer"},V=g(e({__name:"quick-reply",emits:["success"],setup(e,{expose:g,emit:V}){const E=i({visible:!1}),U=i({id:0,quickReply:[]}),A=l(!1),O=V;function P(){U.quickReply.push("")}function S(){const e=U.quickReply.filter((e=>""!==e.trim()));0!==e.length?(A.value=!0,x.saveQuickReply({id:U.id,quickReply:e}).then((()=>{j.success("保存成功"),E.visible=!1,O("success")})).finally((()=>{A.value=!1}))):j.warning("请至少添加一条快捷语")}return g({open:e=>{E.visible=!0,U.id=e.id;try{const i=e.quickReply?JSON.parse(e.quickReply):[];U.quickReply=Array.isArray(i)?i:[]}catch(i){U.quickReply=[]}}}),(e,i)=>{const l=h,j=q,x=f,g=R,V=v,O=k,Q=_;return o(),s(Q,{modelValue:a(E).visible,"onUpdate:modelValue":i[1]||(i[1]=e=>a(E).visible=e),title:"快捷语设置",width:"600px"},{footer:t((()=>[p("div",w,[r(l,{type:"primary",loading:a(A),onClick:S},{default:t((()=>i[4]||(i[4]=[c("确 定")]))),_:1,__:[4]},8,["loading"]),r(l,{onClick:i[0]||(i[0]=e=>a(E).visible=!1)},{default:t((()=>i[5]||(i[5]=[c("取 消")]))),_:1,__:[5]})])])),default:t((()=>[r(O,{ref:"formRef",model:a(U),"label-width":"0"},{default:t((()=>[r(V,null,{default:t((()=>[p("div",b,[(o(!0),m(d,null,u(a(U).quickReply,((e,s)=>(o(),m("div",{key:s,class:"quick-reply-item"},[r(g,{modelValue:a(U).quickReply[s],"onUpdate:modelValue":e=>a(U).quickReply[s]=e,placeholder:"请输入快捷回复内容",maxlength:100,"show-word-limit":""},{append:t((()=>[r(l,{type:"danger",link:"",onClick:e=>function(e){U.quickReply.splice(e,1)}(s)},{default:t((()=>i[2]||(i[2]=[c("删除")]))),_:2,__:[2]},1032,["onClick"]),r(j,{direction:"vertical"}),r(l,{type:"primary",link:"",onClick:P},{default:t((()=>[r(x,null,{default:t((()=>[r(a(y))])),_:1})])),_:1})])),_:2},1032,["modelValue","onUpdate:modelValue"])])))),128)),0===a(U).quickReply.length?(o(),m("div",C,[r(l,{type:"primary",plain:"",icon:"plus",onClick:P},{default:t((()=>i[3]||(i[3]=[c("新增快捷语")]))),_:1,__:[3]})])):n("",!0)])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-29d4a851"]]);export{V as default};
