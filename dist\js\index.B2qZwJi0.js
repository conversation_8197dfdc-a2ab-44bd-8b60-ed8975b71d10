import{d as e,r as l,S as a,o as t,ap as o,aQ as r,g as i,f as s,C as n,m as d,w as u,i as p,$ as m,Z as c,V as f,e as y,h as _,E as v,D as h,n as k,F as b,j as g,aL as j,P as V,Q as w,aW as U,az as x}from"./index.Dk5pbsTU.js";import{v as T}from"./el-loading.Dqi-qL7c.js";import{E as C}from"./el-drawer.Df_TzNjH.js";import"./el-overlay.DpVCS8zG.js";import{_ as E}from"./index.B_Z-un7A.js";import{E as N}from"./el-input.DiGatoux.js";import{E as A}from"./el-input-number.C02ig7uT.js";import"./el-tooltip.l0sNRNKZ.js";import{E as O}from"./el-popper.Dbn4MgsT.js";import{E as L,a as I}from"./el-radio.w2rep3_A.js";/* empty css               */import"./el-select.CRWkm-it.js";/* empty css                     */import"./el-tree.ChWw39qP.js";import"./el-checkbox.DDYarIkn.js";import"./el-text.6kaKYQ9U.js";import{E as M}from"./el-tree-select.HrmCEXac.js";import{E as P}from"./el-card.DwLhVNHW.js";import{a as q,E as G}from"./el-table-column.DRgE6Qqc.js";import{a as B,E as F}from"./el-form-item.Bw6Zyv_7.js";/* empty css                        */import{a as K,E as R}from"./el-button.CXI119n4.js";/* empty css                       */import{E as S}from"./index.L2DVy5yq.js";import{E as z}from"./index.BcMfjWDS.js";import"./index.C6NthMtN.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./el-popover.Bo2lPKkO.js";import"./dropdown.B_OfpyL_.js";import"./el-tab-pane.CXnN_Izo.js";import"./strings.MqEQKtyI.js";import"./index.C_BbqFDa.js";import"./index.ybpLT-bz.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.DEKElSOG.js";import"./use-form-item.DzRJVC1I.js";import"./use-form-common-props.CQPDkY7k.js";import"./index.Vn8pbgQR.js";import"./index.Cd8M2JyP.js";import"./token.DWNpOE8r.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./index.DLOxQT-M.js";import"./_initCloneObject.BN1anLuC.js";import"./validator.HGn2BZtD.js";import"./index.DFyomGhz.js";var Q=(e=>(e.CATALOG="CATALOG",e.MENU="MENU",e.BUTTON="BUTTON",e.EXTLINK="EXTLINK",e))(Q||{});const X={class:"app-container"},D={class:"search-bar"},W={class:"mb-10px"},$={class:"flex-y-center"},H={class:"flex-y-center"},J={class:"flex-y-center"},Z={class:"flex-y-center"},Y={key:0},ee={key:1},le={class:"dialog-footer"},ae=e({name:"SysMenu",inheritAttrs:!1,__name:"index",setup(e){const ae=l(),te=l(),oe=l(!1),re=a({title:"新增菜单",visible:!1}),ie=a({menuType:"0"}),se=l([]),ne=l([]),de=l({id:void 0,parentId:"0",visible:1,sort:1,type:Q.MENU,alwaysShow:0,keepAlive:1,params:[]}),ue=l({...de.value}),pe=a({parentId:[{required:!0,message:"请选择父级菜单",trigger:"blur"}],name:[{required:!0,message:"请输入菜单名称",trigger:"blur"}],type:[{required:!0,message:"请选择菜单类型",trigger:"blur"}],routeName:[{required:!0,message:"请输入路由名称",trigger:"blur"}],routePath:[{required:!0,message:"请输入路由路径",trigger:"blur"}],component:[{required:!0,message:"请输入组件路径",trigger:"blur"}],visible:[{required:!0,message:"请选择显示状态",trigger:"change"}]}),me=l();function ce(e){ie.menuType!==e&&(ie.menuType=e,fe())}function fe(){oe.value=!0,U.getList(ie).then((e=>{se.value=e})).finally((()=>{oe.value=!1}))}function ye(){ae.value.resetFields(),fe()}function _e(e){me.value=e.id}function ve(e,l){U.getOptions(!0,ie.menuType).then((e=>{ne.value=[{value:"0",label:"顶级菜单",children:e}]})).then((()=>{re.visible=!0,l?(re.title="编辑菜单",U.getFormData(l).then((e=>{de.value={...e},ue.value=e}))):(re.title="新增菜单",ue.value.parentId=null==e?void 0:e.toString(),ue.value.menuType=ie.menuType)}))}function he(){ue.value.type!==de.value.type&&ue.value.type===Q.MENU&&(de.value.type===Q.CATALOG?ue.value.component="":(ue.value.routePath=de.value.routePath,ue.value.component=de.value.component))}function ke(){te.value.validate((e=>{if(e){const e=ue.value.id;if(e){if(ue.value.parentId==e)return void x.error("父级菜单不能为当前菜单");U.update(e,ue.value).then((()=>{x.success("修改成功"),be(),fe()}))}else U.add(ue.value).then((()=>{x.success("新增成功"),be(),fe()}))}}))}function be(){re.visible=!1,te.value.resetFields(),te.value.clearValidate(),ue.value={id:void 0,parentId:"0",visible:1,sort:1,type:Q.MENU,alwaysShow:0,keepAlive:1,params:[]}}return t((()=>{fe()})),(e,l)=>{const a=R,t=K,de=B,me=N,ge=F,je=v,Ve=G,we=S,Ue=q,xe=P,Te=M,Ce=I,Ee=L,Ne=o("QuestionFilled"),Ae=O,Oe=o("CirclePlusFilled"),Le=o("DeleteFilled"),Ie=A,Me=E,Pe=C,qe=r("hasPerm"),Ge=T;return s(),i("div",X,[n("div",D,[d(ge,{ref_key:"queryFormRef",ref:ae,model:p(ie),inline:!0},{default:u((()=>[d(de,{prop:"menu_type","label-width":"0"},{default:u((()=>[d(t,null,{default:u((()=>[d(a,{type:"0"===p(ie).menuType?"primary":"",icon:"tools",onClick:l[0]||(l[0]=e=>ce("0"))},{default:u((()=>l[21]||(l[21]=[m(" 系统菜单 ")]))),_:1,__:[21]},8,["type"]),d(a,{type:"1"===p(ie).menuType?"primary":"",icon:"avatar",onClick:l[1]||(l[1]=e=>ce("1"))},{default:u((()=>l[22]||(l[22]=[m(" 租户菜单 ")]))),_:1,__:[22]},8,["type"])])),_:1})])),_:1}),d(de,{label:"关键字",prop:"keywords"},{default:u((()=>[d(me,{modelValue:p(ie).keywords,"onUpdate:modelValue":l[2]||(l[2]=e=>p(ie).keywords=e),placeholder:"菜单名称",clearable:"",onKeyup:c(fe,["enter"])},null,8,["modelValue"])])),_:1}),d(de,null,{default:u((()=>[d(a,{type:"primary",icon:"search",onClick:fe},{default:u((()=>l[23]||(l[23]=[m("搜索")]))),_:1,__:[23]}),d(a,{icon:"refresh",onClick:ye},{default:u((()=>l[24]||(l[24]=[m("重置")]))),_:1,__:[24]})])),_:1})])),_:1},8,["model"])]),d(xe,{shadow:"never"},{default:u((()=>[n("div",W,[f((s(),y(a,{type:"success",icon:"plus",onClick:l[3]||(l[3]=e=>ve("0"))},{default:u((()=>l[25]||(l[25]=[m(" 新增 ")]))),_:1,__:[25]})),[[qe,["sys:menu:add"]]])]),f((s(),y(Ue,{data:p(se),"highlight-current-row":"","row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"},onRowClick:_e},{default:u((()=>[d(Ve,{label:"菜单名称","min-width":"200"},{default:u((e=>[e.row.icon&&e.row.icon.startsWith("el-icon")?(s(),y(je,{key:0,style:{"vertical-align":"-0.15em"}},{default:u((()=>[(s(),y(h(e.row.icon.replace("el-icon-",""))))])),_:2},1024)):e.row.icon?(s(),i("div",{key:1,class:k(`i-svg:${e.row.icon}`)},null,2)):_("",!0),m(" "+b(e.row.name),1)])),_:1}),d(Ve,{label:"类型",align:"center",width:"80"},{default:u((e=>[e.row.type===p(Q).CATALOG?(s(),y(we,{key:0,type:"warning"},{default:u((()=>l[26]||(l[26]=[m("目录")]))),_:1,__:[26]})):_("",!0),e.row.type===p(Q).MENU?(s(),y(we,{key:1,type:"success"},{default:u((()=>l[27]||(l[27]=[m("菜单")]))),_:1,__:[27]})):_("",!0),e.row.type===p(Q).BUTTON?(s(),y(we,{key:2,type:"danger"},{default:u((()=>l[28]||(l[28]=[m("按钮")]))),_:1,__:[28]})):_("",!0),e.row.type===p(Q).EXTLINK?(s(),y(we,{key:3,type:"info"},{default:u((()=>l[29]||(l[29]=[m("外链")]))),_:1,__:[29]})):_("",!0)])),_:1}),d(Ve,{label:"路由名称",align:"left",width:"150",prop:"routeName"}),d(Ve,{label:"路由路径",align:"left",width:"150",prop:"routePath"}),d(Ve,{label:"组件路径",align:"left",width:"250",prop:"component"}),d(Ve,{label:"权限标识",align:"center",width:"200",prop:"perm"}),d(Ve,{label:"状态",align:"center",width:"80"},{default:u((e=>[1===e.row.visible?(s(),y(we,{key:0,type:"success"},{default:u((()=>l[30]||(l[30]=[m("显示")]))),_:1,__:[30]})):(s(),y(we,{key:1,type:"info"},{default:u((()=>l[31]||(l[31]=[m("隐藏")]))),_:1,__:[31]}))])),_:1}),d(Ve,{label:"排序",align:"center",width:"80",prop:"sort"}),d(Ve,{fixed:"right",align:"center",label:"操作",width:"220"},{default:u((e=>["CATALOG"==e.row.type||"MENU"==e.row.type?f((s(),y(a,{key:0,type:"primary",link:"",size:"small",icon:"plus",onClick:g((l=>ve(e.row.id)),["stop"])},{default:u((()=>l[32]||(l[32]=[m(" 新增 ")]))),_:2,__:[32]},1032,["onClick"])),[[qe,["sys:menu:add"]]]):_("",!0),f((s(),y(a,{type:"primary",link:"",size:"small",icon:"edit",onClick:g((l=>ve(void 0,e.row.id)),["stop"])},{default:u((()=>l[33]||(l[33]=[m(" 编辑 ")]))),_:2,__:[33]},1032,["onClick"])),[[qe,["sys:menu:edit"]]]),f((s(),y(a,{type:"danger",link:"",size:"small",icon:"delete",onClick:g((l=>function(e){if(!e)return x.warning("请勾选删除项"),!1;z.confirm("确认删除已选中的数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{oe.value=!0,U.deleteById(e).then((()=>{x.success("删除成功"),fe()})).finally((()=>{oe.value=!1}))}),(()=>{x.info("已取消删除")}))}(e.row.id)),["stop"])},{default:u((()=>l[34]||(l[34]=[m(" 删除 ")]))),_:2,__:[34]},1032,["onClick"])),[[qe,["sys:menu:delete"]]])])),_:1})])),_:1},8,["data"])),[[Ge,p(oe)]])])),_:1}),d(Pe,{modelValue:p(re).visible,"onUpdate:modelValue":l[20]||(l[20]=e=>p(re).visible=e),title:p(re).title,size:"50%",onClose:be},{footer:u((()=>[n("div",le,[d(a,{type:"primary",onClick:ke},{default:u((()=>l[55]||(l[55]=[m("确 定")]))),_:1,__:[55]}),d(a,{onClick:be},{default:u((()=>l[56]||(l[56]=[m("取 消")]))),_:1,__:[56]})])])),default:u((()=>[d(ge,{ref_key:"menuFormRef",ref:te,model:p(ue),rules:p(pe),"label-width":"100px"},{default:u((()=>[d(de,{label:"父级菜单",prop:"parentId"},{default:u((()=>[d(Te,{modelValue:p(ue).parentId,"onUpdate:modelValue":l[4]||(l[4]=e=>p(ue).parentId=e),placeholder:"选择上级菜单",data:p(ne),filterable:"","check-strictly":"","render-after-expand":!1},null,8,["modelValue","data"])])),_:1}),d(de,{label:"菜单名称",prop:"name"},{default:u((()=>[d(me,{modelValue:p(ue).name,"onUpdate:modelValue":l[5]||(l[5]=e=>p(ue).name=e),placeholder:"请输入菜单名称"},null,8,["modelValue"])])),_:1}),d(de,{label:"菜单类型",prop:"type"},{default:u((()=>[d(Ee,{modelValue:p(ue).type,"onUpdate:modelValue":l[6]||(l[6]=e=>p(ue).type=e),onChange:he},{default:u((()=>[d(Ce,{value:"CATALOG"},{default:u((()=>l[35]||(l[35]=[m("目录")]))),_:1,__:[35]}),d(Ce,{value:"MENU"},{default:u((()=>l[36]||(l[36]=[m("菜单")]))),_:1,__:[36]}),d(Ce,{value:"BUTTON"},{default:u((()=>l[37]||(l[37]=[m("按钮")]))),_:1,__:[37]}),d(Ce,{value:"EXTLINK"},{default:u((()=>l[38]||(l[38]=[m("外链")]))),_:1,__:[38]})])),_:1},8,["modelValue"])])),_:1}),"EXTLINK"==p(ue).type?(s(),y(de,{key:0,label:"外链地址",prop:"path"},{default:u((()=>[d(me,{modelValue:p(ue).routePath,"onUpdate:modelValue":l[7]||(l[7]=e=>p(ue).routePath=e),placeholder:"请输入外链完整路径"},null,8,["modelValue"])])),_:1})):_("",!0),p(ue).type==p(Q).MENU?(s(),y(de,{key:1,prop:"routeName"},{label:u((()=>[n("div",$,[l[40]||(l[40]=m(" 路由名称 ")),d(Ae,{placement:"bottom",effect:"light"},{content:u((()=>l[39]||(l[39]=[m(" 如果需要开启缓存，需保证页面 defineOptions 中的 name 与此处一致，建议使用驼峰。 ")]))),default:u((()=>[d(je,{class:"ml-1 cursor-pointer"},{default:u((()=>[d(Ne)])),_:1})])),_:1})])])),default:u((()=>[d(me,{modelValue:p(ue).routeName,"onUpdate:modelValue":l[8]||(l[8]=e=>p(ue).routeName=e),placeholder:"User"},null,8,["modelValue"])])),_:1})):_("",!0),p(ue).type==p(Q).CATALOG||p(ue).type==p(Q).MENU?(s(),y(de,{key:2,prop:"routePath"},{label:u((()=>[n("div",H,[l[42]||(l[42]=m(" 路由路径 ")),d(Ae,{placement:"bottom",effect:"light"},{content:u((()=>l[41]||(l[41]=[m(" 定义应用中不同页面对应的 URL 路径，目录需以 / 开头，菜单项不用。例如：系统管理目录 /system，系统管理下的用户管理菜单 user。 ")]))),default:u((()=>[d(je,{class:"ml-1 cursor-pointer"},{default:u((()=>[d(Ne)])),_:1})])),_:1})])])),default:u((()=>[p(ue).type==p(Q).CATALOG?(s(),y(me,{key:0,modelValue:p(ue).routePath,"onUpdate:modelValue":l[9]||(l[9]=e=>p(ue).routePath=e),placeholder:"system"},null,8,["modelValue"])):(s(),y(me,{key:1,modelValue:p(ue).routePath,"onUpdate:modelValue":l[10]||(l[10]=e=>p(ue).routePath=e),placeholder:"user"},null,8,["modelValue"]))])),_:1})):_("",!0),p(ue).type==p(Q).MENU?(s(),y(de,{key:3,prop:"component"},{label:u((()=>[n("div",J,[l[44]||(l[44]=m(" 组件路径 ")),d(Ae,{placement:"bottom",effect:"light"},{content:u((()=>l[43]||(l[43]=[m(" 组件页面完整路径，相对于 src/views/，如 system/user/index，缺省后缀 .vue ")]))),default:u((()=>[d(je,{class:"ml-1 cursor-pointer"},{default:u((()=>[d(Ne)])),_:1})])),_:1})])])),default:u((()=>[d(me,{modelValue:p(ue).component,"onUpdate:modelValue":l[11]||(l[11]=e=>p(ue).component=e),placeholder:"system/user/index",style:{width:"95%"}},j({_:2},[p(ue).type==p(Q).MENU?{name:"prepend",fn:u((()=>[l[45]||(l[45]=m("src/views/"))])),key:"0"}:void 0,p(ue).type==p(Q).MENU?{name:"append",fn:u((()=>[l[46]||(l[46]=m(".vue"))])),key:"1"}:void 0]),1032,["modelValue"])])),_:1})):_("",!0),p(ue).type==p(Q).MENU?(s(),y(de,{key:4},{label:u((()=>[n("div",Z,[l[48]||(l[48]=m(" 路由参数 ")),d(Ae,{placement:"bottom",effect:"light"},{content:u((()=>l[47]||(l[47]=[m(" 组件页面使用 `useRoute().query.参数名` 获取路由参数值。 ")]))),default:u((()=>[d(je,{class:"ml-1 cursor-pointer"},{default:u((()=>[d(Ne)])),_:1})])),_:1})])])),default:u((()=>[p(ue).params&&0!==p(ue).params.length?(s(),i("div",ee,[(s(!0),i(V,null,w(p(ue).params,((e,a)=>(s(),i("div",{key:a},[d(me,{modelValue:e.key,"onUpdate:modelValue":l=>e.key=l,placeholder:"参数名",style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),l[50]||(l[50]=n("span",{class:"mx-1"},"=",-1)),d(me,{modelValue:e.value,"onUpdate:modelValue":l=>e.value=l,placeholder:"参数值",style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),p(ue).params.indexOf(e)===p(ue).params.length-1?(s(),y(je,{key:0,class:"ml-2 cursor-pointer color-[var(--el-color-success)]",style:{"vertical-align":"-0.15em"},onClick:l[13]||(l[13]=e=>p(ue).params.push({key:"",value:""}))},{default:u((()=>[d(Oe)])),_:1})):_("",!0),d(je,{class:"ml-2 cursor-pointer color-[var(--el-color-danger)]",style:{"vertical-align":"-0.15em"},onClick:l=>p(ue).params.splice(p(ue).params.indexOf(e),1)},{default:u((()=>[d(Le)])),_:2},1032,["onClick"])])))),128))])):(s(),i("div",Y,[d(a,{type:"success",plain:"",onClick:l[12]||(l[12]=e=>p(ue).params=[{key:"",value:""}])},{default:u((()=>l[49]||(l[49]=[m(" 添加路由参数 ")]))),_:1,__:[49]})]))])),_:1})):_("",!0),p(ue).type!==p(Q).BUTTON?(s(),y(de,{key:5,prop:"visible",label:"显示状态"},{default:u((()=>[d(Ee,{modelValue:p(ue).visible,"onUpdate:modelValue":l[14]||(l[14]=e=>p(ue).visible=e)},{default:u((()=>[d(Ce,{value:1},{default:u((()=>l[51]||(l[51]=[m("显示")]))),_:1,__:[51]}),d(Ce,{value:0},{default:u((()=>l[52]||(l[52]=[m("隐藏")]))),_:1,__:[52]})])),_:1},8,["modelValue"])])),_:1})):_("",!0),p(ue).type===p(Q).MENU?(s(),y(de,{key:6,label:"缓存页面"},{default:u((()=>[d(Ee,{modelValue:p(ue).keepAlive,"onUpdate:modelValue":l[15]||(l[15]=e=>p(ue).keepAlive=e)},{default:u((()=>[d(Ce,{value:1},{default:u((()=>l[53]||(l[53]=[m("开启")]))),_:1,__:[53]}),d(Ce,{value:0},{default:u((()=>l[54]||(l[54]=[m("关闭")]))),_:1,__:[54]})])),_:1},8,["modelValue"])])),_:1})):_("",!0),d(de,{label:"排序",prop:"sort"},{default:u((()=>[d(Ie,{modelValue:p(ue).sort,"onUpdate:modelValue":l[16]||(l[16]=e=>p(ue).sort=e),style:{width:"100px"},"controls-position":"right",min:0},null,8,["modelValue"])])),_:1}),p(ue).type==p(Q).BUTTON?(s(),y(de,{key:7,label:"权限标识",prop:"perm"},{default:u((()=>[d(me,{modelValue:p(ue).perm,"onUpdate:modelValue":l[17]||(l[17]=e=>p(ue).perm=e),placeholder:"sys:user:add"},null,8,["modelValue"])])),_:1})):_("",!0),p(ue).type!==p(Q).BUTTON?(s(),y(de,{key:8,label:"图标",prop:"icon"},{default:u((()=>[d(Me,{modelValue:p(ue).icon,"onUpdate:modelValue":l[18]||(l[18]=e=>p(ue).icon=e)},null,8,["modelValue"])])),_:1})):_("",!0),p(ue).type==p(Q).CATALOG?(s(),y(de,{key:9,label:"跳转路由"},{default:u((()=>[d(me,{modelValue:p(ue).redirect,"onUpdate:modelValue":l[19]||(l[19]=e=>p(ue).redirect=e),placeholder:"跳转路由"},null,8,["modelValue"])])),_:1})):_("",!0)])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}});export{ae as default};
