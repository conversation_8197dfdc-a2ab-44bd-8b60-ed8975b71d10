<template>
  <div class="app-container" style="padding: 0">
    <div class="search-bar" style="box-shadow: none">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="keywords" label="城市检索">
          <el-cascader
            v-model="page.query.province"
            :props="{ value: 'label' }"
            :options="provinceAndCityData as any"
          />
        </el-form-item>
        <el-form-item prop="status" label="发言数">
          <el-input v-model="page.query.commentCount" oninput="value=value.replace(/[^\d]/g,'')" />
        </el-form-item>
        <el-form-item prop="userStatus" label="状态">
          <dict v-model="page.query.userStatus" code="live_user_status" />
        </el-form-item>
        <el-form-item prop="userType" label="用户身份">
          <dict v-model="page.query.userType" code="live_user_type" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="状态" align="center" width="100">
          <template #default="{ row }">
            <dict-label v-model="row.userStatus" code="live_user_status" />
          </template>
        </el-table-column>
        <el-table-column label="头像" align="center" prop="name" width="80">
          <template #default="{ row }">
            <el-image
              style="width: 30px; height: 30px; border-radius: 30px"
              :preview-src-list="[row.avatar]"
              preview-teleported
              :src="row.avatar"
            />
          </template>
        </el-table-column>
        <el-table-column label="昵称" align="center" prop="nickname" width="120" />
        <el-table-column label="标签" show-overflow-tooltip align="center" prop="commentCount" min-width="100">
          <template #default="{ row }">
            {{ row.tagList.length ? row.tagList.join("、") : "-" }}
          </template>
        </el-table-column>
        <el-table-column label="累计发言数" align="center" prop="commentCount" width="100">
          <template #default="{ row }">
            <el-link type="primary" @click="lookUserInfoRef?.open(row)">
              {{ row.commentCount }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="IP" align="center" prop="commentCount" width="150">
          <template #default="{ row }">{{ row.province }}-{{ row.city }}</template>
        </el-table-column>
        <el-table-column label="累计观看时长" align="center" prop="durationStr" width="120" />
        <el-table-column label="用户身份" align="center" width="100">
          <template #default="{ row }">
            <dict-label v-model="row.userType" code="live_user_type" />
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
  </div>
  <look-user-info ref="lookUserInfoRef" :session-id="String(sessionId || '')" />
</template>
<script setup lang="ts">
import LiveSessionApi, {
  LiveWatchingRecordVo,
  RecordListQueryPageDto,
} from "@/api/live/liveSession";
import { ref } from "vue";
import { usePage } from "@/utils/commonSetup";
import { provinceAndCityData } from "element-china-area-data";
import LookUserInfo from "./lookUserInfo.vue";

const lookUserInfoRef = ref<InstanceType<typeof LookUserInfo>>();
const props = defineProps({
  sessionId: {
    type: Number,
    default: -1,
  },
  inviterType: {
    type: Number,
  },
  inviterId: {
    type: Number,
  },
});
const { page, getPage, resetQuery } = usePage<RecordListQueryPageDto, LiveWatchingRecordVo>(
  new RecordListQueryPageDto(props.sessionId, props.inviterId, props.inviterType),
  LiveSessionApi.recordList
);
</script>
