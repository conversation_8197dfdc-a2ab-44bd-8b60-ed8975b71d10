<template>
  <el-container class="app-container">
    <el-main style="padding: 0; height: 100%">
      <el-card shadow="never">
        <el-table
          ref="dataTableRef"
          v-loading="sendInfo.loading"
          :data="sendInfo.data || []"
          highlight-current-row
          border
        >
          <el-table-column label="序号" align="center" width="55" type="index" />
          <el-table-column label="等级名称" align="center" prop="levelName" min-width="120" />
          <el-table-column label="积分值" align="center" min-width="100">
            <template #header>
              <div class="text-align-center">
                积分值
                <el-button
                  v-hasPerm="['vipLevel:config:edit']"
                  type="primary"
                  style="right: 0; top: 50%; transform: translateY(-50%)"
                  class="pos-absolute"
                  icon="edit"
                  size="small"
                  @click="editModelRef?.open(sendInfo.data || [])"
                >
                  编辑
                </el-button>
              </div>
            </template>
            <template #default="{ row }">
              <div>
                {{ row.startPoints }}
                <span class="m-l-1 m-r-1">至</span>
                {{ row.endPoints }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-main>
    <edit-model ref="editModelRef" @success="onSend" />
  </el-container>
</template>

<script setup lang="ts">
defineOptions({ name: "VipLevelConfig" });
import EditModel from "./edit.vue";
import { useApi } from "@/utils/commonSetup";
import GradeApi, { GradeConfigVo } from "@/api/system/grade";

const editModelRef = ref<InstanceType<typeof EditModel>>();
const { sendInfo, onSend } = useApi<null, GradeConfigVo[]>(GradeApi.configList);
onSend();
</script>
