import{aT as a,d as e,r as t,I as s,e as o,f as n,w as i,m as r,i as m,$ as l,az as d}from"./index.Dk5pbsTU.js";import{E as u,a as p,b as c}from"./el-main.CclDHmVj.js";import{E as f}from"./el-button.CXI119n4.js";import{E as _}from"./el-card.DwLhVNHW.js";import{_ as j}from"./index.vue_vue_type_script_setup_true_lang.BOVTspW2.js";import{a as x}from"./commonSetup.Dm-aByKQ.js";import"./index.DuiNpp1i.js";import"./use-form-item.DzRJVC1I.js";import"./use-form-common-props.CQPDkY7k.js";import"./index.D6CER_Ot.js";import"./index.WzKGworL.js";const g=()=>a.get("/api/basic/introduction"),v=e=>a.post("/api/basic/introduction",{content:e}),y=e({name:"CompanyIntro",__name:"index",setup(a){const e=t(""),{sendInfo:y,onSend:b}=x(g);b(),s((()=>y.data),(a=>{a&&(e.value=a.content)}),{immediate:!0});const{sendInfo:h,onSend:w}=x(v),E=async()=>{if(e.value){h.params=e.value;try{await w(),d.success("保存成功"),b()}catch(a){}}else d.warning("请输入公司介绍内容")};return(a,t)=>{const s=j,d=_,x=u,g=f,v=p,y=c;return n(),o(y,null,{default:i((()=>[r(x,{class:"p-0 m-0"},{default:i((()=>[r(d,{shadow:"never",title:"公司介绍"},{default:i((()=>[r(s,{modelValue:e.value,"onUpdate:modelValue":t[0]||(t[0]=a=>e.value=a),style:{height:"500px"}},null,8,["modelValue"])])),_:1})])),_:1}),r(v,{class:"text-align-center justify-center"},{default:i((()=>[r(g,{type:"primary",loading:m(h).loading,onClick:E},{default:i((()=>t[1]||(t[1]=[l("保 存")]))),_:1,__:[1]},8,["loading"])])),_:1})])),_:1})}}});export{y as default};
