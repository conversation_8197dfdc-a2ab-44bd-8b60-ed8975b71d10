import{d as e,b2 as a,cY as t,ai as s,ap as l,e as i,f as o,w as r,h as p,E as u,j as n,m as d,az as c}from"./index.Dk5pbsTU.js";import{E as m}from"./el-progress.BQBUwu9o.js";import{E as f}from"./el-image-viewer.BH897zgF.js";import{F as g}from"./index.WzKGworL.js";import{_ as h}from"./_plugin-vue_export-helper.BCo6x5W8.js";const y=h(e({__name:"SingleImageUpload",props:a({data:{type:Object,default:()=>({})},name:{type:String,default:"file"},maxFileSize:{type:Number,default:10},accept:{type:String,default:"image/*"},style:{type:Object,default:()=>({width:"150px",height:"150px"})}},{modelValue:{type:String,required:!0,default:()=>""},modelModifiers:{}}),emits:["update:modelValue"],setup(e){t((e=>({"14badb4e":a.style.width,b63e8218:a.style.height})));const a=e,h=s(e,"modelValue");function y(e){return a.accept.split(",").map((e=>e.trim())).some((a=>"image/*"===a?e.type.startsWith("image/"):a.startsWith(".")?e.name.toLowerCase().endsWith(a):e.type===a))?!(e.size>1024*a.maxFileSize*1024)||(c.warning("上传图片不能大于"+a.maxFileSize+"M"),!1):(c.warning(`上传文件的格式不正确，仅支持：${a.accept}`),!1)}function _(e){return new Promise(((t,s)=>{const l=e.file,i=new FormData;i.append(a.name,l),Object.keys(a.data).forEach((e=>{i.append(e,a.data[e])})),g.upload(i).then((e=>{t(e)})).catch((e=>{s(e)}))}))}function b(){h.value=""}const v=e=>{c.success("上传成功"),h.value=e.url},w=e=>{};return(e,t)=>{const s=f,c=l("CircleCloseFilled"),g=u,j=l("Plus"),x=m;return o(),i(x,{modelValue:h.value,"onUpdate:modelValue":t[0]||(t[0]=e=>h.value=e),class:"single-upload","list-type":"picture-card","show-file-list":!1,accept:a.accept,"before-upload":y,"http-request":_,"on-success":v,"on-error":w,multiple:""},{default:r((()=>[h.value?(o(),i(s,{key:0,src:h.value},null,8,["src"])):p("",!0),h.value?(o(),i(g,{key:1,class:"single-upload__delete-btn",onClick:n(b,["stop"])},{default:r((()=>[d(c)])),_:1})):(o(),i(g,{key:2,class:"single-upload__add-btn"},{default:r((()=>[d(j)])),_:1}))])),_:1},8,["modelValue","accept"])}}}),[["__scopeId","data-v-620b31a1"]]);export{y as _};
