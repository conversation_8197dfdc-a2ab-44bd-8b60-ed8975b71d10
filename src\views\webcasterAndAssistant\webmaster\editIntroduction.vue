<script setup lang="ts">
import WebmasterApi, {
  SaveWebmasterDto,
  WebmasterPageVO,
} from "@/api/webcasterAndAssistant/webcaster";

const dialog = reactive({
  visible: false,
  title: "主播介绍",
});
const formData = ref(new SaveWebmasterDto());
const emits = defineEmits(["success"]);
const loading = ref(false);

function handleSubmit() {
  loading.value = true;
  WebmasterApi.save(formData.value)
    .then(() => {
      ElMessage.success("保存成功");
      dialog.visible = false;
      emits("success");
    })
    .finally(() => (loading.value = false));
}

defineExpose({
  open: (_row?: WebmasterPageVO) => {
    dialog.visible = true;
    formData.value = new SaveWebmasterDto(_row);
  },
});
</script>

<template>
  <el-drawer v-model="dialog.visible" :title="dialog.title" size="80vw">
    <wang-editor v-model="formData.introduction" style="height: 100%" height="calc(100% - 100px)" />
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="handleSubmit">确 定</el-button>
        <el-button :loading="loading" @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped lang="scss"></style>
