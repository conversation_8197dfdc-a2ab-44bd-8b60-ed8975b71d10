import request from "@/utils/request";
import { SaveWebmasterDto, WebmasterPageVO } from "@/api/webcasterAndAssistant/webcaster";

export interface UserVo {
  // 用户头像
  avatar: string;
  // 用户昵称
  nickname: string;
  userName: string; // 姓名
  // 用户ID
  appUserId: number;
}

export class SaveSalesDto {
  id?: number; // 主播ID，新增时为空
  appUserId: number = 0; // 用户id，改为number类型
  mobile: string = ""; // 手机号
  userName: string = ""; // 姓名
  nickName: string = ""; // 昵称
  avatar: string = ""; // 用户头像
  // 微信名称
  wechatName: string = "";

  constructor(e?: SaveSalesDto | UserVo) {
    if (e) {
      if (e instanceof SaveSalesDto) {
        this.id = e.id;
        this.appUserId = e.appUserId;
        this.userName = e.userName;
        this.nickName = e.nickName;
        this.avatar = e.avatar;
      } else {
        this.avatar = e.avatar;
        this.wechatName = e.nickname;
        this.appUserId = e.appUserId;
        this.userName = e.userName;
        console.log(this);
      }
    }
  }
}

// 修改状态
export class UpdateSalesStatusDto {
  id: number = 0; // 主播ID
  status: number = 0; // 状态 0-禁用 1-启用
  constructor(data?: WebmasterPageVO) {
    if (data) {
      this.id = data.id;
      this.status = data.status;
    }
  }
}

const SalesApi = {
  /**
   * 删除
   *
   * @param id ID
   */
  remove(id: number) {
    return request<any, null>({
      url: `/webcaster/removeAssistant?id=${id}&type=2`,
      method: "delete",
    });
  },
  /*根据用户uid查询用户*/
  getUserByPhone(uids: string) {
    return request<any, UserVo[]>({
      url: `/webcaster/getUserByPhone?uids=${uids}&type=2`,
      method: "get",
    });
  },
  /**
   * 保存
   * @param data 表单数据
   */
  save(data: SaveSalesDto[]) {
    return request({
      url: `/webcaster/saveAssistant`,
      method: "post",
      data: { list: data, type: 2 },
    });
  },
  /**
   * 修改状态
   * @param data 表单数据
   */
  editStatus(data: UpdateSalesStatusDto) {
    return request({
      url: `/webcaster/editAssistantStatus`,
      method: "post",
      data: {
        ...data,
        type: 2,
      },
    });
  },
};

export default SalesApi;
