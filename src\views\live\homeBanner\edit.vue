<template>
  <el-dialog
    :title="form.id ? '编辑轮播图' : '新增轮播图'"
    v-model="visible"
    width="500px"
    append-to-body
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="轮播图" prop="bannerUrl">
        <single-image-upload v-model="form.bannerUrl" />
      </el-form-item>
      <el-form-item label="跳转链接" prop="targetUrl">
        <el-input v-model="form.targetUrl" placeholder="请输入跳转链接" />
      </el-form-item>
      <el-form-item label="展示状态" prop="isShow">
        <el-radio-group v-model="form.isShow">
          <el-radio :label="1">展示</el-radio>
          <el-radio :label="0">隐藏</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import BannerApi, { SaveBannerDto } from '@/api/live/homeBanner'
import SingleImageUpload from '@/components/Upload/SingleImageUpload.vue'

defineOptions({ name: 'EditBanner' })

const emit = defineEmits(['success'])
const visible = ref(false)
const loading = ref(false)

const formRef = ref<FormInstance>()
const form = reactive<SaveBannerDto>({
  bannerUrl: '',
  targetUrl: '',
  isShow: 1
})

const rules = reactive<FormRules>({
  bannerUrl: [{ required: true, message: '请上传轮播图', trigger: 'change' }],
  targetUrl: [{ required: true, message: '请输入跳转链接', trigger: 'blur' }],
  isShow: [{ required: true, message: '请选择展示状态', trigger: 'change' }]
})

/** 取消按钮 */
function cancel() {
  reset()
}

/** 提交按钮 */
function submitForm() {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true
      BannerApi.saveOrUpdate(form)
        .then(() => {
          ElMessage.success('操作成功')
          emit('success')
          reset()
        })
        .finally(() => {
          loading.value = false
        })
    }
  })
}

/** 表单重置 */
function reset() {
  visible.value = false
  form.bannerUrl = ''
  form.targetUrl = ''
  form.isShow = 1
  form.id = undefined
}

/** 打开弹窗 */
function open(row?: SaveBannerDto) {
  reset()
  if (row) {
    Object.assign(form, row)
  }
  visible.value = true
}

defineExpose({
  open
})
</script> 