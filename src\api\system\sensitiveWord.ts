import request from "@/utils/request";

// 敏感词分页查询参数
export interface SensitiveWordPageQuery {
  pageNumber: number;
  pageSize: number;
  word?: string;
  tags?: string;
  isSensitive?: string;
  isEnable?: string;
}

// 敏感词VO
export interface SensitiveWordVo {
  id?: number;
  word: string;
  tags: string;
  isSensitive: number;
  isEnable: number;
  createdAt: string;
  updatedAt: string;
}

// 分页返回结构
export interface PageResultSensitiveWordVo {
  list: SensitiveWordVo[];
  total: number;
}

// 敏感词DTO
export interface SensitiveWordDto {
  id?: number;
  word: string;
  tags?: string;
  isSensitive: number;
  isEnable?: number;
}

// 分页查询敏感词
export function getSensitiveWordPage(params: SensitiveWordPageQuery) {
  return request<PageResultSensitiveWordVo>({
    url: "/sensitiveWord/page",
    method: "get",
    params,
  });
}

// 添加敏感词
export function addSensitiveWord(data: SensitiveWordDto) {
  return request({
    url: "/sensitiveWord/save",
    method: "post",
    data,
  });
}

// 根据主键删除敏感词
export function deleteSensitiveWordById(id: number) {
  return request({
    url: `/sensitiveWord/remove/${id}`,
    method: "delete",
  });
}

// 根据主键更新敏感词
export function updateSensitiveWord(data: SensitiveWordDto) {
  return request({
    url: "/sensitiveWord/update",
    method: "put",
    data,
  });
}

// 根据主键获取敏感词详细信息
export function getSensitiveWordById(id: number) {
  return request({
    url: `/sensitiveWord/getInfo/${id}`,
    method: "get",
  });
}

// 刷新敏感词库
export function refreshSensitiveWord() {
  return request({
    url: "/sensitiveWord/refresh",
    method: "post",
  });
}

// 导入敏感词
export function importSensitiveWords(file: File) {
  const formData = new FormData();
  formData.append("file", file);
  return request({
    url: "/sensitiveWord/importWords",
    method: "post",
    headers: { "Content-Type": "multipart/form-data" },
    data: formData,
  });
}

// 下载敏感词导入模板
export function downloadSensitiveWordTemplate() {
  return request({
    url: "/sensitiveWord/template",
    method: "get",
    responseType: "blob",
  });
}

// 更新敏感词启用状态
export function enableSensitiveWord(data: SensitiveWordDto) {
  return request({
    url: "/sensitiveWord/enable",
    method: "put",
    data,
  });
}

// 租户刷新敏感词库
export function refreshSensitiveWordByTenant() {
  return request({
    url: "/sensitiveWord/refreshByTenant",
    method: "post",
    data: {},
  });
}

export default {
  getSensitiveWordPage,
  addSensitiveWord,
  deleteSensitiveWordById,
  updateSensitiveWord,
  getSensitiveWordById,
  refreshSensitiveWord,
  importSensitiveWords,
  downloadSensitiveWordTemplate,
  enableSensitiveWord,
  refreshSensitiveWordByTenant,
};
