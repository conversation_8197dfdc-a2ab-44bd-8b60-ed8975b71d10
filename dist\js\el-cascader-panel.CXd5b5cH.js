import{d as e,a7 as a,b as l,_ as t,ap as s,g as n,f as o,h as i,e as d,m as c,j as r,w as u,C as p,n as h,P as v,a4 as f,bh as m,cL as g,E as b,A as k,c as y,Q as C,$ as x,F as N,l as E,x as w,r as $,B as S,cK as L,br as T,a5 as V,a9 as _,c9 as M,t as D,z as j,ba as F,y as P,S as z,I as B,cZ as A,o as H,a0 as q,H as I,K as U,q as K,bb as R,cy as Z,N as O,bw as W,cv as Q,bi as X,ab as J,V as G,i as Y,ak as ee,X as ae,k as le,aL as te,bA as se,W as ne,Z as oe,b3 as ie,c_ as de}from"./index.Dk5pbsTU.js";import{E as ce}from"./index.ybpLT-bz.js";import{E as re}from"./el-checkbox.DDYarIkn.js";import{a as ue}from"./el-radio.w2rep3_A.js";import{u as pe}from"./index.D6CER_Ot.js";import{c as he}from"./strings.MqEQKtyI.js";import{i as ve}from"./isEqual.C0S6DIiJ.js";import{d as fe,f as me,g as ge}from"./index.C6NthMtN.js";import{u as be,c as ke}from"./arrays.CygeFE-H.js";import{s as ye}from"./scroll.CVc-P3_z.js";import{U as Ce,C as xe}from"./event.BwRzfsZt.js";import{b as Ne}from"./index.C9UdVphc.js";import{c as Ee}from"./cloneDeep.DcCMo0F4.js";import{E as we}from"./el-input.DiGatoux.js";import{u as $e,b as Se,E as Le}from"./el-popper.Dbn4MgsT.js";import{t as Te,E as Ve}from"./index.L2DVy5yq.js";import{C as _e}from"./index.wZTqlYZ6.js";import{u as Me}from"./use-form-item.DzRJVC1I.js";import{a as De}from"./index.Vn8pbgQR.js";import{u as je}from"./use-form-common-props.CQPDkY7k.js";import{d as Fe}from"./error.D_Dr4eZ1.js";import{d as Pe}from"./debounce.DJJTSR8O.js";var ze=1/0;var Be=e({name:"NodeContent",setup:()=>({ns:l("cascader-node")}),render(){const{ns:e}=this,{node:l,panel:t}=this.$parent,{data:s,label:n}=l,{renderLabelFn:o}=t;return a("span",{class:e.e("label")},o?o({node:l,data:s}):n)}});const Ae=Symbol(),He=e({name:"ElCascaderNode",components:{ElCheckbox:re,ElRadio:ue,NodeContent:Be,ElIcon:b,Check:g,Loading:m,ArrowRight:f},props:{node:{type:Object,required:!0},menuId:String},emits:["expand"],setup(e,{emit:a}){const t=k(Ae),s=l("cascader-node"),n=y((()=>t.isHoverMenu)),o=y((()=>t.config.multiple)),i=y((()=>t.config.checkStrictly)),d=y((()=>{var e;return null==(e=t.checkedNodes[0])?void 0:e.uid})),c=y((()=>e.node.isDisabled)),r=y((()=>e.node.isLeaf)),u=y((()=>i.value&&!r.value||!c.value)),p=y((()=>v(t.expandingNode))),h=y((()=>i.value&&t.checkedNodes.some(v))),v=a=>{var l;const{level:t,uid:s}=e.node;return(null==(l=null==a?void 0:a.pathNodes[t-1])?void 0:l.uid)===s},f=()=>{p.value||t.expandNode(e.node)},m=a=>{const{node:l}=e;a!==l.checked&&t.handleCheckChange(l,a)},g=()=>{t.lazyLoad(e.node,(()=>{r.value||f()}))},b=()=>{const{node:a}=e;u.value&&!a.loading&&(a.loaded?f():g())},C=a=>{e.node.loaded?(m(a),!i.value&&f()):g()};return{panel:t,isHoverMenu:n,multiple:o,checkStrictly:i,checkedNodeId:d,isDisabled:c,isLeaf:r,expandable:u,inExpandingPath:p,inCheckedPath:h,ns:s,handleHoverExpand:e=>{n.value&&(b(),!r.value&&a("expand",e))},handleExpand:b,handleClick:()=>{n.value&&!r.value||(!r.value||c.value||i.value||o.value?b():C(!0))},handleCheck:C,handleSelectCheck:a=>{i.value?(m(a),e.node.loaded&&f()):C(a)}}}});var qe=t(e({name:"ElCascaderMenu",components:{Loading:m,ElIcon:b,ElScrollbar:ce,ElCascaderNode:t(He,[["render",function(e,a,l,t,f,m){const g=s("el-checkbox"),b=s("el-radio"),k=s("check"),y=s("el-icon"),C=s("node-content"),x=s("loading"),N=s("arrow-right");return o(),n("li",{id:`${e.menuId}-${e.node.uid}`,role:"menuitem","aria-haspopup":!e.isLeaf,"aria-owns":e.isLeaf?void 0:e.menuId,"aria-expanded":e.inExpandingPath,tabindex:e.expandable?-1:void 0,class:h([e.ns.b(),e.ns.is("selectable",e.checkStrictly),e.ns.is("active",e.node.checked),e.ns.is("disabled",!e.expandable),e.inExpandingPath&&"in-active-path",e.inCheckedPath&&"in-checked-path"]),onMouseenter:e.handleHoverExpand,onFocus:e.handleHoverExpand,onClick:e.handleClick},[i(" prefix "),e.multiple?(o(),d(g,{key:0,"model-value":e.node.checked,indeterminate:e.node.indeterminate,disabled:e.isDisabled,onClick:r((()=>{}),["stop"]),"onUpdate:modelValue":e.handleSelectCheck},null,8,["model-value","indeterminate","disabled","onClick","onUpdate:modelValue"])):e.checkStrictly?(o(),d(b,{key:1,"model-value":e.checkedNodeId,label:e.node.uid,disabled:e.isDisabled,"onUpdate:modelValue":e.handleSelectCheck,onClick:r((()=>{}),["stop"])},{default:u((()=>[i("\n        Add an empty element to avoid render label,\n        do not use empty fragment here for https://github.com/vuejs/vue-next/pull/2485\n      "),p("span")])),_:1},8,["model-value","label","disabled","onUpdate:modelValue","onClick"])):e.isLeaf&&e.node.checked?(o(),d(y,{key:2,class:h(e.ns.e("prefix"))},{default:u((()=>[c(k)])),_:1},8,["class"])):i("v-if",!0),i(" content "),c(C),i(" postfix "),e.isLeaf?i("v-if",!0):(o(),n(v,{key:3},[e.node.loading?(o(),d(y,{key:0,class:h([e.ns.is("loading"),e.ns.e("postfix")])},{default:u((()=>[c(x)])),_:1},8,["class"])):(o(),d(y,{key:1,class:h(["arrow-right",e.ns.e("postfix")])},{default:u((()=>[c(N)])),_:1},8,["class"]))],64))],42,["id","aria-haspopup","aria-owns","aria-expanded","tabindex","onMouseenter","onFocus","onClick"])}],["__file","node.vue"]])},props:{nodes:{type:Array,required:!0},index:{type:Number,required:!0}},setup(e){const a=S(),t=l("cascader-menu"),{t:s}=w(),n=pe();let o=null,i=null;const d=k(Ae),c=$(null),r=y((()=>!e.nodes.length)),u=y((()=>!d.initialLoaded)),p=y((()=>`${n.value}-${e.index}`)),h=()=>{i&&(clearTimeout(i),i=null)},v=()=>{c.value&&(c.value.innerHTML="",h())};return{ns:t,panel:d,hoverZone:c,isEmpty:r,isLoading:u,menuId:p,t:s,handleExpand:e=>{o=e.target},handleMouseMove:e=>{if(d.isHoverMenu&&o&&c.value)if(o.contains(e.target)){h();const l=a.vnode.el,{left:t}=l.getBoundingClientRect(),{offsetWidth:s,offsetHeight:n}=l,i=e.clientX-t,d=o.offsetTop,r=d+o.offsetHeight;c.value.innerHTML=`\n          <path style="pointer-events: auto;" fill="transparent" d="M${i} ${d} L${s} 0 V${d} Z" />\n          <path style="pointer-events: auto;" fill="transparent" d="M${i} ${r} L${s} ${n} V${r} Z" />\n        `}else i||(i=window.setTimeout(v,d.config.hoverThreshold))},clearHoverZone:v}}}),[["render",function(e,a,l,t,r,p){const f=s("el-cascader-node"),m=s("loading"),g=s("el-icon"),b=s("el-scrollbar");return o(),d(b,{key:e.menuId,tag:"ul",role:"menu",class:h(e.ns.b()),"wrap-class":e.ns.e("wrap"),"view-class":[e.ns.e("list"),e.ns.is("empty",e.isEmpty)],onMousemove:e.handleMouseMove,onMouseleave:e.clearHoverZone},{default:u((()=>{var a;return[(o(!0),n(v,null,C(e.nodes,(a=>(o(),d(f,{key:a.uid,node:a,"menu-id":e.menuId,onExpand:e.handleExpand},null,8,["node","menu-id","onExpand"])))),128)),e.isLoading?(o(),n("div",{key:0,class:h(e.ns.e("empty-text"))},[c(g,{size:"14",class:h(e.ns.is("loading"))},{default:u((()=>[c(m)])),_:1},8,["class"]),x(" "+N(e.t("el.cascader.loading")),1)],2)):e.isEmpty?(o(),n("div",{key:1,class:h(e.ns.e("empty-text"))},[E(e.$slots,"empty",{},(()=>[x(N(e.t("el.cascader.noData")),1)]))],2)):(null==(a=e.panel)?void 0:a.isHoverMenu)?(o(),n(v,{key:2},[i(" eslint-disable-next-line vue/html-self-closing "),(o(),n("svg",{ref:"hoverZone",class:h(e.ns.e("hover-zone"))},null,2))],2112)):i("v-if",!0)]})),_:3},8,["class","wrap-class","view-class","onMousemove","onMouseleave"])}],["__file","menu.vue"]]);let Ie=0;class Ue{constructor(e,a,l,t=!1){this.data=e,this.config=a,this.parent=l,this.root=t,this.uid=Ie++,this.checked=!1,this.indeterminate=!1,this.loading=!1;const{value:s,label:n,children:o}=a,i=e[o],d=(e=>{const a=[e];let{parent:l}=e;for(;l;)a.unshift(l),l=l.parent;return a})(this);this.level=t?0:l?l.level+1:1,this.value=e[s],this.label=e[n],this.pathNodes=d,this.pathValues=d.map((e=>e.value)),this.pathLabels=d.map((e=>e.label)),this.childrenData=i,this.children=(i||[]).map((e=>new Ue(e,a,this))),this.loaded=!a.lazy||this.isLeaf||!L(i)}get isDisabled(){const{data:e,parent:a,config:l}=this,{disabled:t,checkStrictly:s}=l;return(T(t)?t(e,this):!!e[t])||!s&&(null==a?void 0:a.isDisabled)}get isLeaf(){const{data:e,config:a,childrenData:l,loaded:t}=this,{lazy:s,leaf:n}=a,o=T(n)?n(e,this):e[n];return V(o)?!(s&&!t)&&!(_(l)&&l.length):!!o}get valueByOption(){return this.config.emitPath?this.pathValues:this.value}appendChild(e){const{childrenData:a,children:l}=this,t=new Ue(e,this.config,this);return _(a)?a.push(e):this.childrenData=[e],l.push(t),t}calcText(e,a){const l=e?this.pathLabels.join(a):this.label;return this.text=l,l}broadcast(e,...a){const l=`onParent${he(e)}`;this.children.forEach((t=>{t&&(t.broadcast(e,...a),t[l]&&t[l](...a))}))}emit(e,...a){const{parent:l}=this,t=`onChild${he(e)}`;l&&(l[t]&&l[t](...a),l.emit(e,...a))}onParentCheck(e){this.isDisabled||this.setCheckState(e)}onChildCheck(){const{children:e}=this,a=e.filter((e=>!e.isDisabled)),l=!!a.length&&a.every((e=>e.checked));this.setCheckState(l)}setCheckState(e){const a=this.children.length,l=this.children.reduce(((e,a)=>e+(a.checked?1:a.indeterminate?.5:0)),0);this.checked=this.loaded&&this.children.filter((e=>!e.isDisabled)).every((e=>e.loaded&&e.checked))&&e,this.indeterminate=this.loaded&&l!==a&&l>0}doCheck(e){if(this.checked===e)return;const{checkStrictly:a,multiple:l}=this.config;a||!l?this.checked=e:(this.broadcast("check",e),this.setCheckState(e),this.emit("check"))}}const Ke=(e,a)=>e.reduce(((e,l)=>(l.isLeaf?e.push(l):(!a&&e.push(l),e=e.concat(Ke(l.children,a))),e)),[]);class Re{constructor(e,a){this.config=a;const l=(e||[]).map((e=>new Ue(e,this.config)));this.nodes=l,this.allNodes=Ke(l,!1),this.leafNodes=Ke(l,!0)}getNodes(){return this.nodes}getFlattedNodes(e){return e?this.leafNodes:this.allNodes}appendNode(e,a){const l=a?a.appendChild(e):new Ue(e,this.config);a||this.nodes.push(l),this.appendAllNodesAndLeafNodes(l)}appendNodes(e,a){e.forEach((e=>this.appendNode(e,a)))}appendAllNodesAndLeafNodes(e){this.allNodes.push(e),e.isLeaf&&this.leafNodes.push(e),e.children&&e.children.forEach((e=>{this.appendAllNodesAndLeafNodes(e)}))}getNodeByValue(e,a=!1){if(M(e))return null;return this.getFlattedNodes(a).find((a=>ve(a.value,e)||ve(a.pathValues,e)))||null}getSameNode(e){if(!e)return null;return this.getFlattedNodes(!1).find((({value:a,level:l})=>ve(e.value,a)&&e.level===l))||null}}const Ze=D({modelValue:{type:j([Number,String,Array])},options:{type:j(Array),default:()=>[]},props:{type:j(Object),default:()=>({})}}),Oe={expandTrigger:"click",multiple:!1,checkStrictly:!1,emitPath:!0,lazy:!1,lazyLoad:F,value:"value",label:"label",children:"children",leaf:"leaf",disabled:"disabled",hoverThreshold:500},We=e=>{if(!e)return 0;const a=e.id.split("-");return Number(a[a.length-2])};const Qe=K(t(e({name:"ElCascaderPanel",components:{ElCascaderMenu:qe},props:{...Ze,border:{type:Boolean,default:!0},renderLabel:Function},emits:[Ce,xe,"close","expand-change"],setup(e,{emit:a,slots:t}){let s=!1;const n=l("cascader"),o=(e=>y((()=>({...Oe,...e.props}))))(e);let i=null;const d=$(!0),c=$([]),r=$(null),u=$([]),p=$(null),h=$([]),v=y((()=>"hover"===o.value.expandTrigger)),f=y((()=>e.renderLabel||t.default)),m=(e,a)=>{const l=o.value;(e=e||new Ue({},l,void 0,!0)).loading=!0;l.lazyLoad(e,(l=>{const t=e,s=t.root?null:t;l&&(null==i||i.appendNodes(l,s)),t.loading=!1,t.loaded=!0,t.childrenData=t.childrenData||[],a&&a(l)}))},g=(e,l)=>{var t;const{level:s}=e,n=u.value.slice(0,s);let o;e.isLeaf?o=e.pathNodes[s-2]:(o=e,n.push(e.children)),(null==(t=p.value)?void 0:t.uid)!==(null==o?void 0:o.uid)&&(p.value=e,u.value=n,!l&&a("expand-change",(null==e?void 0:e.pathValues)||[]))},b=(e,l,t=!0)=>{const{checkStrictly:n,multiple:i}=o.value,d=h.value[0];s=!0,!i&&(null==d||d.doCheck(!1)),e.doCheck(l),N(),t&&!i&&!n&&a("close"),!t&&!i&&!n&&k(e)},k=e=>{e&&(e=e.parent,k(e),e&&g(e))},C=e=>null==i?void 0:i.getFlattedNodes(e),x=e=>{var a;return null==(a=C(e))?void 0:a.filter((e=>!1!==e.checked))},N=()=>{var e;const{checkStrictly:a,multiple:l}=o.value,t=((e,a)=>{const l=a.slice(0),t=l.map((e=>e.uid)),s=e.reduce(((e,a)=>{const s=t.indexOf(a.uid);return s>-1&&(e.push(a),l.splice(s,1),t.splice(s,1)),e}),[]);return s.push(...l),s})(h.value,x(!a)),s=t.map((e=>e.valueByOption));h.value=t,r.value=l?s:null!=(e=s[0])?e:null},E=(a=!1,l=!1)=>{const{modelValue:t}=e,{lazy:n,multiple:c,checkStrictly:u}=o.value,p=!u;var h;if(d.value&&!s&&(l||!ve(t,r.value)))if(n&&!a){const e=be(null!=(h=ke(t))&&h.length?Ne(h,ze):[]).map((e=>null==i?void 0:i.getNodeByValue(e))).filter((e=>!!e&&!e.loaded&&!e.loading));e.length?e.forEach((e=>{m(e,(()=>E(!1,l)))})):E(!0,l)}else{const e=c?ke(t):[t],a=be(e.map((e=>null==i?void 0:i.getNodeByValue(e,p))));w(a,l),r.value=Ee(t)}},w=(e,a=!0)=>{const{checkStrictly:l}=o.value,t=h.value,s=e.filter((e=>!!e&&(l||e.isLeaf))),n=null==i?void 0:i.getSameNode(p.value),d=a&&n||s[0];d?d.pathNodes.forEach((e=>g(e,!0))):p.value=null,t.forEach((e=>e.doCheck(!1))),z(s).forEach((e=>e.doCheck(!0))),h.value=s,q(S)},S=()=>{I&&c.value.forEach((e=>{const a=null==e?void 0:e.$el;if(a){const e=a.querySelector(`.${n.namespace.value}-scrollbar__wrap`),l=a.querySelector(`.${n.b("node")}.${n.is("active")}`)||a.querySelector(`.${n.b("node")}.in-active-path`);ye(e,l)}}))};return P(Ae,z({config:o,expandingNode:p,checkedNodes:h,isHoverMenu:v,initialLoaded:d,renderLabelFn:f,lazyLoad:m,expandNode:g,handleCheckChange:b})),B([o,()=>e.options],(()=>{const{options:a}=e,l=o.value;s=!1,i=new Re(a,l),u.value=[i.getNodes()],l.lazy&&L(e.options)?(d.value=!1,m(void 0,(e=>{e&&(i=new Re(e,l),u.value=[i.getNodes()]),d.value=!0,E(!1,!0)}))):E(!1,!0)}),{deep:!0,immediate:!0}),B((()=>e.modelValue),(()=>{s=!1,E()}),{deep:!0}),B((()=>r.value),(l=>{ve(l,e.modelValue)||(a(Ce,l),a(xe,l))})),A((()=>c.value=[])),H((()=>!L(e.modelValue)&&E())),{ns:n,menuList:c,menus:u,checkedNodes:h,handleKeyDown:e=>{const a=e.target,{code:l}=e;switch(l){case U.up:case U.down:{e.preventDefault();const t=l===U.up?-1:1;me(ge(a,t,`.${n.b("node")}[tabindex="-1"]`));break}case U.left:{e.preventDefault();const l=c.value[We(a)-1],t=null==l?void 0:l.$el.querySelector(`.${n.b("node")}[aria-expanded="true"]`);me(t);break}case U.right:{e.preventDefault();const l=c.value[We(a)+1],t=null==l?void 0:l.$el.querySelector(`.${n.b("node")}[tabindex="-1"]`);me(t);break}case U.enter:case U.numpadEnter:(e=>{if(!e)return;const a=e.querySelector("input");a?a.click():fe(e)&&e.click()})(a)}},handleCheckChange:b,getFlattedNodes:C,getCheckedNodes:x,clearCheckedNodes:()=>{h.value.forEach((e=>e.doCheck(!1))),N(),u.value=u.value.slice(0,1),p.value=null,a("expand-change",[])},calculateCheckedValue:N,scrollToExpandingNode:S}}}),[["render",function(e,a,l,t,i,c){const r=s("el-cascader-menu");return o(),n("div",{class:h([e.ns.b("panel"),e.ns.is("bordered",e.border)]),onKeydown:e.handleKeyDown},[(o(!0),n(v,null,C(e.menus,((a,l)=>(o(),d(r,{key:l,ref_for:!0,ref:a=>e.menuList[l]=a,index:l,nodes:[...a]},{empty:u((()=>[E(e.$slots,"empty")])),_:2},1032,["index","nodes"])))),128))],42,["onKeydown"])}],["__file","index.vue"]])),Xe=D({...Ze,size:O,placeholder:String,disabled:Boolean,clearable:Boolean,filterable:Boolean,filterMethod:{type:j(Function),default:(e,a)=>e.text.includes(a)},separator:{type:String,default:" / "},showAllLevels:{type:Boolean,default:!0},collapseTags:Boolean,maxCollapseTags:{type:Number,default:1},collapseTagsTooltip:{type:Boolean,default:!1},debounce:{type:Number,default:300},beforeFilter:{type:j(Function),default:()=>!0},placement:{type:j(String),values:Se,default:"bottom-start"},fallbackPlacements:{type:j(Array),default:["bottom-start","bottom","top-start","top","right","left"]},popperClass:{type:String,default:""},teleported:$e.teleported,tagType:{...Te.type,default:"info"},tagEffect:{...Te.effect,default:"light"},validateEvent:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},...Z}),Je={[Ce]:e=>!0,[xe]:e=>!0,focus:e=>e instanceof FocusEvent,blur:e=>e instanceof FocusEvent,clear:()=>!0,visibleChange:e=>R(e),expandChange:e=>!!e,removeTag:e=>!!e},Ge=e({name:"ElCascader"});const Ye=K(t(e({...Ge,props:Xe,emits:Je,setup(e,{expose:a,emit:t}){const s=e,f={modifiers:[{name:"arrowPosition",enabled:!0,phase:"main",fn:({state:e})=>{const{modifiersData:a,placement:l}=e;["right","left","bottom","top"].includes(l)||a.arrow&&(a.arrow.x=35)},requires:["arrow"]}]},m=W();let k=0,x=0;const S=l("cascader"),L=l("input"),{t:T}=w(),{form:V,formItem:_}=Me(),{valueOnClear:M}=Q(s),{isComposing:D,handleComposition:j}=De({afterComposition(e){var a;const l=null==(a=e.target)?void 0:a.value;pa(l)}}),F=$(null),P=$(null),z=$(null),A=$(null),K=$(null),R=$(!1),Z=$(!1),O=$(!1),re=$(!1),ue=$(""),pe=$(""),he=$([]),ve=$([]),fe=$([]),be=y((()=>m.style)),ke=y((()=>s.disabled||(null==V?void 0:V.disabled))),ye=y((()=>s.placeholder||T("el.cascader.placeholder"))),Ne=y((()=>pe.value||he.value.length>0||D.value?"":ye.value)),$e=je(),Se=y((()=>"small"===$e.value?"small":"default")),Te=y((()=>!!s.props.multiple)),ze=y((()=>!s.filterable||Te.value)),Be=y((()=>Te.value?pe.value:ue.value)),Ae=y((()=>{var e;return(null==(e=A.value)?void 0:e.checkedNodes)||[]})),He=y((()=>!(!s.clearable||ke.value||O.value||!Z.value)&&!!Ae.value.length)),qe=y((()=>{const{showAllLevels:e,separator:a}=s,l=Ae.value;return l.length?Te.value?"":l[0].calcText(e,a):""})),Ie=y((()=>(null==_?void 0:_.validateState)||"")),Ue=y({get:()=>Ee(s.modelValue),set(e){const a=null!=e?e:M.value;t(Ce,a),t(xe,a),s.validateEvent&&(null==_||_.validate("change").catch((e=>Fe())))}}),Ke=y((()=>[S.b(),S.m($e.value),S.is("disabled",ke.value),m.class])),Re=y((()=>[L.e("icon"),"icon-arrow-down",S.is("reverse",R.value)])),Ze=y((()=>S.is("focus",R.value||re.value))),Oe=y((()=>{var e,a;return null==(a=null==(e=F.value)?void 0:e.popperRef)?void 0:a.contentRef})),We=e=>{var a,l,n;ke.value||(e=null!=e?e:!R.value)!==R.value&&(R.value=e,null==(l=null==(a=P.value)?void 0:a.input)||l.setAttribute("aria-expanded",`${e}`),e?(Xe(),q(null==(n=A.value)?void 0:n.scrollToExpandingNode)):s.filterable&&oa(),t("visibleChange",e))},Xe=()=>{q((()=>{var e;null==(e=F.value)||e.updatePopper()}))},Je=()=>{O.value=!1},Ge=e=>{const{showAllLevels:a,separator:l}=s;return{node:e,key:e.uid,text:e.calcText(a,l),hitState:!1,closable:!ke.value&&!e.isDisabled,isCollapseTag:!1}},Ye=e=>{var a;const l=e.node;l.doCheck(!1),null==(a=A.value)||a.calculateCheckedValue(),t("removeTag",l.valueByOption)},ea=()=>{var e,a;const{filterMethod:l,showAllLevels:t,separator:n}=s,o=null==(a=null==(e=A.value)?void 0:e.getFlattedNodes(!s.props.checkStrictly))?void 0:a.filter((e=>!e.isDisabled&&(e.calcText(t,n),l(e,Be.value))));Te.value&&(he.value.forEach((e=>{e.hitState=!1})),ve.value.forEach((e=>{e.hitState=!1}))),O.value=!0,fe.value=o,Xe()},aa=()=>{var e;let a;a=O.value&&K.value?K.value.$el.querySelector(`.${S.e("suggestion-item")}`):null==(e=A.value)?void 0:e.$el.querySelector(`.${S.b("node")}[tabindex="-1"]`),a&&(a.focus(),!O.value&&a.click())},la=()=>{var e,a;const l=null==(e=P.value)?void 0:e.input,t=z.value,s=null==(a=K.value)?void 0:a.$el;if(I&&l){if(s){s.querySelector(`.${S.e("suggestion-list")}`).style.minWidth=`${l.offsetWidth}px`}if(t){const{offsetHeight:e}=t,a=he.value.length>0?Math.max(e,k)-2+"px":`${k}px`;l.style.height=a,Xe()}}},ta=e=>{Xe(),t("expandChange",e)},sa=e=>{if(!D.value)switch(e.code){case U.enter:case U.numpadEnter:We();break;case U.down:We(!0),q(aa),e.preventDefault();break;case U.esc:!0===R.value&&(e.preventDefault(),e.stopPropagation(),We(!1));break;case U.tab:We(!1)}},na=()=>{var e;null==(e=A.value)||e.clearCheckedNodes(),!R.value&&s.filterable&&oa(),We(!1),t("clear")},oa=()=>{const{value:e}=qe;ue.value=e,pe.value=e},ia=e=>{const a=e.target,{code:l}=e;switch(l){case U.up:case U.down:{e.preventDefault();const t=l===U.up?-1:1;me(ge(a,t,`.${S.e("suggestion-item")}[tabindex="-1"]`));break}case U.enter:case U.numpadEnter:a.click()}},da=()=>{const e=he.value,a=e[e.length-1];x=pe.value?0:x+1,!a||!x||s.collapseTags&&e.length>1||(a.hitState?Ye(a):a.hitState=!0)},ca=e=>{const a=e.target,l=S.e("search-input");a.className===l&&(re.value=!0),t("focus",e)},ra=e=>{re.value=!1,t("blur",e)},ua=Pe((()=>{const{value:e}=Be;if(!e)return;const a=s.beforeFilter(e);X(a)?a.then(ea).catch((()=>{})):!1!==a?ea():Je()}),s.debounce),pa=(e,a)=>{!R.value&&We(!0),(null==a?void 0:a.isComposing)||(e?ua():Je())},ha=e=>Number.parseFloat(de(L.cssVarName("input-height"),e).value)-2;return B(O,Xe),B([Ae,ke,()=>s.collapseTags],(()=>{if(!Te.value)return;const e=Ae.value,a=[],l=[];if(e.forEach((e=>l.push(Ge(e)))),ve.value=l,e.length){e.slice(0,s.maxCollapseTags).forEach((e=>a.push(Ge(e))));const l=e.slice(s.maxCollapseTags),t=l.length;t&&(s.collapseTags?a.push({key:-1,text:`+ ${t}`,closable:!1,isCollapseTag:!0}):l.forEach((e=>a.push(Ge(e)))))}he.value=a})),B(he,(()=>{q((()=>la()))})),B($e,(async()=>{await q();const e=P.value.input;k=ha(e)||k,la()})),B(qe,oa,{immediate:!0}),H((()=>{const e=P.value.input,a=ha(e);k=e.offsetHeight||a,J(e,la)})),a({getCheckedNodes:e=>{var a;return null==(a=A.value)?void 0:a.getCheckedNodes(e)},cascaderPanelRef:A,togglePopperVisible:We,contentRef:Oe,presentText:qe}),(e,a)=>(o(),d(Y(Le),{ref_key:"tooltipRef",ref:F,visible:R.value,teleported:e.teleported,"popper-class":[Y(S).e("dropdown"),e.popperClass],"popper-options":f,"fallback-placements":e.fallbackPlacements,"stop-popper-mouse-event":!1,"gpu-acceleration":!1,placement:e.placement,transition:`${Y(S).namespace.value}-zoom-in-top`,effect:"light",pure:"",persistent:e.persistent,onHide:Je},{default:u((()=>[G((o(),n("div",{class:h(Y(Ke)),style:le(Y(be)),onClick:()=>We(!Y(ze)||void 0),onKeydown:sa,onMouseenter:e=>Z.value=!0,onMouseleave:e=>Z.value=!1},[c(Y(we),{ref_key:"input",ref:P,modelValue:ue.value,"onUpdate:modelValue":e=>ue.value=e,placeholder:Y(Ne),readonly:Y(ze),disabled:Y(ke),"validate-event":!1,size:Y($e),class:h(Y(Ze)),tabindex:Y(Te)&&e.filterable&&!Y(ke)?-1:void 0,onCompositionstart:Y(j),onCompositionupdate:Y(j),onCompositionend:Y(j),onFocus:ca,onBlur:ra,onInput:pa},te({suffix:u((()=>[Y(He)?(o(),d(Y(b),{key:"clear",class:h([Y(L).e("icon"),"icon-circle-close"]),onClick:r(na,["stop"])},{default:u((()=>[c(Y(se))])),_:1},8,["class","onClick"])):(o(),d(Y(b),{key:"arrow-down",class:h(Y(Re)),onClick:r((e=>We()),["stop"])},{default:u((()=>[c(Y(ne))])),_:1},8,["class","onClick"]))])),_:2},[e.$slots.prefix?{name:"prefix",fn:u((()=>[E(e.$slots,"prefix")]))}:void 0]),1032,["modelValue","onUpdate:modelValue","placeholder","readonly","disabled","size","class","tabindex","onCompositionstart","onCompositionupdate","onCompositionend"]),Y(Te)?(o(),n("div",{key:0,ref_key:"tagWrapper",ref:z,class:h([Y(S).e("tags"),Y(S).is("validate",Boolean(Y(Ie)))])},[(o(!0),n(v,null,C(he.value,(a=>(o(),d(Y(Ve),{key:a.key,type:e.tagType,size:Y(Se),effect:e.tagEffect,hit:a.hitState,closable:a.closable,"disable-transitions":"",onClose:e=>Ye(a)},{default:u((()=>[!1===a.isCollapseTag?(o(),n("span",{key:0},N(a.text),1)):(o(),d(Y(Le),{key:1,disabled:R.value||!e.collapseTagsTooltip,"fallback-placements":["bottom","top","right","left"],placement:"bottom",effect:"light"},{default:u((()=>[p("span",null,N(a.text),1)])),content:u((()=>[p("div",{class:h(Y(S).e("collapse-tags"))},[(o(!0),n(v,null,C(ve.value.slice(e.maxCollapseTags),((a,l)=>(o(),n("div",{key:l,class:h(Y(S).e("collapse-tag"))},[(o(),d(Y(Ve),{key:a.key,class:"in-tooltip",type:e.tagType,size:Y(Se),effect:e.tagEffect,hit:a.hitState,closable:a.closable,"disable-transitions":"",onClose:e=>Ye(a)},{default:u((()=>[p("span",null,N(a.text),1)])),_:2},1032,["type","size","effect","hit","closable","onClose"]))],2)))),128))],2)])),_:2},1032,["disabled"]))])),_:2},1032,["type","size","effect","hit","closable","onClose"])))),128)),e.filterable&&!Y(ke)?G((o(),n("input",{key:0,"onUpdate:modelValue":e=>pe.value=e,type:"text",class:h(Y(S).e("search-input")),placeholder:Y(qe)?"":Y(ye),onInput:e=>pa(pe.value,e),onClick:r((e=>We(!0)),["stop"]),onKeydown:oe(da,["delete"]),onCompositionstart:Y(j),onCompositionupdate:Y(j),onCompositionend:Y(j),onFocus:ca,onBlur:ra},null,42,["onUpdate:modelValue","placeholder","onInput","onClick","onKeydown","onCompositionstart","onCompositionupdate","onCompositionend"])),[[ie,pe.value]]):i("v-if",!0)],2)):i("v-if",!0)],46,["onClick","onMouseenter","onMouseleave"])),[[Y(_e),()=>We(!1),Y(Oe)]])])),content:u((()=>[G(c(Y(Qe),{ref_key:"cascaderPanelRef",ref:A,modelValue:Y(Ue),"onUpdate:modelValue":e=>ee(Ue)?Ue.value=e:null,options:e.options,props:s.props,border:!1,"render-label":e.$slots.default,onExpandChange:ta,onClose:a=>e.$nextTick((()=>We(!1)))},{empty:u((()=>[E(e.$slots,"empty")])),_:3},8,["modelValue","onUpdate:modelValue","options","props","render-label","onClose"]),[[ae,!O.value]]),e.filterable?G((o(),d(Y(ce),{key:0,ref_key:"suggestionPanel",ref:K,tag:"ul",class:h(Y(S).e("suggestion-panel")),"view-class":Y(S).e("suggestion-list"),onKeydown:ia},{default:u((()=>[fe.value.length?(o(!0),n(v,{key:0},C(fe.value,(a=>(o(),n("li",{key:a.uid,class:h([Y(S).e("suggestion-item"),Y(S).is("checked",a.checked)]),tabindex:-1,onClick:e=>(e=>{var a,l;const{checked:t}=e;Te.value?null==(a=A.value)||a.handleCheckChange(e,!t,!1):(!t&&(null==(l=A.value)||l.handleCheckChange(e,!0,!1)),We(!1))})(a)},[E(e.$slots,"suggestion-item",{item:a},(()=>[p("span",null,N(a.text),1),a.checked?(o(),d(Y(b),{key:0},{default:u((()=>[c(Y(g))])),_:1})):i("v-if",!0)]))],10,["onClick"])))),128)):E(e.$slots,"empty",{key:1},(()=>[p("li",{class:h(Y(S).e("empty-text"))},N(Y(T)("el.cascader.noMatch")),3)]))])),_:3},8,["class","view-class"])),[[ae,O.value]]):i("v-if",!0)])),_:3},8,["visible","teleported","popper-class","fallback-placements","placement","transition","persistent"]))}}),[["__file","cascader.vue"]]));export{Ye as E};
