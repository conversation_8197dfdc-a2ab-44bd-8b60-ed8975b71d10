import{H as e,b6 as t,L as a,t as o,ad as n,z as l,v as s,N as i,_ as r,d as u,bw as p,b9 as d,c,b as f,s as m,r as v,bx as x,by as h,bz as y,M as g,ab as b,I as w,o as S,a0 as C,ae as z,g as k,f as E,h as I,P,C as F,n as j,i as B,l as N,e as $,w as M,D as _,E as R,U as V,m as H,bA as T,j as L,ba as O,F as A,k as U,aa as W,q as K}from"./index.Dk5pbsTU.js";import{u as q}from"./index.C9UdVphc.js";import{U as D,I as Y,C as G}from"./event.BwRzfsZt.js";import{u as J}from"./index.DEKElSOG.js";import{u as Q,a as X}from"./use-form-item.DzRJVC1I.js";import{u as Z,a as ee}from"./use-form-common-props.CQPDkY7k.js";import{u as te,a as ae}from"./index.Vn8pbgQR.js";import{d as oe}from"./error.D_Dr4eZ1.js";const ne=()=>e&&/firefox/i.test(window.navigator.userAgent);let le;const se={height:"0",visibility:"hidden",overflow:ne()?"":"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0"},ie=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing"];function re(e,a=1,o){var n;le||(le=document.createElement("textarea"),document.body.appendChild(le));const{paddingSize:l,borderSize:s,boxSizing:i,contextStyle:r}=function(e){const t=window.getComputedStyle(e),a=t.getPropertyValue("box-sizing"),o=Number.parseFloat(t.getPropertyValue("padding-bottom"))+Number.parseFloat(t.getPropertyValue("padding-top")),n=Number.parseFloat(t.getPropertyValue("border-bottom-width"))+Number.parseFloat(t.getPropertyValue("border-top-width"));return{contextStyle:ie.map((e=>[e,t.getPropertyValue(e)])),paddingSize:o,borderSize:n,boxSizing:a}}(e);r.forEach((([e,t])=>null==le?void 0:le.style.setProperty(e,t))),Object.entries(se).forEach((([e,t])=>null==le?void 0:le.style.setProperty(e,t,"important"))),le.value=e.value||e.placeholder||"";let u=le.scrollHeight;const p={};"border-box"===i?u+=s:"content-box"===i&&(u-=l),le.value="";const d=le.scrollHeight-l;if(t(a)){let e=d*a;"border-box"===i&&(e=e+l+s),u=Math.max(e,u),p.minHeight=`${e}px`}if(t(o)){let e=d*o;"border-box"===i&&(e=e+l+s),u=Math.min(e,u)}return p.height=`${u}px`,null==(n=le.parentNode)||n.removeChild(le),le=void 0,p}const ue=o({id:{type:String,default:void 0},size:i,disabled:Boolean,modelValue:{type:l([String,Number,Object]),default:""},maxlength:{type:[String,Number]},minlength:{type:[String,Number]},type:{type:String,default:"text"},resize:{type:String,values:["none","both","horizontal","vertical"]},autosize:{type:l([Boolean,Object]),default:!1},autocomplete:{type:String,default:"off"},formatter:{type:Function},parser:{type:Function},placeholder:{type:String},form:{type:String},readonly:Boolean,clearable:Boolean,showPassword:Boolean,showWordLimit:Boolean,suffixIcon:{type:s},prefixIcon:{type:s},containerRole:{type:String,default:void 0},tabindex:{type:[String,Number],default:0},validateEvent:{type:Boolean,default:!0},inputStyle:{type:l([Object,Array,String]),default:()=>n({})},autofocus:Boolean,rows:{type:Number,default:2},...q(["ariaLabel"])}),pe={[D]:e=>a(e),input:e=>a(e),change:e=>a(e),focus:e=>e instanceof FocusEvent,blur:e=>e instanceof FocusEvent,clear:()=>!0,mouseleave:e=>e instanceof MouseEvent,mouseenter:e=>e instanceof MouseEvent,keydown:e=>e instanceof Event,compositionstart:e=>e instanceof CompositionEvent,compositionupdate:e=>e instanceof CompositionEvent,compositionend:e=>e instanceof CompositionEvent};const de=u({name:"ElInput",inheritAttrs:!1});const ce=K(r(u({...de,props:ue,emits:pe,setup(t,{expose:a,emit:o}){const n=t,l=p(),s=J(),i=d(),r=c((()=>["textarea"===n.type?ue.b():ie.b(),ie.m(le.value),ie.is("disabled",se.value),ie.is("exceed",Ne.value),{[ie.b("group")]:i.prepend||i.append,[ie.m("prefix")]:i.prefix||n.prefixIcon,[ie.m("suffix")]:i.suffix||n.suffixIcon||n.clearable||n.showPassword,[ie.bm("suffix","password-clear")]:Pe.value&&Fe.value,[ie.b("hidden")]:"hidden"===n.type},l.class])),u=c((()=>[ie.e("wrapper"),ie.is("focus",ye.value)])),{form:K,formItem:q}=Q(),{inputId:ne}=X(n,{formItemContext:q}),le=Z(),se=ee(),ie=f("input"),ue=f("textarea"),pe=m(),de=m(),ce=v(!1),fe=v(!1),me=v(),ve=m(n.inputStyle),xe=c((()=>pe.value||de.value)),{wrapperRef:he,isFocused:ye,handleFocus:ge,handleBlur:be}=te(xe,{beforeFocus:()=>se.value,afterBlur(){var e;n.validateEvent&&(null==(e=null==q?void 0:q.validate)||e.call(q,"blur").catch((e=>oe())))}}),we=c((()=>{var e;return null!=(e=null==K?void 0:K.statusIcon)&&e})),Se=c((()=>(null==q?void 0:q.validateState)||"")),Ce=c((()=>Se.value&&x[Se.value])),ze=c((()=>fe.value?h:y)),ke=c((()=>[l.style])),Ee=c((()=>[n.inputStyle,ve.value,{resize:n.resize}])),Ie=c((()=>g(n.modelValue)?"":String(n.modelValue))),Pe=c((()=>n.clearable&&!se.value&&!n.readonly&&!!Ie.value&&(ye.value||ce.value))),Fe=c((()=>n.showPassword&&!se.value&&!!Ie.value)),je=c((()=>n.showWordLimit&&!!n.maxlength&&("text"===n.type||"textarea"===n.type)&&!se.value&&!n.readonly&&!n.showPassword)),Be=c((()=>Ie.value.length)),Ne=c((()=>!!je.value&&Be.value>Number(n.maxlength))),$e=c((()=>!!i.suffix||!!n.suffixIcon||Pe.value||n.showPassword||je.value||!!Se.value&&we.value)),[Me,_e]=function(e){let t;return[function(){if(null==e.value)return;const{selectionStart:a,selectionEnd:o,value:n}=e.value;if(null==a||null==o)return;const l=n.slice(0,Math.max(0,a)),s=n.slice(Math.max(0,o));t={selectionStart:a,selectionEnd:o,value:n,beforeTxt:l,afterTxt:s}},function(){if(null==e.value||null==t)return;const{value:a}=e.value,{beforeTxt:o,afterTxt:n,selectionStart:l}=t;if(null==o||null==n||null==l)return;let s=a.length;if(a.endsWith(n))s=a.length-n.length;else if(a.startsWith(o))s=o.length;else{const e=o[l-1],t=a.indexOf(e,l-1);-1!==t&&(s=t+1)}e.value.setSelectionRange(s,s)}]}(pe);b(de,(e=>{if(Ve(),!je.value||"both"!==n.resize)return;const t=e[0],{width:a}=t.contentRect;me.value={right:`calc(100% - ${a+15+6}px)`}}));const Re=()=>{const{type:t,autosize:a}=n;if(e&&"textarea"===t&&de.value)if(a){const e=W(a)?a.minRows:void 0,t=W(a)?a.maxRows:void 0,o=re(de.value,e,t);ve.value={overflowY:"hidden",...o},C((()=>{de.value.offsetHeight,ve.value=o}))}else ve.value={minHeight:re(de.value).minHeight}},Ve=(e=>{let t=!1;return()=>{var a;if(t||!n.autosize)return;null===(null==(a=de.value)?void 0:a.offsetParent)||(e(),t=!0)}})(Re),He=()=>{const e=xe.value,t=n.formatter?n.formatter(Ie.value):Ie.value;e&&e.value!==t&&(e.value=t)},Te=async e=>{Me();let{value:t}=e.target;n.formatter&&n.parser&&(t=n.parser(t)),Oe.value||(t!==Ie.value?(o(D,t),o(Y,t),await C(),He(),_e()):He())},Le=e=>{let{value:t}=e.target;n.formatter&&n.parser&&(t=n.parser(t)),o(G,t)},{isComposing:Oe,handleCompositionStart:Ae,handleCompositionUpdate:Ue,handleCompositionEnd:We}=ae({emit:o,afterComposition:Te}),Ke=()=>{Me(),fe.value=!fe.value,setTimeout(_e)},qe=e=>{ce.value=!1,o("mouseleave",e)},De=e=>{ce.value=!0,o("mouseenter",e)},Ye=e=>{o("keydown",e)},Ge=()=>{o(D,""),o(G,""),o("clear"),o(Y,"")};return w((()=>n.modelValue),(()=>{var e;C((()=>Re())),n.validateEvent&&(null==(e=null==q?void 0:q.validate)||e.call(q,"change").catch((e=>oe())))})),w(Ie,(()=>He())),w((()=>n.type),(async()=>{await C(),He(),Re()})),S((()=>{!n.formatter&&n.parser,He(),C(Re)})),a({input:pe,textarea:de,ref:xe,textareaStyle:Ee,autosize:z(n,"autosize"),isComposing:Oe,focus:()=>{var e;return null==(e=xe.value)?void 0:e.focus()},blur:()=>{var e;return null==(e=xe.value)?void 0:e.blur()},select:()=>{var e;null==(e=xe.value)||e.select()},clear:Ge,resizeTextarea:Re}),(e,t)=>(E(),k("div",{class:j([B(r),{[B(ie).bm("group","append")]:e.$slots.append,[B(ie).bm("group","prepend")]:e.$slots.prepend}]),style:U(B(ke)),onMouseenter:De,onMouseleave:qe},[I(" input "),"textarea"!==e.type?(E(),k(P,{key:0},[I(" prepend slot "),e.$slots.prepend?(E(),k("div",{key:0,class:j(B(ie).be("group","prepend"))},[N(e.$slots,"prepend")],2)):I("v-if",!0),F("div",{ref_key:"wrapperRef",ref:he,class:j(B(u))},[I(" prefix slot "),e.$slots.prefix||e.prefixIcon?(E(),k("span",{key:0,class:j(B(ie).e("prefix"))},[F("span",{class:j(B(ie).e("prefix-inner"))},[N(e.$slots,"prefix"),e.prefixIcon?(E(),$(B(R),{key:0,class:j(B(ie).e("icon"))},{default:M((()=>[(E(),$(_(e.prefixIcon)))])),_:1},8,["class"])):I("v-if",!0)],2)],2)):I("v-if",!0),F("input",V({id:B(ne),ref_key:"input",ref:pe,class:B(ie).e("inner")},B(s),{minlength:e.minlength,maxlength:e.maxlength,type:e.showPassword?fe.value?"text":"password":e.type,disabled:B(se),readonly:e.readonly,autocomplete:e.autocomplete,tabindex:e.tabindex,"aria-label":e.ariaLabel,placeholder:e.placeholder,style:e.inputStyle,form:e.form,autofocus:e.autofocus,role:e.containerRole,onCompositionstart:B(Ae),onCompositionupdate:B(Ue),onCompositionend:B(We),onInput:Te,onChange:Le,onKeydown:Ye}),null,16,["id","minlength","maxlength","type","disabled","readonly","autocomplete","tabindex","aria-label","placeholder","form","autofocus","role","onCompositionstart","onCompositionupdate","onCompositionend"]),I(" suffix slot "),B($e)?(E(),k("span",{key:1,class:j(B(ie).e("suffix"))},[F("span",{class:j(B(ie).e("suffix-inner"))},[B(Pe)&&B(Fe)&&B(je)?I("v-if",!0):(E(),k(P,{key:0},[N(e.$slots,"suffix"),e.suffixIcon?(E(),$(B(R),{key:0,class:j(B(ie).e("icon"))},{default:M((()=>[(E(),$(_(e.suffixIcon)))])),_:1},8,["class"])):I("v-if",!0)],64)),B(Pe)?(E(),$(B(R),{key:1,class:j([B(ie).e("icon"),B(ie).e("clear")]),onMousedown:L(B(O),["prevent"]),onClick:Ge},{default:M((()=>[H(B(T))])),_:1},8,["class","onMousedown"])):I("v-if",!0),B(Fe)?(E(),$(B(R),{key:2,class:j([B(ie).e("icon"),B(ie).e("password")]),onClick:Ke},{default:M((()=>[(E(),$(_(B(ze))))])),_:1},8,["class"])):I("v-if",!0),B(je)?(E(),k("span",{key:3,class:j(B(ie).e("count"))},[F("span",{class:j(B(ie).e("count-inner"))},A(B(Be))+" / "+A(e.maxlength),3)],2)):I("v-if",!0),B(Se)&&B(Ce)&&B(we)?(E(),$(B(R),{key:4,class:j([B(ie).e("icon"),B(ie).e("validateIcon"),B(ie).is("loading","validating"===B(Se))])},{default:M((()=>[(E(),$(_(B(Ce))))])),_:1},8,["class"])):I("v-if",!0)],2)],2)):I("v-if",!0)],2),I(" append slot "),e.$slots.append?(E(),k("div",{key:1,class:j(B(ie).be("group","append"))},[N(e.$slots,"append")],2)):I("v-if",!0)],64)):(E(),k(P,{key:1},[I(" textarea "),F("textarea",V({id:B(ne),ref_key:"textarea",ref:de,class:[B(ue).e("inner"),B(ie).is("focus",B(ye))]},B(s),{minlength:e.minlength,maxlength:e.maxlength,tabindex:e.tabindex,disabled:B(se),readonly:e.readonly,autocomplete:e.autocomplete,style:B(Ee),"aria-label":e.ariaLabel,placeholder:e.placeholder,form:e.form,autofocus:e.autofocus,rows:e.rows,role:e.containerRole,onCompositionstart:B(Ae),onCompositionupdate:B(Ue),onCompositionend:B(We),onInput:Te,onFocus:B(ge),onBlur:B(be),onChange:Le,onKeydown:Ye}),null,16,["id","minlength","maxlength","tabindex","disabled","readonly","autocomplete","aria-label","placeholder","form","autofocus","rows","role","onCompositionstart","onCompositionupdate","onCompositionend","onFocus","onBlur"]),B(je)?(E(),k("span",{key:0,style:U(me.value),class:j(B(ie).e("count"))},A(B(Be))+" / "+A(e.maxlength),7)):I("v-if",!0)],64))],38))}}),[["__file","input.vue"]]));export{ce as E,ne as i};
