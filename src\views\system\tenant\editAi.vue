<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px">
    <el-form ref="editFormRef" :model="formData" :rules="rules" label-width="110px">
      <el-form-item label="AI助理数" prop="aiVestNum">
        <el-input-number v-model="formData.aiVestNum" :min="0" controls-position="right" />
      </el-form-item>

      <el-form-item label="负面信息预警" prop="isEarlyWarning">
        <el-switch v-model="formData.isEarlyWarning" :inactive-value="0" :active-value="1" />
      </el-form-item>
      <el-form-item label="主营范围" prop="businessScope">
        <el-input
          v-model="formData.businessScope"
          :maxlength="100"
          type="textarea"
          show-word-limit
          placeholder="请输入负责人姓名"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import TenantApi, { AiAuthorizeDto, TenantPageVO } from "@/api/system/tenant";

const dialog = reactive({
  visible: false,
  title: "AI授权",
});
const formData = ref(new AiAuthorizeDto());
const editFormRef = ref();
const emits = defineEmits(["success"]);
const loading = ref(false);
const rules = {
  aiVestNum: [{ required: true, message: "请输入AI马甲数", trigger: "blur" }],
};

function handleSubmit() {
  editFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      TenantApi.aiAuthorize(formData.value)
        .then(() => {
          ElMessage.success("保存成功");
          dialog.visible = false;
          emits("success");
        })
        .finally(() => (loading.value = false));
    }
  });
}

defineExpose({
  open: (_row?: TenantPageVO) => {
    dialog.visible = true;
    formData.value = new AiAuthorizeDto(_row);
  },
});
</script>

<style scoped lang="scss"></style>
