<script setup lang="ts">
import livePreview, { PageSessionUserDto } from "@/api/live/livePreview";
import IScrollList from "@/components/IScrollList/index.vue";
import useClipboard from "vue-clipboard3";
import dayjs from "dayjs";

const { toClipboard } = useClipboard();
const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
});
const userListRef = ref<any>();
const userListQuery = ref<PageSessionUserDto>(new PageSessionUserDto());
watch(
  () => props.info.id,
  (_newVal: number) => {
    if (_newVal) {
      userListQuery.value.sessionId = String(props.info.id);
      userListQuery.value.type = "1";
      userListQuery.value.pageNum = 1;
      nextTick(() => {
        userListRef.value?.reset();
      });
    }
  },
  { deep: true, immediate: true }
);

function onCopyId(_id: number) {
  toClipboard(String(_id))
    .then(() => {
      ElMessage.success("复制成功");
    })
    .catch(() => {
      ElMessage.error("复制失败");
    });
}

function convertSecondsToHMS(seconds: number) {
  const dur = dayjs.duration(seconds, "seconds");
  const hours = Math.floor(dur.asHours());
  const minutes = dur.minutes();
  const secs = dur.seconds();

  if (hours > 0) {
    return `${hours}h${minutes}min`;
  } else if (minutes > 0) {
    return `${minutes}min`;
  } else {
    return `${secs}s`;
  }
}
</script>

<template>
  <div style="width: 100%; height: 100%; background: #f8f9fa">
    <i-scroll-list
      ref="userListRef"
      v-model="userListQuery"
      class="user-scroll-list"
      style="
        --i-scroll-list-padding: 10px 10px 0 10px;
        --i-scroll-list-divider-bg: #f8f9fa;
        --i-scroll-list-item-margin-bottom: 6px;
      "
      :api="livePreview.sessionUser as any"
    >
      <template #default="{ item, itemIndex }">
        <div class="user-item" :class="`user-item-${itemIndex + 1}`">
          <div class="user-item-index">
            <span v-if="itemIndex > 2">{{ itemIndex + 1 }}</span>
            <img
              v-else-if="itemIndex === 0"
              class="user-item-index-icon"
              src="@/assets/icons/ph1.png"
            />
            <img
              v-else-if="itemIndex === 1"
              class="user-item-index-icon"
              src="@/assets/icons/ph2.png"
            />
            <img
              v-else-if="itemIndex === 2"
              class="user-item-index-icon"
              src="@/assets/icons/ph3.png"
            />
          </div>
          <div class="user-item-info">
            <img class="avatar" :src="(item as any).avatar" />
            <div class="user-info">
              <div class="id-info">
                UID：{{ (item as any).userId }}
                <el-icon
                  style="color: var(--el-color-primary); margin: 0 12px 0 3px; cursor: pointer"
                  @click="onCopyId((item as any).userId)"
                >
                  <CopyDocument />
                </el-icon>
                <div style="width: 0; flex: 1">
                  <div style="width: 100%">
                    <el-scrollbar style="width: 100%" :scroll-x="true">
                      <div style="display: inline-flex">
                        <div
                          v-for="(tag, index) in (item as any).tagIds || []"
                          :key="index"
                          class="tag"
                        >
                          {{ tag.name }}
                        </div>
                      </div>
                    </el-scrollbar>
                  </div>
                </div>
              </div>
              <div class="other-info">
                <div class="name">
                  {{ (item as any).nickname }}
                  <div v-if="(item as any).inviterName" class="inviter-name">
                    {{ (item as any).inviterName }}
                  </div>
                </div>
                <div class="address" style="min-width: 120px">
                  IP地址：
                  <span class="address-info">{{ (item as any).ipAddress }}</span>
                  <span class="address-info" style="margin-left: 8px">
                    {{ (item as any).province }}|{{ (item as any).city }}
                  </span>
                </div>
                <div class="address" style="min-width: 120px">
                  累计观看：
                  <span class="address-info">
                    {{ convertSecondsToHMS((item as any).duration) }}
                  </span>
                </div>
                <div class="address" style="min-width: 120px">
                  弹幕数量：
                  <span class="address-info">{{ (item as any).commentCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </i-scroll-list>
  </div>
</template>

<style scoped lang="scss">
.user-item {
  padding: 14px 18px;
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 6px;

  &-index {
    width: 20px;
    height: 21px;
    line-height: 21px;
    text-align: center;
    font-weight: 400;
    font-size: 12px;
    color: #3d3d3d;

    &-icon {
      width: 100%;
      height: 100%;
    }
  }

  &-info {
    display: flex;
    margin-left: 10px;
    width: 0;
    flex: 1;

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 32px;
      margin-right: 17px;
    }

    .user-info {
      flex: 1;

      .id-info {
        font-weight: 400;
        font-size: 10px;
        color: #666666;
        width: 100%;
        display: flex;
        align-items: center;

        .tag {
          height: 13px;
          padding: 0 5px;
          background: #4cce9c;
          border-radius: 9px;
          margin-right: 4px;
          font-weight: 400;
          font-size: 10px;
          color: #ffffff;
          text-align: center;
          line-height: 13px;
        }
      }

      .other-info {
        display: flex;
        margin-top: 5px;
        align-items: flex-end;
        justify-content: space-between;

        .name {
          font-weight: 500;
          font-size: 12px;
          color: #333333;
          display: flex;
          width: 150px;
        }

        .inviter-name {
          color: var(--el-color-primary);
          margin-left: 10px;
          font-weight: 500;
          font-size: 10px;
        }

        .address {
          font-weight: 400;
          font-size: 10px;
          color: #999999;

          &-info {
            font-weight: 400;
            font-size: 10px;
            color: #333333;
          }
        }
      }
    }
  }
}

.user-item-1 {
  background: linear-gradient(270deg, #ffffff 0%, #fff4e6 100%);
}

.user-item-2 {
  background: linear-gradient(270deg, #ffffff 0%, #ddeafc 100%);
}

.user-item-3 {
  background: linear-gradient(270deg, #ffffff 0%, #ffedef 100%);
}
</style>
