import{c as o,B as s,r as e,bD as i,A as l,i as r}from"./index.Dk5pbsTU.js";const a=Symbol("formContextKey"),d=Symbol("formItemContextKey"),n=e=>{const i=s();return o((()=>{var o,s;return null==(s=null==(o=null==i?void 0:i.proxy)?void 0:o.$props)?void 0:s[e]}))},t=(s,t={})=>{const v=e(void 0),u=t.prop?v:n("size"),m=t.global?v:i(),b=t.form?{size:void 0}:l(a,void 0),p=t.formItem?{size:void 0}:l(d,void 0);return o((()=>u.value||r(s)||(null==p?void 0:p.size)||(null==b?void 0:b.size)||m.value||""))},v=s=>{const e=n("disabled"),i=l(a,void 0);return o((()=>e.value||r(s)||(null==i?void 0:i.disabled)||!1))};export{v as a,a as b,n as c,d as f,t as u};
