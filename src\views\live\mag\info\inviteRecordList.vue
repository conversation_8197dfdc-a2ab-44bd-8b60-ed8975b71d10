<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="转发人" align="center" prop="nickName" min-width="120" />
        <el-table-column label="转发数" align="center" prop="sharedCount" min-width="120" />
        <el-table-column label="引流数" align="center" prop="inviteCount" min-width="120">
          <template #default="{ row }">
            <el-link type="primary" @click="$refs?.inviteCountLogRef?.open(row)">
              {{ row.inviteCount }}
            </el-link>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
    <invite-count-log ref="inviteCountLogRef" :sessionId="sessionId" />
  </div>
</template>
<script setup lang="ts">
import LiveSessionApi, { InviteRecordPageDto, PageInviteRecordVo } from "@/api/live/liveSession";
import { usePage } from "@/utils/commonSetup";
import InviteCountLog from "@/views/live/mag/info/inviteCountLog.vue";

const props = defineProps({
  sessionId: {
    type: Number,
    default: -1,
  },
});
const { page, getPage } = usePage<InviteRecordPageDto, PageInviteRecordVo>(
  new InviteRecordPageDto(props.sessionId),
  LiveSessionApi.inviteRecordList
);
</script>
