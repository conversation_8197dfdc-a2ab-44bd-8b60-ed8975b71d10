import{d as e,g as t,f as a,C as r,m as o,w as i,Z as s,i as l,$ as p,V as n,e as m,h as d,F as u}from"./index.Dk5pbsTU.js";import{v as j}from"./el-loading.Dqi-qL7c.js";import{E as c}from"./el-card.DwLhVNHW.js";import _ from"./index.Cywy93e7.js";import{a as f,E as g}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{_ as y}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as w}from"./el-image-viewer.BH897zgF.js";/* empty css               */import{a as b,E as h}from"./el-form-item.Bw6Zyv_7.js";import{E as x}from"./el-button.CXI119n4.js";import{_ as v}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as k}from"./el-input.DiGatoux.js";import{T as q,A as U}from"./tenant.BHgTweuE.js";import{u as V}from"./commonSetup.Dm-aByKQ.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./event.BwRzfsZt.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.DuiNpp1i.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./position.DfR5znly.js";import"./el-radio.w2rep3_A.js";import"./index.C-k5aYA-.js";const A={class:"app-container"},C={class:"search-bar"},E=e({name:"AppUser",__name:"index",setup(e){const{page:E,getPage:P,resetQuery:R}=V(new U,q.appUserPage);return(e,q)=>{const U=k,V=b,N=v,K=x,S=h,F=g,I=w,L=y,O=f,T=_,z=c,D=j;return a(),t("div",A,[r("div",C,[o(S,{ref:"queryFormRef",model:l(E).query,inline:!0},{default:i((()=>[o(V,{prop:"keywords",label:""},{default:i((()=>[o(U,{modelValue:l(E).query.keywords,"onUpdate:modelValue":q[0]||(q[0]=e=>l(E).query.keywords=e),placeholder:"昵称、账号检索",clearable:"",onKeyup:s(l(P),["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),o(V,{prop:"isAssistant",label:"是否是助理"},{default:i((()=>[o(N,{modelValue:l(E).query.isAssistant,"onUpdate:modelValue":q[1]||(q[1]=e=>l(E).query.isAssistant=e),code:"yes_or_no"},null,8,["modelValue"])])),_:1}),o(V,null,{default:i((()=>[o(K,{type:"primary",icon:"search",onClick:l(P)},{default:i((()=>q[5]||(q[5]=[p("搜索")]))),_:1,__:[5]},8,["onClick"]),o(K,{icon:"refresh",onClick:l(R)},{default:i((()=>q[6]||(q[6]=[p("重置")]))),_:1,__:[6]},8,["onClick"])])),_:1})])),_:1},8,["model"])]),o(z,{shadow:"never"},{default:i((()=>[n((a(),m(O,{ref:"dataTableRef",data:l(E).data.records,"highlight-current-row":"",border:""},{default:i((()=>[o(F,{label:"序号",align:"center",width:"55",type:"index"}),o(F,{label:"头像",align:"center",prop:"name",width:"100"},{default:i((({row:e})=>[o(I,{style:{width:"30px",height:"30px","border-radius":"30px"},"preview-src-list":[e.avatar],"preview-teleported":"",src:e.avatar},null,8,["preview-src-list","src"])])),_:1}),o(F,{label:"昵称",align:"center",prop:"nickname","min-width":"120"}),o(F,{label:"助理信息",align:"center"},{default:i((()=>[o(F,{label:"是否是助理",align:"center",width:"80"},{default:i((({row:e})=>[o(L,{modelValue:e.isAssistant,"onUpdate:modelValue":t=>e.isAssistant=t,code:"yes_or_no"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),o(F,{label:"归属公司",align:"center",prop:"nickName","min-width":"100"},{default:i((({row:e})=>[p(u(e.isAssistant?e.tenantName:"-"),1)])),_:1})])),_:1}),o(F,{label:"最近登录时间",align:"center",prop:"loginDate","min-width":"100"}),o(F,{label:"账号",align:"center",prop:"username","min-width":"100"})])),_:1},8,["data"])),[[D,l(E).loading]]),l(E).data.totalRow?(a(),m(T,{key:0,total:l(E).data.totalRow,"onUpdate:total":q[2]||(q[2]=e=>l(E).data.totalRow=e),page:l(E).query.pageNum,"onUpdate:page":q[3]||(q[3]=e=>l(E).query.pageNum=e),limit:l(E).query.pageSize,"onUpdate:limit":q[4]||(q[4]=e=>l(E).query.pageSize=e),onPagination:l(P)},null,8,["total","page","limit","onPagination"])):d("",!0)])),_:1})])}}});export{E as default};
