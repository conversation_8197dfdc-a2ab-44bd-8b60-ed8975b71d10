import{B as e,s as o,r as t,I as a,u as n,br as u,a0 as l}from"./index.Dk5pbsTU.js";import{c as s}from"./use-form-common-props.CQPDkY7k.js";function i(l,{beforeFocus:i,afterFocus:r,beforeBlur:c,afterBlur:d}={}){const m=e(),{emit:v}=m,p=o(),f=s("disabled"),b=t(!1),h=e=>{!!u(i)&&i(e)||b.value||(b.value=!0,v("focus",e),null==r||r())},C=e=>{var o;!!u(c)&&c(e)||e.relatedTarget&&(null==(o=p.value)?void 0:o.contains(e.relatedTarget))||(b.value=!1,v("blur",e),null==d||d())};return a([p,f],(([e,o])=>{e&&(o?e.removeAttribute("tabindex"):e.setAttribute("tabindex","-1"))})),n(p,"focus",h,!0),n(p,"blur",C,!0),n(p,"click",(()=>{var e,o;(null==(e=p.value)?void 0:e.contains(document.activeElement))&&p.value!==document.activeElement||f.value||null==(o=l.value)||o.focus()}),!0),{isFocused:b,wrapperRef:p,handleFocus:h,handleBlur:C}}function r({afterComposition:e,emit:o}){const a=t(!1),n=e=>{var t;null==o||o("compositionupdate",e);const n=null==(t=e.target)?void 0:t.value,u=n[n.length-1]||"";a.value=!(e=>/([\uAC00-\uD7AF\u3130-\u318F])+/gi.test(e))(u)},u=t=>{null==o||o("compositionend",t),a.value&&(a.value=!1,l((()=>e(t))))};return{isComposing:a,handleComposition:e=>{"compositionend"===e.type?u(e):n(e)},handleCompositionStart:e=>{null==o||o("compositionstart",e),a.value=!0},handleCompositionUpdate:n,handleCompositionEnd:u}}export{r as a,i as u};
