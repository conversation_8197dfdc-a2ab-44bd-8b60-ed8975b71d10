<template>
  <el-drawer
    v-model="isShow"
    class="live-mag-info-drawer"
    :title="info.title + '详情'"
    @close="info = {} as any"
    size="80vw"
  >
    <el-tabs v-model="tabIndex" class="info-tabs" type="border-card">
      <el-tab-pane label="基础信息" name="base">
        <base-info v-if="info.id" :session-id="info.id" />
      </el-tab-pane>
      <el-tab-pane
        v-if="info.status === 2"
        :label="`观看用户（${info.watchCount}）`"
        name="lookUser"
      >
        <look-user v-if="info.id" :session-id="info.id" />
      </el-tab-pane>
      <!--      <el-tab-pane-->
      <!--        v-if="info.status === 2"-->
      <!--        :label="`发言记录（${info.commentCount}）`"-->
      <!--        name="comment"-->
      <!--      >-->
      <!--        <comment-list v-if="info.id" :session-id="info.id" />-->
      <!--      </el-tab-pane>-->
      <el-tab-pane v-if="info.status === 2" label="引流总览" name="invite">
        <invite-record-list v-if="info.id" :session-id="info.id" />
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>

<script setup lang="ts">
import { LiveSessionPageVo } from "@/api/live/liveSession";
import BaseInfo from "./base.vue";
import LookUser from "./lookUser.vue";
import CommentList from "@/views/live/mag/info/commentList.vue";
import InviteRecordList from "@/views/live/mag/info/inviteRecordList.vue";

const isShow = ref(false);
const info = ref<LiveSessionPageVo>({ id: 0 } as LiveSessionPageVo);
const tabIndex = ref("base");
defineExpose({
  open: (data: LiveSessionPageVo) => {
    info.value = JSON.parse(JSON.stringify(data));
    tabIndex.value = "base";
    isShow.value = true;
  },
});
</script>
<style lang="scss">
.live-mag-info-drawer {
  & > .el-drawer__header {
    margin-bottom: 10px;
  }

  & > .el-drawer__body {
    padding: 0;
  }
}
</style>
<style lang="scss" scoped>
.info-tabs {
  height: 100%;

  :deep(.el-tabs__content) {
    height: calc(100% - 40px);
  }
}
</style>
