import{aT as e,d as t,r,S as a,o,g as i,f as s,C as l,m as p,w as m,Z as n,i as d,$ as u,V as j,e as c,h as g,ak as f}from"./index.Dk5pbsTU.js";import{v as h}from"./el-loading.Dqi-qL7c.js";import{E as b}from"./el-card.DwLhVNHW.js";import _ from"./index.Cywy93e7.js";import{a as w,E as v}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     *//* empty css               */import{a as x,E as y}from"./el-form-item.Bw6Zyv_7.js";import{E as k}from"./el-button.CXI119n4.js";import{E as T}from"./el-date-picker.B6TshyBV.js";import{E as V}from"./el-input.DiGatoux.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./event.BwRzfsZt.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.DuiNpp1i.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./dayjs.min.D7B9fRnU.js";import"./arrays.CygeFE-H.js";import"./index.Cd8M2JyP.js";const U="/api/v1/logs",E={getPage:t=>e({url:`${U}/page`,method:"get",params:t}),getVisitTrend:t=>e({url:`${U}/visit-trend`,method:"get",params:t}),getVisitStats:()=>e({url:`${U}/visit-stats`,method:"get"})},P={class:"app-container"},S={class:"search-bar"},$=t({name:"Log",inheritAttrs:!1,__name:"index",setup(e){const t=r(),U=r(!1),$=r(0),C=a({pageNum:1,pageSize:10,keywords:"",createTime:["",""]}),Y=r();function A(){U.value=!0,E.getPage(C).then((e=>{Y.value=e.list,$.value=e.total})).finally((()=>{U.value=!1}))}function I(){t.value.resetFields(),C.pageNum=1,C.createTime=void 0,A()}return o((()=>{A()})),(e,r)=>{const a=V,o=x,E=T,M=k,N=y,q=v,z=w,K=_,L=b,D=h;return s(),i("div",P,[l("div",S,[p(N,{ref_key:"queryFormRef",ref:t,model:d(C),inline:!0},{default:m((()=>[p(o,{prop:"keywords",label:"关键字"},{default:m((()=>[p(a,{modelValue:d(C).keywords,"onUpdate:modelValue":r[0]||(r[0]=e=>d(C).keywords=e),placeholder:"日志内容",clearable:"",onKeyup:n(A,["enter"])},null,8,["modelValue"])])),_:1}),p(o,{prop:"createTime",label:"操作时间"},{default:m((()=>[p(E,{modelValue:d(C).createTime,"onUpdate:modelValue":r[1]||(r[1]=e=>d(C).createTime=e),editable:!1,class:"!w-[240px]",type:"daterange","range-separator":"~","start-placeholder":"开始时间","end-placeholder":"截止时间","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),p(o,null,{default:m((()=>[p(M,{type:"primary",icon:"search",onClick:A},{default:m((()=>r[5]||(r[5]=[u("搜索")]))),_:1,__:[5]}),p(M,{icon:"refresh",onClick:I},{default:m((()=>r[6]||(r[6]=[u("重置")]))),_:1,__:[6]})])),_:1})])),_:1},8,["model"])]),p(L,{shadow:"never"},{default:m((()=>[j((s(),c(z,{data:d(Y),"highlight-current-row":"",border:""},{default:m((()=>[p(q,{label:"操作时间",prop:"createdAt",width:"180"}),p(q,{label:"操作人",prop:"operator",width:"120"}),p(q,{label:"日志模块",prop:"module",width:"100"}),p(q,{label:"日志内容",prop:"content","min-width":"200"}),p(q,{label:"IP 地址",prop:"ip",width:"150"}),p(q,{label:"地区",prop:"region",width:"150"}),p(q,{label:"浏览器",prop:"browser",width:"150"}),p(q,{label:"终端系统",prop:"os",width:"200","show-overflow-tooltip":""}),p(q,{label:"执行时间(ms)",prop:"executionTime",width:"150"})])),_:1},8,["data"])),[[D,d(U)]]),d($)>0?(s(),c(K,{key:0,total:d($),"onUpdate:total":r[2]||(r[2]=e=>f($)?$.value=e:null),page:d(C).pageNum,"onUpdate:page":r[3]||(r[3]=e=>d(C).pageNum=e),limit:d(C).pageSize,"onUpdate:limit":r[4]||(r[4]=e=>d(C).pageSize=e),onPagination:A},null,8,["total","page","limit"])):g("",!0)])),_:1})])}}});export{$ as default};
