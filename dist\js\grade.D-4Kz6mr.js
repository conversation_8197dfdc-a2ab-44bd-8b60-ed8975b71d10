var t=Object.defineProperty,e=(e,s,i)=>((e,s,i)=>s in e?t(e,s,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[s]=i)(e,"symbol"!=typeof s?s+"":s,i);import{aT as s}from"./index.Dk5pbsTU.js";class i{constructor(){e(this,"pageNum",1),e(this,"pageSize",10),e(this,"level",""),e(this,"startTime",""),e(this,"endTime","")}}class a{constructor(){e(this,"pageNum",1),e(this,"pageSize",10),e(this,"type",""),e(this,"startTime",""),e(this,"endTime",""),e(this,"id",0)}}class r{constructor(t){e(this,"startPoints",""),e(this,"endPoints",""),e(this,"levelName",""),t&&(this.startPoints=t.startPoints.toString(),this.endPoints=(t.endPoints||"").toString(),this.levelName=t.levelName)}}const o={configList:()=>s({url:"/grade/config",method:"get"}),editConfig:t=>s({url:"/grade/edit/config",method:"put",data:{leveConfigs:t}}),userList:t=>s({url:"/grade/userList",method:"get",params:t}),pointsPage:t=>s({url:"/grade/detail",method:"get",params:t}),pointTypeList:()=>s({url:"/grade/point/type",method:"get"})};export{r as G,a as P,o as a,i as b};
