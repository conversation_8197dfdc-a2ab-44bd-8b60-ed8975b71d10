import{t as e,v as n,_ as l,d as a,bf as s,c as i,bb as r,b as t,g as o,f as d,e as u,h as v,l as f,w as c,D as p,i as h,E as y,n as m,q as b}from"./index.Dk5pbsTU.js";import{u as g}from"./index.DuiNpp1i.js";const k=e({type:{type:String,values:["primary","success","warning","info","danger","default"],default:void 0},underline:{type:[Boolean,String],values:[!0,!1,"always","never","hover"],default:void 0},disabled:Boolean,href:{type:String,default:""},target:{type:String,default:"_self"},icon:{type:n}}),w={click:e=>e instanceof MouseEvent},S=a({name:"ElLink"});const _=b(l(a({...S,props:k,emits:w,setup(e,{emit:n}){const l=e,a=s("link");g({scope:"el-link",from:"The underline option (boolean)",replacement:"'always' | 'hover' | 'never'",version:"3.0.0",ref:"https://element-plus.org/en-US/component/link.html#underline"},i((()=>r(l.underline))));const b=t("link"),k=i((()=>{var e,n,s;return[b.b(),b.m(null!=(s=null!=(n=l.type)?n:null==(e=a.value)?void 0:e.type)?s:"default"),b.is("disabled",l.disabled),b.is("underline","always"===w.value),b.is("hover-underline","hover"===w.value&&!l.disabled)]})),w=i((()=>{var e,n,s;return r(l.underline)?l.underline?"hover":"never":null!=(s=null!=(n=l.underline)?n:null==(e=a.value)?void 0:e.underline)?s:"hover"}));function S(e){l.disabled||n("click",e)}return(e,n)=>(d(),o("a",{class:m(h(k)),href:e.disabled||!e.href?void 0:e.href,target:e.disabled||!e.href?void 0:e.target,onClick:S},[e.icon?(d(),u(h(y),{key:0},{default:c((()=>[(d(),u(p(e.icon)))])),_:1})):v("v-if",!0),e.$slots.default?(d(),o("span",{key:1,class:m(h(b).e("inner"))},[f(e.$slots,"default")],2)):v("v-if",!0),e.$slots.icon?f(e.$slots,"icon",{key:2}):v("v-if",!0)],10,["href","target"]))}}),[["__file","link.vue"]]));export{_ as E};
