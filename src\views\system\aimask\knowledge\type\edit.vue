<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px">
    <el-form
      ref="editFormRef"
      :model="formData"
      label-position="top"
      :rules="rules"
      label-width="140px"
    >
      <el-form-item label="类型名称" prop="name">
        <el-input
          v-model="formData.name"
          :maxlength="50"
          show-word-limit
          placeholder="请输入类型名称"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import KnowledgeTypeApi, {
  KnowledgeTypePageVo,
  SaveOrEditKnowledgeTypeDto,
} from "@/api/system/knowledgeType";

const dialog = reactive({
  visible: false,
  title: "",
});
const formData = ref(new SaveOrEditKnowledgeTypeDto());
const editFormRef = ref();
const emits = defineEmits(["success"]);
const loading = ref(false);
const rules = {
  name: [{ required: true, message: "请输入类型名称", trigger: "blur" }],
};

function handleSubmit() {
  editFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      KnowledgeTypeApi.saveOrEdit(formData.value)
        .then(() => {
          ElMessage.success("保存成功");
          dialog.visible = false;
          emits("success");
        })
        .finally(() => (loading.value = false));
    }
  });
}

defineExpose({
  open: (_row?: KnowledgeTypePageVo) => {
    dialog.visible = true;
    dialog.title = _row?.id ? "编辑知识库类型" : "新增知识库类型";
    formData.value = new SaveOrEditKnowledgeTypeDto(_row);
  },
});
</script>
