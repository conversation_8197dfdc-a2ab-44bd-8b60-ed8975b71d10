import{t as e,z as o,_ as n,d as t,l,r,A as a,c as i,i as s,y as d,ae as u,aK as c,I as p,u as v,ap as f,e as m,f as g,w as b,m as w,au as h,av as I,g as y,h as E,aL as F,U as C,n as _,W as k,E as R,b as x,x as T,J as S,a6 as B,B as D,K as $,a0 as P,C as M,D as K,j as L,P as G,k as z,q as O,G as A,aM as j,al as H,ax as N,Q as J,$ as U,F as W,az as Y}from"./index.Dk5pbsTU.js";import{E as q}from"./el-button.CXI119n4.js";/* empty css                        */import{c as Z,O as Q,E as V,w as X}from"./el-popper.Dbn4MgsT.js";/* empty css                     */import{E as ee}from"./index.ybpLT-bz.js";import{c as oe,d as ne,E as te,a as le,C as re,b as ae,e as ie,f as se,g as de,F as ue,L as ce}from"./dropdown.B_OfpyL_.js";import{u as pe}from"./index.D6CER_Ot.js";import{u as ve}from"./use-form-common-props.CQPDkY7k.js";import{c as fe}from"./castArray.C4RhTg2c.js";import{c as me}from"./refs.BzVvuxps.js";import{F as ge}from"./index.C6NthMtN.js";const be=e({style:{type:o([String,Array,Object])},currentTabId:{type:o(String)},defaultCurrentTabId:String,loop:Boolean,dir:{type:String,values:["ltr","rtl"],default:"ltr"},orientation:{type:o(String)},onBlur:Function,onFocus:Function,onMousedown:Function}),{ElCollection:we,ElCollectionItem:he,COLLECTION_INJECTION_KEY:Ie,COLLECTION_ITEM_INJECTION_KEY:ye}=oe("RovingFocusGroup"),Ee=Symbol("elRovingFocusGroup"),Fe=Symbol("elRovingFocusGroupItem"),Ce={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"},_e=e=>{const{activeElement:o}=document;for(const n of e){if(n===o)return;if(n.focus(),o!==document.activeElement)return}},ke="currentTabIdChange",Re="rovingFocusGroup.entryFocus",xe={bubbles:!1,cancelable:!0},Te=t({name:"ElRovingFocusGroupImpl",inheritAttrs:!1,props:be,emits:[ke,"entryFocus"],setup(e,{emit:o}){var n;const t=r(null!=(n=e.currentTabId||e.defaultCurrentTabId)?n:null),l=r(!1),f=r(!1),m=r(),{getItems:g}=a(Ie,void 0),b=i((()=>[{outline:"none"},e.style])),w=Z((o=>{var n;null==(n=e.onMousedown)||n.call(e,o)}),(()=>{f.value=!0})),h=Z((o=>{var n;null==(n=e.onFocus)||n.call(e,o)}),(e=>{const o=!s(f),{target:n,currentTarget:r}=e;if(n===r&&o&&!s(l)){const e=new Event(Re,xe);if(null==r||r.dispatchEvent(e),!e.defaultPrevented){const e=g().filter((e=>e.focusable)),o=[e.find((e=>e.active)),e.find((e=>e.id===s(t))),...e].filter(Boolean).map((e=>e.ref));_e(o)}}f.value=!1})),I=Z((o=>{var n;null==(n=e.onBlur)||n.call(e,o)}),(()=>{l.value=!1}));d(Ee,{currentTabbedId:c(t),loop:u(e,"loop"),tabIndex:i((()=>s(l)?-1:0)),rovingFocusGroupRef:m,rovingFocusGroupRootStyle:b,orientation:u(e,"orientation"),dir:u(e,"dir"),onItemFocus:e=>{o(ke,e)},onItemShiftTab:()=>{l.value=!0},onBlur:I,onFocus:h,onMousedown:w}),p((()=>e.currentTabId),(e=>{t.value=null!=e?e:null})),v(m,Re,((...e)=>{o("entryFocus",...e)}))}});var Se=n(t({name:"ElRovingFocusGroup",components:{ElFocusGroupCollection:we,ElRovingFocusGroupImpl:n(Te,[["render",function(e,o,n,t,r,a){return l(e.$slots,"default")}],["__file","roving-focus-group-impl.vue"]])}}),[["render",function(e,o,n,t,r,a){const i=f("el-roving-focus-group-impl"),s=f("el-focus-group-collection");return g(),m(s,null,{default:b((()=>[w(i,h(I(e.$attrs)),{default:b((()=>[l(e.$slots,"default")])),_:3},16)])),_:3})}],["__file","roving-focus-group.vue"]]);const Be=Symbol("elDropdown"),{ButtonGroup:De}=q;var $e=n(t({name:"ElDropdown",components:{ElButton:q,ElButtonGroup:De,ElScrollbar:ee,ElDropdownCollection:te,ElTooltip:V,ElRovingFocusGroup:Se,ElOnlyChild:Q,ElIcon:R,ArrowDown:k},props:ne,emits:["visible-change","click","command"],setup(e,{emit:o}){const n=D(),t=x("dropdown"),{t:l}=T(),a=r(),c=r(),v=r(),f=r(),m=r(null),g=r(null),b=r(!1),w=i((()=>({maxHeight:S(e.maxHeight)}))),h=i((()=>[t.m(C.value)])),I=i((()=>fe(e.trigger))),y=pe().value,E=i((()=>e.id||y));function F(){var e;null==(e=v.value)||e.onClose()}p([a,I],(([e,o],[n])=>{var t,l,r;(null==(t=null==n?void 0:n.$el)?void 0:t.removeEventListener)&&n.$el.removeEventListener("pointerenter",_),(null==(l=null==e?void 0:e.$el)?void 0:l.removeEventListener)&&e.$el.removeEventListener("pointerenter",_),(null==(r=null==e?void 0:e.$el)?void 0:r.addEventListener)&&o.includes("hover")&&e.$el.addEventListener("pointerenter",_)}),{immediate:!0}),B((()=>{var e,o;(null==(o=null==(e=a.value)?void 0:e.$el)?void 0:o.removeEventListener)&&a.value.$el.removeEventListener("pointerenter",_)}));const C=ve();function _(){var e,o;null==(o=null==(e=a.value)?void 0:e.$el)||o.focus()}d(Be,{contentRef:f,role:i((()=>e.role)),triggerId:E,isUsingKeyboard:b,onItemEnter:function(){},onItemLeave:function(){const e=s(f);I.value.includes("hover")&&(null==e||e.focus()),g.value=null}}),d("elDropdown",{instance:n,dropdownSize:C,handleClick:function(){F()},commandHandler:function(...e){o("command",...e)},trigger:u(e,"trigger"),hideOnClick:u(e,"hideOnClick")});return{t:l,ns:t,scrollbar:m,wrapStyle:w,dropdownTriggerKls:h,dropdownSize:C,triggerId:E,currentTabId:g,handleCurrentTabIdChange:function(e){g.value=e},handlerMainButtonClick:e=>{o("click",e)},handleEntryFocus:function(e){b.value||(e.preventDefault(),e.stopImmediatePropagation())},handleClose:F,handleOpen:function(){var e;null==(e=v.value)||e.onOpen()},handleBeforeShowTooltip:function(){o("visible-change",!0)},handleShowTooltip:function(e){var o;"keydown"===(null==e?void 0:e.type)&&(null==(o=f.value)||o.focus())},handleBeforeHideTooltip:function(){o("visible-change",!1)},onFocusAfterTrapped:e=>{var o,n;e.preventDefault(),null==(n=null==(o=f.value)?void 0:o.focus)||n.call(o,{preventScroll:!0})},popperRef:v,contentRef:f,triggeringElementRef:a,referenceElementRef:c}}}),[["render",function(e,o,n,t,r,a){var i;const s=f("el-dropdown-collection"),d=f("el-roving-focus-group"),u=f("el-scrollbar"),c=f("el-only-child"),p=f("el-tooltip"),v=f("el-button"),h=f("arrow-down"),I=f("el-icon"),k=f("el-button-group");return g(),y("div",{class:_([e.ns.b(),e.ns.is("disabled",e.disabled)])},[w(p,{ref:"popperRef",role:e.role,effect:e.effect,"fallback-placements":["bottom","top"],"popper-options":e.popperOptions,"gpu-acceleration":!1,"hide-after":"hover"===e.trigger?e.hideTimeout:0,"manual-mode":!0,placement:e.placement,"popper-class":[e.ns.e("popper"),e.popperClass],"reference-element":null==(i=e.referenceElementRef)?void 0:i.$el,trigger:e.trigger,"trigger-keys":e.triggerKeys,"trigger-target-el":e.contentRef,"show-after":"hover"===e.trigger?e.showTimeout:0,"stop-popper-mouse-event":!1,"virtual-ref":e.triggeringElementRef,"virtual-triggering":e.splitButton,disabled:e.disabled,transition:`${e.ns.namespace.value}-zoom-in-top`,teleported:e.teleported,pure:"",persistent:e.persistent,onBeforeShow:e.handleBeforeShowTooltip,onShow:e.handleShowTooltip,onBeforeHide:e.handleBeforeHideTooltip},F({content:b((()=>[w(u,{ref:"scrollbar","wrap-style":e.wrapStyle,tag:"div","view-class":e.ns.e("list")},{default:b((()=>[w(d,{loop:e.loop,"current-tab-id":e.currentTabId,orientation:"horizontal",onCurrentTabIdChange:e.handleCurrentTabIdChange,onEntryFocus:e.handleEntryFocus},{default:b((()=>[w(s,null,{default:b((()=>[l(e.$slots,"dropdown")])),_:3})])),_:3},8,["loop","current-tab-id","onCurrentTabIdChange","onEntryFocus"])])),_:3},8,["wrap-style","view-class"])])),_:2},[e.splitButton?void 0:{name:"default",fn:b((()=>[w(c,{id:e.triggerId,ref:"triggeringElementRef",role:"button",tabindex:e.tabindex},{default:b((()=>[l(e.$slots,"default")])),_:3},8,["id","tabindex"])]))}]),1032,["role","effect","popper-options","hide-after","placement","popper-class","reference-element","trigger","trigger-keys","trigger-target-el","show-after","virtual-ref","virtual-triggering","disabled","transition","teleported","persistent","onBeforeShow","onShow","onBeforeHide"]),e.splitButton?(g(),m(k,{key:0},{default:b((()=>[w(v,C({ref:"referenceElementRef"},e.buttonProps,{size:e.dropdownSize,type:e.type,disabled:e.disabled,tabindex:e.tabindex,onClick:e.handlerMainButtonClick}),{default:b((()=>[l(e.$slots,"default")])),_:3},16,["size","type","disabled","tabindex","onClick"]),w(v,C({id:e.triggerId,ref:"triggeringElementRef"},e.buttonProps,{role:"button",size:e.dropdownSize,type:e.type,class:e.ns.e("caret-button"),disabled:e.disabled,tabindex:e.tabindex,"aria-label":e.t("el.dropdown.toggleDropdown")}),{default:b((()=>[w(I,{class:_(e.ns.e("icon"))},{default:b((()=>[w(h)])),_:1},8,["class"])])),_:1},16,["id","size","type","class","disabled","tabindex","aria-label"])])),_:3})):E("v-if",!0)],2)}],["__file","dropdown.vue"]]);var Pe=n(t({components:{ElRovingFocusCollectionItem:he},props:{focusable:{type:Boolean,default:!0},active:{type:Boolean,default:!1}},emits:["mousedown","focus","keydown"],setup(e,{emit:o}){const{currentTabbedId:n,loop:t,onItemFocus:l,onItemShiftTab:u}=a(Ee,void 0),{getItems:c}=a(Ie,void 0),p=pe(),v=r(),f=Z((e=>{o("mousedown",e)}),(o=>{e.focusable?l(s(p)):o.preventDefault()})),m=Z((e=>{o("focus",e)}),(()=>{l(s(p))})),g=Z((e=>{o("keydown",e)}),(e=>{const{code:o,shiftKey:n,target:l,currentTarget:r}=e;if(o===$.tab&&n)return void u();if(l!==r)return;const a=(e=>{const o=e.code;return Ce[o]})(e);if(a){e.preventDefault();let o=c().filter((e=>e.focusable)).map((e=>e.ref));switch(a){case"last":o.reverse();break;case"prev":case"next":{"prev"===a&&o.reverse();const e=o.indexOf(r);o=t.value?(s=e+1,(i=o).map(((e,o)=>i[(o+s)%i.length]))):o.slice(e+1);break}}P((()=>{_e(o)}))}var i,s})),b=i((()=>n.value===s(p)));return d(Fe,{rovingFocusGroupItemRef:v,tabIndex:i((()=>s(b)?0:-1)),handleMousedown:f,handleFocus:m,handleKeydown:g}),{id:p,handleKeydown:g,handleFocus:m,handleMousedown:f}}}),[["render",function(e,o,n,t,r,a){const i=f("el-roving-focus-collection-item");return g(),m(i,{id:e.id,focusable:e.focusable,active:e.active},{default:b((()=>[l(e.$slots,"default")])),_:3},8,["id","focusable","active"])}],["__file","roving-focus-item.vue"]]);const Me=t({name:"DropdownItemImpl",components:{ElIcon:R},props:le,emits:["pointermove","pointerleave","click","clickimpl"],setup(e,{emit:o}){const n=x("dropdown"),{role:t}=a(Be,void 0),{collectionItemRef:l}=a(ae,void 0),{collectionItemRef:r}=a(ye,void 0),{rovingFocusGroupItemRef:s,tabIndex:d,handleFocus:u,handleKeydown:c,handleMousedown:p}=a(Fe,void 0),v=me(l,r,s),f=i((()=>"menu"===t.value?"menuitem":"navigation"===t.value?"link":"button")),m=Z((e=>{if([$.enter,$.numpadEnter,$.space].includes(e.code))return e.preventDefault(),e.stopImmediatePropagation(),o("clickimpl",e),!0}),c);return{ns:n,itemRef:v,dataset:{[re]:""},role:f,tabIndex:d,handleFocus:u,handleKeydown:m,handleMousedown:p}}});const Ke=()=>{const e=a("elDropdown",{}),o=i((()=>null==e?void 0:e.dropdownSize));return{elDropdown:e,_elDropdownSize:o}};var Le=n(t({name:"ElDropdownItem",components:{ElDropdownCollectionItem:ie,ElRovingFocusItem:Pe,ElDropdownItemImpl:n(Me,[["render",function(e,o,n,t,r,a){const i=f("el-icon");return g(),y(G,null,[e.divided?(g(),y("li",{key:0,role:"separator",class:_(e.ns.bem("menu","item","divided"))},null,2)):E("v-if",!0),M("li",C({ref:e.itemRef},{...e.dataset,...e.$attrs},{"aria-disabled":e.disabled,class:[e.ns.be("menu","item"),e.ns.is("disabled",e.disabled)],tabindex:e.tabIndex,role:e.role,onClick:o=>e.$emit("clickimpl",o),onFocus:e.handleFocus,onKeydown:L(e.handleKeydown,["self"]),onMousedown:e.handleMousedown,onPointermove:o=>e.$emit("pointermove",o),onPointerleave:o=>e.$emit("pointerleave",o)}),[e.icon?(g(),m(i,{key:0},{default:b((()=>[(g(),m(K(e.icon)))])),_:1})):E("v-if",!0),l(e.$slots,"default")],16,["aria-disabled","tabindex","role","onClick","onFocus","onKeydown","onMousedown","onPointermove","onPointerleave"])],64)}],["__file","dropdown-item-impl.vue"]])},inheritAttrs:!1,props:le,emits:["pointermove","pointerleave","click"],setup(e,{emit:o,attrs:n}){const{elDropdown:t}=Ke(),l=D(),d=r(null),u=i((()=>{var e,o;return null!=(o=null==(e=s(d))?void 0:e.textContent)?o:""})),{onItemEnter:c,onItemLeave:p}=a(Be,void 0),v=Z((e=>(o("pointermove",e),e.defaultPrevented)),X((o=>{if(e.disabled)return void p(o);const n=o.currentTarget;n===document.activeElement||n.contains(document.activeElement)||(c(o),o.defaultPrevented||null==n||n.focus())}))),f=Z((e=>(o("pointerleave",e),e.defaultPrevented)),X(p));return{handleClick:Z((n=>{if(!e.disabled)return o("click",n),"keydown"!==n.type&&n.defaultPrevented}),(o=>{var n,r,a;e.disabled?o.stopImmediatePropagation():((null==(n=null==t?void 0:t.hideOnClick)?void 0:n.value)&&(null==(r=t.handleClick)||r.call(t)),null==(a=t.commandHandler)||a.call(t,e.command,l,o))})),handlePointerMove:v,handlePointerLeave:f,textContent:u,propsAndAttrs:i((()=>({...e,...n})))}}}),[["render",function(e,o,n,t,r,a){var i;const s=f("el-dropdown-item-impl"),d=f("el-roving-focus-item"),u=f("el-dropdown-collection-item");return g(),m(u,{disabled:e.disabled,"text-value":null!=(i=e.textValue)?i:e.textContent},{default:b((()=>[w(d,{focusable:!e.disabled},{default:b((()=>[w(s,C(e.propsAndAttrs,{onPointerleave:e.handlePointerLeave,onPointermove:e.handlePointerMove,onClickimpl:e.handleClick}),{default:b((()=>[l(e.$slots,"default")])),_:3},16,["onPointerleave","onPointermove","onClickimpl"])])),_:3},8,["focusable"])])),_:3},8,["disabled","text-value"])}],["__file","dropdown-item.vue"]]);var Ge=n(t({name:"ElDropdownMenu",props:se,setup(e){const o=x("dropdown"),{_elDropdownSize:n}=Ke(),t=n.value,{focusTrapRef:l,onKeydown:r}=a(ge,void 0),{contentRef:d,role:u,triggerId:c}=a(Be,void 0),{collectionRef:p,getItems:v}=a(de,void 0),{rovingFocusGroupRef:f,rovingFocusGroupRootStyle:m,tabIndex:g,onBlur:b,onFocus:w,onMousedown:h}=a(Ee,void 0),{collectionRef:I}=a(Ie,void 0),y=i((()=>[o.b("menu"),o.bm("menu",null==t?void 0:t.value)])),E=me(d,p,l,f,I),F=Z((o=>{var n;null==(n=e.onKeydown)||n.call(e,o)}),(e=>{const{currentTarget:o,code:n,target:t}=e;if(o.contains(t),$.tab===n&&e.stopImmediatePropagation(),e.preventDefault(),t!==s(d)||!ue.includes(n))return;const l=v().filter((e=>!e.disabled)).map((e=>e.ref));ce.includes(n)&&l.reverse(),_e(l)}));return{size:t,rovingFocusGroupRootStyle:m,tabIndex:g,dropdownKls:y,role:u,triggerId:c,dropdownListWrapperRef:E,handleKeydown:e=>{F(e),r(e)},onBlur:b,onFocus:w,onMousedown:h}}}),[["render",function(e,o,n,t,r,a){return g(),y("ul",{ref:e.dropdownListWrapperRef,class:_(e.dropdownKls),style:z(e.rovingFocusGroupRootStyle),tabindex:-1,role:e.role,"aria-labelledby":e.triggerId,onBlur:e.onBlur,onFocus:e.onFocus,onKeydown:L(e.handleKeydown,["self"]),onMousedown:L(e.onMousedown,["self"])},[l(e.$slots,"default")],46,["role","aria-labelledby","onBlur","onFocus","onKeydown","onMousedown"])}],["__file","dropdown-menu.vue"]]);const ze=O($e,{DropdownItem:Le,DropdownMenu:Ge}),Oe=A(Le),Ae=A(Ge),je=t({__name:"index",props:{size:{type:String,required:!1}},setup(e){const o=[{label:"中文",value:j.ZH_CN},{label:"English",value:j.EN}],n=H(),{locale:t,t:l}=N();function r(e){t.value=e,n.changeLanguage(e),Y.success(l("langSelect.message.success"))}return(e,t)=>{const l=Oe,a=Ae,i=ze;return g(),m(i,{trigger:"click",onCommand:r},{dropdown:b((()=>[w(a,null,{default:b((()=>[(g(),y(G,null,J(o,(e=>w(l,{key:e.value,disabled:s(n).language===e.value,command:e.value},{default:b((()=>[U(W(e.label),1)])),_:2},1032,["disabled","command"]))),64))])),_:1})])),default:b((()=>[t[0]||(t[0]=M("div",null,[M("div",{class:"i-svg:language"})],-1))])),_:1,__:[0]})}}});export{Ae as E,je as _,ze as a,Oe as b};
