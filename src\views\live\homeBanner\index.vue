<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="keywords" label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          type="success"
          v-hasPerm="['basic:banner:save']"
          icon="plus"
          @click="$refs.editModelRef?.open()"
        >
          新增
        </el-button>
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="轮播图" align="center" prop="bannerUrl" min-width="120">
          <template #default="{ row }">
            <el-image
              style="width: 100px; height: 60px;"
              :preview-src-list="[row.bannerUrl]"
              preview-teleported
              :src="row.bannerUrl"
            />
          </template>
        </el-table-column>
        <el-table-column label="跳转链接" align="center" prop="targetUrl" min-width="200" />
        <el-table-column label="展示状态" align="center" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isShow ? 'success' : 'info'">
              {{ row.isShow ? '展示中' : '已隐藏' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createdAt" min-width="160" />
        <el-table-column fixed="right" label="操作" width="240">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              v-hasPerm="['basic:banner:save']"
              link
              icon="edit"
              :loading="scope.row.loading"
              @click="$refs.editModelRef?.open(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              v-hasPerm="['basic:banner:delete']"
              link
              icon="delete"
              :loading="scope.row.loading"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
            <el-button
              type="warning"
              size="small"
              link
              :loading="scope.row.loading"
              v-hasPerm="['basic:banner:save']"
              @click="editStatus(scope.row)"
            >
              {{ scope.row.isShow ? "隐藏" : "展示" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
    <edit-model ref="editModelRef" @success="resetQuery" />
  </div>
</template>

<script setup lang="ts">
import EditModel from "./edit.vue";
import BannerApi, {
  BannerPageQuery,
  BannerPageVO,
  UpdateBannerStatusDto
} from "@/api/live/homeBanner";
import { usePage } from "@/utils/commonSetup";
import { ref } from 'vue';

defineOptions({ name: "HomeBanner" });

const dateRange = ref<[string, string] | null>(null);

const { page, getPage, resetQuery: originalResetQuery } = usePage<BannerPageQuery, BannerPageVO>(
  new BannerPageQuery(),
  BannerApi.page
);

// Override resetQuery to also reset dateRange
const resetQuery = () => {
  dateRange.value = null;
  originalResetQuery();
};

function handleDateRangeChange(val: [string, string] | null) {
  if (val) {
    page.query.startTime = val[0];
    page.query.endTime = val[1];
  } else {
    page.query.startTime = undefined;
    page.query.endTime = undefined;
  }
}

function handleDelete(_row: BannerPageVO) {
  ElMessageBox.confirm('确定删除该轮播图吗？', '删除', {
    confirmButtonText: "确定删除",
    cancelButtonText: "取消",
    confirmButtonClass: "el-button--danger",
    type: "error",
  })
    .then(() => {
      _row.loading = true;
      BannerApi.remove(_row.id)
        .then(() => {
          ElMessage.success("删除成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}

function editStatus(_row: BannerPageVO) {
  ElMessageBox.confirm(
    `确定要${_row.isShow ? "隐藏" : "展示"}该轮播图吗？`,
    `${_row.isShow ? "隐藏" : "展示"}轮播图`,
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      confirmButtonClass: "el-button--danger",
      type: "error",
    }
  )
    .then(() => {
      const formData = new UpdateBannerStatusDto(_row);
      formData.isShow = _row.isShow ? 0 : 1;
      _row.loading = true;
      BannerApi.editStatus(formData)
        .then(() => {
          ElMessage.success("操作成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}
</script> 