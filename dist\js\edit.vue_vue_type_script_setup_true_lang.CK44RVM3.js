import{d as e,S as l,r as a,e as o,f as t,w as r,m as i,i as m,C as s,$ as d,az as u}from"./index.Dk5pbsTU.js";import{E as n}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as p}from"./el-button.CXI119n4.js";import{E as g,a as f}from"./el-form-item.Bw6Zyv_7.js";import{E as _}from"./el-input.DiGatoux.js";import{E as V}from"./el-input-number.C02ig7uT.js";import{_ as b}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{_ as c}from"./SingleImageUpload.WGBxPB_4.js";import{a as h,T as v}from"./tenant.BHgTweuE.js";import{f as y,i as w}from"./validate.Bicq6Mu8.js";const x={class:"dialog-footer"},j=e({__name:"edit",emits:["success"],setup(e,{expose:j,emit:U}){const k=l({visible:!1,title:""}),q=a(new h),S=a(),E=U,R=a(!1),N={name:[{required:!0,message:"请输入公司名称",trigger:"blur"}],maxRoom:[{required:!0,message:"请输入可用房间数",trigger:"blur"}],logo:[{required:!0,message:"请上传公司logo",trigger:"blur"}],nickName:[{required:!0,message:"请输入负责人名称",trigger:"blur"}],smsSignature:[{required:!0,message:"请选择短信签名",trigger:"blur"}],phone:[{required:!0,message:"请输入负责人电话",trigger:"blur"},y(w,"手机号错误")],deployWay:[{required:!0,message:"请选择部署方式",trigger:"blur"}]};function W(){S.value.validate((e=>{e&&(R.value=!0,v.save(q.value).then((()=>{u.success("保存成功"),k.visible=!1,E("success")})).finally((()=>R.value=!1)))}))}return j({open:e=>{k.visible=!0,k.title=(null==e?void 0:e.id)?"编辑授权公司":"授权新公司",q.value=new h(e)}}),(e,l)=>{const a=_,u=f,h=c,v=b,y=V,w=g,j=p,U=n;return t(),o(U,{modelValue:m(k).visible,"onUpdate:modelValue":l[9]||(l[9]=e=>m(k).visible=e),title:m(k).title,width:"500px"},{footer:r((()=>[s("div",x,[i(j,{type:"primary",onClick:W},{default:r((()=>l[10]||(l[10]=[d("确 定")]))),_:1,__:[10]}),i(j,{onClick:l[8]||(l[8]=e=>m(k).visible=!1)},{default:r((()=>l[11]||(l[11]=[d("取 消")]))),_:1,__:[11]})])])),default:r((()=>[i(w,{ref_key:"editFormRef",ref:S,model:m(q),rules:N,"label-width":"110px"},{default:r((()=>[i(u,{label:"公司名称",prop:"name"},{default:r((()=>[i(a,{modelValue:m(q).name,"onUpdate:modelValue":l[0]||(l[0]=e=>m(q).name=e),maxlength:50,"show-word-limit":"",placeholder:"请输入公司名称"},null,8,["modelValue"])])),_:1}),i(u,{label:"公司logo",prop:"logo"},{default:r((()=>[i(h,{style:{width:"100px",height:"100px"},modelValue:m(q).logo,"onUpdate:modelValue":l[1]||(l[1]=e=>m(q).logo=e)},null,8,["modelValue"])])),_:1}),i(u,{label:"负责人姓名",prop:"nickName"},{default:r((()=>[i(a,{modelValue:m(q).nickName,"onUpdate:modelValue":l[2]||(l[2]=e=>m(q).nickName=e),maxlength:25,"show-word-limit":"",placeholder:"请输入负责人姓名"},null,8,["modelValue"])])),_:1}),i(u,{label:"负责人电话",prop:"phone"},{default:r((()=>[i(a,{modelValue:m(q).phone,"onUpdate:modelValue":l[3]||(l[3]=e=>m(q).phone=e),maxlength:11,"show-word-limit":"",placeholder:"请输入负责人电话"},null,8,["modelValue"])])),_:1}),i(u,{label:"部署方式",prop:"deployWay"},{default:r((()=>[i(v,{modelValue:m(q).deployWay,"onUpdate:modelValue":l[4]||(l[4]=e=>m(q).deployWay=e),type:"radio",code:"tenant_deploy_way"},null,8,["modelValue"])])),_:1}),i(u,{label:"短信签名",prop:"smsSignature"},{default:r((()=>[i(v,{modelValue:m(q).smsSignature,"onUpdate:modelValue":l[5]||(l[5]=e=>m(q).smsSignature=e),style:{width:"100%"},clearable:!1,code:"sms_signature"},null,8,["modelValue"])])),_:1}),i(u,{label:"可用房间数",prop:"maxRoom"},{default:r((()=>[i(y,{modelValue:m(q).maxRoom,"onUpdate:modelValue":l[6]||(l[6]=e=>m(q).maxRoom=e),style:{width:"100%"},"controls-position":"right",min:1},null,8,["modelValue"])])),_:1}),i(u,{label:"租户私有域名",prop:"privateDomain"},{default:r((()=>[i(a,{modelValue:m(q).privateDomain,"onUpdate:modelValue":l[7]||(l[7]=e=>m(q).privateDomain=e),"show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}});export{j as _};
