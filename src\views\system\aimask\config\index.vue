<template>
  <div class="app-container">
    <el-card shadow="never" class="mb-4">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item prop="vestCascader" label="选择马甲">
          <el-cascader
            v-model="queryParams.vestCascader"
            :options="searchCascaderOptions"
            :props="cascaderProps"
            placeholder="请选择直播间/分组/马甲"
            clearable
            filterable
            style="width: 300px"
            @change="handleSearchCascaderChange"
          />
        </el-form-item>
        <el-form-item prop="type" label="人设类型">
          <el-select
            v-model="queryParams.type"
            placeholder="请选择人设类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="item in personaTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="ruleId" label="触发规则">
          <div style="display: flex; align-items: center">
            <el-select
              v-model="queryParams.ruleId"
              placeholder="请选择触发规则"
              clearable
              filterable
              style="width: 200px"
            >
              <el-option
                v-for="rule in triggerRuleOptions"
                :key="rule.id"
                :label="rule.ruleName"
                :value="rule.id"
              />
            </el-select>
            <el-tooltip
              content="需要先去触发规则配置页面配置触发规则，再返回此处选择"
              placement="top"
              effect="light"
            >
              <el-icon class="ml-2 text-gray-400 cursor-help">
                <question-filled />
              </el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item prop="status" label="状态">
          <el-select
            v-model="queryParams.status"
            style="width: 100px"
            placeholder="请选择"
            clearable
          >
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          <el-button icon="refresh" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card shadow="never">
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">AI马甲配置列表</h3>
          <el-button
            v-hasPerm="['sys:aimask:add']"
            type="success"
            icon="plus"
            :loading="loading"
            @click="handleOpenDialog()"
          >
            新增配置
          </el-button>
        </div>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="aiVestConfigList"
        highlight-current-row
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="马甲昵称" prop="vestNickname" min-width="120" />
        <el-table-column label="人设类型" prop="typeName" min-width="100" />
        <el-table-column label="触发规则" prop="ruleName" min-width="100">
          <template #default="scope">
            {{
              scope.row.ruleName ||
              (scope.row.triggerModeName ? `${scope.row.triggerModeName}（旧）` : "无")
            }}
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" min-width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :disabled="!checkPerms(['sys:aimask:add']) || statusLoading"
              :loading="statusLoading && scope.row.id === currentStatusId"
              :active-value="1"
              :inactive-value="0"
              @change="(val) => handleStatusChange(scope.row, val)"
            />
          </template>
        </el-table-column>
        <el-table-column label="激活状态" align="center" prop="status" width="100">
          <template #default="{ row }">
            <dict-label v-model="row.activeState" code="active_state" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createdAt" min-width="160" />
        <el-table-column label="更新时间" prop="updatedAt" min-width="160" />
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="scope">
            <el-button
              v-hasPerm="['sys:aimask:add']"
              :type="scope.row.activeState ? 'danger' : 'success'"
              size="small"
              link
              :loading="loading"
              @click="handleEditStatus(scope.row)"
            >
              {{ scope.row.activeState ? "停用" : "激活" }}
            </el-button>
            <el-button
              v-hasPerm="['sys:aimask:add']"
              type="primary"
              size="small"
              link
              :loading="loading"
              @click="handleOpenDialog(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-hasPerm="['sys:aimask:del']"
              type="danger"
              size="small"
              link
              :loading="loading"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- AI马甲配置表单弹窗 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      size="850px"
      @close="handleCloseDialog"
    >
      <AiVestConfigForm
        ref="aiVestConfigFormRef"
        :model-value="formData"
        :trigger-rule-options="triggerRuleOptions"
        :ai-vest-config-list="aiVestConfigList"
        :persona-type-options="personaTypeOptions"
        @update:modelValue="(val) => Object.assign(formData, val)"
        @go-to-rule-config="goToRuleConfig"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="loading" @click="handleSubmit">确 定</el-button>
          <el-button :disabled="loading" @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { CascaderValue } from "element-plus";
import { QuestionFilled } from "@element-plus/icons-vue";
import { checkPerms } from "@/directive/permission";
import { useRouter } from "vue-router";
import AiVestConfigForm from "./component/AiVestConfigForm.vue";
import {
  getAiVestConfigList,
  updateAiVestConfigActiveState,
  saveOrUpdateAiVestConfig,
  updateAiVestConfigStatus,
  deleteAiVestConfig,
  getAiVestConfigByVestId,
  getEnabledTriggerRules,
  getVestUserType,
  AiVestConfigListQuery,
  AiVestConfigVo,
  AiVestConfigDto,
  AiVestTriggerRulesVo,
} from "@/api/system/aiVestConfig";
import VestAPI, { LiveVestGroupVo, LiveVestVo } from "@/api/live/vest";
import LiveRoomApi, { LiveRoomPageDto, LiveRoomPageVo } from "@/api/live/liveRoom";

defineOptions({
  name: "AiVestConfig",
  inheritAttrs: false,
});

const queryFormRef = ref();
const aiVestConfigFormRef = ref();
const loading = ref(false);
const statusLoading = ref(false);
const total = ref(0);
const currentStatusId = ref<number | undefined>(undefined);
const triggerRuleOptions = ref<AiVestTriggerRulesVo[]>([]);
const personaTypeOptions = ref<{ label: string; value: number }[]>([]);

// 级联选择器配置
const cascaderProps = {
  value: "id",
  label: "label",
  children: "children",
  emitPath: true,
  lazy: true,
  lazyLoad: async (node: any, resolve: any) => {
    const { level } = node;

    if (level === 0) {
      // 加载直播间
      try {
        const query = new LiveRoomPageDto();
        query.pageSize = 100;
        const res = await LiveRoomApi.page(query);
        const rooms = (res as any).records || [];
        const nodes = rooms.map((room: LiveRoomPageVo) => ({
          id: room.id,
          label: room.roomName,
          leaf: false,
        }));
        resolve(nodes);
      } catch {
        resolve([]);
      }
    } else if (level === 1) {
      // 加载分组
      try {
        const roomId = node.data.id;
        const res = await VestAPI.getAllGroups(roomId);
        const groups = (res as any).data || res || [];
        const nodes = groups.map((group: LiveVestGroupVo) => ({
          id: group.id,
          label: group.groupName,
          leaf: false,
        }));
        resolve(nodes);
      } catch {
        resolve([]);
      }
    } else if (level === 2) {
      // 加载马甲
      try {
        const groupId = node.data.id;
        const res = await VestAPI.getVestsByGroupId(groupId);
        const vests = (res as any).data || res || [];
        const nodes = vests.map((vest: LiveVestVo) => ({
          id: vest.id,
          label: vest.nickname,
          leaf: true,
          vest: vest, // 保存完整的马甲信息
        }));
        resolve(nodes);
      } catch {
        resolve([]);
      }
    }
  },
};

// 搜索用级联选择器选项
const searchCascaderOptions = ref<any[]>([]);

const queryParams = reactive<AiVestConfigListQuery & { vestCascader?: any[] }>({
  vestCascader: [],
  vestId: "",
  vestNickname: "",
  type: "",
  ruleId: undefined,
  status: "",
  pageNum: 1,
  pageSize: 10,
});

const aiVestConfigList = ref<AiVestConfigVo[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

// AI马甲配置表单
const formData = reactive<
  Omit<AiVestConfigDto, "temperature"> & { temperature: number; vestCascader?: any[] }
>({
  id: undefined,
  vestCascader: [],
  vestId: 0,
  temperature: 0.7,
  promptTemplate: "",
  contextWindow: 0,
  useRag: 0,
  type: 0,
  isDelay: 0,
  fuzzinessRate: 0,
  isAuto: 0,
  triggerMode: 0,
  ruleId: 0,
  sessionRound: 1,
  status: 1,
  knowledgeIds: [],
  sex: "",
  age: "",
  intro: "",
});

// 获取启用的触发规则列表
async function fetchEnabledTriggerRules() {
  try {
    loading.value = true;
    const res = await getEnabledTriggerRules();
    console.log("获取到的触发规则列表:", res);

    // 处理不同的API响应结构
    if (res && res.data && Array.isArray(res.data)) {
      triggerRuleOptions.value = res.data;
    } else if (Array.isArray(res)) {
      triggerRuleOptions.value = res;
    } else {
      triggerRuleOptions.value = [];
      console.warn("触发规则列表格式不正确:", res);
    }

    console.log("处理后的触发规则列表:", triggerRuleOptions.value);
  } catch (error) {
    console.error("获取触发规则列表失败", error);
    ElMessage.error("获取触发规则列表失败");
    triggerRuleOptions.value = [];
  } finally {
    loading.value = false;
  }
}

function handleQuery() {
  loading.value = true;

  // 构建查询参数，使用解构赋值创建一个新对象
  const params = { ...queryParams };

  // 确保API参数格式正确
  if (params.ruleId === null || params.ruleId === undefined) {
    delete params.ruleId; // 不传递ruleId参数
  }

  // 确保type参数格式正确
  if (params.type === null || params.type === undefined || params.type === "") {
    delete params.type; // 不传递type参数
  } else {
    // 确保type是数字类型
    params.type = params.type.toString();
  }

  getAiVestConfigList(params)
    .then((res: any) => {
      aiVestConfigList.value = res.records || (res.data && res.data.records) || [];
      total.value = res.totalRow || (res.data && res.data.totalRow) || 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.pageNum = 1;
  queryParams.vestCascader = [];
  queryParams.vestId = "";
  queryParams.type = "";
  queryParams.ruleId = undefined;
  queryParams.status = "";
  handleQuery();
}

// 搜索级联选择器变化
function handleSearchCascaderChange(value: CascaderValue) {
  if (value && Array.isArray(value) && value.length === 3) {
    const vestId = value[2];
    if (typeof vestId === "number" || (typeof vestId === "string" && /^\d+$/.test(vestId))) {
      queryParams.vestId = vestId.toString();
    } else {
      queryParams.vestId = "";
      console.error("无效的马甲ID:", vestId);
    }
  } else {
    queryParams.vestId = "";
  }
}

// 打开AI马甲配置弹窗
async function handleOpenDialog(row?: AiVestConfigVo) {
  // 确保触发规则列表已加载
  if (triggerRuleOptions.value.length === 0) {
    loading.value = true;
    await fetchEnabledTriggerRules();
    loading.value = false;
  }

  if (row && row.id) {
    dialog.title = "修改AI马甲配置";
    loading.value = true;
    try {
      const res: any = await getAiVestConfigByVestId(row.vestId);
      const config = res.data || res;

      Object.assign(formData, {
        id: config.id,
        vestId: config.vestId,
        modelName: config.modelName,
        temperature: config.temperature,
        promptTemplate: config.promptTemplate,
        contextWindow: config.contextWindow,
        useRag: config.useRag,
        type: config.type,
        isDelay: config.isDelay,
        fuzzinessRate: config.fuzzinessRate,
        isAuto: config.isAuto,
        triggerMode: config.triggerMode,
        ruleId: config.ruleId ? Number(config.ruleId) : 0, // 确保ruleId为数字类型
        sessionRound: config.sessionRound,
        status: config.status,
        sex: config.sex,
        age: config.age,
        intro: config.intro,
        vestCascader: [], // 编辑时不显示级联选择器
      });

      dialog.visible = true; // 数据加载成功后再显示弹窗
    } catch (error) {
      console.error("获取配置信息失败", error);
      ElMessage.error("获取配置信息失败");
    } finally {
      loading.value = false;
    }
  } else {
    dialog.title = "新增AI马甲配置";
    // 重置表单数据 - 关键修复：确保级联选择器被清空
    const defaultType = personaTypeOptions.value.length > 0 ? personaTypeOptions.value[0].value : 0;
    console.log("重置表单，设置默认人设类型:", defaultType, "可用选项:", personaTypeOptions.value);

    Object.assign(formData, {
      id: undefined,
      vestCascader: [], // 清空级联选择器
      vestId: 0,
      temperature: 0.7,
      promptTemplate: "",
      contextWindow: 0,
      useRag: 0,
      type: defaultType,
      isDelay: 0,
      fuzzinessRate: 0,
      isAuto: 0,
      triggerMode: 0,
      ruleId: 0,
      sessionRound: 1,
      status: 1,
      knowledgeIds: [],
      sex: "",
      age: "",
      intro: "",
    });

    dialog.visible = true;
    // 等待弹窗打开后再重置表单组件
    setTimeout(() => {
      if (aiVestConfigFormRef.value) {
        aiVestConfigFormRef.value.resetForm();
      }
    }, 100);
  }
}

// 提交AI马甲配置表单
async function handleSubmit() {
  try {
    console.log("开始提交表单，当前formData:", formData);

    // 先验证表单
    const valid = await aiVestConfigFormRef.value.validate();
    console.log("表单验证结果:", valid);

    if (!valid) {
      ElMessage.error("请完善表单信息");
      return;
    }

    loading.value = true;
    const submitData = { ...formData };
    delete submitData.vestCascader; // 删除级联选择器数据

    console.log("删除级联选择器后的数据:", submitData);

    // 确保数据类型正确
    submitData.vestId = Number(submitData.vestId);
    submitData.ruleId = Number(submitData.ruleId) || 0;
    submitData.temperature = Number(submitData.temperature) || 0.7;
    submitData.type = Number(submitData.type);

    console.log("类型转换后的数据:", submitData);

    // 如果ruleId不为0，则将triggerMode设置为2（多维度条件触发）
    if (submitData.ruleId > 0) {
      submitData.triggerMode = 2;
    }

    console.log("最终提交的数据:", submitData);

    // 最终验证 - 对于新增，必须有有效的vestId
    if (!formData.id && (!submitData.vestId || submitData.vestId <= 0)) {
      ElMessage.error("请选择有效的马甲");
      console.error("马甲ID验证失败:", submitData.vestId);
      loading.value = false;
      return;
    }

    // 检查温度范围
    if (submitData.temperature < 0 || submitData.temperature > 2) {
      ElMessage.error("生成温度必须在0-2之间");
      loading.value = false;
      return;
    }

    saveOrUpdateAiVestConfig(submitData)
      .then((res) => {
        console.log("保存结果:", res);
        ElMessage.success(formData.id ? "修改成功" : "新增成功");
        handleCloseDialog();
        handleResetQuery();
      })
      .catch((error: any) => {
        console.error("保存失败:", error);
        // const errorMsg = error?.response?.data?.message || error?.message || "保存失败";
        // ElMessage.error(errorMsg);
      })
      .finally(() => (loading.value = false));
  } catch (error) {
    console.error("表单验证失败:", error);
    ElMessage.error("表单验证失败，请检查输入");
    loading.value = false;
  }
}

// 关闭弹窗
function handleCloseDialog() {
  dialog.visible = false;
  // 使用表单组件的重置方法，确保级联选择器被正确清空
  if (aiVestConfigFormRef.value) {
    aiVestConfigFormRef.value.resetForm();
  }
}

// 状态切换
function handleStatusChange(row: AiVestConfigVo, val: string | number | boolean) {
  if (statusLoading.value) {
    // 如果正在处理其他请求，回滚状态
    row.status = Number(val) === 1 ? 0 : 1;
    return;
  }

  statusLoading.value = true;
  currentStatusId.value = row.id;

  const statusValue = Number(val);

  updateAiVestConfigStatus(row.id, statusValue)
    .then(() => {
      ElMessage.success(statusValue === 1 ? "已启用" : "已禁用");
    })
    .catch(() => {
      row.status = statusValue === 1 ? 0 : 1; // 回滚
    })
    .finally(() => {
      statusLoading.value = false;
      currentStatusId.value = undefined;
    });
}

// 删除AI马甲配置
function handleEditStatus(row: AiVestConfigVo) {
  ElMessageBox.confirm(
    `确认${row.activeState === 1 ? "停用" : "激活"}马甲"${row.vestNickname}"的AI配置吗？`,
    `${row.activeState === 1 ? "停用" : "激活"}确认`,
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      loading.value = true;
      updateAiVestConfigActiveState(row.id, row.activeState === 1 ? 0 : 1)
        .then(() => {
          ElMessage.success("操作成功");
          handleQuery();
        })
        .finally(() => {
          loading.value = false;
        });
    })
    .catch(() => {
      // 用户取消删除
    });
}

// 删除AI马甲配置
function handleDelete(row: AiVestConfigVo) {
  ElMessageBox.confirm(`确认删除马甲"${row.vestNickname}"的AI配置吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      loading.value = true;
      deleteAiVestConfig(row.id)
        .then(() => {
          ElMessage.success("删除成功");
          handleQuery();
        })
        .finally(() => {
          loading.value = false;
        });
    })
    .catch(() => {
      // 用户取消删除
    });
}

const router = useRouter();

// 跳转到触发规则配置页面
function goToRuleConfig() {
  router.push("/aimask/maskRuleConfig");
}

// 获取人设类型列表
async function fetchPersonaTypes() {
  try {
    loading.value = true;
    const res = await getVestUserType();
    console.log("获取到的人设类型列表:", res);

    // 处理不同的API响应结构
    if (res && res.data && Array.isArray(res.data)) {
      personaTypeOptions.value = res.data.map((item) => ({
        label: item.description,
        value: item.type,
      }));
    } else if (Array.isArray(res)) {
      personaTypeOptions.value = res.map((item) => ({
        label: item.description,
        value: item.type,
      }));
    } else {
      console.warn("人设类型列表格式不正确:", res);
      personaTypeOptions.value = []; // 设置为空数组
    }

    console.log("处理后的人设类型列表:", personaTypeOptions.value);
  } catch (error) {
    console.error("获取人设类型列表失败", error);
    ElMessage.error("获取人设类型列表失败");
    personaTypeOptions.value = []; // 设置为空数组
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  // 并行加载触发规则列表和人设类型列表
  await Promise.all([fetchEnabledTriggerRules(), fetchPersonaTypes()]);
  handleQuery();
});
</script>
