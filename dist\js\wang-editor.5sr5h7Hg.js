import{d as e,r as a,g as t,f as s,m as l,C as n,w as o,$ as r,ak as i,i as u,V as d,b3 as m}from"./index.Dk5pbsTU.js";import{E as p}from"./el-link.qHYW6llJ.js";import{_}from"./index.vue_vue_type_script_setup_true_lang.BOVTspW2.js";import"./index.DuiNpp1i.js";import"./index.WzKGworL.js";const g={class:"app-container"},x={style:{"margin-top":"10px"}},c=e({__name:"wang-editor",setup(e){const c=a("初始化内容");return(e,a)=>{const v=p;return s(),t("div",g,[l(v,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/wang-editor.vue",type:"primary",target:"_blank",class:"mb-[20px]"},{default:o((()=>a[2]||(a[2]=[r(" 示例源码 请点击>>>> ")]))),_:1,__:[2]}),l(_,{modelValue:u(c),"onUpdate:modelValue":a[0]||(a[0]=e=>i(c)?c.value=e:null),height:"400px"},null,8,["modelValue"]),n("div",x,[d(n("textarea",{"onUpdate:modelValue":a[1]||(a[1]=e=>i(c)?c.value=e:null),readonly:"",style:{width:"100%",height:"200px",outline:"none"}},null,512),[[m,u(c)]])])])}}});export{c as default};
