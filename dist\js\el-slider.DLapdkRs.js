import{b6 as e,a9 as a,t,z as l,N as i,A as o,r as n,c as r,I as s,u,a0 as d,K as m,_ as v,d as p,b as c,S as g,bX as f,g as b,f as h,m as V,w,C as y,n as x,i as S,F as k,k as B,L as M,a7 as C,s as E,o as L,x as P,y as z,e as N,h as D,P as T,Q as F,j as I,q as X}from"./index.Dk5pbsTU.js";import{E as j}from"./el-input-number.C02ig7uT.js";import{b as Y,E as _}from"./el-popper.Dbn4MgsT.js";import{u as U}from"./index.C9UdVphc.js";import{C as K,I as W,U as R}from"./event.BwRzfsZt.js";import{d as $}from"./debounce.DJJTSR8O.js";import{u as O,a as A}from"./use-form-item.DzRJVC1I.js";import{t as q,d as H}from"./error.D_Dr4eZ1.js";import{u as J}from"./use-form-common-props.CQPDkY7k.js";const Q=Symbol("sliderContextKey"),G=t({modelValue:{type:l([Number,Array]),default:0},id:{type:String,default:void 0},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:Number,default:1},showInput:Boolean,showInputControls:{type:Boolean,default:!0},size:i,inputSize:i,showStops:Boolean,showTooltip:{type:Boolean,default:!0},formatTooltip:{type:l(Function),default:void 0},disabled:Boolean,range:Boolean,vertical:Boolean,height:String,debounce:{type:Number,default:300},rangeStartLabel:{type:String,default:void 0},rangeEndLabel:{type:String,default:void 0},formatValueText:{type:l(Function),default:void 0},tooltipClass:{type:String,default:void 0},placement:{type:String,values:Y,default:"top"},marks:{type:l(Object)},validateEvent:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},...U(["ariaLabel"])}),Z=t=>e(t)||a(t)&&t.every(e),ee={[R]:Z,[W]:Z,[K]:Z},ae=t({modelValue:{type:Number,default:0},vertical:Boolean,tooltipClass:String,placement:{type:String,values:Y,default:"top"}}),te={[R]:a=>e(a)},le=(e,a,t)=>{const{disabled:l,min:i,max:v,step:p,showTooltip:c,persistent:g,precision:f,sliderSize:b,formatTooltip:h,emitChange:V,resetSize:w,updateDragging:y}=o(Q),{tooltip:x,tooltipVisible:S,formatValue:k,displayTooltip:B,hideTooltip:M}=((e,a,t)=>{const l=n(),i=n(!1),o=r((()=>a.value instanceof Function)),s=r((()=>o.value&&a.value(e.modelValue)||e.modelValue)),u=$((()=>{t.value&&(i.value=!0)}),50),d=$((()=>{t.value&&(i.value=!1)}),50);return{tooltip:l,tooltipVisible:i,formatValue:s,displayTooltip:u,hideTooltip:d}})(e,h,c),C=n(),E=r((()=>(e.modelValue-i.value)/(v.value-i.value)*100+"%")),L=r((()=>e.vertical?{bottom:E.value}:{left:E.value})),P=e=>{l.value||(e.preventDefault(),D(e),window.addEventListener("mousemove",T),window.addEventListener("touchmove",T),window.addEventListener("mouseup",F),window.addEventListener("touchend",F),window.addEventListener("contextmenu",F),C.value.focus())},z=e=>{l.value||(a.newPosition=Number.parseFloat(E.value)+e/(v.value-i.value)*100,I(a.newPosition),V())},N=e=>{let a,t;return e.type.startsWith("touch")?(t=e.touches[0].clientY,a=e.touches[0].clientX):(t=e.clientY,a=e.clientX),{clientX:a,clientY:t}},D=t=>{a.dragging=!0,a.isClick=!0;const{clientX:l,clientY:i}=N(t);e.vertical?a.startY=i:a.startX=l,a.startPosition=Number.parseFloat(E.value),a.newPosition=a.startPosition},T=t=>{if(a.dragging){let l;a.isClick=!1,B(),w();const{clientX:i,clientY:o}=N(t);e.vertical?(a.currentY=o,l=(a.startY-a.currentY)/b.value*100):(a.currentX=i,l=(a.currentX-a.startX)/b.value*100),a.newPosition=a.startPosition+l,I(a.newPosition)}},F=()=>{a.dragging&&(setTimeout((()=>{a.dragging=!1,a.hovering||M(),a.isClick||I(a.newPosition),V()}),0),window.removeEventListener("mousemove",T),window.removeEventListener("touchmove",T),window.removeEventListener("mouseup",F),window.removeEventListener("touchend",F),window.removeEventListener("contextmenu",F))},I=async l=>{if(null===l||Number.isNaN(+l))return;l<0?l=0:l>100&&(l=100);const o=100/((v.value-i.value)/p.value);let n=Math.round(l/o)*o*(v.value-i.value)*.01+i.value;n=Number.parseFloat(n.toFixed(f.value)),n!==e.modelValue&&t(R,n),a.dragging||e.modelValue===a.oldValue||(a.oldValue=e.modelValue),await d(),a.dragging&&B(),x.value.updatePopper()};return s((()=>a.dragging),(e=>{y(e)})),u(C,"touchstart",P,{passive:!1}),{disabled:l,button:C,tooltip:x,tooltipVisible:S,showTooltip:c,persistent:g,wrapperStyle:L,formatValue:k,handleMouseEnter:()=>{a.hovering=!0,B()},handleMouseLeave:()=>{a.hovering=!1,a.dragging||M()},onButtonDown:P,onKeyDown:e=>{let a=!0;switch(e.code){case m.left:case m.down:z(-p.value);break;case m.right:case m.up:z(p.value);break;case m.home:l.value||(I(0),V());break;case m.end:l.value||(I(100),V());break;case m.pageDown:z(4*-p.value);break;case m.pageUp:z(4*p.value);break;default:a=!1}a&&e.preventDefault()},setPosition:I}},ie=p({name:"ElSliderButton"});var oe=v(p({...ie,props:ae,emits:te,setup(e,{expose:a,emit:t}){const l=e,i=c("slider"),o=g({hovering:!1,dragging:!1,isClick:!1,startX:0,currentX:0,startY:0,currentY:0,startPosition:0,newPosition:0,oldValue:l.modelValue}),n=r((()=>!!m.value&&v.value)),{disabled:s,button:u,tooltip:d,showTooltip:m,persistent:v,tooltipVisible:p,wrapperStyle:M,formatValue:C,handleMouseEnter:E,handleMouseLeave:L,onButtonDown:P,onKeyDown:z,setPosition:N}=le(l,o,t),{hovering:D,dragging:T}=f(o);return a({onButtonDown:P,onKeyDown:z,setPosition:N,hovering:D,dragging:T}),(e,a)=>(h(),b("div",{ref_key:"button",ref:u,class:x([S(i).e("button-wrapper"),{hover:S(D),dragging:S(T)}]),style:B(S(M)),tabindex:S(s)?-1:0,onMouseenter:S(E),onMouseleave:S(L),onMousedown:S(P),onFocus:S(E),onBlur:S(L),onKeydown:S(z)},[V(S(_),{ref_key:"tooltip",ref:d,visible:S(p),placement:e.placement,"fallback-placements":["top","bottom","right","left"],"stop-popper-mouse-event":!1,"popper-class":e.tooltipClass,disabled:!S(m),persistent:S(n)},{content:w((()=>[y("span",null,k(S(C)),1)])),default:w((()=>[y("div",{class:x([S(i).e("button"),{hover:S(D),dragging:S(T)}])},null,2)])),_:1},8,["visible","placement","popper-class","disabled","persistent"])],46,["tabindex","onMouseenter","onMouseleave","onMousedown","onFocus","onBlur","onKeydown"]))}}),[["__file","button.vue"]]);var ne=p({name:"ElSliderMarker",props:t({mark:{type:l([String,Object]),default:void 0}}),setup(e){const a=c("slider"),t=r((()=>M(e.mark)?e.mark:e.mark.label)),l=r((()=>M(e.mark)?void 0:e.mark.style));return()=>C("div",{class:a.e("marks-text"),style:l.value},t.value)}});const re=(e,a,t)=>{const{form:l,formItem:i}=O(),o=E(),s=n(),u=n(),m={firstButton:s,secondButton:u},v=r((()=>e.disabled||(null==l?void 0:l.disabled)||!1)),p=r((()=>Math.min(a.firstValue,a.secondValue))),c=r((()=>Math.max(a.firstValue,a.secondValue))),g=r((()=>e.range?100*(c.value-p.value)/(e.max-e.min)+"%":100*(a.firstValue-e.min)/(e.max-e.min)+"%")),f=r((()=>e.range?100*(p.value-e.min)/(e.max-e.min)+"%":"0%")),b=r((()=>e.vertical?{height:e.height}:{})),h=r((()=>e.vertical?{height:g.value,bottom:f.value}:{width:g.value,left:f.value})),V=()=>{o.value&&(a.sliderSize=o.value["client"+(e.vertical?"Height":"Width")])},w=t=>{const l=(t=>{const l=e.min+t*(e.max-e.min)/100;if(!e.range)return s;let i;return i=Math.abs(p.value-l)<Math.abs(c.value-l)?a.firstValue<a.secondValue?"firstButton":"secondButton":a.firstValue>a.secondValue?"firstButton":"secondButton",m[i]})(t);return l.value.setPosition(t),l},y=e=>{t(R,e),t(W,e)},x=async()=>{await d(),t(K,e.range?[p.value,c.value]:e.modelValue)},S=t=>{var l,i,n,r,s,u;if(v.value||a.dragging)return;V();let d=0;if(e.vertical){const e=null!=(n=null==(i=null==(l=t.touches)?void 0:l.item(0))?void 0:i.clientY)?n:t.clientY;d=(o.value.getBoundingClientRect().bottom-e)/a.sliderSize*100}else{d=((null!=(u=null==(s=null==(r=t.touches)?void 0:r.item(0))?void 0:s.clientX)?u:t.clientX)-o.value.getBoundingClientRect().left)/a.sliderSize*100}return d<0||d>100?void 0:w(d)};return{elFormItem:i,slider:o,firstButton:s,secondButton:u,sliderDisabled:v,minValue:p,maxValue:c,runwayStyle:b,barStyle:h,resetSize:V,setPosition:w,emitChange:x,onSliderWrapperPrevent:e=>{var a,t;((null==(a=m.firstButton.value)?void 0:a.dragging)||(null==(t=m.secondButton.value)?void 0:t.dragging))&&e.preventDefault()},onSliderClick:e=>{S(e)&&x()},onSliderDown:async e=>{const a=S(e);a&&(await d(),a.value.onButtonDown(e))},onSliderMarkerDown:e=>{if(v.value||a.dragging)return;w(e)&&x()},setFirstValue:t=>{a.firstValue=null!=t?t:e.min,y(e.range?[p.value,c.value]:null!=t?t:e.min)},setSecondValue:t=>{a.secondValue=t,e.range&&y([p.value,c.value])}}},se=p({name:"ElSlider"});const ue=X(v(p({...se,props:G,emits:ee,setup(t,{expose:l,emit:i}){const o=t,m=c("slider"),{t:v}=P(),p=g({firstValue:0,secondValue:0,oldValue:0,dragging:!1,sliderSize:1}),{elFormItem:w,slider:k,firstButton:M,secondButton:C,sliderDisabled:E,minValue:X,maxValue:Y,runwayStyle:_,barStyle:U,resetSize:K,emitChange:$,onSliderWrapperPrevent:O,onSliderClick:G,onSliderDown:Z,onSliderMarkerDown:ee,setFirstValue:ae,setSecondValue:te}=re(o,p,i),{stops:le,getStopStyle:ie}=((e,a,t,l)=>({stops:r((()=>{if(!e.showStops||e.min>e.max)return[];if(0===e.step)return[];const i=(e.max-e.min)/e.step,o=100*e.step/(e.max-e.min),n=Array.from({length:i-1}).map(((e,a)=>(a+1)*o));return e.range?n.filter((a=>a<100*(t.value-e.min)/(e.max-e.min)||a>100*(l.value-e.min)/(e.max-e.min))):n.filter((t=>t>100*(a.firstValue-e.min)/(e.max-e.min)))})),getStopStyle:a=>e.vertical?{bottom:`${a}%`}:{left:`${a}%`}}))(o,p,X,Y),{inputId:se,isLabeledByFormItem:ue}=A(o,{formItemContext:w}),de=J(),me=r((()=>o.inputSize||de.value)),ve=r((()=>o.ariaLabel||v("el.slider.defaultLabel",{min:o.min,max:o.max}))),pe=r((()=>o.range?o.rangeStartLabel||v("el.slider.defaultRangeStartLabel"):ve.value)),ce=r((()=>o.formatValueText?o.formatValueText(ye.value):`${ye.value}`)),ge=r((()=>o.rangeEndLabel||v("el.slider.defaultRangeEndLabel"))),fe=r((()=>o.formatValueText?o.formatValueText(xe.value):`${xe.value}`)),be=r((()=>[m.b(),m.m(de.value),m.is("vertical",o.vertical),{[m.m("with-input")]:o.showInput}])),he=(e=>r((()=>e.marks?Object.keys(e.marks).map(Number.parseFloat).sort(((e,a)=>e-a)).filter((a=>a<=e.max&&a>=e.min)).map((a=>({point:a,position:100*(a-e.min)/(e.max-e.min),mark:e.marks[a]}))):[])))(o);((t,l,i,o,n,r)=>{const u=e=>{n(R,e),n(W,e)},d=()=>t.range?![i.value,o.value].every(((e,a)=>e===l.oldValue[a])):t.modelValue!==l.oldValue,m=()=>{var i,o;t.min>t.max&&q("Slider","min should not be greater than max.");const n=t.modelValue;t.range&&a(n)?n[1]<t.min?u([t.min,t.min]):n[0]>t.max?u([t.max,t.max]):n[0]<t.min?u([t.min,n[1]]):n[1]>t.max?u([n[0],t.max]):(l.firstValue=n[0],l.secondValue=n[1],d()&&(t.validateEvent&&(null==(i=null==r?void 0:r.validate)||i.call(r,"change").catch((e=>H()))),l.oldValue=n.slice())):t.range||!e(n)||Number.isNaN(n)||(n<t.min?u(t.min):n>t.max?u(t.max):(l.firstValue=n,d()&&(t.validateEvent&&(null==(o=null==r?void 0:r.validate)||o.call(r,"change").catch((e=>H()))),l.oldValue=n)))};m(),s((()=>l.dragging),(e=>{e||m()})),s((()=>t.modelValue),((e,t)=>{l.dragging||a(e)&&a(t)&&e.every(((e,a)=>e===t[a]))&&l.firstValue===e[0]&&l.secondValue===e[1]||m()}),{deep:!0}),s((()=>[t.min,t.max]),(()=>{m()}))})(o,p,X,Y,i,w);const Ve=r((()=>{const e=[o.min,o.max,o.step].map((e=>{const a=`${e}`.split(".")[1];return a?a.length:0}));return Math.max.apply(null,e)})),{sliderWrapper:we}=((t,l,i)=>{const o=n();return L((async()=>{t.range?(a(t.modelValue)?(l.firstValue=Math.max(t.min,t.modelValue[0]),l.secondValue=Math.min(t.max,t.modelValue[1])):(l.firstValue=t.min,l.secondValue=t.max),l.oldValue=[l.firstValue,l.secondValue]):(!e(t.modelValue)||Number.isNaN(t.modelValue)?l.firstValue=t.min:l.firstValue=Math.min(t.max,Math.max(t.min,t.modelValue)),l.oldValue=l.firstValue),u(window,"resize",i),await d(),i()})),{sliderWrapper:o}})(o,p,K),{firstValue:ye,secondValue:xe,sliderSize:Se}=f(p);return u(we,"touchstart",O,{passive:!1}),u(we,"touchmove",O,{passive:!1}),z(Q,{...f(o),sliderSize:Se,disabled:E,precision:Ve,emitChange:$,resetSize:K,updateDragging:e=>{p.dragging=e}}),l({onSliderClick:G}),(e,a)=>{var t,l;return h(),b("div",{id:e.range?S(se):void 0,ref_key:"sliderWrapper",ref:we,class:x(S(be)),role:e.range?"group":void 0,"aria-label":e.range&&!S(ue)?S(ve):void 0,"aria-labelledby":e.range&&S(ue)?null==(t=S(w))?void 0:t.labelId:void 0},[y("div",{ref_key:"slider",ref:k,class:x([S(m).e("runway"),{"show-input":e.showInput&&!e.range},S(m).is("disabled",S(E))]),style:B(S(_)),onMousedown:S(Z),onTouchstartPassive:S(Z)},[y("div",{class:x(S(m).e("bar")),style:B(S(U))},null,6),V(oe,{id:e.range?void 0:S(se),ref_key:"firstButton",ref:M,"model-value":S(ye),vertical:e.vertical,"tooltip-class":e.tooltipClass,placement:e.placement,role:"slider","aria-label":e.range||!S(ue)?S(pe):void 0,"aria-labelledby":!e.range&&S(ue)?null==(l=S(w))?void 0:l.labelId:void 0,"aria-valuemin":e.min,"aria-valuemax":e.range?S(xe):e.max,"aria-valuenow":S(ye),"aria-valuetext":S(ce),"aria-orientation":e.vertical?"vertical":"horizontal","aria-disabled":S(E),"onUpdate:modelValue":S(ae)},null,8,["id","model-value","vertical","tooltip-class","placement","aria-label","aria-labelledby","aria-valuemin","aria-valuemax","aria-valuenow","aria-valuetext","aria-orientation","aria-disabled","onUpdate:modelValue"]),e.range?(h(),N(oe,{key:0,ref_key:"secondButton",ref:C,"model-value":S(xe),vertical:e.vertical,"tooltip-class":e.tooltipClass,placement:e.placement,role:"slider","aria-label":S(ge),"aria-valuemin":S(ye),"aria-valuemax":e.max,"aria-valuenow":S(xe),"aria-valuetext":S(fe),"aria-orientation":e.vertical?"vertical":"horizontal","aria-disabled":S(E),"onUpdate:modelValue":S(te)},null,8,["model-value","vertical","tooltip-class","placement","aria-label","aria-valuemin","aria-valuemax","aria-valuenow","aria-valuetext","aria-orientation","aria-disabled","onUpdate:modelValue"])):D("v-if",!0),e.showStops?(h(),b("div",{key:1},[(h(!0),b(T,null,F(S(le),((e,a)=>(h(),b("div",{key:a,class:x(S(m).e("stop")),style:B(S(ie)(e))},null,6)))),128))])):D("v-if",!0),S(he).length>0?(h(),b(T,{key:2},[y("div",null,[(h(!0),b(T,null,F(S(he),((e,a)=>(h(),b("div",{key:a,style:B(S(ie)(e.position)),class:x([S(m).e("stop"),S(m).e("marks-stop")])},null,6)))),128))]),y("div",{class:x(S(m).e("marks"))},[(h(!0),b(T,null,F(S(he),((e,a)=>(h(),N(S(ne),{key:a,mark:e.mark,style:B(S(ie)(e.position)),onMousedown:I((a=>S(ee)(e.position)),["stop"])},null,8,["mark","style","onMousedown"])))),128))],2)],64)):D("v-if",!0)],46,["onMousedown","onTouchstartPassive"]),e.showInput&&!e.range?(h(),N(S(j),{key:0,ref:"input","model-value":S(ye),class:x(S(m).e("input")),step:e.step,disabled:S(E),controls:e.showInputControls,min:e.min,max:e.max,precision:S(Ve),debounce:e.debounce,size:S(me),"onUpdate:modelValue":S(ae),onChange:S($)},null,8,["model-value","class","step","disabled","controls","min","max","precision","debounce","size","onUpdate:modelValue","onChange"])):D("v-if",!0)],10,["id","role","aria-label","aria-labelledby"])}}}),[["__file","slider.vue"]]));export{ue as E};
