import request from '@/utils/request'

export class BannerPageQuery {
  pageNum = 1
  pageSize = 10
  startTime?: string
  endTime?: string

  constructor(pageSize = '10') {
    this.pageSize = parseInt(pageSize)
  }
}

export interface BannerPageVO {
  id: number
  tenantId: number
  bannerUrl: string
  targetUrl: string
  isShow: number
  createdAt: string
  updatedAt: string
  loading?: boolean
}

export class UpdateBannerStatusDto {
  id: number
  isShow: number

  constructor(data: BannerPageVO) {
    this.id = data.id
    this.isShow = data.isShow
  }
}

export interface SaveBannerDto {
  id?: number
  bannerUrl: string
  targetUrl: string
  isShow: number
}

export default class BannerApi {
  static page(params: BannerPageQuery) {
    return request.get('/api/basic/banner/list', { params })
  }

  static editStatus(data: UpdateBannerStatusDto) {
    return request.put('/api/basic/banner/status', data)
  }

  static remove(id: number) {
    return request.delete(`/api/basic/banner/${id}`)
  }

  static saveOrUpdate(data: SaveBannerDto) {
    return request.post('/api/basic/banner', data)
  }
} 