// 是不是手机号
export function isPhone(tel: string): boolean {
  return /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(tel);
}
// 版本号验证
export function isVersion(version: string): boolean {
  return /^\d+(?:\.\d+){0,9}$/.test(version);
}

/**
 * 生成表单验证器
 * @param fun 验证函数
 * @param errMessage 错误信息
 * @returns 表单验证器
 */
export function formValidator(fun: any, errMessage: string): any {
  return {
    validator: function (_: any, value: any, callback: any): any {
      if (!!value && !fun(value)) {
        return callback(new Error(errMessage));
      } else {
        callback();
      }
    },
    trigger: "blur",
  };
}
