<template>
  <div style="padding: 0 10px">
    <div v-html="info.configValue" />
  </div>
</template>

<script setup lang="ts">
import ConfigAPI, { ConfigForm } from "@/api/system/config";

const code = String(useRoute().query.code || "");
const info = ref<ConfigForm>({} as ConfigForm);

function getContentText() {
  ConfigAPI.getValueByKey(code).then((res) => {
    info.value = res;
    document.title = res.configName || "";
  });
}

if (code) {
  getContentText();
}
</script>
