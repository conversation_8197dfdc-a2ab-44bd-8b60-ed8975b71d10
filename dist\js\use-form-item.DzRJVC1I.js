import{b as e,f as a}from"./use-form-common-props.CQPDkY7k.js";import{u as o}from"./index.D6CER_Ot.js";import{A as n,r as t,c as d,o as l,I as s,ae as u,bC as r}from"./index.Dk5pbsTU.js";const i=()=>({form:n(e,void 0),formItem:n(a,void 0)}),m=(e,{formItemContext:a,disableIdGeneration:n,disableIdManagement:i})=>{n||(n=t(!1)),i||(i=t(!1));const m=t();let v;const I=d((()=>{var o;return!!(!e.label&&!e.ariaLabel&&a&&a.inputIds&&(null==(o=a.inputIds)?void 0:o.length)<=1)}));return l((()=>{v=s([u(e,"id"),n],(([e,n])=>{const t=null!=e?e:n?void 0:o().value;t!==m.value&&((null==a?void 0:a.removeInputId)&&(m.value&&a.removeInputId(m.value),(null==i?void 0:i.value)||n||!t||a.addInputId(t)),m.value=t)}),{immediate:!0})})),r((()=>{v&&v(),(null==a?void 0:a.removeInputId)&&m.value&&a.removeInputId(m.value)})),{isLabeledByFormItem:I,inputId:m}};export{m as a,i as u};
