<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="status" label="状态">
          <dict v-model="page.query.status" code="version_status" />
        </el-form-item>
        <el-form-item prop="status" label="操作系统">
          <dict v-model="page.query.deviceType" code="version_device_type" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          v-hasPerm="['sys:version:save']"
          type="success"
          icon="plus"
          @click="editModelRef?.open()"
        >
          新增
        </el-button>
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="版本号" align="center" prop="versionName" min-width="100" />
        <el-table-column label="操作系统" align="center" width="100">
          <template #default="{ row }">
            <dict-label v-model="row.deviceType" code="version_device_type" />
          </template>
        </el-table-column>
        <!--        <el-table-column label="所属平台" align="center" width="80">-->
        <!--          <template #default="{ row }">-->
        <!--            <dict-label v-model="row.platformType" code="version_platform_type" />-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="状态" align="center" width="90">
          <template #default="{ row }">
            <dict-label v-model="row.status" code="version_status" />
          </template>
        </el-table-column>
        <el-table-column label="是否热更新" align="center" width="80">
          <template #default="{ row }">
            <dict-label v-model="row.isHotUpdate" code="yes_or_no" />
          </template>
        </el-table-column>
        <el-table-column
          label="更新内容"
          align="center"
          prop="updateContent"
          min-width="150"
          show-overflow-tooltip
          :tooltip-options="{ 'popper-class': 'table-popper-class' }"
        />
        <el-table-column label="发版时间" align="center" prop="publishTime" min-width="100" />
        <el-table-column label="创建时间" align="center" prop="createdAt" min-width="100" />
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === 0 && hasAuth('sys:version:save')"
              type="primary"
              size="small"
              link
              icon="edit"
              @click="editModelRef?.open(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="scope.row.status === 0 && hasAuth('sys:version:delete')"
              type="danger"
              size="small"
              link
              icon="delete"
              :loading="scope.row.loading"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
            <el-button
              v-if="scope.row.status === 0 && hasAuth('sys:version:save')"
              type="warning"
              size="small"
              link
              :loading="scope.row.loading"
              @click="publish(scope.row)"
            >
              发布
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
    <edit-model ref="editModelRef" @success="resetQuery" />
  </div>
</template>

<script setup lang="ts">
import { hasAuth } from "@/plugins/permission";
import EditModel from "./edit.vue";

defineOptions({ name: "AppVersion" });
import { usePage } from "@/utils/commonSetup";
import VersionApi, { AppVersionQuery, AppVersionVO } from "@/api/system/version";
const editModelRef = ref<InstanceType<typeof EditModel>>();
const { page, getPage, resetQuery } = usePage<AppVersionQuery, AppVersionVO>(
  new AppVersionQuery(),
  VersionApi.page
);

function handleDelete(_row: AppVersionVO) {
  ElMessageBox.confirm(`确定删除《${_row.versionName}》版本吗？`, `删除`, {
    confirmButtonText: "确定删除",
    cancelButtonText: "取消",
    confirmButtonClass: "el-button--danger",
    type: "error",
  })
    .then(() => {
      _row.loading = true;
      VersionApi.remove(_row.id)
        .then(() => {
          ElMessage.success("删除成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}

function publish(_row: AppVersionVO) {
  ElMessageBox.confirm(`确定要发布新版本吗？`, `版本发布`, {
    confirmButtonText: "确定发布",
    cancelButtonText: "取消",
    confirmButtonClass: "el-button--danger",
    type: "error",
  })
    .then(() => {
      _row.loading = true;
      VersionApi.publish(_row.id)
        .then(() => {
          ElMessage.success("操作成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}
</script>
<style lang="scss">
.table-popper-class {
  max-width: 500px;
}
</style>
