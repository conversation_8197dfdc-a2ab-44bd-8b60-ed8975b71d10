import{d as e,r as t,aQ as a,g as o,f as r,C as s,m as i,w as l,Z as n,i as p,$ as m,V as d,e as u,h as c,F as j,az as f}from"./index.Dk5pbsTU.js";import{v as g}from"./el-loading.Dqi-qL7c.js";import{E as _}from"./el-card.DwLhVNHW.js";import y from"./index.Cywy93e7.js";import{a as w,E as h}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{_ as b}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as v}from"./el-image-viewer.BH897zgF.js";/* empty css               */import{a as x,E as k}from"./el-form-item.Bw6Zyv_7.js";import{E as C}from"./el-button.CXI119n4.js";import{_ as q}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as U}from"./el-input.DiGatoux.js";import{E as V,S as E,U as N}from"./edit.m8foxpUe.js";import{W as S,a as B}from"./webcaster.CYqw_lYq.js";import{u as R}from"./commonSetup.Dm-aByKQ.js";/* empty css                       */import"./el-overlay.DpVCS8zG.js";import{E as P}from"./index.BcMfjWDS.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./event.BwRzfsZt.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.DuiNpp1i.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./position.DfR5znly.js";import"./el-radio.w2rep3_A.js";import"./el-dialog.Cnp8BitR.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./validator.HGn2BZtD.js";const T={class:"app-container"},z={class:"search-bar"},$={class:"mb-10px"},K=e({name:"Sales",__name:"index",setup(e){const K=t(),{page:A,getPage:F,resetQuery:I}=R(new S("2"),B.page);return(e,t)=>{const S=U,B=x,R=q,L=C,M=k,O=h,Q=v,H=b,J=w,Z=y,D=_,G=a("hasPerm"),W=g;return r(),o("div",T,[s("div",z,[i(M,{ref:"queryFormRef",model:p(A).query,inline:!0},{default:l((()=>[i(B,{prop:"keywords",label:""},{default:l((()=>[i(S,{modelValue:p(A).query.keywords,"onUpdate:modelValue":t[0]||(t[0]=e=>p(A).query.keywords=e),placeholder:"姓名、昵称检索",clearable:"",onKeyup:n(p(F),["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),i(B,{prop:"status",label:"账号状态"},{default:l((()=>[i(R,{modelValue:p(A).query.status,"onUpdate:modelValue":t[1]||(t[1]=e=>p(A).query.status=e),code:"disable_status"},null,8,["modelValue"])])),_:1}),i(B,null,{default:l((()=>[i(L,{type:"primary",icon:"search",onClick:p(F)},{default:l((()=>t[6]||(t[6]=[m("搜索")]))),_:1,__:[6]},8,["onClick"]),i(L,{icon:"refresh",onClick:p(I)},{default:l((()=>t[7]||(t[7]=[m("重置")]))),_:1,__:[7]},8,["onClick"])])),_:1})])),_:1},8,["model"])]),i(D,{shadow:"never"},{default:l((()=>[s("div",$,[d((r(),u(L,{type:"success",icon:"plus",onClick:t[2]||(t[2]=e=>{var t;return null==(t=p(K))?void 0:t.open()})},{default:l((()=>t[8]||(t[8]=[m(" 新增 ")]))),_:1,__:[8]})),[[G,["wa:sales:save"]]])]),d((r(),u(J,{ref:"dataTableRef",data:p(A).data.records,"highlight-current-row":"",border:""},{default:l((()=>[i(O,{label:"序号",align:"center",width:"55",type:"index"}),i(O,{label:"头像",align:"center",prop:"name",width:"100"},{default:l((({row:e})=>[i(Q,{style:{width:"30px",height:"30px","border-radius":"30px"},"preview-src-list":[e.avatar],"preview-teleported":"",src:e.avatar},null,8,["preview-src-list","src"])])),_:1}),i(O,{label:"微信昵称",align:"center",prop:"sysUserName","min-width":"120"}),i(O,{label:"销售姓名",align:"center",prop:"userName","min-width":"120"}),i(O,{label:"销售昵称",align:"center",prop:"nickName","min-width":"120"}),i(O,{label:"账号状态",align:"center",width:"80"},{default:l((({row:e})=>[i(H,{modelValue:e.status,"onUpdate:modelValue":t=>e.status=t,code:"disable_status"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),i(O,{fixed:"right",label:"操作",width:"180"},{default:l((e=>[d((r(),u(L,{type:"danger",size:"small",link:"",icon:"delete",loading:e.row.loading,onClick:t=>{return a=e.row,void P.confirm(`确定删除销售《${a.nickName}》吗？`,"删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{a.loading=!0,E.remove(a.id).then((()=>{f.success("删除成功"),I()})).finally((()=>a.loading=!1))})).catch((()=>f.info("已取消")));var a}},{default:l((()=>t[9]||(t[9]=[m(" 删除 ")]))),_:2,__:[9]},1032,["loading","onClick"])),[[G,["wa:sales:delete"]]]),d((r(),u(L,{type:"warning",size:"small",link:"",loading:e.row.loading,onClick:t=>{return a=e.row,void P.confirm(`确定要${a.status?"停用":"启用"}销售《${a.nickName}》吗？`,(a.status?"停用":"启用")+"授权",{confirmButtonText:"确定",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{const e=new N(a);e.status?e.status=0:e.status=1,a.loading=!0,E.editStatus(e).then((()=>{f.success("操作成功"),I()})).finally((()=>a.loading=!1))})).catch((()=>f.info("已取消")));var a}},{default:l((()=>[m(j(e.row.status?"停用":"启用"),1)])),_:2},1032,["loading","onClick"])),[[G,["wa:sales:save"]]])])),_:1})])),_:1},8,["data"])),[[W,p(A).loading]]),p(A).data.totalRow?(r(),u(Z,{key:0,total:p(A).data.totalRow,"onUpdate:total":t[3]||(t[3]=e=>p(A).data.totalRow=e),page:p(A).query.pageNum,"onUpdate:page":t[4]||(t[4]=e=>p(A).query.pageNum=e),limit:p(A).query.pageSize,"onUpdate:limit":t[5]||(t[5]=e=>p(A).query.pageSize=e),onPagination:p(F)},null,8,["total","page","limit","onPagination"])):c("",!0)])),_:1}),i(V,{ref_key:"editModelRef",ref:K,onSuccess:p(I)},null,8,["onSuccess"])])}}});export{K as default};
