<template>
  <el-dialog v-model="dialog.visible" title="快捷语设置" width="600px">
    <el-form ref="formRef" :model="formData" label-width="0">
      <el-form-item>
        <div class="quick-reply-list">
          <div v-for="(item, index) in formData.quickReply" :key="index" class="quick-reply-item">
            <el-input
              v-model="formData.quickReply[index]"
              placeholder="请输入快捷回复内容"
              :maxlength="100"
              show-word-limit
            >
              <template #append>
                <el-button type="danger" link @click="handleRemove(index)">删除</el-button>
                <el-divider direction="vertical" />
                <el-button type="primary" link @click="handleAdd">
                  <el-icon><plus /></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
          <div v-if="formData.quickReply.length === 0" class="empty-list">
            <el-button type="primary" plain icon="plus" @click="handleAdd">新增快捷语</el-button>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="handleSubmit">确 定</el-button>
        <el-button @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import LiveRoomApi from "@/api/live/liveRoom";
import type { LiveRoomPageVo } from "@/api/live/liveRoom";
import { Plus } from '@element-plus/icons-vue'

const dialog = reactive({
  visible: false,
});

const formData = reactive({
  id: 0,
  quickReply: [] as string[],
});

const loading = ref(false);
const emits = defineEmits(["success"]);

function handleAdd() {
  formData.quickReply.push("");
}

function handleRemove(index: number) {
  formData.quickReply.splice(index, 1);
}

function handleSubmit() {
  // 过滤掉空字符串
  const quickReply = formData.quickReply.filter((item) => item.trim() !== "");
  
  if (quickReply.length === 0) {
    ElMessage.warning("请至少添加一条快捷语");
    return;
  }

  loading.value = true;
  LiveRoomApi.saveQuickReply({
    id: formData.id,
    quickReply: quickReply,
  })
    .then(() => {
      ElMessage.success("保存成功");
      dialog.visible = false;
      emits("success");
    })
    .finally(() => {
      loading.value = false;
    });
}

defineExpose({
  open: (row: LiveRoomPageVo) => {
    dialog.visible = true;
    formData.id = row.id;
    try {
      // 尝试解析JSON字符串
      const quickReply = row.quickReply ? JSON.parse(row.quickReply as string) : [];
      formData.quickReply = Array.isArray(quickReply) ? quickReply : [];
    } catch (e) {
      formData.quickReply = [];
      console.warn('快捷回复格式解析错误:', e);
    }
  },
});
</script>

<style scoped>
.quick-reply-list {
  max-height: 400px;
  overflow-y: auto;
}

.quick-reply-item {
  margin-bottom: 10px;
}

.quick-reply-item:last-child {
  margin-bottom: 0;
}

.empty-list {
  text-align: center;
  padding: 20px 0;
}

:deep(.el-input-group__append) {
  padding: 0;
}

:deep(.el-input-group__append .el-button) {
  border: none;
  padding: 8px 15px;
  margin: 0;
}

:deep(.el-divider--vertical) {
  margin: 0;
}
</style> 