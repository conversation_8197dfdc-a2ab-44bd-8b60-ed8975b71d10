<template>
  <el-dialog
    :title="form.id ? '编辑标签' : '新增标签'"
    v-model="visible"
    width="500px"
    append-to-body
  >
    <el-form ref="formRef" :model="form" label-position="top" :rules="rules" label-width="80px">
      <el-form-item label="标签名称" prop="name">
        <el-input
          v-model="form.name"
          :placeholder="form.id ? '请输入标签名称' : '请输入标签名称，多个标签用英文逗号分隔'"
          :maxlength="11"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import TagApi, { UpdateTagDto, BatchAddTagDto } from "@/api/live/tags";

defineOptions({ name: "EditTag" });

const emit = defineEmits(["success"]);
const visible = ref(false);
const loading = ref(false);

const formRef = ref<FormInstance>();
const form = reactive<UpdateTagDto & { id: string }>({
  id: "",
  name: "",
  status: 1,
});

const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入标签名称", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
});

/** 取消按钮 */
function cancel() {
  reset();
}

/** 提交按钮 */
function submitForm() {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      if (form.id) {
        // 编辑模式
        TagApi.update(form)
          .then(() => {
            ElMessage.success("操作成功");
            emit("success");
            reset();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        // 新增模式，支持批量
        const tagNames = form.name
          .split(",")
          .map((tag) => tag.trim())
          .filter((tag) => tag);
        if (tagNames.length === 0) {
          ElMessage.warning("请输入有效的标签名称");
          loading.value = false;
          return;
        }
        const batchForm: BatchAddTagDto = {
          tagNames: tagNames.join(","),
        };
        TagApi.batchAdd(batchForm)
          .then(() => {
            ElMessage.success("操作成功");
            emit("success");
            reset();
          })
          .finally(() => {
            loading.value = false;
          });
      }
    }
  });
}

/** 表单重置 */
function reset() {
  visible.value = false;
  form.name = "";
  form.status = 1;
  form.id = "";
}

/** 打开弹窗 */
function open(row?: UpdateTagDto) {
  reset();
  if (row) {
    Object.assign(form, row);
  }
  visible.value = true;
}

defineExpose({
  open,
});
</script>
