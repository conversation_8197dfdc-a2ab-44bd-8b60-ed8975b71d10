<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="700px">
    <el-form ref="editFormRef" :model="formData" :rules="rules" label-width="140px">
      <el-form-item label="直播间名称" prop="roomName">
        <el-input
          v-model="formData.roomName"
          :maxlength="50"
          show-word-limit
          placeholder="请输入直播间名称"
        />
      </el-form-item>
      <el-form-item label="封面图" prop="coverUrl">
        <SingleImageUpload
          v-model="formData.coverUrl"
          :style="{ width: '100px', height: '100px' }"
        />
      </el-form-item>

      <el-form-item label="直播间介绍" prop="description">
        <el-input
          v-model="formData.description"
          :maxlength="200"
          show-word-limit
          type="textarea"
          :rows="5"
          placeholder="请输入直播间介绍"
        />
      </el-form-item>

      <el-form-item label="直播间公告" prop="announcement">
        <el-input
          v-model="formData.announcement"
          :maxlength="200"
          show-word-limit
          type="textarea"
          :rows="4"
          placeholder="请输入直播间公告"
        />
      </el-form-item>

      <el-form-item label="温馨提示" prop="notice">
        <el-input
          v-model="formData.notice"
          :maxlength="80"
          show-word-limit
          type="textarea"
          :rows="4"
          placeholder="请输入温馨提示"
        />
      </el-form-item>

      <el-form-item label="在线用户显示设置">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="主播" label-width="80px">
              <el-switch
                v-model="formData.anchorShowUserCount"
                inline-prompt
                active-text="开"
                inactive-text="关"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="助理" label-width="80px">
              <el-switch
                v-model="formData.assistantShowUserCount"
                inline-prompt
                active-text="开"
                inactive-text="关"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="销售" label-width="80px">
              <el-switch
                v-model="formData.saleShowUserCount"
                inline-prompt
                active-text="开"
                inactive-text="关"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户" label-width="80px">
              <el-switch
                v-model="formData.userShowUserCount"
                inline-prompt
                active-text="开"
                inactive-text="关"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <el-row :gutter="20" style="margin: 0">
        <el-col :span="12">
          <el-form-item label="直播间默认字号" prop="fontSize">
            <el-select v-model="formData.fontSize" placeholder="请选择字号">
              <el-option label="14px" value="14" />
              <el-option label="15px" value="15" />
              <el-option label="16px" value="16" />
              <el-option label="17px" value="17" />
              <el-option label="18px" value="18" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否允许普通用户回复" label-width="160px" prop="formData">
            <el-switch v-model="formData.userReply" :active-value="1" :inactive-value="0" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="直播暂停自动结束" prop="stopLiveMinutes">
        <el-input-number
          v-model="formData.stopLiveMinutes"
          :min="1"
          :max="999"
          placeholder="请输入分钟数"
        />
        <span style="margin-left: 10px">分钟</span>
      </el-form-item>
      <div class="el-form-item-msg">当直播间暂停时长等于该值，则自动结束</div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import LiveRoomApi, { LiveRoomDto, LiveRoomPageVo } from "@/api/live/liveRoom";

const dialog = reactive({
  visible: false,
  title: "",
});
const formData = ref(new LiveRoomDto());
const editFormRef = ref();
const emits = defineEmits(["success"]);
const loading = ref(false);
const rules = {
  coverUrl: [{ required: true, message: "请上传封面图", trigger: "blur" }],
  roomName: [{ required: true, message: "请输入直播间名称", trigger: "blur" }],
};

function handleSubmit() {
  editFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      LiveRoomApi.save(formData.value.toAPI() as any)
        .then(() => {
          ElMessage.success("保存成功");
          dialog.visible = false;
          emits("success");
        })
        .finally(() => (loading.value = false));
    }
  });
}

defineExpose({
  open: (_row?: LiveRoomPageVo) => {
    dialog.visible = true;
    dialog.title = _row?.id ? "编辑直播间" : "新增直播间";
    formData.value = new LiveRoomDto(_row);
  },
});
</script>

<style scoped>
.el-form-item-msg {
  margin-left: 140px;
  display: inline-block;
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
</style>
