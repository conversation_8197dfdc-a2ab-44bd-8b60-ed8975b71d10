import{t as e,_ as a,d as l,A as t,b as s,r,c as o,a6 as i,u as n,ae as u,e as v,f as c,w as f,V as m,C as d,j as p,n as y,i as b,k as h,X as g,T as w,H as S,g as z,m as x,P as _,b6 as E,z as L,J as T,I as k,y as C,S as H,b7 as B,o as R,a0 as j,b8 as N,h as A,l as M,D as O,aa as W,ab as $,q}from"./index.Dk5pbsTU.js";import{t as P}from"./error.D_Dr4eZ1.js";import{u as X}from"./index.C9UdVphc.js";const I={vertical:{offset:"offsetHeight",scroll:"scrollTop",scrollSize:"scrollHeight",size:"height",key:"vertical",axis:"Y",client:"clientY",direction:"top"},horizontal:{offset:"offsetWidth",scroll:"scrollLeft",scrollSize:"scrollWidth",size:"width",key:"horizontal",axis:"X",client:"clientX",direction:"left"}},K=Symbol("scrollbarContextKey"),Y=e({vertical:Boolean,size:String,move:Number,ratio:{type:Number,required:!0},always:Boolean});var D=a(l({__name:"thumb",props:Y,setup(e){const a=e,l=t(K),z=s("scrollbar");l||P("Thumb","can not inject scrollbar context");const x=r(),_=r(),E=r({}),L=r(!1);let T=!1,k=!1,C=S?document.onselectstart:null;const H=o((()=>I[a.vertical?"vertical":"horizontal"])),B=o((()=>(({move:e,size:a,bar:l})=>({[l.size]:a,transform:`translate${l.axis}(${e}%)`}))({size:a.size,move:a.move,bar:H.value}))),R=o((()=>x.value[H.value.offset]**2/l.wrapElement[H.value.scrollSize]/a.ratio/_.value[H.value.offset])),j=e=>{var a;if(e.stopPropagation(),e.ctrlKey||[1,2].includes(e.button))return;null==(a=window.getSelection())||a.removeAllRanges(),A(e);const l=e.currentTarget;l&&(E.value[H.value.axis]=l[H.value.offset]-(e[H.value.client]-l.getBoundingClientRect()[H.value.direction]))},N=e=>{if(!_.value||!x.value||!l.wrapElement)return;const a=100*(Math.abs(e.target.getBoundingClientRect()[H.value.direction]-e[H.value.client])-_.value[H.value.offset]/2)*R.value/x.value[H.value.offset];l.wrapElement[H.value.scroll]=a*l.wrapElement[H.value.scrollSize]/100},A=e=>{e.stopImmediatePropagation(),T=!0,document.addEventListener("mousemove",M),document.addEventListener("mouseup",O),C=document.onselectstart,document.onselectstart=()=>!1},M=e=>{if(!x.value||!_.value)return;if(!1===T)return;const a=E.value[H.value.axis];if(!a)return;const t=100*(-1*(x.value.getBoundingClientRect()[H.value.direction]-e[H.value.client])-(_.value[H.value.offset]-a))*R.value/x.value[H.value.offset];l.wrapElement[H.value.scroll]=t*l.wrapElement[H.value.scrollSize]/100},O=()=>{T=!1,E.value[H.value.axis]=0,document.removeEventListener("mousemove",M),document.removeEventListener("mouseup",O),W(),k&&(L.value=!1)};i((()=>{W(),document.removeEventListener("mouseup",O)}));const W=()=>{document.onselectstart!==C&&(document.onselectstart=C)};return n(u(l,"scrollbarElement"),"mousemove",(()=>{k=!1,L.value=!!a.size})),n(u(l,"scrollbarElement"),"mouseleave",(()=>{k=!0,L.value=T})),(e,a)=>(c(),v(w,{name:b(z).b("fade"),persisted:""},{default:f((()=>[m(d("div",{ref_key:"instance",ref:x,class:y([b(z).e("bar"),b(z).is(b(H).key)]),onMousedown:N,onClick:p((()=>{}),["stop"])},[d("div",{ref_key:"thumb",ref:_,class:y(b(z).e("thumb")),style:h(b(B)),onMousedown:j},null,38)],42,["onClick"]),[[g,e.always||L.value]])])),_:1},8,["name"]))}}),[["__file","thumb.vue"]]);var J=a(l({__name:"bar",props:e({always:{type:Boolean,default:!0},minSize:{type:Number,required:!0}}),setup(e,{expose:a}){const l=e,s=t(K),o=r(0),i=r(0),n=r(""),u=r(""),v=r(1),f=r(1);return a({handleScroll:e=>{if(e){const a=e.offsetHeight-4,l=e.offsetWidth-4;i.value=100*e.scrollTop/a*v.value,o.value=100*e.scrollLeft/l*f.value}},update:()=>{const e=null==s?void 0:s.wrapElement;if(!e)return;const a=e.offsetHeight-4,t=e.offsetWidth-4,r=a**2/e.scrollHeight,o=t**2/e.scrollWidth,i=Math.max(r,l.minSize),c=Math.max(o,l.minSize);v.value=r/(a-r)/(i/(a-i)),f.value=o/(t-o)/(c/(t-c)),u.value=i+4<a?`${i}px`:"",n.value=c+4<t?`${c}px`:""}}),(e,a)=>(c(),z(_,null,[x(D,{move:o.value,ratio:f.value,size:n.value,always:e.always},null,8,["move","ratio","size","always"]),x(D,{move:i.value,ratio:v.value,size:u.value,vertical:"",always:e.always},null,8,["move","ratio","size","always"])],64))}}),[["__file","bar.vue"]]);const U=e({height:{type:[String,Number],default:""},maxHeight:{type:[String,Number],default:""},native:{type:Boolean,default:!1},wrapStyle:{type:L([String,Object,Array]),default:""},wrapClass:{type:[String,Array],default:""},viewClass:{type:[String,Array],default:""},viewStyle:{type:[String,Array,Object],default:""},noresize:Boolean,tag:{type:String,default:"div"},always:Boolean,minSize:{type:Number,default:20},tabindex:{type:[String,Number],default:void 0},id:String,role:String,...X(["ariaLabel","ariaOrientation"])}),V={scroll:({scrollTop:e,scrollLeft:a})=>[e,a].every(E)},F=l({name:"ElScrollbar"});const G=q(a(l({...F,props:U,emits:V,setup(e,{expose:a,emit:l}){const t=e,i=s("scrollbar");let u,m,p=0,g=0;const w=r(),S=r(),x=r(),_=r(),L=o((()=>{const e={};return t.height&&(e.height=T(t.height)),t.maxHeight&&(e.maxHeight=T(t.maxHeight)),[t.wrapStyle,e]})),q=o((()=>[t.wrapClass,i.e("wrap"),{[i.em("wrap","hidden-default")]:!t.native}])),P=o((()=>[i.e("view"),t.viewClass])),X=()=>{var e;S.value&&(null==(e=_.value)||e.handleScroll(S.value),p=S.value.scrollTop,g=S.value.scrollLeft,l("scroll",{scrollTop:S.value.scrollTop,scrollLeft:S.value.scrollLeft}))};const I=()=>{var e;null==(e=_.value)||e.update()};return k((()=>t.noresize),(e=>{e?(null==u||u(),null==m||m()):(({stop:u}=$(x,I)),m=n("resize",I))}),{immediate:!0}),k((()=>[t.maxHeight,t.height]),(()=>{t.native||j((()=>{var e;I(),S.value&&(null==(e=_.value)||e.handleScroll(S.value))}))})),C(K,H({scrollbarElement:w,wrapElement:S})),B((()=>{S.value&&(S.value.scrollTop=p,S.value.scrollLeft=g)})),R((()=>{t.native||j((()=>{I()}))})),N((()=>I())),a({wrapRef:S,update:I,scrollTo:function(e,a){W(e)?S.value.scrollTo(e):E(e)&&E(a)&&S.value.scrollTo(e,a)},setScrollTop:e=>{E(e)&&(S.value.scrollTop=e)},setScrollLeft:e=>{E(e)&&(S.value.scrollLeft=e)},handleScroll:X}),(e,a)=>(c(),z("div",{ref_key:"scrollbarRef",ref:w,class:y(b(i).b())},[d("div",{ref_key:"wrapRef",ref:S,class:y(b(q)),style:h(b(L)),tabindex:e.tabindex,onScroll:X},[(c(),v(O(e.tag),{id:e.id,ref_key:"resizeRef",ref:x,class:y(b(P)),style:h(e.viewStyle),role:e.role,"aria-label":e.ariaLabel,"aria-orientation":e.ariaOrientation},{default:f((()=>[M(e.$slots,"default")])),_:3},8,["id","class","style","role","aria-label","aria-orientation"]))],46,["tabindex"]),e.native?A("v-if",!0):(c(),v(J,{key:0,ref_key:"barRef",ref:_,always:e.always,"min-size":e.minSize},null,8,["always","min-size"]))],2))}}),[["__file","scrollbar.vue"]]));export{G as E,V as s};
