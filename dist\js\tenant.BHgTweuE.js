var s=Object.defineProperty,t=(t,i,a)=>((t,i,a)=>i in t?s(t,i,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[i]=a)(t,"symbol"!=typeof i?i+"":i,a);import{aT as i}from"./index.Dk5pbsTU.js";import{d as a}from"./index.C-k5aYA-.js";class e{constructor(){t(this,"pageNum",1),t(this,"pageSize",10),t(this,"keywords",""),t(this,"status",""),t(this,"deployWay","")}}class o{constructor(){t(this,"pageNum",1),t(this,"pageSize",10),t(this,"keywords",""),t(this,"isAssistant","")}}class n{constructor(s){t(this,"id"),t(this,"name",""),t(this,"logo",""),t(this,"phone",""),t(this,"status",1),t(this,"deployWay",""),t(this,"nickName",""),t(this,"smsSignature","0"),t(this,"maxRoom",0),t(this,"privateDomain",""),s&&(this.id=s.id,this.name=s.name,this.logo=s.logo,this.maxRoom=s.maxRoom,this.phone=s.phone,this.status=s.status,this.nickName=s.nickName,this.deployWay=s.deployWay.toString(),this.smsSignature=String(s.smsSignature),this.privateDomain=s.privateDomain)}}class r{constructor(s){t(this,"id",0),t(this,"aiVestNum",0),t(this,"isEarlyWarning",0),t(this,"businessScope",""),s&&(this.id=s.id,this.aiVestNum=s.aiVestNum,this.isEarlyWarning=s.isEarlyWarning,this.businessScope=s.businessScope)}}const h={aiAuthorize:s=>i({url:"/tenant/aiAuthorize",method:"post",data:s}),appUserPage:s=>i({url:"/tenant/appUserPage",method:"get",params:s}),page:s=>i({url:"/tenant/list",method:"get",params:s}),export(s){a("/tenant/export",s)},remove:s=>i({url:`/tenant/remove/${s}`,method:"delete"}),save:s=>i({url:"/tenant/save",method:"post",data:s})};export{o as A,h as T,n as a,r as b,e as c};
