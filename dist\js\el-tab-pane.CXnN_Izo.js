import{t as e,ad as a,z as t,_ as l,d as s,A as o,b as n,r,I as i,ab as u,a6 as c,g as d,f as v,k as b,n as p,i as f,B as m,a0 as h,b_ as y,b$ as g,c as P,o as $,b8 as x,m as C,E as w,c0 as k,a4 as T,K as B,Y as N,L as R,b6 as E,l as S,c1 as A,a5 as F,y as L,b9 as _,bt as j,S as K,bC as V,V as q,h as z,X as M,q as U,G as W}from"./index.Dk5pbsTU.js";import{t as X}from"./error.D_Dr4eZ1.js";import{c as Y}from"./strings.MqEQKtyI.js";import{U as G}from"./event.BwRzfsZt.js";import{u as H}from"./index.C_BbqFDa.js";const I=Symbol("tabsRootContextKey"),O=e({tabs:{type:t(Array),default:()=>a([])}}),D="ElTabBar",J=s({name:D});var Q=l(s({...J,props:O,setup(e,{expose:a}){const t=e,l=m(),s=o(I);s||X(D,"<el-tabs><el-tab-bar /></el-tabs>");const y=n("tabs"),g=r(),P=r(),$=()=>P.value=(()=>{let e=0,a=0;const o=["top","bottom"].includes(s.props.tabPosition)?"width":"height",n="width"===o?"x":"y",r="x"===n?"left":"top";return t.tabs.every((t=>{var s,n;const i=null==(n=null==(s=l.parent)?void 0:s.refs)?void 0:n[`tab-${t.uid}`];if(!i)return!1;if(!t.active)return!0;e=i[`offset${Y(r)}`],a=i[`client${Y(o)}`];const u=window.getComputedStyle(i);return"width"===o&&(a-=Number.parseFloat(u.paddingLeft)+Number.parseFloat(u.paddingRight),e+=Number.parseFloat(u.paddingLeft)),!1})),{[o]:`${a}px`,transform:`translate${Y(n)}(${e}px)`}})(),x=[];i((()=>t.tabs),(async()=>{await h(),$(),(()=>{var e;x.forEach((e=>e.stop())),x.length=0;const a=null==(e=l.parent)?void 0:e.refs;if(a)for(const t in a)if(t.startsWith("tab-")){const e=a[t];e&&x.push(u(e,$))}})()}),{immediate:!0});const C=u(g,(()=>$()));return c((()=>{x.forEach((e=>e.stop())),x.length=0,C.stop()})),a({ref:g,update:$}),(e,a)=>(v(),d("div",{ref_key:"barRef",ref:g,class:p([f(y).e("active-bar"),f(y).is(f(s).props.tabPosition)]),style:b(P.value)},null,6))}}),[["__file","tab-bar.vue"]]);const Z=e({panes:{type:t(Array),default:()=>a([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),ee="ElTabNav",ae=s({name:ee,props:Z,emits:{tabClick:(e,a,t)=>t instanceof Event,tabRemove:(e,a)=>a instanceof Event},setup(e,{expose:a,emit:t}){const l=o(I);l||X(ee,"<el-tabs><tab-nav /></el-tabs>");const s=n("tabs"),c=y(),d=g(),v=r(),b=r(),p=r(),f=r(),m=r(!1),R=r(0),E=r(!1),S=r(!0),A=P((()=>["top","bottom"].includes(l.props.tabPosition)?"width":"height")),F=P((()=>({transform:`translate${"width"===A.value?"X":"Y"}(-${R.value}px)`}))),L=()=>{if(!v.value)return;const e=v.value[`offset${Y(A.value)}`],a=R.value;if(!a)return;const t=a>e?a-e:0;R.value=t},_=()=>{if(!v.value||!b.value)return;const e=b.value[`offset${Y(A.value)}`],a=v.value[`offset${Y(A.value)}`],t=R.value;if(e-t<=a)return;const l=e-t>2*a?t+a:e-a;R.value=l},j=async()=>{const e=b.value;if(!(m.value&&p.value&&v.value&&e))return;await h();const a=p.value.querySelector(".is-active");if(!a)return;const t=v.value,s=["top","bottom"].includes(l.props.tabPosition),o=a.getBoundingClientRect(),n=t.getBoundingClientRect(),r=s?e.offsetWidth-n.width:e.offsetHeight-n.height,i=R.value;let u=i;s?(o.left<n.left&&(u=i-(n.left-o.left)),o.right>n.right&&(u=i+o.right-n.right)):(o.top<n.top&&(u=i-(n.top-o.top)),o.bottom>n.bottom&&(u=i+(o.bottom-n.bottom))),u=Math.max(u,0),R.value=Math.min(u,r)},K=()=>{var a;if(!b.value||!v.value)return;e.stretch&&(null==(a=f.value)||a.update());const t=b.value[`offset${Y(A.value)}`],l=v.value[`offset${Y(A.value)}`],s=R.value;l<t?(m.value=m.value||{},m.value.prev=s,m.value.next=s+l<t,t-s<l&&(R.value=t-l)):(m.value=!1,s>0&&(R.value=0))},V=e=>{let a=0;switch(e.code){case B.left:case B.up:a=-1;break;case B.right:case B.down:a=1;break;default:return}const t=Array.from(e.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)"));let l=t.indexOf(e.target)+a;l<0?l=t.length-1:l>=t.length&&(l=0),t[l].focus({preventScroll:!0}),t[l].click(),q()},q=()=>{S.value&&(E.value=!0)},z=()=>E.value=!1;return i(c,(e=>{"hidden"===e?S.value=!1:"visible"===e&&setTimeout((()=>S.value=!0),50)})),i(d,(e=>{e?setTimeout((()=>S.value=!0),50):S.value=!1})),u(p,K),$((()=>setTimeout((()=>j()),0))),x((()=>K())),a({scrollToActiveTab:j,removeFocus:z,tabListRef:b,tabBarRef:f}),()=>{const a=m.value?[C("span",{class:[s.e("nav-prev"),s.is("disabled",!m.value.prev)],onClick:L},[C(w,null,{default:()=>[C(k,null,null)]})]),C("span",{class:[s.e("nav-next"),s.is("disabled",!m.value.next)],onClick:_},[C(w,null,{default:()=>[C(T,null,null)]})])]:null,o=e.panes.map(((a,o)=>{var n,r,i,u;const c=a.uid,d=a.props.disabled,v=null!=(r=null!=(n=a.props.name)?n:a.index)?r:`${o}`,b=!d&&(a.isClosable||e.editable);a.index=`${o}`;const p=b?C(w,{class:"is-icon-close",onClick:e=>t("tabRemove",a,e)},{default:()=>[C(N,null,null)]}):null,f=(null==(u=(i=a.slots).label)?void 0:u.call(i))||a.props.label,m=!d&&a.active?0:-1;return C("div",{ref:`tab-${c}`,class:[s.e("item"),s.is(l.props.tabPosition),s.is("active",a.active),s.is("disabled",d),s.is("closable",b),s.is("focus",E.value)],id:`tab-${v}`,key:`tab-${c}`,"aria-controls":`pane-${v}`,role:"tab","aria-selected":a.active,tabindex:m,onFocus:()=>q(),onBlur:()=>z(),onClick:e=>{z(),t("tabClick",a,v,e)},onKeydown:e=>{!b||e.code!==B.delete&&e.code!==B.backspace||t("tabRemove",a,e)}},[f,p])}));return C("div",{ref:p,class:[s.e("nav-wrap"),s.is("scrollable",!!m.value),s.is(l.props.tabPosition)]},[a,C("div",{class:s.e("nav-scroll"),ref:v},[C("div",{class:[s.e("nav"),s.is(l.props.tabPosition),s.is("stretch",e.stretch&&["top","bottom"].includes(l.props.tabPosition))],ref:b,style:F.value,role:"tablist",onKeydown:V},[e.type?null:C(Q,{ref:f,tabs:[...e.panes]},null),o])])])}}}),te=e({type:{type:String,values:["card","border-card",""],default:""},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:t(Function),default:()=>!0},stretch:Boolean}),le=e=>R(e)||E(e);var se=s({name:"ElTabs",props:te,emits:{[G]:e=>le(e),tabClick:(e,a)=>a instanceof Event,tabChange:e=>le(e),edit:(e,a)=>["remove","add"].includes(a),tabRemove:e=>le(e),tabAdd:()=>!0},setup(e,{emit:a,slots:t,expose:l}){var s;const o=n("tabs"),u=P((()=>["left","right"].includes(e.tabPosition))),{children:c,addChild:d,removeChild:v}=H(m(),"ElTabPane"),b=r(),p=r(null!=(s=e.modelValue)?s:"0"),f=async(t,l=!1)=>{var s,o;if(p.value!==t&&!F(t))try{let n;if(e.beforeLeave){const a=e.beforeLeave(t,p.value);n=a instanceof Promise?await a:a}else n=!0;!1!==n&&(p.value=t,l&&(a(G,t),a("tabChange",t)),null==(o=null==(s=b.value)?void 0:s.removeFocus)||o.call(s))}catch(n){}},y=(e,t,l)=>{e.props.disabled||(a("tabClick",e,l),f(t,!0))},g=(e,t)=>{e.props.disabled||F(e.props.name)||(t.stopPropagation(),a("edit",e.props.name,"remove"),a("tabRemove",e.props.name))},$=()=>{a("edit",void 0,"add"),a("tabAdd")};i((()=>e.modelValue),(e=>f(e))),i(p,(async()=>{var e;await h(),null==(e=b.value)||e.scrollToActiveTab()})),L(I,{props:e,currentName:p,registerPane:e=>{c.value.push(e)},sortPane:d,unregisterPane:v}),l({currentName:p,tabNavRef:b});const x=({render:e})=>e();return()=>{const a=t["add-icon"],l=e.editable||e.addable?C("div",{class:[o.e("new-tab"),u.value&&o.e("new-tab-vertical")],tabindex:"0",onClick:$,onKeydown:e=>{[B.enter,B.numpadEnter].includes(e.code)&&$()}},[a?S(t,"add-icon"):C(w,{class:o.is("icon-plus")},{default:()=>[C(A,null,null)]})]):null,s=C("div",{class:[o.e("header"),u.value&&o.e("header-vertical"),o.is(e.tabPosition)]},[C(x,{render:()=>{const a=c.value.some((e=>e.slots.label));return C(ae,{ref:b,currentName:p.value,editable:e.editable,type:e.type,panes:c.value,stretch:e.stretch,onTabClick:y,onTabRemove:g},{$stable:!a})}},null),l]),n=C("div",{class:o.e("content")},[S(t,"default")]);return C("div",{class:[o.b(),o.m(e.tabPosition),{[o.m("card")]:"card"===e.type,[o.m("border-card")]:"border-card"===e.type}]},[n,s])}}});const oe=e({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),ne="ElTabPane",re=s({name:ne});var ie=l(s({...re,props:oe,setup(e){const a=e,t=m(),l=_(),s=o(I);s||X(ne,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const u=n("tab-pane"),c=r(),b=P((()=>a.closable||s.props.closable)),h=j((()=>{var e;return s.currentName.value===(null!=(e=a.name)?e:c.value)})),y=r(h.value),g=P((()=>{var e;return null!=(e=a.name)?e:c.value})),x=j((()=>!a.lazy||y.value||h.value));i(h,(e=>{e&&(y.value=!0)}));const C=K({uid:t.uid,slots:l,props:a,paneName:g,active:h,index:c,isClosable:b});return s.registerPane(C),$((()=>{s.sortPane(C)})),V((()=>{s.unregisterPane(C.uid)})),(e,a)=>f(x)?q((v(),d("div",{key:0,id:`pane-${f(g)}`,class:p(f(u).b()),role:"tabpanel","aria-hidden":!f(h),"aria-labelledby":`tab-${f(g)}`},[S(e.$slots,"default")],10,["id","aria-hidden","aria-labelledby"])),[[M,f(h)]]):z("v-if",!0)}}),[["__file","tab-pane.vue"]]);const ue=U(se,{TabPane:ie}),ce=W(ie);export{ue as E,ce as a};
