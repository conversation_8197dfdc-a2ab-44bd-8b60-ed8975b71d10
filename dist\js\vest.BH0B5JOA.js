import{d as e,r as a,S as l,I as o,ap as t,aQ as i,e as r,f as s,w as n,C as u,m as d,Z as p,E as m,V as c,g as v,h as f,P as g,Q as j,n as _,F as y,j as b,$ as h,az as k}from"./index.Dk5pbsTU.js";import{v as w}from"./el-loading.Dqi-qL7c.js";import{E as V}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as x,a as I}from"./el-form-item.Bw6Zyv_7.js";import{E as C,a as U}from"./el-radio.w2rep3_A.js";import{_ as E}from"./SingleImageUpload.WGBxPB_4.js";import N from"./index.Cywy93e7.js";import{a as S,E as z}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     *//* empty css               */import{E as P}from"./el-avatar.DtvYzXsq.js";import{E as K}from"./el-button.CXI119n4.js";import{E as R}from"./el-input.DiGatoux.js";import{V as q}from"./vest.j8NavXyg.js";/* empty css                       */import{E as $}from"./index.ybpLT-bz.js";import{E as A}from"./index.L2DVy5yq.js";import{E as F}from"./index.BcMfjWDS.js";import{_ as G}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./index.C9UdVphc.js";import"./use-form-item.DzRJVC1I.js";import"./el-progress.BQBUwu9o.js";import"./cloneDeep.DcCMo0F4.js";import"./isEqual.C0S6DIiJ.js";import"./el-image-viewer.BH897zgF.js";import"./debounce.DJJTSR8O.js";import"./position.DfR5znly.js";import"./index.DEKElSOG.js";import"./index.WzKGworL.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./index.Vn8pbgQR.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./validator.HGn2BZtD.js";const Q={class:"vest-container"},D={class:"group-list"},H={class:"group-header"},J={class:"group-items"},L=["onClick"],M={class:"group-name"},O={key:0,class:"no-data"},T={class:"group-footer"},Z={class:"vest-panel"},Y={class:"panel-header"},B={class:"search-bar"},W={class:"dialog-footer"},X=G(e({__name:"vest",props:{roomId:{}},emits:["success"],setup(e,{expose:G,emit:X}){const ee=e,ae=a(!1),le=l({roomId:ee.roomId,pageNum:1,pageSize:999,groupName:""}),oe=a([]),te=a(!1),ie=a(),re=a(""),se=l({pageNum:1,pageSize:10,nickname:""}),ne=a([]),ue=a(0),de=a(!1),pe=l({visible:!1,title:""}),me=a(),ce=l({nickname:"",avatar:"",status:1,roomId:0,groupId:0}),ve={nickname:[{required:!0,message:"请输入马甲昵称",trigger:"blur"}],avatar:[{required:!0,message:"请上传头像",trigger:"change"}]};function fe(){ie.value=void 0,ne.value=[],ue.value=0,re.value=""}async function ge(){if(ee.roomId){te.value=!0;try{const e=await q.groupPage(le);oe.value=e.records||[]}catch(e){oe.value=[]}finally{te.value=!1}}}async function je(){if(ie.value){se.groupId=ie.value.id,de.value=!0;try{const e=await q.page(se);ne.value=[...e.records||[]],ue.value=e.totalRow||0}catch(e){ne.value=[],ue.value=0}finally{de.value=!1}}}async function _e(){re.value?(await q.saveGroup({groupName:re.value,roomId:ee.roomId}),k.success("新增成功"),re.value="",ge()):k.warning("请输入分组名称")}function ye(){ie.value&&(pe.title="新增马甲",ce.id=void 0,ce.nickname="",ce.avatar="",ce.status=1,ce.roomId=ee.roomId,ce.groupId=ie.value.id,pe.visible=!0)}async function be(){me.value&&(await me.value.validate(),await q.save(ce),k.success(ce.id?"修改成功":"新增成功"),pe.visible=!1,je())}return o((()=>ee.roomId),(e=>{e&&(le.roomId=e,ge())}),{immediate:!0}),o(ne,(e=>{}),{deep:!0}),G({open:function(){ae.value=!0,ge()}}),(e,a)=>{const l=t("search"),o=m,G=R,X=K,he=$,ke=t("plus"),we=z,Ve=P,xe=A,Ie=S,Ce=N,Ue=I,Ee=E,Ne=U,Se=C,ze=x,Pe=V,Ke=i("hasPerm"),Re=w;return s(),r(Pe,{modelValue:ae.value,"onUpdate:modelValue":a[12]||(a[12]=e=>ae.value=e),title:"马甲管理",width:"1000px","append-to-body":"","destroy-on-close":"",onClosed:fe},{default:n((()=>[u("div",Q,[u("div",D,[u("div",H,[d(G,{modelValue:le.groupName,"onUpdate:modelValue":a[0]||(a[0]=e=>le.groupName=e),placeholder:"搜索分组",clearable:"",onKeyup:p(ge,["enter"])},{prefix:n((()=>[d(o,null,{default:n((()=>[d(l)])),_:1})])),_:1},8,["modelValue"])]),d(he,null,{default:n((()=>[c((s(),v("ul",J,[(s(!0),v(g,null,j(oe.value,(e=>{var a;return s(),v("li",{key:e.id,class:_({active:(null==(a=ie.value)?void 0:a.id)===e.id}),onClick:a=>{return l=e,ie.value=l,se.pageNum=1,void je();var l}},[u("span",M,y(e.groupName),1),c(d(X,{type:"danger",link:"",icon:"delete",onClick:b((a=>async function(e){var a;try{await F.confirm(`确认删除分组"${e.groupName}"吗？`,"提示",{type:"warning"}),await q.removeGroup(e.id),k.success("删除成功"),(null==(a=ie.value)?void 0:a.id)===e.id&&(ie.value=void 0,ne.value=[],ue.value=0),ge()}catch(l){}}(e)),["stop"])},null,8,["onClick"]),[[Ke,["live:vest"]]])],10,L)})),128)),te.value||oe.value&&0!==oe.value.length?f("",!0):(s(),v("li",O," 暂无分组数据 "))])),[[Re,te.value]])])),_:1}),u("div",T,[d(G,{modelValue:re.value,"onUpdate:modelValue":a[1]||(a[1]=e=>re.value=e),placeholder:"输入分组名称",onKeyup:p(_e,["enter"])},{append:n((()=>[d(X,{onClick:_e},{default:n((()=>[d(o,null,{default:n((()=>[d(ke)])),_:1})])),_:1})])),_:1},8,["modelValue"])])]),u("div",Z,[u("div",Y,[u("span",null,y(ie.value?ie.value.groupName:"请选择分组"),1),c((s(),r(X,{type:"primary",link:"",icon:"plus",disabled:!ie.value,onClick:ye},{default:n((()=>a[13]||(a[13]=[h(" 新增马甲 ")]))),_:1,__:[13]},8,["disabled"])),[[Ke,["live:vest"]]])]),u("div",B,[d(G,{modelValue:se.nickname,"onUpdate:modelValue":a[2]||(a[2]=e=>se.nickname=e),placeholder:"马甲昵称",clearable:"",onKeyup:p(je,["enter"])},null,8,["modelValue"]),d(X,{type:"primary",icon:"search",onClick:je},{default:n((()=>a[14]||(a[14]=[h("搜索")]))),_:1,__:[14]})]),c((s(),r(Ie,{ref:"vestTableRef",data:ne.value,"highlight-current-row":"",border:"",onRowClick:a[3]||(a[3]=e=>{})},{default:n((()=>[d(we,{type:"index",label:"序号",width:"60",align:"center"}),d(we,{label:"头像",width:"70",align:"center"},{default:n((({row:e})=>[d(Ve,{size:40,src:e.avatar,fit:"cover"},null,8,["src"])])),_:1}),d(we,{label:"昵称",prop:"nickname","min-width":"120","show-overflow-tooltip":""}),d(we,{label:"状态",width:"80",align:"center"},{default:n((({row:e})=>[d(xe,{type:1===e.status?"success":"info"},{default:n((()=>[h(y(1===e.status?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),d(we,{label:"创建时间",prop:"createdAt",width:"160",align:"center","show-overflow-tooltip":""}),d(we,{fixed:"right",label:"操作",width:"120",align:"center"},{default:n((e=>[c((s(),r(X,{type:"primary",link:"",icon:"edit",onClick:a=>{return l=e.row,pe.title="编辑马甲",ce.id=l.id,ce.nickname=l.nickname,ce.avatar=l.avatar,ce.status=l.status,ce.roomId=ee.roomId,ce.groupId=l.groupId,void(pe.visible=!0);var l}},{default:n((()=>a[15]||(a[15]=[h(" 编辑 ")]))),_:2,__:[15]},1032,["onClick"])),[[Ke,["live:vest"]]]),c((s(),r(X,{type:"danger",link:"",icon:"delete",onClick:a=>async function(e){try{await F.confirm(`确认删除马甲"${e.nickname}"吗？`,"提示",{type:"warning"}),await q.remove(e.id),k.success("删除成功"),je()}catch(a){}}(e.row)},{default:n((()=>a[16]||(a[16]=[h(" 删除 ")]))),_:2,__:[16]},1032,["onClick"])),[[Ke,["live:vest"]]])])),_:1})])),_:1},8,["data"])),[[Re,de.value]]),ue.value?(s(),r(Ce,{key:0,total:ue.value,"onUpdate:total":a[4]||(a[4]=e=>ue.value=e),page:se.pageNum,"onUpdate:page":a[5]||(a[5]=e=>se.pageNum=e),limit:se.pageSize,"onUpdate:limit":a[6]||(a[6]=e=>se.pageSize=e),onPagination:je},null,8,["total","page","limit"])):f("",!0)])]),d(Pe,{modelValue:pe.visible,"onUpdate:modelValue":a[11]||(a[11]=e=>pe.visible=e),title:pe.title,width:"500px","append-to-body":"","destroy-on-close":""},{footer:n((()=>[u("div",W,[d(X,{type:"primary",onClick:be},{default:n((()=>a[19]||(a[19]=[h("确 定")]))),_:1,__:[19]}),d(X,{onClick:a[10]||(a[10]=e=>pe.visible=!1)},{default:n((()=>a[20]||(a[20]=[h("取 消")]))),_:1,__:[20]})])])),default:n((()=>[d(ze,{ref_key:"vestFormRef",ref:me,model:ce,rules:ve,"label-width":"100px"},{default:n((()=>[d(Ue,{label:"马甲昵称",prop:"nickname"},{default:n((()=>[d(G,{modelValue:ce.nickname,"onUpdate:modelValue":a[7]||(a[7]=e=>ce.nickname=e),placeholder:"请输入马甲昵称"},null,8,["modelValue"])])),_:1}),d(Ue,{label:"头像",prop:"avatar"},{default:n((()=>[d(Ee,{modelValue:ce.avatar,"onUpdate:modelValue":a[8]||(a[8]=e=>ce.avatar=e)},null,8,["modelValue"])])),_:1}),d(Ue,{label:"状态",prop:"status"},{default:n((()=>[d(Se,{modelValue:ce.status,"onUpdate:modelValue":a[9]||(a[9]=e=>ce.status=e)},{default:n((()=>[d(Ne,{label:1},{default:n((()=>a[17]||(a[17]=[h("启用")]))),_:1,__:[17]}),d(Ne,{label:0},{default:n((()=>a[18]||(a[18]=[h("禁用")]))),_:1,__:[18]})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-3bae535b"]]);export{X as default};
