import{d as e,r as l,S as a,I as t,o,ap as i,g as s,f as r,C as n,m as d,w as u,Z as m,i as p,$ as c,V as f,e as _,h as v,ak as j,F as g,E as y,az as h,aW as b}from"./index.Dk5pbsTU.js";import{v as k}from"./el-loading.Dqi-qL7c.js";import{E as x}from"./el-drawer.Df_TzNjH.js";import"./el-overlay.DpVCS8zG.js";import{E as V}from"./el-tree.ChWw39qP.js";import{E as w}from"./el-checkbox.DDYarIkn.js";import"./el-text.6kaKYQ9U.js";import"./el-tooltip.l0sNRNKZ.js";import{E as C}from"./el-popper.Dbn4MgsT.js";import{E}from"./el-dialog.Cnp8BitR.js";import{E as U}from"./el-input.DiGatoux.js";import{E as z}from"./el-input-number.C02ig7uT.js";import{E as R,a as S}from"./el-radio.w2rep3_A.js";import{E as T}from"./el-card.DwLhVNHW.js";import F from"./index.Cywy93e7.js";import{a as q,E as I}from"./el-table-column.DRgE6Qqc.js";/* empty css                     *//* empty css               */import{a as M,E as N}from"./el-form-item.Bw6Zyv_7.js";import{E as O}from"./el-button.CXI119n4.js";import{R as P}from"./role.DI5RFLtW.js";/* empty css                       */import{E as A}from"./index.L2DVy5yq.js";import{E as B}from"./index.BcMfjWDS.js";import"./index.C6NthMtN.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./token.DWNpOE8r.js";import"./index.DLOxQT-M.js";import"./use-form-common-props.CQPDkY7k.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./use-form-item.DzRJVC1I.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DEKElSOG.js";import"./index.Vn8pbgQR.js";import"./index.Cd8M2JyP.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./_initCloneObject.BN1anLuC.js";import"./validator.HGn2BZtD.js";const K={class:"app-container"},Q={class:"search-bar"},Z={class:"mb-10px"},G={class:"dialog-footer"},H={class:"flex-x-between"},J={class:"flex-center ml-5"},L={class:"dialog-footer"},$=e({name:"Role",inheritAttrs:!1,__name:"index",setup(e){const $=l(),D=l(),W=l(),Y=l(!1),X=l([]),ee=l(0),le=a({pageNum:1,pageSize:10,roleType:""}),ae=l(),te=l([]),oe=a({title:"",visible:!1}),ie=a({sort:1,status:1,roleType:0}),se=a({name:[{required:!0,message:"请输入角色名称",trigger:"blur"}],code:[{required:!0,message:"请输入角色编码",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"blur"}],roleType:[{required:!0,message:"请选择角色类型",trigger:"blur"}]}),re=l({}),ne=l(!1),de=l(""),ue=l(!0),me=l(!0);function pe(){Y.value=!0,P.getPage(le).then((e=>{ae.value=e.list,ee.value=e.total})).finally((()=>{Y.value=!1}))}function ce(){$.value.resetFields(),le.pageNum=1,pe()}function fe(e){X.value=e.map((e=>e.id))}function _e(e){oe.visible=!0,e?(oe.title="修改角色",P.getFormData(e).then((e=>{Object.assign(ie,e)}))):oe.title="新增角色"}function ve(){D.value.validate((e=>{if(e){Y.value=!0;const e=ie.id;ie.dataScope=0,e?P.update(e,ie).then((()=>{h.success("修改成功"),je(),ce()})).finally((()=>Y.value=!1)):P.add(ie).then((()=>{h.success("新增成功"),je(),ce()})).finally((()=>Y.value=!1))}}))}function je(){oe.visible=!1,D.value.resetFields(),D.value.clearValidate(),ie.id=void 0,ie.sort=1,ie.status=1}function ge(e){const l=[e||X.value].join(",");l?B.confirm("确认删除已选中的数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{Y.value=!0,P.deleteByIds(l).then((()=>{h.success("删除成功"),ce()})).finally((()=>Y.value=!1))}),(()=>{h.info("已取消删除")})):h.warning("请勾选删除项")}function ye(){const e=re.value.id;if(e){const l=W.value.getCheckedNodes(!1,!0).map((e=>e.value));Y.value=!0,P.updateRoleMenus(e,l).then((()=>{h.success("分配权限成功"),ne.value=!1,ce()})).finally((()=>{Y.value=!1}))}}function he(){ue.value=!ue.value,W.value&&Object.values(W.value.store.nodesMap).forEach((e=>{ue.value?e.expand():e.collapse()}))}function be(e,l){return!e||l.label.includes(e)}function ke(e){me.value=e}return t(de,(e=>{W.value.filter(e)})),o((()=>{pe()})),(e,l)=>{const a=U,t=M,o=O,h=N,B=I,xe=A,Ve=q,we=F,Ce=T,Ee=S,Ue=R,ze=z,Re=E,Se=i("Search"),Te=i("Switch"),Fe=w,qe=i("QuestionFilled"),Ie=y,Me=C,Ne=V,Oe=x,Pe=k;return r(),s("div",K,[n("div",Q,[d(h,{ref_key:"queryFormRef",ref:$,model:p(le),inline:!0},{default:u((()=>[d(t,{prop:"keywords",label:"关键字"},{default:u((()=>[d(a,{modelValue:p(le).keywords,"onUpdate:modelValue":l[0]||(l[0]=e=>p(le).keywords=e),placeholder:"角色名称",clearable:"",onKeyup:m(pe,["enter"])},null,8,["modelValue"])])),_:1}),d(t,null,{default:u((()=>[d(o,{type:"primary",icon:"search",onClick:pe},{default:u((()=>l[15]||(l[15]=[c("搜索")]))),_:1,__:[15]}),d(o,{icon:"refresh",onClick:ce},{default:u((()=>l[16]||(l[16]=[c("重置")]))),_:1,__:[16]})])),_:1})])),_:1},8,["model"])]),d(Ce,{shadow:"never"},{default:u((()=>[n("div",Z,[d(o,{type:"success",icon:"plus",onClick:l[1]||(l[1]=e=>_e())},{default:u((()=>l[17]||(l[17]=[c("新增")]))),_:1,__:[17]}),d(o,{type:"danger",disabled:0===p(X).length,icon:"delete",onClick:l[2]||(l[2]=e=>ge())},{default:u((()=>l[18]||(l[18]=[c(" 删除 ")]))),_:1,__:[18]},8,["disabled"])]),f((r(),_(Ve,{ref:"dataTableRef",data:p(ae),"highlight-current-row":"",border:"",onSelectionChange:fe},{default:u((()=>[d(B,{type:"selection",width:"55",align:"center"}),d(B,{label:"角色名称",prop:"name","min-width":"100"}),d(B,{label:"角色编码",prop:"code",width:"150"}),d(B,{label:"状态",align:"center",width:"100"},{default:u((e=>[1===e.row.status?(r(),_(xe,{key:0,type:"success"},{default:u((()=>l[19]||(l[19]=[c("正常")]))),_:1,__:[19]})):(r(),_(xe,{key:1,type:"info"},{default:u((()=>l[20]||(l[20]=[c("禁用")]))),_:1,__:[20]}))])),_:1}),d(B,{label:"排序",align:"center",width:"80",prop:"sort"}),d(B,{fixed:"right",label:"操作",width:"220"},{default:u((e=>[d(o,{type:"primary",size:"small",link:"",icon:"position",onClick:l=>async function(e){const l=e.id;l&&(ne.value=!0,Y.value=!0,re.value.id=l,re.value.name=e.name,te.value=await b.getOptions(void 0,e.roleType),P.getRoleMenuIds(l).then((e=>{e.forEach((e=>W.value.setChecked(e,!0,!1)))})).finally((()=>{Y.value=!1})))}(e.row)},{default:u((()=>l[21]||(l[21]=[c(" 分配权限 ")]))),_:2,__:[21]},1032,["onClick"]),d(o,{type:"primary",size:"small",link:"",icon:"edit",onClick:l=>_e(e.row.id)},{default:u((()=>l[22]||(l[22]=[c(" 编辑 ")]))),_:2,__:[22]},1032,["onClick"]),d(o,{type:"danger",size:"small",link:"",icon:"delete",onClick:l=>ge(e.row.id)},{default:u((()=>l[23]||(l[23]=[c(" 删除 ")]))),_:2,__:[23]},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Pe,p(Y)]]),p(ee)>0?(r(),_(we,{key:0,total:p(ee),"onUpdate:total":l[3]||(l[3]=e=>j(ee)?ee.value=e:null),page:p(le).pageNum,"onUpdate:page":l[4]||(l[4]=e=>p(le).pageNum=e),limit:p(le).pageSize,"onUpdate:limit":l[5]||(l[5]=e=>p(le).pageSize=e),onPagination:pe},null,8,["total","page","limit"])):v("",!0)])),_:1}),d(Re,{modelValue:p(oe).visible,"onUpdate:modelValue":l[10]||(l[10]=e=>p(oe).visible=e),title:p(oe).title,width:"500px",onClose:je},{footer:u((()=>[n("div",G,[d(o,{type:"primary",onClick:ve},{default:u((()=>l[26]||(l[26]=[c("确 定")]))),_:1,__:[26]}),d(o,{onClick:je},{default:u((()=>l[27]||(l[27]=[c("取 消")]))),_:1,__:[27]})])])),default:u((()=>[d(h,{ref_key:"roleFormRef",ref:D,model:p(ie),rules:p(se),"label-width":"100px"},{default:u((()=>[d(t,{label:"角色名称",prop:"name"},{default:u((()=>[d(a,{modelValue:p(ie).name,"onUpdate:modelValue":l[6]||(l[6]=e=>p(ie).name=e),placeholder:"请输入角色名称"},null,8,["modelValue"])])),_:1}),d(t,{label:"角色编码",prop:"code"},{default:u((()=>[d(a,{modelValue:p(ie).code,"onUpdate:modelValue":l[7]||(l[7]=e=>p(ie).code=e),placeholder:"请输入角色编码"},null,8,["modelValue"])])),_:1}),d(t,{label:"状态",prop:"status"},{default:u((()=>[d(Ue,{modelValue:p(ie).status,"onUpdate:modelValue":l[8]||(l[8]=e=>p(ie).status=e)},{default:u((()=>[d(Ee,{label:1},{default:u((()=>l[24]||(l[24]=[c("正常")]))),_:1,__:[24]}),d(Ee,{label:0},{default:u((()=>l[25]||(l[25]=[c("停用")]))),_:1,__:[25]})])),_:1},8,["modelValue"])])),_:1}),d(t,{label:"排序",prop:"sort"},{default:u((()=>[d(ze,{modelValue:p(ie).sort,"onUpdate:modelValue":l[9]||(l[9]=e=>p(ie).sort=e),"controls-position":"right",min:0,style:{width:"100px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),d(Oe,{modelValue:p(ne),"onUpdate:modelValue":l[14]||(l[14]=e=>j(ne)?ne.value=e:null),title:"【"+p(re).name+"】权限分配",size:"500"},{footer:u((()=>[n("div",L,[d(o,{type:"primary",onClick:ye},{default:u((()=>l[30]||(l[30]=[c("确 定")]))),_:1,__:[30]}),d(o,{onClick:l[13]||(l[13]=e=>ne.value=!1)},{default:u((()=>l[31]||(l[31]=[c("取 消")]))),_:1,__:[31]})])])),default:u((()=>[n("div",H,[d(a,{modelValue:p(de),"onUpdate:modelValue":l[11]||(l[11]=e=>j(de)?de.value=e:null),clearable:"",class:"w-[150px]",placeholder:"菜单权限名称"},{prefix:u((()=>[d(Se)])),_:1},8,["modelValue"]),n("div",J,[d(o,{type:"primary",size:"small",plain:"",onClick:he},{icon:u((()=>[d(Te)])),default:u((()=>[c(" "+g(p(ue)?"收缩":"展开"),1)])),_:1}),d(Fe,{modelValue:p(me),"onUpdate:modelValue":l[12]||(l[12]=e=>j(me)?me.value=e:null),class:"ml-5",onChange:ke},{default:u((()=>l[28]||(l[28]=[c(" 父子联动 ")]))),_:1,__:[28]},8,["modelValue"]),d(Me,{placement:"bottom"},{content:u((()=>l[29]||(l[29]=[c(" 如果只需勾选菜单权限，不需要勾选子菜单或者按钮权限，请关闭父子联动 ")]))),default:u((()=>[d(Ie,{class:"ml-1 color-[--el-color-primary] inline-block cursor-pointer"},{default:u((()=>[d(qe)])),_:1})])),_:1})])]),d(Ne,{ref_key:"permTreeRef",ref:W,"node-key":"value","show-checkbox":"",data:p(te),"filter-node-method":be,"default-expand-all":!0,"check-strictly":!p(me),class:"mt-5"},{default:u((({data:e})=>[c(g(e.label),1)])),_:1},8,["data","check-strictly"])])),_:1},8,["modelValue","title"])])}}});export{$ as default};
