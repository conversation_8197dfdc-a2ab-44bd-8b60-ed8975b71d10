<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" label-position="top">
    <!-- Section 1: Basic Info -->
    <el-divider content-position="left">
      <div class="flex items-center">
        <el-icon>
          <info-filled />
        </el-icon>
        <span class="ml-2">基本信息</span>
      </div>
    </el-divider>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item v-if="!formData.id" label="选择马甲" prop="vestCascader">
          <el-cascader
            ref="cascaderRef"
            :key="cascaderKey"
            v-model="formData.vestCascader"
            :props="cascaderProps"
            placeholder="请选择直播间/分组/马甲"
            filterable
            style="width: 100%"
            @change="handleCascaderChange"
            @clear="handleCascaderClear"
          />
        </el-form-item>
        <el-form-item v-if="formData.id" label="当前配置马甲">
          <el-tag type="success" size="large">{{ selectedVestInfo }}</el-tag>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="性别" prop="sex">
          <dict v-model="formData.sex" code="gender" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="年龄" prop="age">
          <el-input v-model="formData.age" oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="人物介绍" prop="intro">
          <el-input v-model="formData.intro" :maxlength="200" type="textarea" />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- Section 2: AI Model Config -->
    <el-divider content-position="left">
      <div class="flex items-center">
        <el-icon>
          <cpu />
        </el-icon>
        <span class="ml-2">AI 模型配置</span>
      </div>
    </el-divider>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="生成温度 (Temperature)" prop="temperature">
          <el-slider v-model="formData.temperature" :min="0" :max="2" :step="0.1" show-input />
          <div class="el-form-item__description mt-2">
            值越低，输出越保守准确 (适合事实回答)；值越高，输出越随机发散 (适合创意写作)。
            <el-tooltip content="低温(0.2): 🎯 保守准确" placement="top">
              <el-tag type="info" size="small" class="mx-1">保守</el-tag>
            </el-tooltip>
            <el-tooltip content="中温(0.7): ⚖️ 平衡创造与逻辑" placement="top">
              <el-tag type="primary" size="small" class="mx-1">平衡</el-tag>
            </el-tooltip>
            <el-tooltip content="高温(>1.0): 🎨 随机发散" placement="top">
              <el-tag type="warning" size="small" class="mx-1">创意</el-tag>
            </el-tooltip>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="上下文窗口 (Context Window)" prop="contextWindow">
          <el-slider v-model="formData.contextWindow" :min="0" :max="10" :step="1" show-input />
          <div class="el-form-item__description mt-2">
            模型能记忆的对话轮次。0表示只基于当前输入回复。
          </div>
        </el-form-item>
      </el-col>
    </el-row>

    <!--    <el-form-item label="提示词模板 (Prompt Template)" prop="promptTemplate">-->
    <!--      <el-input-->
    <!--        v-model="formData.promptTemplate"-->
    <!--        type="textarea"-->
    <!--        :rows="3"-->
    <!--        placeholder="定义AI的角色和回复风格。例如：你是一个幽默的助手，请用俏皮的风格回答问题。"-->
    <!--      />-->
    <!--    </el-form-item>-->

    <!-- Section 3: Behavior & Persona -->
    <el-divider content-position="left">
      <div class="flex items-center">
        <el-icon>
          <user />
        </el-icon>
        <span class="ml-2">行为与人设</span>
      </div>
    </el-divider>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="人设类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择人设类型" style="width: 100%">
            <el-option
              v-for="item in personaTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="模糊话概率 (Fuzziness Rate)" prop="fuzzinessRate">
          <el-slider v-model="formData.fuzzinessRate" :min="0" :max="10" :step="1" show-input />
          <div class="el-form-item__description mt-2">AI回复不确定性或“打哈哈”的概率。</div>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="使用RAG增强" prop="useRag">
          <el-switch
            v-model="formData.useRag"
            :active-value="1"
            :inactive-value="0"
            inline-prompt
            active-text="是"
            inactive-text="否"
          />
          <el-tooltip
            content="Retrieval-Augmented Generation，使用知识库增强回复的准确性"
            placement="top"
          >
            <el-icon class="ml-2 cursor-help text-gray-400">
              <question-filled />
            </el-icon>
          </el-tooltip>
          <el-button
            v-if="formData.useRag"
            type="primary"
            link
            style="margin-left: 10px"
            @click="setKnowledgeFileRef?.open(formData.knowledgeIds)"
          >
            设置知识库文件
          </el-button>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="随机延迟回复" prop="isDelay">
          <el-switch
            v-model="formData.isDelay"
            :active-value="1"
            :inactive-value="0"
            inline-prompt
            active-text="是"
            inactive-text="否"
          />
          <el-tooltip content="模拟真人的思考和打字时间" placement="top">
            <el-icon class="ml-2 cursor-help text-gray-400">
              <question-filled />
            </el-icon>
          </el-tooltip>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="自动发送回复" prop="isAuto">
          <el-switch
            v-model="formData.isAuto"
            :active-value="1"
            :inactive-value="0"
            inline-prompt
            active-text="是"
            inactive-text="否"
          />
          <el-tooltip content="AI生成回复后是否需要人工审核再发送" placement="top">
            <el-icon class="ml-2 cursor-help text-gray-400">
              <question-filled />
            </el-icon>
          </el-tooltip>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- Section 4: Trigger & Session -->
    <el-divider content-position="left">
      <div class="flex items-center">
        <el-icon>
          <pointer />
        </el-icon>
        <span class="ml-2">触发与会话</span>
      </div>
    </el-divider>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="触发规则" prop="ruleId">
          <div class="w-full">
            <el-select
              v-model="formData.ruleId"
              placeholder="默认（无特定规则）"
              class="w-full"
              filterable
              clearable
            >
              <el-option label="无特定规则" :value="0" />
              <el-option
                v-for="rule in triggerRuleOptions"
                :key="rule.id"
                :label="rule.ruleName"
                :value="rule.id"
              >
                <div class="flex items-center justify-between">
                  <span
                    :title="rule.ruleName"
                    class="overflow-hidden text-ellipsis whitespace-nowrap"
                  >
                    {{ rule.ruleName }}
                  </span>
                  <el-tooltip v-if="rule.description" :content="rule.description" placement="left">
                    <el-icon class="text-gray-400">
                      <info-filled />
                    </el-icon>
                  </el-tooltip>
                </div>
              </el-option>
            </el-select>
            <div class="mt-1">
              <el-link type="primary" @click="$emit('goToRuleConfig')">
                <el-icon class="mr-1">
                  <setting />
                </el-icon>
                管理触发规则
              </el-link>
            </div>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="会话轮次 (Session Round)" prop="sessionRound">
          <el-select
            v-model="formData.sessionRound"
            placeholder="请选择会话轮次"
            class="w-full"
            filterable
            allow-create
            @change="handleSessionRoundChange"
          >
            <el-option label="无限制（需手动停止）" :value="-1" />
            <el-option label="随机" :value="0" />
            <el-option v-for="n in 20" :key="n" :label="n + '轮后终止'" :value="n" />
          </el-select>
          <div class="el-form-item__description mt-2">一次完整的对话交互持续的轮次。</div>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- Section 5: Status -->
    <el-divider content-position="left">
      <div class="flex items-center">
        <el-icon>
          <switch-button />
        </el-icon>
        <span class="ml-2">状态</span>
      </div>
    </el-divider>

    <el-form-item label="配置状态" prop="status">
      <el-switch
        v-model="formData.status"
        :active-value="1"
        :inactive-value="0"
        inline-prompt
        active-text="启用"
        inactive-text="禁用"
      />
      <div class="el-form-item__description ml-4">禁用后，该AI马甲配置将不会生效。</div>
    </el-form-item>
  </el-form>
  <set-knowledge-file ref="setKnowledgeFileRef" @success="(e) => (formData.knowledgeIds = e)" />
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onMounted } from "vue";
import type { CascaderValue } from "element-plus";
import {
  InfoFilled,
  QuestionFilled,
  Setting,
  Cpu,
  User,
  Pointer,
  SwitchButton,
} from "@element-plus/icons-vue";
import type {
  AiVestConfigDto,
  AiVestTriggerRulesVo,
  AiVestConfigVo,
} from "@/api/system/aiVestConfig";
import { getVestUserType } from "@/api/system/aiVestConfig";
import VestAPI, { LiveVestGroupVo, LiveVestVo } from "@/api/live/vest";
import LiveRoomApi, { LiveRoomPageDto, LiveRoomPageVo } from "@/api/live/liveRoom";
import SetKnowledgeFile from "./SetKnowledgeFile.vue";

// Props 定义
interface Props {
  modelValue: Omit<AiVestConfigDto, "temperature"> & {
    temperature: number;
    vestCascader?: any[];
  };
  triggerRuleOptions: AiVestTriggerRulesVo[];
  aiVestConfigList: AiVestConfigVo[];
  personaTypeOptions?: { label: string; value: number }[];
}

// Emits 定义
interface Emits {
  (_: "update:modelValue", _value: Props["modelValue"]): void;

  (_: "goToRuleConfig"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const setKnowledgeFileRef = ref<InstanceType<typeof SetKnowledgeFile>>();
const formRef = ref();
const cascaderRef = ref(); // 级联选择器的引用
const cascaderKey = ref(0); // 用于强制重新渲染级联选择器

// 表单数据
const formData = reactive({ ...props.modelValue });

// 监听 props 变化，同步到本地数据
watch(
  () => props.modelValue,
  (newValue) => {
    Object.assign(formData, newValue);
  },
  { deep: true }
);

// 监听本地数据变化，同步到父组件
watch(
  formData,
  (newValue) => {
    emit("update:modelValue", { ...newValue });
  },
  { deep: true }
);

// 监听vestId变化，自动触发验证
watch(
  () => formData.vestId,
  (newVestId) => {
    console.log("vestId变化:", newVestId);
    // 当vestId变化时，触发级联选择器的验证
    if (formRef.value && !formData.id) {
      setTimeout(() => {
        formRef.value.validateField("vestCascader");
      }, 100);
    }
  }
);

// 级联选择器配置
const cascaderProps = {
  value: "id",
  label: "label",
  children: "children",
  emitPath: true,
  lazy: true,
  lazyLoad: async (node: any, resolve: any) => {
    const { level } = node;

    if (level === 0) {
      // 加载直播间
      try {
        const query = new LiveRoomPageDto();
        query.pageSize = 100;
        const res = await LiveRoomApi.page(query);
        const rooms = (res as any).records || [];
        const nodes = rooms.map((room: LiveRoomPageVo) => ({
          id: room.id,
          label: room.roomName,
          leaf: false,
        }));
        resolve(nodes);
      } catch {
        resolve([]);
      }
    } else if (level === 1) {
      // 加载分组
      try {
        const roomId = node.data.id;
        const res = await VestAPI.getAllGroups(roomId);
        const groups = (res as any).data || res || [];
        const nodes = groups.map((group: LiveVestGroupVo) => ({
          id: group.id,
          label: group.groupName,
          leaf: false,
        }));
        resolve(nodes);
      } catch {
        resolve([]);
      }
    } else if (level === 2) {
      // 加载马甲
      try {
        const groupId = node.data.id;
        const res = await VestAPI.getVestsByGroupId(groupId);
        const vests = (res as any).data || res || [];
        const nodes = vests.map((vest: LiveVestVo) => ({
          id: vest.id,
          label: vest.nickname,
          leaf: true,
          vest: vest, // 保存完整的马甲信息
        }));
        resolve(nodes);
      } catch {
        resolve([]);
      }
    }
  },
};

// 编辑时显示的马甲信息
const selectedVestInfo = computed(() => {
  if (formData.id && props.aiVestConfigList.length > 0) {
    const config = props.aiVestConfigList.find((item) => item.id === formData.id);
    return config ? `马甲ID: ${config.vestId} - ${config.vestNickname}` : "";
  }
  return "";
});

// 表单验证规则
const rules = reactive({
  sex: [
    {
      required: true,
      message: "请选择性别",
      trigger: "change",
    },
  ],
  age: [
    {
      required: true,
      message: "请输入年龄",
      trigger: "change",
    },
  ],
  vestCascader: [
    {
      required: true,
      message: "请选择马甲",
      trigger: "change",
      validator: (rule: any, value: any, callback: any) => {
        // 如果是编辑模式，跳过级联选择器验证
        if (formData.id) {
          callback();
          return;
        }

        if (!value || value.length !== 3) {
          callback(new Error("请完整选择直播间、分组和马甲"));
        } else {
          // 验证是否正确设置了vestId
          if (!formData.vestId || formData.vestId <= 0) {
            callback(new Error("马甲ID获取失败，请重新选择"));
          } else {
            callback();
          }
        }
      },
    },
  ],
  ruleId: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value === undefined || value === null || value === "") {
          formData.ruleId = 0; // 设置默认值
          callback();
        } else if (typeof value === "number" && value >= 0) {
          callback();
        } else if (typeof value === "string" && /^\d+$/.test(value) && Number(value) >= 0) {
          formData.ruleId = Number(value);
          callback();
        } else {
          callback(new Error("请选择有效的触发规则"));
        }
      },
      trigger: "change",
    },
  ],
  temperature: [
    {
      required: true,
      message: "请设置生成温度",
      trigger: "change",
    },
    {
      validator: (rule: any, value: any, callback: any) => {
        const num = Number(value);
        if (isNaN(num) || num < 0 || num > 2) {
          callback(new Error("生成温度必须在0-2之间"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  type: [
    {
      required: true,
      message: "请选择人设类型",
      trigger: "change",
    },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value === undefined || value === null || value === "") {
          callback(new Error("请选择人设类型"));
        } else if (
          typeof value === "number" ||
          (typeof value === "string" && /^\d+$/.test(value))
        ) {
          // 确保类型是数字
          formData.type = Number(value);
          callback();
        } else {
          callback(new Error("请选择有效的人设类型"));
        }
      },
      trigger: "change",
    },
  ],
});

// 人设类型选项
const personaTypeOptions = ref<{ label: string; value: number }[]>([]);

// 级联选择器变化处理
function handleCascaderChange(value: CascaderValue) {
  console.log("级联选择器变化:", value);
  console.log("级联选择器变化类型:", typeof value, Array.isArray(value));

  if (value && Array.isArray(value) && value.length === 3) {
    // 确保从级联选择器中获取的是真正的马甲ID（第三层级）
    const rawVestId = value[2];
    console.log("原始马甲ID值:", rawVestId, "类型:", typeof rawVestId);

    const vestId = Number(rawVestId);
    console.log("转换后的马甲ID:", vestId);

    if (isNaN(vestId) || vestId <= 0) {
      console.error("无效的马甲ID:", rawVestId, "转换后:", vestId);
      formData.vestId = 0;
    } else {
      formData.vestId = vestId;
      console.log("成功设置马甲ID:", vestId);

      // 手动触发表单验证，确保验证状态更新
      setTimeout(() => {
        if (formRef.value) {
          formRef.value.validateField("vestCascader");
        }
      }, 100);
    }
  } else {
    formData.vestId = 0;
    console.log("清空马甲ID，设为0");
  }

  console.log("当前formData.vestId:", formData.vestId);
}

// 级联选择器清空处理
function handleCascaderClear() {
  console.log("级联选择器被清空");
  formData.vestCascader = [];
  formData.vestId = 0;
}

// 会话轮次变化处理
function handleSessionRoundChange(val: string | number) {
  // 允许用户手动输入大于0的数字
  if (typeof val === "string" && /^\d+$/.test(val) && Number(val) > 0) {
    formData.sessionRound = Number(val);
  }
}

// 重置表单 - 关键修复：确保级联选择器被完全清空
async function resetForm() {
  console.log("开始重置表单...");

  // 重置表单数据
  const defaultType = personaTypeOptions.value.length > 0 ? personaTypeOptions.value[0].value : 0;
  console.log("重置表单，设置默认人设类型:", defaultType, "可用选项:", personaTypeOptions.value);

  Object.assign(formData, {
    id: undefined,
    vestCascader: [], // 清空级联选择器
    vestId: 0,
    temperature: 0.7,
    promptTemplate: "",
    contextWindow: 0,
    useRag: 0,
    type: defaultType,
    isDelay: 0,
    fuzzinessRate: 0,
    isAuto: 0,
    triggerMode: 0,
    ruleId: 0,
    sessionRound: 1,
    status: 1,
    knowledgeIds: [],
    sex: "",
    age: "",
    intro: "",
  });

  // 强制重新渲染级联选择器 - 这是最有效的清空方法
  cascaderKey.value++;

  // 等待DOM更新
  await nextTick();

  // 清空级联选择器的内部状态
  if (cascaderRef.value) {
    console.log("清空级联选择器内部状态");
    cascaderRef.value.clearCheckedNodes();
    cascaderRef.value.presentText = "";
    cascaderRef.value.inputValue = "";
  }

  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields();
    formRef.value.clearValidate();
  }

  console.log("表单重置完成，当前vestCascader:", formData.vestCascader);
}

// 表单验证
function validate() {
  return formRef.value?.validate();
}

// 清除验证
function clearValidate() {
  formRef.value?.clearValidate();
}

// 使用props中的personaTypeOptions，如果没有提供则从API获取
async function fetchPersonaTypes() {
  // 如果props中已经提供了personaTypeOptions，则不需要再次获取
  if (props.personaTypeOptions && props.personaTypeOptions.length > 0) {
    personaTypeOptions.value = props.personaTypeOptions;
    console.log("使用父组件提供的人设类型列表:", personaTypeOptions.value);
    return;
  }

  try {
    console.log("从API获取人设类型列表");
    const res = await getVestUserType();
    console.log("API返回的人设类型数据:", res);

    // 处理不同的API响应结构
    if (res && res.data && Array.isArray(res.data)) {
      personaTypeOptions.value = res.data.map((item) => ({
        label: item.description,
        value: item.type,
      }));
    } else if (Array.isArray(res)) {
      personaTypeOptions.value = res.map((item) => ({
        label: item.description,
        value: item.type,
      }));
    } else {
      console.warn("人设类型列表格式不正确:", res);
      personaTypeOptions.value = []; // 设置为空数组
    }

    console.log("处理后的人设类型列表:", personaTypeOptions.value);
  } catch (error) {
    console.error("获取人设类型列表失败", error);
    personaTypeOptions.value = []; // 设置为空数组
  }
}

onMounted(() => {
  fetchPersonaTypes();
});

// 监听props中的personaTypeOptions变化
watch(
  () => props.personaTypeOptions,
  (newOptions) => {
    if (newOptions && newOptions.length > 0) {
      personaTypeOptions.value = newOptions;
    }
  },
  { immediate: true }
);

// 暴露方法给父组件
defineExpose({
  resetForm,
  validate,
  clearValidate,
});
</script>
