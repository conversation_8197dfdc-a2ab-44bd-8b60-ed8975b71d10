import{d as e,S as l,r as a,c as t,e as r,f as o,w as s,m as i,i as u,h as d,g as m,P as n,Q as p,ak as c,C as v,$ as f,az as g}from"./index.Dk5pbsTU.js";import{E as h}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as b,a as V}from"./el-form-item.Bw6Zyv_7.js";import{E as _}from"./el-date-picker.B6TshyBV.js";import{E as y}from"./el-input.DiGatoux.js";/* empty css                     */import"./el-popper.Dbn4MgsT.js";import{E as w}from"./el-button.CXI119n4.js";import{E as j}from"./el-switch.kQ5v4arH.js";import{_ as U}from"./SingleImageUpload.WGBxPB_4.js";/* empty css               */import{E as x,a as I}from"./el-select.CRWkm-it.js";import{L as k,a as E}from"./liveSession.RdnykuzD.js";const Y={class:"flex items-center"},q={class:"dialog-footer"},M=e({__name:"edit",emits:["success"],setup(e,{expose:M,emit:S}){const K=l({visible:!1,title:""}),L=a(new k),H=a(),T=S,C=a(!1),D=a([]),z=a([]),A=a([]);const F=t({get:()=>1===L.value.isSecret,set:e=>{L.value.isSecret=e?1:0,e||(L.value.secretKey="")}}),G={title:[{required:!0,message:"请输入直播名称",trigger:"blur"}],coverUrl:[{required:!0,message:"请上传封面图",trigger:"blur"}],anchorId:[{required:!0,message:"请选择主播",trigger:"blur"}],roomId:[{required:!0,message:"请选择直播间",trigger:"blur"}],startTime:[{required:!0,message:"请选择预计开播时间",trigger:"blur"}],logo:[{required:!0,message:"请上传logo",trigger:"blur"}],secretKey:[{required:!0,message:"请输入口令密码",trigger:"blur",validator:(e,l,a)=>{if(1===L.value.isSecret&&!l)return a(new Error("请输入口令密码"));a()}}],assistIdList:[{required:!0,message:"请选择助理",trigger:"blur",validator:(e,l,a)=>{if(!(null==l?void 0:l.length))return a(new Error("请选择助理"));a()}}]};function J(){H.value.validate((e=>{e&&(C.value=!0,E.save(L.value).then((()=>{g.success("保存成功"),K.visible=!1,T("success")})).finally((()=>C.value=!1)))}))}function P(){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";let l="";for(let a=0;a<7;a++)l+=e.charAt(Math.floor(62*Math.random()));L.value.secretKey=l}return M({open:(e,l=!1)=>{var a;E.anchorList().then((e=>{D.value=e})),E.roomList().then((e=>{z.value=e})),E.assistList().then((e=>{A.value=e})),L.value=new k(e),l&&(L.value.id="",L.value.anchorId=""),K.title=(null==(a=L.value)?void 0:a.id)?"编辑直播场次":"新增直播间场次",K.visible=!0}}),(e,l)=>{const a=y,t=V,g=I,k=x,E=U,M=j,S=w,T=_,C=b,A=h;return o(),r(A,{modelValue:u(K).visible,"onUpdate:modelValue":l[9]||(l[9]=e=>u(K).visible=e),title:u(K).title,width:"500px"},{footer:s((()=>[v("div",q,[i(S,{type:"primary",onClick:J},{default:s((()=>l[12]||(l[12]=[f("确 定")]))),_:1,__:[12]}),i(S,{onClick:l[8]||(l[8]=e=>u(K).visible=!1)},{default:s((()=>l[13]||(l[13]=[f("取 消")]))),_:1,__:[13]})])])),default:s((()=>[i(C,{ref_key:"editFormRef",ref:H,model:u(L),rules:G,"label-width":"110px"},{default:s((()=>[i(t,{label:"直播名称",prop:"title"},{default:s((()=>[i(a,{modelValue:u(L).title,"onUpdate:modelValue":l[0]||(l[0]=e=>u(L).title=e),maxlength:50,"show-word-limit":"",placeholder:"请输入直播名称"},null,8,["modelValue"])])),_:1}),i(t,{label:"直播间",prop:"roomId"},{default:s((()=>[i(k,{modelValue:u(L).roomId,"onUpdate:modelValue":l[1]||(l[1]=e=>u(L).roomId=e),style:{width:"100%"}},{default:s((()=>[(o(!0),m(n,null,p(u(z),((e,l)=>(o(),r(g,{key:l,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"主播",prop:"anchorId"},{default:s((()=>[i(k,{modelValue:u(L).anchorId,"onUpdate:modelValue":l[2]||(l[2]=e=>u(L).anchorId=e),style:{width:"100%"}},{default:s((()=>[(o(!0),m(n,null,p(u(D),((e,l)=>(o(),r(g,{key:l,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"封面图",prop:"coverUrl"},{default:s((()=>[i(E,{modelValue:u(L).coverUrl,"onUpdate:modelValue":l[3]||(l[3]=e=>u(L).coverUrl=e),style:{width:"100px",height:"100px"}},null,8,["modelValue"])])),_:1}),i(t,{label:"是否私密直播",prop:"isSecret"},{default:s((()=>[i(M,{modelValue:u(F),"onUpdate:modelValue":l[4]||(l[4]=e=>c(F)?F.value=e:null)},null,8,["modelValue"])])),_:1}),u(F)?(o(),r(t,{key:0,label:"口令密码",prop:"secretKey"},{default:s((()=>[v("div",Y,[i(a,{modelValue:u(L).secretKey,"onUpdate:modelValue":l[5]||(l[5]=e=>u(L).secretKey=e),placeholder:"请输入口令密码"},null,8,["modelValue"]),i(S,{type:"primary",class:"ml-2",onClick:P},{default:s((()=>l[10]||(l[10]=[f("生成口令")]))),_:1,__:[10]})]),l[11]||(l[11]=v("div",{class:"el-form-item-msg"},"可手动编辑或点击生成按钮随机生成",-1))])),_:1,__:[11]})):d("",!0),i(t,{label:"预计开播时间",prop:"startTime"},{default:s((()=>[i(T,{modelValue:u(L).startTime,"onUpdate:modelValue":l[6]||(l[6]=e=>u(L).startTime=e),style:{width:"100%"},type:"datetime",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm",placeholder:"选择日期时间"},null,8,["modelValue"])])),_:1}),i(t,{label:"本场直播介绍",prop:"description"},{default:s((()=>[i(a,{modelValue:u(L).description,"onUpdate:modelValue":l[7]||(l[7]=e=>u(L).description=e),maxlength:200,"show-word-limit":"",type:"textarea",rows:5,placeholder:"请输入直播间介绍"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}});export{M as _};
