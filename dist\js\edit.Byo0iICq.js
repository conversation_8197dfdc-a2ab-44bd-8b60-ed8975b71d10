import{d as e,S as l,r as a,e as i,f as t,w as r,C as s,m as o,g as n,h as d,ak as p,i as m,$ as u,E as c,aR as f,az as v}from"./index.Dk5pbsTU.js";import{E as j}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{a as _,E as b}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{E as h}from"./el-image-viewer.BH897zgF.js";/* empty css               */import{E as g,a as x}from"./el-form-item.Bw6Zyv_7.js";import{E as w}from"./el-button.CXI119n4.js";import{E as y}from"./el-input.DiGatoux.js";import{A as U,S as V}from"./assistant.DzsSf80o.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./index.ybpLT-bz.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./_Uint8Array.n_j8oILW.js";import"./_initCloneObject.BN1anLuC.js";import"./_baseIteratee.CPRpgrLu.js";import"./isEqual.C0S6DIiJ.js";import"./debounce.DJJTSR8O.js";import"./index.wZTqlYZ6.js";import"./use-form-common-props.CQPDkY7k.js";import"./use-form-item.DzRJVC1I.js";import"./position.DfR5znly.js";import"./index.DEKElSOG.js";import"./castArray.C4RhTg2c.js";import"./index.Vn8pbgQR.js";const I={style:{"text-align":"right",width:"100%"}},E={key:0},C={class:"dialog-footer"},N=k(e({__name:"edit",emits:["success"],setup(e,{expose:k,emit:N}){const A=l({visible:!1,title:"新增助理"}),P=a(""),D=a([]),S=N,$=a(!1);function q(){if(!P.value)return void v.warning("请输入Uid");const e=P.value.replaceAll("，",",");if(e)return $.value=!0,U.getUserByPhone(e).then((e=>{e.forEach((e=>{const l=D.value.findIndex((l=>e.appUserId===l.appUserId)),a=new V(e);l>=0?D.value[l]=a:D.value.push(a)}))})).finally((()=>$.value=!1));v.warning("请输入有效的uid")}function L(){if(0!==D.value.length){for(const e of D.value){if(!e.userName)return void v.warning("请填写姓名");if(!e.nickName)return void v.warning("请填写昵称")}$.value=!0,U.save(D.value).then((()=>{v.success("保存成功"),A.visible=!1,S("success")})).finally((()=>{$.value=!1}))}else v.warning("请至少添加一个助理")}function O(){P.value="",D.value=[]}return k({open:()=>{A.visible=!0,A.title="新增助理",P.value="",D.value=[]}}),(e,l)=>{const a=y,v=x,U=w,V=g,k=b,N=h,S=c,$=_,R=j;return t(),i(R,{modelValue:m(A).visible,"onUpdate:modelValue":l[2]||(l[2]=e=>m(A).visible=e),title:m(A).title,width:"800px",onClosed:O},{footer:r((()=>[s("div",C,[o(U,{onClick:l[1]||(l[1]=e=>m(A).visible=!1)},{default:r((()=>l[5]||(l[5]=[u("取 消")]))),_:1,__:[5]}),o(U,{type:"primary",disabled:0===m(D).length,onClick:L},{default:r((()=>l[6]||(l[6]=[u(" 确认添加 ")]))),_:1,__:[6]},8,["disabled"])])])),default:r((()=>[s("div",null,[o(V,{class:"mb-20px"},{default:r((()=>[o(v,{label:"输入UID",required:""},{default:r((()=>[o(a,{modelValue:m(P),"onUpdate:modelValue":l[0]||(l[0]=e=>p(P)?P.value=e:null),clearable:"",type:"textarea",placeholder:"请输入UID，若多个UID，请用'逗号'隔开"},null,8,["modelValue"])])),_:1}),o(v,null,{default:r((()=>[s("div",I,[o(U,{type:"primary",disabled:!m(P),onClick:q},{default:r((()=>l[3]||(l[3]=[u(" 识别 ")]))),_:1,__:[3]},8,["disabled"])])])),_:1})])),_:1}),m(D).length>0?(t(),n("div",E,[l[4]||(l[4]=s("div",{class:"mb-10px"},"成功识别如下用户信息：",-1)),o($,{data:m(D),border:""},{default:r((()=>[o(k,{label:"序号",align:"center",width:"55",type:"index"}),o(k,{label:"头像",align:"center",width:"100"},{default:r((({row:e})=>[o(N,{style:{width:"30px",height:"30px","border-radius":"30px"},"preview-src-list":[e.avatar],"preview-teleported":"",src:e.avatar},null,8,["preview-src-list","src"])])),_:1}),o(k,{label:"微信昵称",align:"center",prop:"wechatName","min-width":"120"}),o(k,{label:"姓名",align:"center","min-width":"120"},{default:r((({row:e})=>[o(a,{modelValue:e.userName,"onUpdate:modelValue":l=>e.userName=l,placeholder:"请输入姓名"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),o(k,{label:"助理昵称",align:"center","min-width":"120"},{default:r((({row:e})=>[o(a,{modelValue:e.nickName,"onUpdate:modelValue":l=>e.nickName=l,placeholder:"请输入昵称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),o(k,{label:"操作",align:"center",width:"80"},{default:r((({$index:e})=>[o(U,{type:"danger",circle:"",onClick:l=>{return a=e,void D.value.splice(a,1);var a}},{default:r((()=>[o(S,null,{default:r((()=>[o(m(f))])),_:1})])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])):d("",!0)])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-421485c6"]]);export{N as default};
