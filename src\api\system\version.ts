import request from "@/utils/request";

// 分页查询
export class AppVersionQuery implements PageQuery {
  pageNum = 1;
  pageSize = 10;
  // 版本号
  versionName: string = "";
  // 设备类型：0-安卓，1-iOS
  deviceType: string = "";
  // 状态：0-未发布，1-已发布
  status: string = "";
  // 平台类型 平台类型：0-主播端，1-用户端
  platformType: string = "";
}

export interface AppVersionVO {
  // ID
  id: number;
  // 版本号
  versionName: string;
  // 状态: 0-未发布，1-已发布
  status: number;
  // 操作系统: 0-安卓，1-iOS
  deviceType: number;
  // 是否热更新: 0-否，1-是
  isHotUpdate: number;
  // 是否强制更新: 0-否，1-是
  isForceUpdate: number;
  // 更新内容
  updateContent: string;
  // 发布时间
  publishTime: string;
  // 创建时间
  createdAt: string;
  // 平台类型: 0-主播端，1-用户端
  platformType: number;
  // 下载地址
  downloadUrl: string;
  // 操作加载
  loading?: boolean;
}

export class SaveAppVersionDto {
  id?: number; // ID，更新时传入
  versionName: string = ""; // 版本号
  deviceType: number = 0; // 设备类型：0-安卓，1-iOS
  platformType: number = 0; // 平台类型：0-主播端，1-用户端
  isHotUpdate: number = 0; // 是否热更新：0-否，1-是
  isForceUpdate: number = 0; // 是否强制更新：0-否，1-是
  downloadUrl: string = ""; // 安装包下载地址
  downloadUrls: string[] = [];
  updateContent: string = ""; // 更新内容描述

  constructor(data?: AppVersionVO) {
    if (data) {
      this.id = data.id;
      this.versionName = data.versionName;
      this.deviceType = data.deviceType;
      this.platformType = data.platformType;
      this.isHotUpdate = data.isHotUpdate;
      this.isForceUpdate = data.isForceUpdate;
      this.downloadUrl = data.downloadUrl;
      this.downloadUrls = [data.downloadUrl];
      this.updateContent = data.updateContent;
    }
  }
}

const VersionApi = {
  /** 分页*/
  page(queryParams?: AppVersionQuery) {
    return request<any, PageResult<AppVersionVO[]>>({
      url: `/adminConfig/version/page`,
      method: "post",
      data: queryParams,
    });
  },
  /**
   * 删除
   *
   * @param id ID
   */
  remove(id: number) {
    return request<any, null>({
      url: `/adminConfig/version/remove/${id}`,
      method: "post",
    });
  },

  /**
   * 保存
   * @param data 表单数据
   */
  save(data: SaveAppVersionDto) {
    return request({
      url: `/adminConfig/version/saveOrUpdate`,
      method: "post",
      data: data,
    });
  },
  /** 发布*/
  publish(id: number) {
    return request<any, null>({
      url: `/adminConfig/version/publish/${id}`,
      method: "post",
    });
  },
};

export default VersionApi;
