import request from "@/utils/request";

// 分页查询
export class LiveSessionPageQueryDto implements PageQuery {
  pageNum = 1;
  pageSize = 10;
  // 关键字
  keywords: string = "";
  // 状态
  status: string = "";
}

// 观看记录分页
export class RecordListQueryPageDto implements PageQuery {
  pageNum = 1;
  pageSize = 10;
  // 直播场次id
  sessionId?: number;
  // 关键字
  keywords: string = "";
  // 省市
  province: string[] = [];
  //用户状态 0:普通封禁 1:永久封禁 2:拉黑
  userStatus: string = "";
  // 	用户类型 0普通用户 1:主播 2:助理
  userType: string = "";
  // 评论数
  commentCount: string = "";
  // 邀请人id
  inviterId?: number;
  // 邀请人类型
  inviterType?: number;

  constructor(id?: number, inviterId?: number, inviterType?: number) {
    this.sessionId = id;
    this.inviterId = inviterId;
    this.inviterType = inviterType;
  }
}

// 观看记录分页返回信息
export interface LiveWatchingRecordVo {
  userId: number;
  id: number; // 记录ID
  sessionId: number; // 直播场次id
  tenantUserId: number; // 租户用户id
  duration: number; // 观看时长(秒)
  country: string; // 国
  province: string; // 省
  city: string; // 市
  nickname: string; // 昵称
  avatar: string; // 头像
  userType: number; // 0普通用户 1:主播 2:助理
  durationBan?: number; // 封禁时长(秒) 封禁类型为0时
  userStatus?: number; // 0:普通封禁 1:永久封禁 2:拉黑 为空就是正常
  commentCount: number; // 发言数
  tagList: string[]; // 标签列表
  durationStr: string;
}

// 发言记录分页查询
export class CommentListQueryPageDto implements PageQuery {
  pageNum = 1;
  pageSize = 10;
  // 直播场次id
  sessionId?: number;
  // 关键字
  keyWords: string = "";
  //结束时间
  endTime: string = "";
  // 	开始时间
  startTime: string = "";
  // 租户用户idiot
  userId: string = "";

  constructor(id?: number, userId?: string) {
    this.sessionId = id;
    this.userId = userId || "";
  }
}

// 发言记录分页返回信息
export interface PageLiveCommentVo {
  id: number; // 评论ID
  tenantUserId: number; // 租户用户ID
  userId: number; // 用户ID
  content: string; // 评论内容
  commentUsername: string; // 发言人名称
  createdAt: string; // 评论时间
  isWebcaster: number; // 是否是主播 0-否 1-是
  isAssistant: number; // 是否是助理 0-否 1-是
  replyUserName?: string; // 回复对象名称
  replyUserId?: number; // 回复对象ID
}

// 引流记录分页查询
export class InviteRecordPageDto implements PageQuery {
  pageNum = 1;
  pageSize = 10;
  // 直播场次id
  sessionId?: number;

  constructor(id?: number) {
    this.sessionId = id;
  }
}

// 引流记录分页返回信息
export interface PageInviteRecordVo {
  id: number; // 记录id
  sharedCount: number; // 转发数
  inviteCount: number; // 引流数
  nickName: string; // 用户名称
  userId: number; //用户id
  userType: number; //用户类型 0普通用户 1:主播 2:助理
  currentLevel: string; // 会员等级名
}

// 分页返回信息
export interface LiveSessionPageVo {
  allowReplay: number; // 0-不允许回放 1-允许回放
  id: number; // 场次id
  aiVestNum: number; //  vest数量
  roomId: number; // 关联直播间ID
  roomName: string; // 直播间名称
  title: string; // 直播标题
  anchorId: number; // 主播id
  anchorName: string; // 主播名称
  anchorAvatar: string; // 主播头像
  liveType: number; // 直播类型 0:预设直播 1:自主直播
  startTime: string; // 计划开始时间
  actualStartTime: string; // 实际开始时间
  actualEndTime: string; // 实际结束时间
  duration: number; // 直播时长(秒)
  status: number; // 0-未开始 1-直播中 2-已结束
  watchCount: number; // 观看次数
  commentCount: number; // 发言数
  replayUrl: string; // 回放地址
  createdAt: string; // 创建时间
  coverUrl: string; // 封面图URL
  description: string;
  loading: boolean;
  isEarlyWarning: number; // 是否预警 0-否 1-是
  businessScope: string; // 业务范围
  assistIdList: number[]; // 助理id列表
  logo: string;
  isSecret: boolean; // 是否私密直播
  secretKey: string; // 口令密码
}

export class LiveSessionDto {
  id?: number | string = ""; // 场次ID (可选)
  roomId!: number; // 关联直播间ID
  title: string = ""; // 直播标题
  anchorId: number | string = ""; // 主播id
  startTime: string = ""; // 计划开始时间
  coverUrl: string = ""; // 封面图URL
  description: string = ""; // 直播间描述
  assistIdList: number[] = []; // 助理id列表
  logo: string = "";
  isSecret: number = 0; // 是否私密直播 0-否 1-是
  secretKey: string = ""; // 口令密码

  constructor(e?: LiveSessionPageVo) {
    if (e) {
      this.id = e.id;
      this.logo = e.logo;
      this.roomId = e.roomId;
      this.title = e.title;
      this.anchorId = e.anchorId;
      this.startTime = e.startTime;
      this.coverUrl = e.coverUrl;
      this.description = e.description;
      this.assistIdList = e.assistIdList;
      this.logo = e.logo || "";
      this.isSecret = e.isSecret ? 1 : 0;
      this.secretKey = e.secretKey || "";
    }
  }
}

export interface LiveSharedUrlVo {
  h5Url: string; //	h5链接1
  miniProgramUrl: string; //小程序码url
}

export interface LiveSessionBaseInfoVo {
  id: number; // 场次id
  roomId: number; // 关联直播间ID
  roomName: string; // 直播间名称
  title: string; // 直播标题
  anchorId: number; // 主播id
  anchorName: string; // 主播名称
  anchorAvatar: string; // 主播头像
  actualStartTime: string; // 实际开始时间
  duration: number; // 直播时长(秒)
  watchCount: number; // 观看次数
  commentCount: number; // 发言数
  replayUrl: string; // 回放地址
  assistList: string[]; // 助理列表
  durationStr: string; // 直播时长(字符串)
  coverUrl: string;
  status: number;
}

export interface LiveWatchingUserInfoVO {
  id: number; // 记录ID
  currentPoints: number; // 当前积分
  currentLevel: string; // 当前等级
}

const LiveSessionApi = {
  /*修改回放*/
  allowPlayback(id: number) {
    return request<any, null>({
      url: `/tenant/live/session/allowPlayback?sessionId=${id}`,
      method: "get",
    });
  },
  /** 场次列表*/
  page(query: LiveSessionPageQueryDto) {
    return request<any, PageResult<LiveSessionPageVo[]>>({
      url: `/tenant/live/session/list`,
      method: "get",
      params: query,
    });
  },
  /**
   * 保存
   * @param data 表单数据
   */
  save(data: LiveSessionDto) {
    return request({
      url: `/tenant/live/session/saveOrEdit`,
      method: "post",
      data: data,
    });
  },
  /**
   * 删除
   * @param id ID
   */
  remove(id: number) {
    return request<any, null>({
      url: `/tenant/live/session/remove/${id}`,
      method: "delete",
    });
  },
  /*在职主播列表*/
  anchorList() {
    // eslint-disable-next-line no-undef
    return request<any, SelectVo[]>({
      url: `/tenant/live/session/anchorList`,
      method: "post",
    });
  },
  /*在职助手*/
  assistList() {
    // eslint-disable-next-line no-undef
    return request<any, SelectVo[]>({
      url: `/tenant/live/session/assistList`,
      method: "post",
    });
  },
  /*直播间列表*/
  roomList() {
    // eslint-disable-next-line no-undef
    return request<any, SelectVo[]>({
      url: `/tenant/live/session/roomList`,
      method: "post",
    });
  },
  /*直播观看记录*/
  recordList(query: RecordListQueryPageDto) {
    return request<any, PageResult<LiveWatchingRecordVo[]>>({
      url: `/tenant/live/watching/recordList`,
      method: "get",
      params: query,
    });
  },
  /*观看用户信息*/
  watchingUserInfo(id: number) {
    return request<any, LiveWatchingUserInfoVO>({
      url: `/tenant/live/watching/userInfo?userId=${id}`,
      method: "get",
    });
  },
  /*发言列表*/
  commentList(query: CommentListQueryPageDto) {
    return request<any, PageResult<PageLiveCommentVo[]>>({
      url: `/tenant/live/comment/list`,
      method: "get",
      params: query,
    });
  },
  /*引流列表*/
  inviteRecordList(query: InviteRecordPageDto) {
    return request<any, PageResult<PageInviteRecordVo[]>>({
      url: `/tenant/live/session/inviteRecord`,
      method: "get",
      params: query,
    });
  },
  /*分享链接*/
  sharedUrl(id: number) {
    return request<any, LiveSharedUrlVo>({
      url: `/tenant/live/session/sharedUrl?sessionId=${id}`,
      method: "get",
    });
  },
  /*基础信息*/
  baseInfo(id: number) {
    return request<any, LiveSessionBaseInfoVo>({
      url: `/tenant/live/session/baseInfo?sessionId=${id}`,
      method: "get",
    });
  },
};

export default LiveSessionApi;
