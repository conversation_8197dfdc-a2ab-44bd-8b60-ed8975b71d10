import request from "@/utils/request";

export class SaveOrEditKnowledgeTypeDto {
  id?: number;
  /*类型名称*/
  name: string = "";
  /*类型状态*/
  status: number = 1;

  constructor(e?: KnowledgeTypePageVo) {
    if (e) {
      this.id = e.id;
      this.name = e.name;
      this.status = e.status;
    }
  }
}

export class KnowledgeTypePageQueryDto {
  pageNum = 1;
  pageSize = 10;
  /*关键字*/
  keyWord: string = "";
  /*状态*/
  status: string = "";
}

export interface KnowledgeTypePageVo {
  id: number;
  /*类型名称*/
  name: string;
  /*状态 0-禁用 1-启用*/
  status: number;
  loading: boolean;
  statusLoading: boolean;
}

export class KnowledgePageQueryDto {
  pageNum = 1;
  pageSize = 10;
  /*关键字*/
  keyWord: string = "";
  /*状态*/
  status: string = "";
  /*类型id*/
  typeId: string = "";
  /*包含的id*/
  includeIds: number[] = [];
  /*排除的id*/
  excludeIds: number[] = [];
}

export interface KnowledgePageVo {
  /*文件名称*/
  fileName: string;
  /*文件类型id*/
  typeId: number;
  /*类型名称*/
  typeName: string;
  /*类型状态*/
  typeStatus: number;
  /*状态*/
  status: number;
  /*内容*/
  content: string;
  /*id*/
  id: number;
  /*加载状态*/
  loading: boolean;
  /*状态加载*/
  statusLoading: boolean;
}

export class AiKnowledgeDto {
  /*id*/
  id?: number;
  /*文件名称*/
  fileName: string = "";
  /*类型id*/
  typeId: string = "";
  /*状态*/
  status: number = 1;
  /*内容*/
  content: string = "";

  constructor(e?: KnowledgePageVo) {
    if (e) {
      this.id = e.id;
      this.fileName = e.fileName;
      this.typeId = String(e.typeId);
      this.status = e.status;
      this.content = e.content;
    }
  }
}

const KnowledgeTypeApi = {
  /*所有类型*/
  allKnowledgeType(query: KnowledgeTypePageQueryDto) {
    return request<KnowledgeTypePageQueryDto, KnowledgeTypePageVo[]>({
      url: `/system/aiKnowledge/typeAll`,
      method: "get",
      params: query,
    });
  },
  /*知识库分页*/
  aiKnowledgePage(query: KnowledgePageQueryDto) {
    return request<any, PageResult<KnowledgePageVo[]>>({
      url: `/system/aiKnowledge/aiKnowledgePage`,
      method: "get",
      params: query,
    });
  },
  /*删除知识库*/
  removeKnowledge(id: number) {
    return request<any, any>({
      url: `/system/aiKnowledge/removeKnowledge/${id}`,
      method: "delete",
    });
  },
  /*知识库保存*/
  aiKnowledgeSave(data: AiKnowledgeDto) {
    return request<any, any>({
      url: `/system/aiKnowledge/saveOrUpdate`,
      method: "post",
      data: data,
    });
  },
  /** 分页 */
  page(queryParams?: KnowledgeTypePageQueryDto) {
    return request<any, PageResult<KnowledgeTypePageVo[]>>({
      url: `/system/aiKnowledge/typePage`,
      method: "get",
      params: queryParams,
    });
  },
  /*删除*/
  remove(id: number) {
    return request<any, any>({
      url: `/system/aiKnowledge/removeType?id=${id}`,
      method: "delete",
    });
  },
  /*新增或编辑*/
  saveOrEdit(data: SaveOrEditKnowledgeTypeDto) {
    return request({
      url: `/system/aiKnowledge/saveOrEditType`,
      method: "post",
      data: data,
    });
  },
};
export default KnowledgeTypeApi;
