<script setup lang="ts">
const dialog = reactive({
  visible: false,
  title: "查看直播",
});
const liveUrl = ref("");
defineExpose({
  open: (_url: string) => {
    liveUrl.value = _url;
    dialog.visible = true;
    dialog.title = "观看直播";
  },
});
</script>

<template>
  <el-drawer v-model="dialog.visible" class="look-live-drawer" :title="dialog.title" size="455">
    <div style="width: 100%; height: calc(100% - 10px)">
      <iframe
        v-if="dialog.visible"
        :src="liveUrl"
        width="100%"
        height="100%"
        frameborder="0"
        allowfullscreen
      />
    </div>
  </el-drawer>
</template>
<style lang="scss">
.look-live-drawer {
  & > .el-drawer__body {
    padding-top: 0;
  }

  & > .el-drawer__header {
    margin-bottom: 20px;
  }
}
</style>
