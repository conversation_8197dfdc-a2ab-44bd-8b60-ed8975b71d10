var e=Object.defineProperty,a=(a,t,l)=>((a,t,l)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[t]=l)(a,"symbol"!=typeof t?t+"":t,l);import{aT as t,d as l,r,S as s,e as i,f as o,w as n,m as u,$ as d,C as m,az as p}from"./index.Dk5pbsTU.js";import{E as c}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as f}from"./el-button.CXI119n4.js";import{E as g,a as b}from"./el-form-item.Bw6Zyv_7.js";import{E as h,a as _}from"./el-radio.w2rep3_A.js";import{E as U}from"./el-input.DiGatoux.js";import{_ as v}from"./SingleImageUpload.WGBxPB_4.js";class S{constructor(e="10"){a(this,"pageNum",1),a(this,"pageSize",10),a(this,"startTime"),a(this,"endTime"),this.pageSize=parseInt(e)}}class w{constructor(e){a(this,"id"),a(this,"isShow"),this.id=e.id,this.isShow=e.isShow}}class V{static page(e){return t.get("/api/basic/banner/list",{params:e})}static editStatus(e){return t.put("/api/basic/banner/status",e)}static remove(e){return t.delete(`/api/basic/banner/${e}`)}static saveOrUpdate(e){return t.post("/api/basic/banner",e)}}const j={class:"dialog-footer"},y=l({name:"EditBanner",__name:"edit",emits:["success"],setup(e,{expose:a,emit:t}){const l=t,S=r(!1),w=r(!1),y=r(),E=s({bannerUrl:"",targetUrl:"",isShow:1}),x=s({bannerUrl:[{required:!0,message:"请上传轮播图",trigger:"change"}],targetUrl:[{required:!0,message:"请输入跳转链接",trigger:"blur"}],isShow:[{required:!0,message:"请选择展示状态",trigger:"change"}]});function O(){q()}function k(){var e;null==(e=y.value)||e.validate((e=>{e&&(w.value=!0,V.saveOrUpdate(E).then((()=>{p.success("操作成功"),l("success"),q()})).finally((()=>{w.value=!1})))}))}function q(){S.value=!1,E.bannerUrl="",E.targetUrl="",E.isShow=1,E.id=void 0}return a({open:function(e){q(),e&&Object.assign(E,e),S.value=!0}}),(e,a)=>{const t=b,l=U,r=_,s=h,p=g,V=f,q=c;return o(),i(q,{title:E.id?"编辑轮播图":"新增轮播图",modelValue:S.value,"onUpdate:modelValue":a[3]||(a[3]=e=>S.value=e),width:"500px","append-to-body":""},{footer:n((()=>[m("div",j,[u(V,{type:"primary",onClick:k,loading:w.value},{default:n((()=>a[6]||(a[6]=[d("确 定")]))),_:1,__:[6]},8,["loading"]),u(V,{onClick:O},{default:n((()=>a[7]||(a[7]=[d("取 消")]))),_:1,__:[7]})])])),default:n((()=>[u(p,{ref_key:"formRef",ref:y,model:E,rules:x,"label-width":"80px"},{default:n((()=>[u(t,{label:"轮播图",prop:"bannerUrl"},{default:n((()=>[u(v,{modelValue:E.bannerUrl,"onUpdate:modelValue":a[0]||(a[0]=e=>E.bannerUrl=e)},null,8,["modelValue"])])),_:1}),u(t,{label:"跳转链接",prop:"targetUrl"},{default:n((()=>[u(l,{modelValue:E.targetUrl,"onUpdate:modelValue":a[1]||(a[1]=e=>E.targetUrl=e),placeholder:"请输入跳转链接"},null,8,["modelValue"])])),_:1}),u(t,{label:"展示状态",prop:"isShow"},{default:n((()=>[u(s,{modelValue:E.isShow,"onUpdate:modelValue":a[2]||(a[2]=e=>E.isShow=e)},{default:n((()=>[u(r,{label:1},{default:n((()=>a[4]||(a[4]=[d("展示")]))),_:1,__:[4]}),u(r,{label:0},{default:n((()=>a[5]||(a[5]=[d("隐藏")]))),_:1,__:[5]})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"])}}});export{V as B,w as U,y as _,S as a};
