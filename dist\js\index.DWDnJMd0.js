import{d as e,r as l,S as a,o as t,aQ as o,g as r,f as i,m as s,w as n,C as d,i as u,Z as m,$ as p,V as c,e as f,h as _,F as g,P as j,ak as v,Q as b,db as y,aO as h,az as w}from"./index.Dk5pbsTU.js";import{v as V}from"./el-loading.Dqi-qL7c.js";import{E as x}from"./el-drawer.Df_TzNjH.js";import"./el-overlay.DpVCS8zG.js";import{E as k}from"./el-switch.kQ5v4arH.js";import{_ as U}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{a as C,E}from"./el-col.Cfu8vZQ4.js";import{E as I}from"./el-card.DwLhVNHW.js";import T from"./index.Cywy93e7.js";import{a as z,E as R}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     *//* empty css               */import{_ as S}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as A,a as L}from"./el-form-item.Bw6Zyv_7.js";import{E as B}from"./el-button.CXI119n4.js";import{E as F}from"./el-date-picker.B6TshyBV.js";import{E as O}from"./el-input.DiGatoux.js";import{E as P,a as q}from"./el-select.CRWkm-it.js";import{R as D}from"./role.DI5RFLtW.js";import{_ as Z}from"./UserImport.vue_vue_type_script_setup_true_lang.DJ4JeD7q.js";/* empty css                       */import{E as Y}from"./index.L2DVy5yq.js";import{E as M}from"./index.BcMfjWDS.js";import"./index.C6NthMtN.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./validator.HGn2BZtD.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./use-form-item.DzRJVC1I.js";import"./use-form-common-props.CQPDkY7k.js";import"./el-radio.w2rep3_A.js";import"./el-pagination.C5FHY27u.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.ybpLT-bz.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./index.Vn8pbgQR.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./dayjs.min.D7B9fRnU.js";import"./arrays.CygeFE-H.js";import"./index.Cd8M2JyP.js";import"./el-alert.CImT_8mr.js";import"./el-dialog.Cnp8BitR.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./el-progress.BQBUwu9o.js";import"./cloneDeep.DcCMo0F4.js";import"./el-link.qHYW6llJ.js";const N={class:"app-container"},$={class:"search-bar"},K={class:"flex-x-between mb-10px"},Q={class:"dialog-footer"},G=e({name:"User",inheritAttrs:!1,__name:"index",setup(e){const G=l(),H=l(),J=a({pageNum:1,pageSize:10}),W=l(),X=l(0),ee=l(!1),le=a({visible:!1,title:"新增用户"}),ae=a({status:1}),te=a({username:[{required:!0,message:"用户名不能为空",trigger:"blur"}],nickname:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],roleIds:[{required:!0,message:"用户角色不能为空",trigger:"blur"}],email:[{pattern:/\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/,message:"请输入正确的邮箱地址",trigger:"blur"}],mobile:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}),oe=l([]),re=l(),ie=l(!1);function se(){ee.value=!0,null==J.createTime&&(J.createTime=void 0),h.getPage(J).then((e=>{W.value=e.list,X.value=e.total})).finally((()=>{ee.value=!1}))}function ne(e){let l="";"ascending"==e.order&&(l="ASC"),"descending"==e.order&&(l="DESC"),""==l?(J.direction=void 0,J.field=void 0):(J.direction=l,J.field=e.prop),se()}function de(){G.value.resetFields(),J.pageNum=1,J.deptId=void 0,J.createTime=void 0,se()}function ue(e){oe.value=e.map((e=>e.id))}async function me(e){le.visible=!0,re.value=await D.getOptions(),e?(le.title="修改用户",h.getFormData(e).then((e=>{Object.assign(ae,{...e})}))):le.title="新增用户"}function pe(){le.visible=!1,H.value.resetFields(),H.value.clearValidate(),ae.id=void 0,ae.status=1}const ce=y((()=>{H.value.validate((e=>{if(e){const e=ae.id;ee.value=!0,e?h.update(e,ae).then((()=>{w.success("修改用户成功"),pe(),de()})).finally((()=>ee.value=!1)):h.add(ae).then((()=>{w.success("新增用户成功"),pe(),de()})).finally((()=>ee.value=!1))}}))}),1e3);function fe(e){const l=[e||oe.value].join(",");l?M.confirm("确认删除用户?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){ee.value=!0,h.deleteByIds(l).then((()=>{w.success("删除成功"),de()})).finally((()=>ee.value=!1))}),(function(){w.info("已取消删除")})):w.warning("请勾选删除项")}function _e(){ie.value=!0}function ge(){h.export(J).then((e=>{const l=e.data,a=decodeURI(e.headers["content-disposition"].split(";")[1].split("=")[1]),t=new Blob([l],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"}),o=window.URL.createObjectURL(t),r=document.createElement("a");r.href=o,r.download=a,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(o)}))}return t((()=>{se()})),(e,l)=>{const a=O,t=L,y=q,D=P,je=F,ve=B,be=A,ye=R,he=S,we=Y,Ve=z,xe=T,ke=I,Ue=C,Ce=E,Ee=U,Ie=k,Te=x,ze=o("hasPerm"),Re=V;return i(),r("div",N,[s(Ce,{gutter:20},{default:n((()=>[s(Ue,{lg:24,xs:24},{default:n((()=>[d("div",$,[s(be,{ref_key:"queryFormRef",ref:G,model:u(J),inline:!0},{default:n((()=>[s(t,{label:"关键字",prop:"keywords"},{default:n((()=>[s(a,{modelValue:u(J).keywords,"onUpdate:modelValue":l[0]||(l[0]=e=>u(J).keywords=e),placeholder:"用户名/昵称/手机号",clearable:"",style:{width:"200px"},onKeyup:m(se,["enter"])},null,8,["modelValue"])])),_:1}),s(t,{label:"状态",prop:"status"},{default:n((()=>[s(D,{modelValue:u(J).status,"onUpdate:modelValue":l[1]||(l[1]=e=>u(J).status=e),placeholder:"全部",clearable:"",class:"!w-[100px]"},{default:n((()=>[s(y,{label:"正常",value:1}),s(y,{label:"禁用",value:0})])),_:1},8,["modelValue"])])),_:1}),s(t,{label:"创建时间"},{default:n((()=>[s(je,{modelValue:u(J).createTime,"onUpdate:modelValue":l[2]||(l[2]=e=>u(J).createTime=e),editable:!1,class:"!w-[240px]",type:"daterange","range-separator":"~","start-placeholder":"开始时间","end-placeholder":"截止时间","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),s(t,null,{default:n((()=>[s(ve,{type:"primary",icon:"search",onClick:se},{default:n((()=>l[18]||(l[18]=[p("搜索")]))),_:1,__:[18]}),s(ve,{icon:"refresh",onClick:de},{default:n((()=>l[19]||(l[19]=[p("重置")]))),_:1,__:[19]})])),_:1})])),_:1},8,["model"])]),s(ke,{shadow:"never"},{default:n((()=>[d("div",K,[d("div",null,[c((i(),f(ve,{type:"success",icon:"plus",onClick:l[3]||(l[3]=e=>me())},{default:n((()=>l[20]||(l[20]=[p(" 新增 ")]))),_:1,__:[20]})),[[ze,["sys:user:add"]]]),c((i(),f(ve,{type:"danger",icon:"delete",disabled:0===u(oe).length,onClick:l[4]||(l[4]=e=>fe())},{default:n((()=>l[21]||(l[21]=[p(" 删除 ")]))),_:1,__:[21]},8,["disabled"])),[[ze,"sys:user:delete"]])]),d("div",null,[c((i(),f(ve,{icon:"upload",onClick:_e},{default:n((()=>l[22]||(l[22]=[p(" 导入 ")]))),_:1,__:[22]})),[[ze,"sys:user:import"]]),c((i(),f(ve,{icon:"download",onClick:ge},{default:n((()=>l[23]||(l[23]=[p(" 导出 ")]))),_:1,__:[23]})),[[ze,"sys:user:export"]])])]),c((i(),f(Ve,{data:u(W),onSelectionChange:ue,onSortChange:ne},{default:n((()=>[s(ye,{type:"selection",width:"50",align:"center"}),s(ye,{label:"用户名",prop:"username"}),s(ye,{label:"昵称",width:"150",align:"center",prop:"nickname"}),s(ye,{label:"性别",width:"100",align:"center"},{default:n((e=>[s(he,{modelValue:e.row.gender,"onUpdate:modelValue":l=>e.row.gender=l,code:"gender"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(ye,{sortable:"custom",label:"手机号码",align:"center",prop:"mobile",width:"120"}),s(ye,{label:"邮箱",align:"center",prop:"email",width:"160"}),s(ye,{sortable:"custom",label:"状态",align:"center",prop:"status",width:"80"},{default:n((e=>[s(we,{type:1==e.row.status?"success":"info"},{default:n((()=>[p(g(1==e.row.status?"正常":"禁用"),1)])),_:2},1032,["type"])])),_:1}),s(ye,{sortable:"custom",label:"创建时间",align:"center",prop:"createdAt",width:"150"}),s(ye,{label:"操作",fixed:"right",width:"220"},{default:n((e=>[c((i(),f(ve,{type:"primary",icon:"RefreshLeft",size:"small",link:"",onClick:l=>{return a=e.row,void M.prompt("请输入用户【"+a.username+"】的新密码","重置密码",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((({value:e})=>{if(!e||e.length<6)return w.warning("密码至少需要6位字符，请重新输入"),!1;h.resetPassword(a.id,e).then((()=>{w.success("密码重置成功，新密码是："+e)}))}),(()=>{w.info("已取消重置密码")}));var a}},{default:n((()=>l[24]||(l[24]=[p(" 重置密码 ")]))),_:2,__:[24]},1032,["onClick"])),[[ze,"sys:user:password:reset"]]),e.row.self?_("",!0):(i(),r(j,{key:0},[c((i(),f(ve,{type:"primary",icon:"edit",link:"",size:"small",onClick:l=>me(e.row.id)},{default:n((()=>l[25]||(l[25]=[p(" 编辑 ")]))),_:2,__:[25]},1032,["onClick"])),[[ze,"sys:user:edit"]]),c((i(),f(ve,{type:"danger",icon:"delete",link:"",size:"small",onClick:l=>fe(e.row.id)},{default:n((()=>l[26]||(l[26]=[p(" 删除 ")]))),_:2,__:[26]},1032,["onClick"])),[[ze,"sys:user:delete"]])],64))])),_:1})])),_:1},8,["data"])),[[Re,u(ee)]]),u(X)>0?(i(),f(xe,{key:0,total:u(X),"onUpdate:total":l[5]||(l[5]=e=>v(X)?X.value=e:null),page:u(J).pageNum,"onUpdate:page":l[6]||(l[6]=e=>u(J).pageNum=e),limit:u(J).pageSize,"onUpdate:limit":l[7]||(l[7]=e=>u(J).pageSize=e),onPagination:se},null,8,["total","page","limit"])):_("",!0)])),_:1})])),_:1})])),_:1}),s(Te,{modelValue:u(le).visible,"onUpdate:modelValue":l[15]||(l[15]=e=>u(le).visible=e),title:u(le).title,"append-to-body":"",onClose:pe},{footer:n((()=>[d("div",Q,[s(ve,{type:"primary",onClick:u(ce)},{default:n((()=>l[27]||(l[27]=[p("确 定")]))),_:1,__:[27]},8,["onClick"]),s(ve,{onClick:pe},{default:n((()=>l[28]||(l[28]=[p("取 消")]))),_:1,__:[28]})])])),default:n((()=>[s(be,{ref_key:"userFormRef",ref:H,model:u(ae),rules:u(te),"label-width":"80px"},{default:n((()=>[s(t,{label:"用户名",prop:"username"},{default:n((()=>[s(a,{modelValue:u(ae).username,"onUpdate:modelValue":l[8]||(l[8]=e=>u(ae).username=e),readonly:!!u(ae).id,placeholder:"请输入用户名"},null,8,["modelValue","readonly"])])),_:1}),s(t,{label:"用户昵称",prop:"nickname"},{default:n((()=>[s(a,{modelValue:u(ae).nickname,"onUpdate:modelValue":l[9]||(l[9]=e=>u(ae).nickname=e),placeholder:"请输入用户昵称"},null,8,["modelValue"])])),_:1}),s(t,{label:"性别",prop:"gender"},{default:n((()=>[s(Ee,{modelValue:u(ae).gender,"onUpdate:modelValue":l[10]||(l[10]=e=>u(ae).gender=e),code:"gender"},null,8,["modelValue"])])),_:1}),s(t,{label:"角色",prop:"roleIds"},{default:n((()=>[s(D,{modelValue:u(ae).roleIds,"onUpdate:modelValue":l[11]||(l[11]=e=>u(ae).roleIds=e),multiple:"",placeholder:"请选择"},{default:n((()=>[(i(!0),r(j,null,b(u(re),(e=>(i(),f(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),s(t,{label:"手机号码",prop:"mobile"},{default:n((()=>[s(a,{modelValue:u(ae).mobile,"onUpdate:modelValue":l[12]||(l[12]=e=>u(ae).mobile=e),placeholder:"请输入手机号码",maxlength:"11"},null,8,["modelValue"])])),_:1}),s(t,{label:"邮箱",prop:"email"},{default:n((()=>[s(a,{modelValue:u(ae).email,"onUpdate:modelValue":l[13]||(l[13]=e=>u(ae).email=e),placeholder:"请输入邮箱",maxlength:"50"},null,8,["modelValue"])])),_:1}),s(t,{label:"状态",prop:"status"},{default:n((()=>[s(Ie,{modelValue:u(ae).status,"onUpdate:modelValue":l[14]||(l[14]=e=>u(ae).status=e),"inline-prompt":"","active-text":"正常","inactive-text":"禁用","active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),s(Z,{modelValue:u(ie),"onUpdate:modelValue":l[16]||(l[16]=e=>v(ie)?ie.value=e:null),onImportSuccess:l[17]||(l[17]=e=>se())},null,8,["modelValue"])])}}});export{G as default};
