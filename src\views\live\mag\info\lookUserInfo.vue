<script setup lang="ts">
import LiveSessionApi, {
  LiveWatchingRecordVo,
  LiveWatchingUserInfoVO,
} from "@/api/live/liveSession";
import CommentList from "./commentList.vue";

defineProps({
  sessionId: {
    type: String,
    default: "",
  },
});
const dialog = reactive({
  visible: false,
  title: "发言详情",
});
const info = ref<LiveWatchingRecordVo>({} as any);
const pointsInfo = ref<LiveWatchingUserInfoVO>({} as any);
defineExpose({
  open: (item: LiveWatchingRecordVo) => {
    LiveSessionApi.watchingUserInfo(item.userId).then((res) => {
      pointsInfo.value = res;
    });
    info.value = JSON.parse(JSON.stringify(item));
    dialog.visible = true;
  },
});
</script>

<template>
  <el-drawer v-model="dialog.visible" :title="dialog.title" size="80vw">
    <el-container style="height: 100%">
      <el-header style="padding: 0 0 10px 0; height: auto">
        <el-descriptions label-width="80" border :column="4">
          <el-descriptions-item label-width="0px">
            <div style="display: flex; align-items: center">
              <el-image
                style="width: 30px; height: 30px; border-radius: 30px"
                :preview-src-list="[info.avatar]"
                preview-teleported
                :src="info.avatar"
              />
              <span style="margin-left: 5px">{{ info.nickname }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="当前等级">
            {{ pointsInfo.currentLevel || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="本场累计时长">
            {{ info.durationStr }}
          </el-descriptions-item>
          <el-descriptions-item label="当前积分">
            {{ pointsInfo.currentPoints || "-" }}
          </el-descriptions-item>
          <el-descriptions-item :span="4">
            <el-tag
              v-for="(item, index) in info.tagList"
              :key="index"
              round
              effect="light"
              size="small"
              style="margin-right: 5px"
              type="primary"
            >
              {{ item }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-header>
      <el-main style="padding: 0; height: 100%">
        <comment-list
          v-if="dialog.visible"
          hideHead
          :user-id="String(info.userId)"
          :session-id="Number(sessionId)"
        />
      </el-main>
    </el-container>
  </el-drawer>
</template>

<style scoped lang="scss"></style>
