<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="800px">
    <el-table :data="formData || []" highlight-current-row max-height="500px" border>
      <el-table-column label="序号" align="center" width="55" type="index">
        <template #header>
          <el-button type="primary" size="small" circle icon="plus" @click="addNewData" />
        </template>
        <template #default="{ $index }">
          <el-button
            type="danger"
            v-if="formData.length > 1"
            size="small"
            circle
            icon="delete"
            @click="formData.splice($index, 1)"
          />
        </template>
      </el-table-column>
      <el-table-column label="等级名称" align="center" prop="levelName" min-width="100">
        <template #default="{ row }">
          <el-input v-model="row.levelName" />
        </template>
      </el-table-column>
      <el-table-column label="积分区间值" align="center" min-width="100">
        <template #default="{ row }">
          <div class="flex-center">
            <el-input
              v-model="row.startPoints"
              oninput="value=value.replace(/[^\d]/g,'')"
              placeholder="含"
            />
            <span class="m-l-1 m-r-1">至</span>
            <el-input
              v-model="row.endPoints"
              oninput="value=value.replace(/[^\d]/g,'')"
              placeholder="不含"
            />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import GradeApi, { GradeConfigLevelDto, GradeConfigVo } from "@/api/system/grade";

const dialog = reactive({
  visible: false,
  title: "",
});
const formData = ref<GradeConfigLevelDto[]>([]);
const emits = defineEmits(["success"]);
const loading = ref(false);

function addNewData() {
  const _info = new GradeConfigLevelDto();
  if (formData.value.length > 0) {
    _info.startPoints = formData.value[formData.value.length - 1].endPoints;
  }
  formData.value.push(_info);
}

function handleSubmit() {
  GradeApi.editConfig(formData.value)
    .then(() => {
      ElMessage.success("保存成功");
      dialog.visible = false;
      emits("success");
    })
    .finally(() => (loading.value = false));
}

defineExpose({
  open: (_row?: GradeConfigVo[]) => {
    dialog.visible = true;
    dialog.title = "编辑会员等级";
    formData.value = (_row || []).map((m) => new GradeConfigLevelDto(m));
  },
});
</script>

<style scoped lang="scss"></style>
