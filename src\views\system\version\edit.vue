<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px">
    <el-form ref="editFormRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="版本号" prop="versionName">
        <el-input
          v-model="formData.versionName"
          :maxlength="50"
          show-word-limit
          placeholder="请输入版本号"
        />
      </el-form-item>
      <!--      <el-form-item label="所属平台" prop="platformType">-->
      <!--        <dict v-model="formData.platformType" type="radio" code="version_platform_type" />-->
      <!--      </el-form-item>-->
      <el-form-item label="操作系统" prop="deviceType">
        <dict v-model="formData.deviceType" type="radio" code="version_device_type" />
      </el-form-item>
      <el-form-item label="是否热更新" prop="isHotUpdate">
        <el-switch v-model="formData.isHotUpdate" :active-value="1" :inactive-value="0" />
      </el-form-item>
      <el-form-item label="是否强制更新" prop="isForceUpdate">
        <el-switch v-model="formData.isForceUpdate" :active-value="1" :inactive-value="0" />
      </el-form-item>
      <el-form-item label="更新内容" prop="updateContent">
        <el-input v-model="formData.updateContent" type="textarea" :rows="4" />
      </el-form-item>
      <el-form-item v-if="dialog.visible" label="安装包" prop="downloadUrls">
        <FileUpload v-model="formData.downloadUrls" :maxFileSize="300" :limit="1" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { formValidator, isVersion } from "@/utils/validate";
import VersionApi, { AppVersionVO, SaveAppVersionDto } from "@/api/system/version";

const dialog = reactive({
  visible: false,
  title: "",
});
const formData = ref(new SaveAppVersionDto());
const editFormRef = ref();
const emits = defineEmits(["success"]);
const loading = ref(false);
const rules = {
  versionName: [
    {
      required: true,
      message: "请输入版本号",
      trigger: "blur",
    },
    formValidator(isVersion, "版本号错误"),
  ],
  deviceType: [{ required: true, message: "请选择客户端", trigger: "blur" }],
  updateContent: [{ required: true, message: "请输入更新内容", trigger: "blur" }],
  downloadUrls: [{ required: true, message: "请上传安装包", trigger: "blur" }],
};

function handleSubmit() {
  editFormRef.value.validate((valid: boolean) => {
    if (valid) {
      formData.value.downloadUrl = formData.value.downloadUrls[0];
      loading.value = true;
      VersionApi.save(formData.value)
        .then(() => {
          ElMessage.success("保存成功");
          dialog.visible = false;
          emits("success");
        })
        .finally(() => (loading.value = false));
    }
  });
}

defineExpose({
  open: (_row?: AppVersionVO) => {
    dialog.visible = true;
    dialog.title = _row?.id ? "编辑版本" : "新增版本";
    formData.value = new SaveAppVersionDto(_row);
  },
});
</script>

<style scoped lang="scss"></style>
