import{d as e,r as s,b0 as t,g as o,f as i,m as r,w as n,C as a,P as l,Q as p,n as d,F as c,i as m,E as u,e as f,D as _}from"./index.Dk5pbsTU.js";import{a as j,E as v}from"./el-tab-pane.CXnN_Izo.js";import{_ as g}from"./index.vue_vue_type_script_setup_true_lang.B2zcyRxI.js";import"./el-tooltip.l0sNRNKZ.js";import{E as x}from"./el-popper.Dbn4MgsT.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./error.D_Dr4eZ1.js";import"./strings.MqEQKtyI.js";import"./event.BwRzfsZt.js";import"./index.C_BbqFDa.js";import"./vnode.Cbclzz8S.js";import"./el-button.CXI119n4.js";import"./index.DuiNpp1i.js";import"./use-form-item.DzRJVC1I.js";import"./use-form-common-props.CQPDkY7k.js";import"./index.D6CER_Ot.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";const b={class:"icons-container"},h={class:"grid"},k={class:"icon-item"},I={class:"grid"},E={class:"icon-item"},$=y(e({name:"Icons",inheritAttrs:!1,__name:"icons",setup(e){const y=["api","cascader","client","close","close_all","close_left","close_other","close_right","dict","document","download","drag","edit","exit-fullscreen","eye-open","eye","fullscreen","github","homepage","language","link","menu","message","money","monitor","order","password","peoples","perm","publish","role","security","size","skill","system","tree","user","uv","verify-code"],$=s(t);function w(e){return`<div class="i-svg:${e}" />`}function P(e){return`<el-icon><${e} /></el-icon>`}return(e,s)=>{const t=x,U=g,z=j,A=u,C=v;return i(),o("div",b,[r(C,{type:"border-card"},{default:n((()=>[r(z,{label:"Icons"},{default:n((()=>[a("div",h,[(i(),o(l,null,p(y,(e=>a("div",{key:e},[r(U,{text:w(e)},{default:n((()=>[r(t,{effect:"dark",content:w(e),placement:"top"},{default:n((()=>[a("div",k,[a("div",{class:d(`i-svg:${e}`)},null,2),a("span",null,c(e),1)])])),_:2},1032,["content"])])),_:2},1032,["text"])]))),64))])])),_:1}),r(z,{label:"Element-UI Icons"},{default:n((()=>[a("div",I,[(i(!0),o(l,null,p(m($),((e,s)=>(i(),o("div",{key:s},[r(U,{text:P(s)},{default:n((()=>[r(t,{effect:"dark",content:P(s),placement:"top"},{default:n((()=>[a("div",E,[r(A,{size:20},{default:n((()=>[(i(),f(_(e)))])),_:2},1024),a("span",null,c(s),1)])])),_:2},1032,["content"])])),_:2},1032,["text"])])))),128))])])),_:1})])),_:1})])}}}),[["__scopeId","data-v-aadd67f9"]]);export{$ as default};
