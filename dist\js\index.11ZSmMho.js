var e=Object.defineProperty,t=(t,l,o)=>((t,l,o)=>l in t?e(t,l,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[l]=o)(t,"symbol"!=typeof l?l+"":l,o);import{aT as l,d as o,e as a,f as r,w as i,m as s,V as n,i as p,$ as d,az as u}from"./index.Dk5pbsTU.js";import{v as m}from"./el-loading.Dqi-qL7c.js";import{E as c,a as _,b as f}from"./el-main.CclDHmVj.js";import{E as j}from"./el-button.CXI119n4.js";import{E as g,a as y}from"./el-col.Cfu8vZQ4.js";import{E as v}from"./el-card.DwLhVNHW.js";import{a as h,E as b}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{E as V}from"./el-input.DiGatoux.js";import{_ as w}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";/* empty css               */import{_ as x}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{a as U}from"./commonSetup.Dm-aByKQ.js";import"./index.DuiNpp1i.js";import"./use-form-item.DzRJVC1I.js";import"./use-form-common-props.CQPDkY7k.js";import"./index.D6CER_Ot.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./_Uint8Array.n_j8oILW.js";import"./_initCloneObject.BN1anLuC.js";import"./_baseIteratee.CPRpgrLu.js";import"./isEqual.C0S6DIiJ.js";import"./debounce.DJJTSR8O.js";import"./index.wZTqlYZ6.js";import"./event.BwRzfsZt.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.DEKElSOG.js";import"./index.Vn8pbgQR.js";import"./el-radio.w2rep3_A.js";import"./el-select.CRWkm-it.js";import"./index.L2DVy5yq.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./scroll.CVc-P3_z.js";import"./vnode.Cbclzz8S.js";class E{constructor(e){t(this,"times",""),t(this,"ruleKey",""),t(this,"points",""),t(this,"type",""),e&&(this.times=String(e.times||""),this.ruleKey=e.ruleKey,this.points=String(e.points||"0"),this.type=String(e.type))}}const K={get:()=>l({url:"/tenant/live/config/rules",method:"get"}),save:e=>l({url:"/tenant/live/config/save/rules",method:"put",data:{list:e}})},S=o({name:"LiveBaseConfig",__name:"index",setup(e){const{sendInfo:t,onSend:l}=U(K.get);l();const{sendInfo:o,onSend:S}=U(K.save);function k(){var e,a;const r=(null==(e=t.data)?void 0:e.popularityRules)||[],i=(null==(a=t.data)?void 0:a.pointsRules)||[];r.find((e=>!e.points&&0!=e.points||Number(e.points)<0))?u.error("人气值不能为空且最小为0"):i.find((e=>!e.points&&0!=e.points||Number(e.points)<0))?u.error("积分值不能为空且最小为0"):(o.params=[...r.map((e=>new E(e))),...i.map((e=>new E(e)))],t.loading=!0,S().then((()=>{l()})).finally((()=>t.loading=!1)))}return(e,l)=>{const o=x,u=b,U=w,E=V,K=h,S=v,I=y,R=g,C=c,L=j,N=_,O=f,A=m;return r(),a(O,null,{default:i((()=>[s(C,{class:"p-0 m-0"},{default:i((()=>[s(R,{gutter:20},{default:i((()=>[s(I,{span:12,xs:24},{default:i((()=>[s(S,{shadow:"never",title:"直播间人气核算"},{default:i((()=>{var e;return[n((r(),a(K,{data:(null==(e=p(t).data)?void 0:e.popularityRules)||[],"highlight-current-row":"",border:""},{default:i((()=>[s(u,{label:"操作",align:"center",width:"100"},{default:i((({row:e})=>[s(o,{modelValue:e.ruleKey,"onUpdate:modelValue":t=>e.ruleKey=t,code:"live_rule_key"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(u,{label:"核算类型",align:"center",width:"130"},{default:i((({row:e})=>[s(U,{modelValue:e.type,"onUpdate:modelValue":t=>e.type=t,clearable:!1,style:{width:"100px"},code:"live_room_accounting_type"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(u,{label:"人气值",align:"center",prop:"levelName","min-width":"120"},{default:i((({row:e})=>[s(E,{modelValue:e.points,"onUpdate:modelValue":t=>e.points=t,oninput:"value=value.replace(/[^\\d]/g,'')"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["data"])),[[A,p(t).loading]])]})),_:1})])),_:1}),s(I,{span:12,xs:24},{default:i((()=>[s(S,{shadow:"never",title:"直播间人用户积分设置"},{default:i((()=>{var e;return[n((r(),a(K,{data:(null==(e=p(t).data)?void 0:e.pointsRules)||[],"highlight-current-row":"",border:""},{default:i((()=>[s(u,{label:"操作",align:"center",width:"100"},{default:i((({row:e})=>[s(o,{modelValue:e.ruleKey,"onUpdate:modelValue":t=>e.ruleKey=t,code:"live_rule_key"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(u,{label:"核算类型",align:"center",width:"130"},{default:i((({row:e})=>[s(U,{modelValue:e.type,"onUpdate:modelValue":t=>e.type=t,style:{width:"100px"},clearable:!1,code:"live_room_accounting_type"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(u,{label:"积分",align:"center",prop:"levelName","min-width":"120"},{default:i((({row:e})=>[s(E,{modelValue:e.points,"onUpdate:modelValue":t=>e.points=t,oninput:"value=value.replace(/[^\\d]/g,'')"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["data"])),[[A,p(t).loading]])]})),_:1})])),_:1})])),_:1})])),_:1}),s(N,{class:"text-align-center justify-center"},{default:i((()=>[s(L,{type:"primary",onClick:k},{default:i((()=>l[0]||(l[0]=[d("保 存")]))),_:1,__:[0]})])),_:1})])),_:1})}}});export{S as default};
