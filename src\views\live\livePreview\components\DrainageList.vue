<script setup lang="ts">
import livePreview, { PageInviterListQueryDto, PageInviterListVO } from "@/api/live/livePreview";
import IScrollList from "@/components/IScrollList/index.vue";
import UserList from "@/views/live/livePreview/components/UserList.vue";

const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
});
const drainageListQuery = ref(new PageInviterListQueryDto());
const selectDrainageInfo = ref<PageInviterListVO>({} as any);
const drainageListRef = ref<any>();
watch(
  () => props.info.id,
  (_newVal: number) => {
    if (_newVal) {
      drainageListQuery.value.sessionId = String(_newVal);
      drainageListQuery.value.pageNum = 1;
      drainageListRef.value?.reset();
    }
  },
  { deep: true }
);

function onDrainageListUpdate(e: PageInviterListVO[]) {
  if (!selectDrainageInfo.value.id && e.length) {
    selectDrainageInfo.value = e[0];
  }
  if (!e.length) {
    selectDrainageInfo.value = {} as any;
  }
}

function setSelectDrainageInfo(info: PageInviterListVO) {
  selectDrainageInfo.value = JSON.parse(JSON.stringify(info));
}
</script>

<template>
  <el-container class="drainage-list">
    <el-aside class="drainage-list-aside">
      <i-scroll-list
        ref="drainageListRef"
        v-model="drainageListQuery"
        style="
          --i-scroll-list-padding: 10px 10px 0 10px;
          --i-scroll-list-item-margin-bottom: 10px;
          --i-scroll-list-divider-bg: #ffffff;
        "
        :api="livePreview.inviterList as any"
        @update:list="onDrainageListUpdate"
      >
        <template #default="{ item, itemIndex }">
          <div
            class="drainage-list-item"
            :class="`drainage-list-item-${itemIndex + 1} ${(item as any).id === selectDrainageInfo.id ? 'drainage-list-select-item' : ''}`"
            @click="setSelectDrainageInfo(item as any)"
          >
            <div v-if="itemIndex > 2" class="drainage-list-item-index">{{ itemIndex + 1 }}</div>
            <img
              v-else-if="itemIndex === 0"
              class="drainage-list-item-icon"
              src="@/assets/icons/ph1.png"
            />
            <img
              v-else-if="itemIndex === 1"
              class="drainage-list-item-icon"
              src="@/assets/icons/ph2.png"
            />
            <img
              v-else-if="itemIndex === 2"
              class="drainage-list-item-icon"
              src="@/assets/icons/ph3.png"
            />
            <dict-label
              v-model="(item as any).inviterType"
              round
              tagSize="small"
              effect="dark"
              :add-value-list="[{ value: 0, label: '客户', tagType: 'primary' }]"
              code="userType"
            />
            <div class="drainage-list-item-name">{{ (item as any).inviterName }}</div>
          </div>
        </template>
      </i-scroll-list>
    </el-aside>
    <el-main class="drainage-list-main">
      <user-list v-if="selectDrainageInfo.id" :sessionId="info.id" :info="selectDrainageInfo" />
    </el-main>
  </el-container>
</template>

<style scoped lang="scss">
.drainage-list {
  height: 100%;
  width: 100%;
  border-top: 1px solid var(--el-border-color);

  &-item {
    display: flex;
    align-items: center;
    background: linear-gradient(270deg, #f7f7f7 0%, #ffffff 100%);
    border-radius: 6px;
    padding: 16px 14px;
    cursor: pointer;

    &-icon {
      width: 21px;
      height: 20px;
      margin-right: 14px;
    }

    &-index {
      width: 21px;
      height: 20px;
      text-align: center;
      line-height: 21px;
      font-weight: 400;
      font-size: 12px;
      color: #3d3d3d;
      margin-right: 14px;
    }

    &-name {
      font-weight: 400;
      font-size: 12px;
      color: #203044;
      margin-left: 6px;
      width: 0;
      flex: 1;
    }
  }

  .drainage-list-item-1 {
    background: linear-gradient(270deg, #ffffff 0%, #fff4e6 100%);
  }

  .drainage-list-item-2 {
    background: linear-gradient(270deg, #ffffff 0%, #ddeafc 100%);
  }

  .drainage-list-item-3 {
    background: linear-gradient(270deg, #ffffff 0%, #ffedef 100%);
  }

  .drainage-list-select-item {
    border: 1px solid var(--el-color-primary);
  }

  &-aside {
    width: 360px;
    height: 100%;
    padding: 0;
    background: #ffffff;
    border-right: 1px solid var(--el-border-color);
  }

  &-main {
    width: 100%;
    height: 100%;
    padding: 0;
  }
}
</style>
