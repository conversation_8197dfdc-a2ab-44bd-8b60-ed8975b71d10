<template>
  <div>
    <el-steps :active="activeStep" finish-status="success" simple>
      <el-step title="基本信息" />
      <el-step title="触发条件" />
      <el-step title="响应内容" />
      <el-step title="高级设置" />
    </el-steps>

    <div class="mt-6">
      <!-- 第一步：基本信息配置 -->
      <div v-if="activeStep === 0">
        <el-form ref="basicFormRef" :model="formData" :rules="basicRules" label-width="120px">
          <el-form-item label="规则名称" prop="ruleName">
            <el-input v-model="formData.ruleName" placeholder="请输入规则名称" />
          </el-form-item>
          <el-form-item label="规则描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入规则描述"
            />
          </el-form-item>
          <el-form-item label="优先级" prop="priority">
            <el-slider
              v-model="formData.priority"
              :min="1"
              :max="10"
              :step="1"
              show-input
              style="width: 300px"
            />
            <span class="text-sm text-gray-500 ml-2">数值越大优先级越高</span>
          </el-form-item>
        </el-form>
      </div>

      <!-- 第二步：触发条件配置 -->
      <div v-else-if="activeStep === 1">
        <el-form ref="conditionFormRef" :model="conditionsForm" label-width="120px">
          <div class="flex justify-center mb-3">
            <el-button type="primary" :icon="Plus" @click="addCondition">添加条件</el-button>
          </div>

          <div class="condition-list-container">
            <el-empty v-if="!formData.conditions?.length" description="暂无触发条件，请添加" />

            <template v-else>
              <div v-for="(condition, index) in formData.conditions" :key="index" class="mb-4">
                <el-card shadow="hover" class="condition-card condition-item">
                  <template #header>
                    <div class="flex justify-between items-center">
                      <span>条件 #{{ index + 1 }}</span>
                      <div>
                        <el-select
                          v-if="index > 0"
                          v-model="condition.logicalOperator"
                          style="width: 80px; margin-right: 10px"
                        >
                          <el-option label="并且" value="AND" />
                          <el-option label="或者" value="OR" />
                        </el-select>
                        <el-button
                          type="danger"
                          :icon="Delete"
                          circle
                          @click="removeCondition(index)"
                        />
                      </div>
                    </div>
                  </template>
                  <el-form-item :label="'条件类型'" :prop="`conditions[${index}].conditionTypeId`">
                    <el-select
                      v-model="condition.conditionTypeId"
                      placeholder="请选择条件类型"
                      style="width: 100%"
                      @change="() => initConditionConfig(index)"
                    >
                      <el-option
                        v-for="type in conditionTypes"
                        :key="type.id"
                        :label="type.description"
                        :value="type.id"
                      >
                        <div class="flex flex-col">
                          <span>{{ type.description }}</span>
                          <span class="text-xs text-gray-500">{{ type.description }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <!-- 条件配置（根据条件类型动态变化） -->
                  <el-form-item :label="'配置'" :prop="`conditions[${index}].conditionConfig`">
                    <div v-if="condition.conditionTypeId">
                      <div class="mb-2">
                        <div class="text-sm text-gray-500" style="padding:6px;">
                          {{
                            getConditionTypeById(condition.conditionTypeId)?.description ||
                            "请配置参数"
                          }}
                        </div>
                      </div>

                      <!-- 关键词匹配配置 -->
                      <div v-if="getConditionType(condition) === CONDITION_TYPE.KEYWORDS_MATCH">
                        <KeywordsMatchConfig
                          :config="condition.conditionConfig"
                          @update="(val) => updateTypedConditionConfig(index, val)"
                        />
                      </div>

                      <!-- 时间触发配置 -->
                      <div v-else-if="getConditionType(condition) === CONDITION_TYPE.TIME_TRIGGER">
                        <TimeTriggerConfig
                          :config="condition.conditionConfig"
                          @update="(val) => updateTypedConditionConfig(index, val)"
                        />
                      </div>

                      <!-- 指标阈值配置 -->
                      <div
                        v-else-if="getConditionType(condition) === CONDITION_TYPE.METRICS_THRESHOLD"
                      >
                        <MetricsThresholdConfig
                          :config="condition.conditionConfig"
                          @update="(val) => updateTypedConditionConfig(index, val)"
                        />
                      </div>

                      <!-- 置信度配置 -->
                      <div
                        v-else-if="
                          getConditionType(condition) === CONDITION_TYPE.CONFIDENCE_THRESHOLD
                        "
                      >
                        <ConfidenceThresholdConfig
                          :config="condition.conditionConfig"
                          @update="(val) => updateTypedConditionConfig(index, val)"
                        />
                      </div>

                      <!-- 未知类型或暂未支持的类型，使用JSON编辑 -->
                      <div v-else>
                        <el-alert
                          type="warning"
                          :closable="false"
                          title="当前条件类型暂无可视化配置界面，请使用JSON格式配置"
                          class="mb-2"
                        />
                        <el-input
                          v-model="conditionConfigJsons[index]"
                          type="textarea"
                          :rows="3"
                          placeholder="请输入JSON格式的条件配置"
                          @input="updateConditionConfig(index)"
                        />
                      </div>
                    </div>
                    <div v-else class="text-gray-500">请先选择条件类型</div>
                  </el-form-item>
                </el-card>
              </div>
            </template>
          </div>
        </el-form>
      </div>

      <!-- 第三步：响应内容配置 -->
      <div v-else-if="activeStep === 2">
        <el-form ref="responseFormRef" :model="formData" :rules="responseRules" label-width="120px">
          <el-form-item label="响应类型" prop="responseType">
            <el-radio-group v-model="formData.responseType">
              <el-radio value="static">静态回复</el-radio>
              <el-radio value="dynamic">动态生成</el-radio>
              <el-radio value="hybrid">混合模式</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="['static', 'hybrid'].includes(formData.responseType)"
            label="静态回复内容"
            prop="staticResponse"
          >
            <el-input
              v-model="formData.staticResponse"
              type="textarea"
              :rows="4"
              placeholder="请输入静态回复内容"
              @input="handleResponseChange"
            />
          </el-form-item>

          <el-form-item
            v-if="['dynamic', 'hybrid'].includes(formData.responseType)"
            label="动态生成提示词"
            prop="dynamicPrompt"
          >
            <el-input
              v-model="formData.dynamicPrompt"
              type="textarea"
              :rows="4"
              placeholder="请输入动态生成提示词"
              @input="handleResponseChange"
            />
            <div class="text-sm text-gray-500 mt-2">
              提示：可以使用 {{ 变量 }} 语法引用触发条件中的变量
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 第四步：高级设置 -->
      <div v-else-if="activeStep === 3">
        <el-form ref="advancedFormRef" :model="formData" :rules="advancedRules" label-width="120px">
          <el-form-item label="冷却时间(秒)" prop="cooldownSeconds">
            <el-input-number
              v-model="formData.cooldownSeconds"
              :min="0"
              :precision="0"
              style="width: 120px"
            />
            <div class="text-sm text-gray-500 mt-1">
              同一规则在冷却时间内只会触发一次，设置为0表示无冷却限制
            </div>
          </el-form-item>

          <el-form-item label="是否启用" prop="enabled">
            <el-radio-group v-model="formData.enabled">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="dialog-footer mt-4 flex justify-center">
      <el-button v-if="activeStep > 0" @click="prevStep">上一步</el-button>
      <el-button v-if="activeStep < 3" type="primary" @click="nextStep">下一步</el-button>
      <el-button v-else type="success" :loading="submitLoading" @click="handleSubmit">
        提交
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, defineAsyncComponent } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import type {
  AiVestTriggerRuleDto,
  ConditionConfigDto,
  AiVestConditionTypeVo,
} from "@/api/system/aiVestConfig";

// 导入配置组件
const KeywordsMatchConfig = defineAsyncComponent(() => import("./KeywordsMatchConfig.vue"));
const TimeTriggerConfig = defineAsyncComponent(() => import("./TimeTriggerConfig.vue"));
const MetricsThresholdConfig = defineAsyncComponent(() => import("./MetricsThresholdConfig.vue"));
const ConfidenceThresholdConfig = defineAsyncComponent(
  () => import("./ConfidenceThresholdConfig.vue")
);

// 条件配置类型常量
const CONDITION_TYPE = {
  KEYWORDS_MATCH: "keyword",
  TIME_TRIGGER: "timing",
  METRICS_THRESHOLD: "activity",
  CONFIDENCE_THRESHOLD: "llm_judge",
};

const props = defineProps({
  formData: {
    type: Object as () => AiVestTriggerRuleDto,
    required: true,
  },
  conditionTypes: {
    type: Array as () => AiVestConditionTypeVo[],
    default: () => [],
  },
  submitLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["submit", "update:formData"]);

// 步骤状态
const activeStep = ref(0);

// 表单引用
const basicFormRef = ref<FormInstance>();
const conditionFormRef = ref<FormInstance>();
const responseFormRef = ref<FormInstance>();
const advancedFormRef = ref<FormInstance>();

// 条件配置表单数据
const conditionsForm = reactive({
  conditions: [] as ConditionConfigDto[],
});

// 条件JSON编辑器
const conditionConfigJsons = ref<string[]>([]);

// 表单规则（分步表单）
const basicRules = reactive<FormRules>({
  ruleName: [{ required: true, message: "请输入规则名称", trigger: "blur" }],
  priority: [{ required: true, message: "请输入优先级", trigger: "blur" }],
});

const responseRules = reactive<FormRules>({
  responseType: [{ required: true, message: "请选择响应类型", trigger: "change" }],
  staticResponse: [
    {
      required: true,
      message: "请输入静态回复内容",
      trigger: "blur",
      validator: (rule, value, callback) => {
        const { responseType } = props.formData;
        if (["static", "hybrid"].includes(responseType) && !value) {
          callback(new Error("请输入静态回复内容"));
        } else {
          callback();
        }
      },
    },
  ],
  dynamicPrompt: [
    {
      required: true,
      message: "请输入动态生成提示词",
      trigger: "blur",
      validator: (rule, value, callback) => {
        const { responseType } = props.formData;
        if (["dynamic", "hybrid"].includes(responseType) && !value) {
          callback(new Error("请输入动态生成提示词"));
        } else {
          callback();
        }
      },
    },
  ],
});

const advancedRules = reactive<FormRules>({
  cooldownSeconds: [{ required: true, message: "请设置冷却时间", trigger: "blur" }],
  enabled: [{ required: true, message: "请选择是否启用", trigger: "change" }],
});

// 监听formData变化，初始化JSON编辑器
watch(
  () => props.formData,
  (newVal) => {
    if (newVal?.conditions) {
      conditionConfigJsons.value = newVal.conditions.map((condition) => {
        // 如果配置是字符串，尝试解析
        if (typeof condition.conditionConfig === "string") {
          try {
            condition.conditionConfig = JSON.parse(condition.conditionConfig);
          } catch (e) {
            console.error("解析条件配置失败", e);
          }
        }
        // 确保配置是对象
        condition.conditionConfig = condition.conditionConfig || {};
        return JSON.stringify(condition.conditionConfig, null, 2);
      });
    } else {
      conditionConfigJsons.value = [];
    }
  },
  { deep: true, immediate: true }
);

// 页面挂载时初始化
onMounted(() => {
  // 如果是编辑模式，确保每个条件类型配置正确加载
  if (props.formData.id && props.formData.conditions?.length) {
    // 延迟执行，确保类型数据加载完成
    setTimeout(() => {
      const conditions = props.formData.conditions || [];
      conditions.forEach((condition, index) => {
        // 如果条件配置为空，初始化默认配置
        if (!condition.conditionConfig || Object.keys(condition.conditionConfig).length === 0) {
          initConditionConfig(index);
        }
      });
    }, 200);
  }
});

// 根据ID获取条件类型
const getConditionTypeById = (typeId: number): AiVestConditionTypeVo | undefined => {
  return props.conditionTypes.find((type) => type.id === typeId);
};

// 获取条件类型标识符
const getConditionType = (condition: ConditionConfigDto): string => {
  // 基于条件类型ID获取条件类型
  const conditionType = getConditionTypeById(condition.conditionTypeId);
  if (!conditionType) return "";

  // 直接返回typeName
  return conditionType.typeName;
};

// 添加条件
const addCondition = () => {
  console.log("添加条件 - 开始", JSON.stringify(props.formData.conditions));

  // 默认使用第一个条件类型，如果没有可用的条件类型，则使用ID为1的类型
  const defaultTypeId = props.conditionTypes.length > 0 ? props.conditionTypes[0].id : 1;

  // 创建新条件对象
  const newCondition: ConditionConfigDto = {
    conditionTypeId: defaultTypeId,
    conditionConfig: {},
    logicalOperator:
      props.formData.conditions && props.formData.conditions.length > 0 ? "AND" : undefined,
  };

  // 确保条件数组已初始化
  if (!props.formData.conditions) {
    // 直接创建新数组
    const newFormData = {
      ...props.formData,
      conditions: [newCondition],
    };
    emit("update:formData", newFormData);
  } else {
    // 直接修改现有数组
    props.formData.conditions.push(newCondition);
    // 触发更新
    emit("update:formData", { ...props.formData });
  }

  // 添加JSON编辑器的条目
  const newIndex = props.formData.conditions ? props.formData.conditions.length - 1 : 0;
  conditionConfigJsons.value.push(JSON.stringify({}, null, 2));

  // 异步初始化配置，确保在条件类型加载完成后
  setTimeout(() => {
    initConditionConfig(newIndex);
  }, 0);

  console.log("添加条件 - 完成", JSON.stringify(props.formData.conditions));
};

// 移除条件
const removeCondition = (index: number) => {
  console.log("删除条件 - 开始", JSON.stringify(props.formData.conditions));

  if (!props.formData.conditions) return;

  // 直接从数组中移除
  props.formData.conditions.splice(index, 1);

  // 触发更新
  emit("update:formData", { ...props.formData });

  // 同步移除JSON编辑器的条目
  conditionConfigJsons.value.splice(index, 1);

  console.log("删除条件 - 完成", JSON.stringify(props.formData.conditions));
};

// 更新条件配置
const updateConditionConfig = (index: number) => {
  try {
    if (!props.formData.conditions || !props.formData.conditions[index]) return;

    const parsedConfig = JSON.parse(conditionConfigJsons.value[index]);

    // 直接修改条件配置
    props.formData.conditions[index].conditionConfig = parsedConfig;

    // 触发更新
    emit("update:formData", { ...props.formData });

    // 获取条件类型的schema配置
    const conditionType = getConditionTypeById(props.formData.conditions[index].conditionTypeId);
    if (conditionType?.configSchema) {
      // 这里可以添加schema验证逻辑
      console.log("配置应符合schema:", conditionType.configSchema);
    }
  } catch (error) {
    console.error("JSON格式错误", error);
    ElMessage.warning("JSON格式错误，请检查输入");
  }
};

// 更新类型化条件配置
const updateTypedConditionConfig = (index: number, newConfig: any) => {
  if (!props.formData.conditions || !props.formData.conditions[index]) return;

  // 直接修改条件配置
  props.formData.conditions[index].conditionConfig = newConfig;

  // 触发更新
  emit("update:formData", { ...props.formData });

  // 同步更新JSON编辑器内容
  conditionConfigJsons.value[index] = JSON.stringify(newConfig, null, 2);
};

// 处理响应内容变更
const handleResponseChange = () => {
  console.log(
    "响应内容变更",
    props.formData.responseType,
    props.formData.staticResponse,
    props.formData.dynamicPrompt
  );
  // 触发更新
  emit("update:formData", { ...props.formData });
};

// 初始化条件配置
const initConditionConfig = (index: number) => {
  if (!props.formData.conditions || !props.formData.conditions[index]) return;

  const conditionTypeId = props.formData.conditions[index].conditionTypeId;
  const conditionType = getConditionTypeById(conditionTypeId);

  if (!conditionType) {
    // 直接修改条件配置
    props.formData.conditions[index].conditionConfig = {};

    // 触发更新
    emit("update:formData", { ...props.formData });

    conditionConfigJsons.value[index] = JSON.stringify({}, null, 2);
    return;
  }

  // 获取条件类型
  const condition = props.formData.conditions[index];
  const type = getConditionType(condition);
  let templateConfig: Record<string, any> = {};

  // 根据不同类型设置默认配置
  switch (type) {
    case CONDITION_TYPE.KEYWORDS_MATCH:
      templateConfig = { keywords: [], match_mode: "partial" };
      break;
    case CONDITION_TYPE.TIME_TRIGGER:
      templateConfig = {
        delay_time: 0,
      };
      break;
    case CONDITION_TYPE.METRICS_THRESHOLD:
      templateConfig = { metric: "message_count", operator: ">", threshold: 10, time_window: 60 };
      break;
    case CONDITION_TYPE.CONFIDENCE_THRESHOLD:
      templateConfig = { min_confidence: 0.7, prompt_template: "" };
      break;
    default:
      // 如果不是已知类型，尝试从schema生成空白模板
      if (conditionType.configSchema?.properties) {
        const props = conditionType.configSchema.properties;
        Object.keys(props).forEach((key) => {
          // 根据属性类型设置默认值
          const propSchema = props[key] as any;
          if (propSchema.type === "array") {
            templateConfig[key] = [];
          } else if (propSchema.type === "number" || propSchema.type === "integer") {
            templateConfig[key] = 0;
          } else if (propSchema.type === "string") {
            templateConfig[key] = "";
          } else if (propSchema.type === "boolean") {
            templateConfig[key] = false;
          } else if (propSchema.type === "object") {
            templateConfig[key] = {};
          }

          // 如果有枚举值，使用第一个作为默认值
          if (propSchema.enum && propSchema.enum.length) {
            templateConfig[key] = propSchema.enum[0];
          }
        });
      }
  }

  // 直接修改条件配置
  props.formData.conditions[index].conditionConfig = templateConfig;

  // 触发更新
  emit("update:formData", { ...props.formData });

  conditionConfigJsons.value[index] = JSON.stringify(templateConfig, null, 2);
};

// 下一步
const nextStep = async () => {
  // 根据当前步骤验证表单
  try {
    if (activeStep.value === 0) {
      // 验证基本信息
      await basicFormRef.value?.validate();
    } else if (activeStep.value === 1) {
      // 条件配置可以为空
    } else if (activeStep.value === 2) {
      // 验证响应内容
      await responseFormRef.value?.validate();
    }
    // 前进到下一步
    activeStep.value++;
  } catch (error) {
    // 表单验证失败，不进行下一步
    console.error("表单验证失败", error);
  }
};

// 上一步
const prevStep = () => {
  activeStep.value--;
};

// 提交表单
const handleSubmit = async () => {
  // 验证最后一步表单
  try {
    await advancedFormRef.value?.validate();
    emit("submit", props.formData);
  } catch (error) {
    console.error("提交失败", error);
  }
};

// 初始化
const resetSteps = () => {
  activeStep.value = 0;
};

// 暴露方法给父组件
defineExpose({
  resetSteps,
});
</script>

<style scoped>
.condition-card {
  margin-bottom: 16px;
  border: 1px solid #ebeef5;
}

.dialog-footer {
  gap: 12px;
}

:deep(.el-form-item__content) {
  justify-content: flex-start;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  background-color: #f8f9fa;
}

:deep(.el-card__body) {
  padding: 16px;
}

.condition-list-container {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 8px;
}

.condition-item {
  transition: all 0.3s;
}

.condition-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
