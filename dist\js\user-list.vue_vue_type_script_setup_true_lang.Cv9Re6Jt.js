import{d as e,S as a,e as l,f as t,w as o,h as r,i,m as s,Z as n,C as p,$ as d,V as u,F as m}from"./index.Dk5pbsTU.js";import{v as g}from"./el-loading.Dqi-qL7c.js";import{E as y}from"./el-drawer.Df_TzNjH.js";import"./el-overlay.DpVCS8zG.js";import{b as c,d as f,E as h,a as w}from"./el-main.CclDHmVj.js";import j from"./index.Cywy93e7.js";import{a as b,E as _}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     *//* empty css               */import{E as q,a as V}from"./el-form-item.Bw6Zyv_7.js";import{E as v}from"./el-button.CXI119n4.js";import{E as x}from"./el-input.DiGatoux.js";import{u as P}from"./commonSetup.Dm-aByKQ.js";import{b as k,T as U}from"./tags.B175GojK.js";const E={style:{display:"flex","align-items":"center"}},L=e({__name:"user-list",setup(e,{expose:L}){const N=a({show:!1,title:"同标签用户"}),{page:R,getPage:S}=P(new k,U.userPage,!1);return L({open:e=>{R.query=new k,R.query.tagId=e,R.query.pageNum=1,N.show=!0,S()}}),(e,a)=>{const P=x,k=V,U=v,L=q,z=f,C=_,K=b,F=h,I=j,J=w,G=c,H=y,M=g;return t(),l(H,{modelValue:i(N).show,"onUpdate:modelValue":a[6]||(a[6]=e=>i(N).show=e),size:"80vw",title:i(N).title},{default:o((()=>[i(N).show?(t(),l(G,{key:0,style:{height:"100%"}},{default:o((()=>[s(z,{style:{padding:"0"}},{default:o((()=>[s(L,{ref:"queryFormRef",model:i(R).query,inline:!0},{default:o((()=>[s(k,{prop:"name","label-width":"0"},{default:o((()=>[s(P,{modelValue:i(R).query.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>i(R).query.keyword=e),placeholder:"客户昵称检索",clearable:"",onKeyup:n(i(S),["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),s(k,{prop:"status",label:"积分值检索"},{default:o((()=>[p("div",E,[s(P,{modelValue:i(R).query.startPoints,"onUpdate:modelValue":a[1]||(a[1]=e=>i(R).query.startPoints=e),placeholder:"开始积分",clearable:"",oninput:"value=value.replace(/[^\\d]/g,'')",style:{width:"120px"}},null,8,["modelValue"]),a[7]||(a[7]=p("span",{style:{margin:"0 8px"}},"~",-1)),s(P,{modelValue:i(R).query.endPoints,"onUpdate:modelValue":a[2]||(a[2]=e=>i(R).query.endPoints=e),placeholder:"截止积分",clearable:"",oninput:"value=value.replace(/[^\\d]/g,'')",style:{width:"120px"}},null,8,["modelValue"])])])),_:1}),s(k,null,{default:o((()=>[s(U,{type:"primary",icon:"search",onClick:i(S)},{default:o((()=>a[8]||(a[8]=[d("搜索")]))),_:1,__:[8]},8,["onClick"])])),_:1})])),_:1},8,["model"])])),_:1}),s(F,{style:{padding:"0",height:"100%"}},{default:o((()=>[u((t(),l(K,{height:"100%",data:i(R).data.records,border:""},{default:o((()=>[s(C,{label:"序号",align:"center",width:"55",type:"index"}),s(C,{label:"客户",align:"center",prop:"nickName",width:"120"}),s(C,{label:"当前积分",align:"center",prop:"currentPoints",width:"120"}),s(C,{label:"当前用户等级",align:"center",prop:"currentLevel",width:"120"}),s(C,{label:"标签","show-overflow-tooltip":"",align:"center","min-width":"100"},{default:o((({row:e})=>[d(m(e.tagList.length?e.tagList.join("、"):"-"),1)])),_:1})])),_:1},8,["data"])),[[M,i(R).loading]])])),_:1}),s(J,null,{default:o((()=>[i(R).data.totalRow?(t(),l(I,{key:0,total:i(R).data.totalRow,"onUpdate:total":a[3]||(a[3]=e=>i(R).data.totalRow=e),page:i(R).query.pageNum,"onUpdate:page":a[4]||(a[4]=e=>i(R).query.pageNum=e),limit:i(R).query.pageSize,"onUpdate:limit":a[5]||(a[5]=e=>i(R).query.pageSize=e),onPagination:i(S)},null,8,["total","page","limit","onPagination"])):r("",!0)])),_:1})])),_:1})):r("",!0)])),_:1},8,["modelValue","title"])}}});export{L as _};
