const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/index.Cywy93e7.js","js/index.Dk5pbsTU.js","css/index.DZ2Q3hhD.css","js/el-pagination.C5FHY27u.js","js/el-select.CRWkm-it.js","js/el-popper.Dbn4MgsT.js","js/index.C9UdVphc.js","js/_arrayPush.DSBJLlac.js","js/index.C6NthMtN.js","js/index.D6CER_Ot.js","js/isUndefined.DgmxjSXK.js","js/use-form-common-props.CQPDkY7k.js","css/el-popper.DG5wR-qi.css","js/index.ybpLT-bz.js","js/error.D_Dr4eZ1.js","js/index.L2DVy5yq.js","js/token.DWNpOE8r.js","js/strings.MqEQKtyI.js","js/castArray.C4RhTg2c.js","js/isEqual.C0S6DIiJ.js","js/_Uint8Array.n_j8oILW.js","js/index.Vn8pbgQR.js","js/use-form-item.DzRJVC1I.js","js/event.BwRzfsZt.js","js/scroll.CVc-P3_z.js","js/debounce.DJJTSR8O.js","js/_baseIteratee.CPRpgrLu.js","js/index.wZTqlYZ6.js","js/vnode.Cbclzz8S.js","css/el-select.CvzM3W2w.css","js/el-input.DiGatoux.js","js/index.DEKElSOG.js","css/el-input.Cz--kClu.css","js/index.DuiNpp1i.js","css/el-pagination.BNQcHhjS.css","js/_plugin-vue_export-helper.BCo6x5W8.js","css/index.BjmXN5ap.css","css/el-scrollbar.BWxh-h6K.css","css/el-tag.DljBBxJR.css","js/RuleForm.BGLnm7IK.js","js/el-input-number.C02ig7uT.js","js/index.Cd8M2JyP.js","css/el-input-number.DUUPPWGj.css","js/el-radio.w2rep3_A.js","css/el-radio.wSVBx8Lp.css","js/el-card.DwLhVNHW.js","css/el-card.fwQOLwdi.css","js/el-alert.CImT_8mr.js","css/el-alert.G57rL0jl.css","js/el-empty.Dee0wMKK.js","css/el-empty.D4ZqTl4F.css","js/el-button.CXI119n4.js","css/el-button.DhOMi8yW.css","js/el-form-item.Bw6Zyv_7.js","js/_initCloneObject.BN1anLuC.js","css/el-form-item.BqrJjMte.css","js/el-slider.DLapdkRs.js","css/el-slider.DtISwLyR.css","js/el-tooltip.l0sNRNKZ.js","js/el-step.6FNGwSz5.js","js/index.C_BbqFDa.js","css/el-step.D4QfGdWr.css","css/RuleForm.DmknOlra.css"])))=>i.map(i=>d[i]);
import{d as e,S as a,r as l,aQ as o,g as i,f as t,C as s,m as r,w as n,Z as d,i as p,d8 as m,$ as c,d9 as u,V as f,e as y,h as v,c1 as b,d6 as j,da as _,aR as h,d7 as g,az as w,cX as x}from"./index.Dk5pbsTU.js";import{v as V}from"./el-loading.Dqi-qL7c.js";import{E as k}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as N}from"./el-card.DwLhVNHW.js";import{a as O,E as U}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{E as C}from"./el-switch.kQ5v4arH.js";/* empty css               */import{a as E,E as T}from"./el-form-item.Bw6Zyv_7.js";import{E as S}from"./el-button.CXI119n4.js";import{E as R,a as I}from"./el-select.CRWkm-it.js";import{E as P}from"./el-input.DiGatoux.js";import{e as A,f as z,h as J,i as D,j as F,k as L}from"./aiVestConfig.CW5InkHZ.js";import{E as q}from"./index.BcMfjWDS.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./index.ybpLT-bz.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./_Uint8Array.n_j8oILW.js";import"./_initCloneObject.BN1anLuC.js";import"./_baseIteratee.CPRpgrLu.js";import"./isEqual.C0S6DIiJ.js";import"./debounce.DJJTSR8O.js";import"./index.wZTqlYZ6.js";import"./use-form-common-props.CQPDkY7k.js";import"./use-form-item.DzRJVC1I.js";import"./validator.HGn2BZtD.js";import"./castArray.C4RhTg2c.js";import"./index.L2DVy5yq.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./index.Vn8pbgQR.js";import"./index.DEKElSOG.js";const K={class:"app-container"},$={class:"search-bar"},B={class:"mb-10px"},H=e({__name:"index",setup(e){const H=g((()=>x((()=>import("./index.Cywy93e7.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38])))),Q=g((()=>x((()=>import("./RuleForm.BGLnm7IK.js")),__vite__mapDeps([39,1,2,30,6,7,23,31,22,11,9,21,14,32,40,41,42,43,33,44,45,46,47,48,4,5,8,10,12,13,15,16,17,18,19,20,24,25,26,27,28,29,49,50,51,52,53,54,55,56,57,58,59,60,61,35,62,38,37])))),Z=a({ruleName:"",description:"",responseType:"",priority:"",enabled:"",pageNum:1,pageSize:20}),G=l(!1),M=l([]),W=l(0),X=l(!1),Y=l(null),ee=l(!1),ae=a({visible:!1,title:"添加AI马甲触发规则"}),le=l(),oe=l(),ie=l(),te={ruleName:"",description:"",responseType:"static",staticResponse:"",dynamicPrompt:"",priority:1,cooldownSeconds:60,enabled:1,conditions:[]},se=a({...te}),re=l([]),ne=()=>{G.value=!0,z(Z).then((e=>{const{list:a,total:l}=e;M.value=a,W.value=l})).finally((()=>{G.value=!1}))},de=()=>{var e;null==(e=oe.value)||e.resetFields(),Z.pageNum=1,ne()},pe=async e=>{var a,l;if(ae.visible=!0,null==(a=le.value)||a.resetSteps(),e){ae.title="编辑AI马甲触发规则",G.value=!0;try{const a=await J(e.id);Object.keys(se).forEach((e=>{delete se[e]}));const o={id:a.id,ruleName:a.ruleName,description:a.description,responseType:a.responseType,staticResponse:a.staticResponse||"",dynamicPrompt:a.dynamicPrompt||"",priority:a.priority,cooldownSeconds:a.cooldownSeconds,enabled:a.enabled,conditions:(null==(l=a.conditions)?void 0:l.map((e=>({conditionTypeId:e.conditionTypeId,conditionConfig:e.conditionConfig,logicalOperator:e.logicalOperator}))))||[]};Object.assign(se,JSON.parse(JSON.stringify(o)))}catch(o){w.error("获取规则详情失败"),ae.visible=!1}finally{G.value=!1}}else ae.title="添加AI马甲触发规则",Object.keys(se).forEach((e=>{delete se[e]})),Object.assign(se,JSON.parse(JSON.stringify(te)))},me=()=>{ae.visible=!1},ce=async e=>{ee.value=!0;try{await D(e),w.success(e.id?"修改成功":"添加成功"),ae.visible=!1,ne()}catch(a){}finally{ee.value=!1}};return(async()=>{try{const e=await A();re.value=e||[]}catch(e){w.error("获取条件类型列表失败")}})(),ne(),(e,a)=>{const l=P,g=E,x=I,A=R,z=S,J=T,D=U,te=C,ue=O,fe=N,ye=k,ve=o("hasPerm"),be=V;return t(),i("div",K,[s("div",$,[r(J,{ref_key:"queryFormRef",ref:oe,model:Z,inline:!0},{default:n((()=>[r(g,{prop:"ruleName",label:"规则名称"},{default:n((()=>[r(l,{modelValue:Z.ruleName,"onUpdate:modelValue":a[0]||(a[0]=e=>Z.ruleName=e),placeholder:"请输入规则名称",clearable:"",style:{width:"200px"},onKeyup:d(ne,["enter"])},null,8,["modelValue"])])),_:1}),r(g,{prop:"description",label:"规则描述"},{default:n((()=>[r(l,{modelValue:Z.description,"onUpdate:modelValue":a[1]||(a[1]=e=>Z.description=e),placeholder:"请输入规则描述",clearable:"",style:{width:"200px"},onKeyup:d(ne,["enter"])},null,8,["modelValue"])])),_:1}),r(g,{prop:"responseType",label:"响应类型"},{default:n((()=>[r(A,{modelValue:Z.responseType,"onUpdate:modelValue":a[2]||(a[2]=e=>Z.responseType=e),placeholder:"请选择",clearable:"",style:{width:"150px"}},{default:n((()=>[r(x,{label:"静态回复",value:"static"}),r(x,{label:"动态生成",value:"dynamic"})])),_:1},8,["modelValue"])])),_:1}),r(g,{prop:"enabled",label:"状态"},{default:n((()=>[r(A,{modelValue:Z.enabled,"onUpdate:modelValue":a[3]||(a[3]=e=>Z.enabled=e),placeholder:"请选择",clearable:"",style:{width:"120px"}},{default:n((()=>[r(x,{label:"启用",value:"1"}),r(x,{label:"禁用",value:"0"})])),_:1},8,["modelValue"])])),_:1}),r(g,null,{default:n((()=>[r(z,{type:"primary",icon:p(m),onClick:ne},{default:n((()=>a[10]||(a[10]=[c("搜索")]))),_:1,__:[10]},8,["icon"]),r(z,{icon:p(u),onClick:de},{default:n((()=>a[11]||(a[11]=[c("重置")]))),_:1,__:[11]},8,["icon"])])),_:1})])),_:1},8,["model"])]),r(fe,{shadow:"never"},{default:n((()=>[s("div",B,[f((t(),y(z,{type:"success",icon:p(b),onClick:a[4]||(a[4]=e=>pe())},{default:n((()=>a[12]||(a[12]=[c(" 添加新规则 ")]))),_:1,__:[12]},8,["icon"])),[[ve,["sys:aimask:config:saveOrUpdate"]]])]),f((t(),y(ue,{ref_key:"dataTableRef",ref:ie,data:M.value,"highlight-current-row":"",border:""},{default:n((()=>[r(D,{type:"selection",width:"55",align:"center"}),r(D,{label:"规则名称",prop:"ruleName","min-width":"120","show-overflow-tooltip":""}),r(D,{label:"规则描述",prop:"description","min-width":"180","show-overflow-tooltip":""}),r(D,{label:"响应类型",prop:"responseTypeName","min-width":"100"}),r(D,{label:"优先级",prop:"priority","min-width":"80",align:"center"}),r(D,{label:"冷却时间(秒)",prop:"cooldownSeconds","min-width":"100",align:"center"}),r(D,{label:"状态",prop:"enabled","min-width":"80",align:"center"},{default:n((e=>[r(te,{modelValue:e.row.enabled,"onUpdate:modelValue":a=>e.row.enabled=a,disabled:!p(j)(["sys:aimask:config:saveOrUpdate"])||X.value,loading:X.value&&e.row.id===Y.value,"active-value":1,"inactive-value":0,onChange:a=>(async(e,a)=>{X.value=!0,Y.value=e.id;try{await L(e.id,a),w.success("状态更新成功")}catch(l){e.enabled=1===a?0:1}finally{X.value=!1,Y.value=null}})(e.row,a)},null,8,["modelValue","onUpdate:modelValue","disabled","loading","onChange"])])),_:1}),r(D,{label:"创建时间",prop:"createdAt","min-width":"160","show-overflow-tooltip":""}),r(D,{fixed:"right",label:"操作",width:"180"},{default:n((e=>[f((t(),y(z,{type:"primary",size:"small",link:"",icon:p(_),onClick:a=>pe(e.row)},{default:n((()=>a[13]||(a[13]=[c(" 编辑 ")]))),_:2,__:[13]},1032,["icon","onClick"])),[[ve,["sys:aimask:config:saveOrUpdate"]]]),f((t(),y(z,{type:"danger",size:"small",link:"",icon:p(h),onClick:a=>{return l=e.row,void q.confirm(`确认删除规则"${l.ruleName}"吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{G.value=!0;try{await F(l.id),w.success("删除成功"),ne()}catch(e){}finally{G.value=!1}}));var l}},{default:n((()=>a[14]||(a[14]=[c(" 删除 ")]))),_:2,__:[14]},1032,["icon","onClick"])),[[ve,["sys:aimask:config:delete"]]])])),_:1})])),_:1},8,["data"])),[[be,G.value]]),W.value>0?(t(),y(p(H),{key:0,total:W.value,"onUpdate:total":a[5]||(a[5]=e=>W.value=e),page:Z.pageNum,"onUpdate:page":a[6]||(a[6]=e=>Z.pageNum=e),limit:Z.pageSize,"onUpdate:limit":a[7]||(a[7]=e=>Z.pageSize=e),onPagination:ne},null,8,["total","page","limit"])):v("",!0)])),_:1}),r(ye,{modelValue:ae.visible,"onUpdate:modelValue":a[9]||(a[9]=e=>ae.visible=e),title:ae.title,width:"700px",top:"5vh",onClose:me},{default:n((()=>[r(p(Q),{ref_key:"ruleFormRef",ref:le,formData:se,"onUpdate:formData":a[8]||(a[8]=e=>se=e),"condition-types":re.value,"submit-loading":ee.value,onSubmit:ce},null,8,["formData","condition-types","submit-loading"])])),_:1},8,["modelValue","title"])])}}});export{H as default};
