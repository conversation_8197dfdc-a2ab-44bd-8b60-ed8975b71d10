import{d as e,S as l,r as s,ap as a,e as r,f as t,w as o,C as i,g as n,h as d,m as c,$ as m,F as p,i as u,E as f,az as v}from"./index.Dk5pbsTU.js";import{E as y}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as _}from"./el-button.CXI119n4.js";import{E as b}from"./el-image-viewer.BH897zgF.js";import{E as h,a as g}from"./el-descriptions-item.BlvmJIy_.js";import{u as x}from"./index.CcWFbuMM.js";import{a as j}from"./liveSession.RdnykuzD.js";const C={class:"p-2",style:{border:"1px solid var(--el-border-color)"}},U={class:"flex",style:{"align-items":"center"}},k={class:"p-2 m-t-2",style:{border:"1px solid var(--el-border-color)"}},w={key:0,class:"p-2 m-t-2",style:{border:"1px solid var(--el-border-color)"}},E={class:"flex",style:{"align-items":"center"}},S=e({__name:"sharedUrl",setup(e,{expose:S}){const{toClipboard:P}=x(),V=l({visible:!1,title:""}),H=s({}),J=s({});function K(){const e=document.createElement("a");e.href=J.value.miniProgramUrl,e.download="小程序码.png",e.style.display="none",document.body.appendChild(e),e.click(),e.remove()}function N(e){P(e).then((()=>{v.success("复制成功")})).catch((()=>{v.error("复制失败")}))}return S({open:e=>{H.value=JSON.parse(JSON.stringify(e)),j.sharedUrl(H.value.id).then((e=>{J.value=e})),V.visible=!0,V.title="直播间信息分享"}}),(e,l)=>{const s=g,v=a("DocumentCopy"),x=f,j=h,S=b,P=_,O=y;return t(),r(O,{modelValue:u(V).visible,"onUpdate:modelValue":l[3]||(l[3]=e=>u(V).visible=e),title:u(V).title,width:"500px"},{default:o((()=>[i("div",C,[c(j,{title:"直播间H5链接",column:2},{default:o((()=>[c(s,{label:"直播名称",span:2},{default:o((()=>[m(p(u(H).title),1)])),_:1}),c(s,{span:2},{default:o((()=>[i("div",U,[m(p(u(J).h5Url)+" ",1),c(x,{class:"cursor-pointer ml-4 color-primary",onClick:l[0]||(l[0]=e=>N(u(J).h5Url))},{default:o((()=>[c(v)])),_:1})])])),_:1})])),_:1})]),i("div",k,[c(j,{title:"小程序码",column:2},{default:o((()=>[c(s,{span:1},{default:o((()=>[c(S,{style:{width:"60px",height:"60px"},src:u(J).miniProgramUrl},null,8,["src"])])),_:1}),c(s,{span:1},{default:o((()=>[c(P,{type:"primary",onClick:l[1]||(l[1]=e=>N(u(J).miniProgramUrl))},{default:o((()=>l[4]||(l[4]=[m(" 复 制 ")]))),_:1,__:[4]}),c(P,{type:"warning",onClick:K},{default:o((()=>l[5]||(l[5]=[m("下 载")]))),_:1,__:[5]})])),_:1})])),_:1})]),u(H).isSecret?(t(),n("div",w,[c(j,{title:"登录口令",column:2},{default:o((()=>[c(s,{span:2},{default:o((()=>[i("div",E,[m(p(u(H).secretKey)+" ",1),c(x,{class:"cursor-pointer ml-4 color-primary",onClick:l[2]||(l[2]=e=>N(u(H).secretKey))},{default:o((()=>[c(v)])),_:1})])])),_:1})])),_:1})])):d("",!0)])),_:1},8,["modelValue","title"])}}});export{S as _};
