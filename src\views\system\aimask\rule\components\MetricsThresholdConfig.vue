<template>
  <div class="metrics-threshold-config">
    <el-form label-width="80px">
      <el-form-item label="指标类型">
        <el-select v-model="localConfig.metric" placeholder="请选择指标类型">
          <el-option
            v-for="option in metricOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="条件">
        <div class="flex items-center">
          <el-select v-model="localConfig.operator" style="width: 120px">
            <el-option
              v-for="option in operatorOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <el-input-number
            v-model="localConfig.threshold"
            :min="0"
            :step="1"
            step-strictly
            class="ml-2"
          />
        </div>
      </el-form-item>
      <el-form-item label="时间窗口">
        <el-input-number v-model="localConfig.time_window" :min="1" :step="10" step-strictly />
        <span class="ml-2 text-sm text-gray-500">秒</span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";

const props = defineProps({
  config: {
    type: Object,
    default: () => ({ metric: "message_count", operator: ">", threshold: 0, time_window: 60 }),
  },
});

const emit = defineEmits(["update"]);

const localConfig = reactive({
  metric: props.config?.metric || "message_count",
  operator: props.config?.operator || ">",
  threshold: props.config?.threshold || 0,
  time_window: props.config?.time_window || 60,
});

// 监听配置变化并向上传递
watch(
  localConfig,
  (newVal) => {
    emit("update", { ...newVal });
  },
  { deep: true }
);

// 指标类型选项
const metricOptions = [
  { label: "消息数量", value: "message_count" },
  { label: "用户数量", value: "user_count" },
  { label: "点赞数量", value: "like_count" },
];

// 操作符选项
const operatorOptions = [
  { label: "大于", value: ">" },
  { label: "小于", value: "<" },
  { label: "大于等于", value: ">=" },
  { label: "小于等于", value: "<=" },
];
</script>
