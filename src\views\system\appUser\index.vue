<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="keywords" label="">
          <el-input
            v-model="page.query.keywords"
            placeholder="昵称、账号检索"
            clearable
            @keyup.enter="getPage"
          />
        </el-form-item>
        <el-form-item prop="isAssistant" label="是否是助理">
          <dict v-model="page.query.isAssistant" code="yes_or_no" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="头像" align="center" prop="name" width="100">
          <template #default="{ row }">
            <el-image
              style="width: 30px; height: 30px; border-radius: 30px"
              :preview-src-list="[row.avatar]"
              preview-teleported
              :src="row.avatar"
            />
          </template>
        </el-table-column>
        <el-table-column label="昵称" align="center" prop="nickname" min-width="120" />
        <el-table-column label="助理信息" align="center">
          <el-table-column label="是否是助理" align="center" width="80">
            <template #default="{ row }">
              <dict-label v-model="row.isAssistant" code="yes_or_no" />
            </template>
          </el-table-column>
          <el-table-column label="归属公司" align="center" prop="nickName" min-width="100">
            <template #default="{ row }">
              {{ row.isAssistant ? row.tenantName : "-" }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="最近登录时间" align="center" prop="loginDate" min-width="100" />
        <el-table-column label="账号" align="center" prop="username" min-width="100" />
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import TenantApi, { AppUserPageQuery, AppUserPageVo } from "@/api/system/tenant";

defineOptions({ name: "AppUser" });
import { usePage } from "@/utils/commonSetup";

const { page, getPage, resetQuery } = usePage<AppUserPageQuery, AppUserPageVo>(
  new AppUserPageQuery(),
  TenantApi.appUserPage
);
</script>
