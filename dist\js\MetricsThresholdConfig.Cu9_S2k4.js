import{d as e,S as l,I as t,g as o,f as a,m as s,w as i,P as r,Q as m,C as n}from"./index.Dk5pbsTU.js";import{a as p,E as u}from"./el-form-item.Bw6Zyv_7.js";import"./el-input.DiGatoux.js";import{E as d}from"./el-input-number.C02ig7uT.js";/* empty css               */import{E as c,a as j}from"./el-select.CRWkm-it.js";/* empty css                     */import"./el-popper.Dbn4MgsT.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./error.D_Dr4eZ1.js";import"./index.D6CER_Ot.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./index.C9UdVphc.js";import"./event.BwRzfsZt.js";import"./index.DEKElSOG.js";import"./use-form-item.DzRJVC1I.js";import"./index.Vn8pbgQR.js";import"./index.Cd8M2JyP.js";import"./index.ybpLT-bz.js";import"./index.L2DVy5yq.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./isEqual.C0S6DIiJ.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";const f={class:"metrics-threshold-config"},v={class:"flex items-center"},_=e({__name:"MetricsThresholdConfig",props:{config:{type:Object,default:()=>({metric:"message_count",operator:">",threshold:0,time_window:60})}},emits:["update"],setup(e,{emit:_}){var b,h,x,g;const w=e,V=_,y=l({metric:(null==(b=w.config)?void 0:b.metric)||"message_count",operator:(null==(h=w.config)?void 0:h.operator)||">",threshold:(null==(x=w.config)?void 0:x.threshold)||0,time_window:(null==(g=w.config)?void 0:g.time_window)||60});t(y,(e=>{V("update",{...e})}),{deep:!0});const U=[{label:"消息数量",value:"message_count"},{label:"用户数量",value:"user_count"},{label:"点赞数量",value:"like_count"}],k=[{label:"大于",value:">"},{label:"小于",value:"<"},{label:"大于等于",value:">="},{label:"小于等于",value:"<="}];return(e,l)=>{const t=j,_=c,b=p,h=d,x=u;return a(),o("div",f,[s(x,{"label-width":"80px"},{default:i((()=>[s(b,{label:"指标类型"},{default:i((()=>[s(_,{modelValue:y.metric,"onUpdate:modelValue":l[0]||(l[0]=e=>y.metric=e),placeholder:"请选择指标类型"},{default:i((()=>[(a(),o(r,null,m(U,(e=>s(t,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),s(b,{label:"条件"},{default:i((()=>[n("div",v,[s(_,{modelValue:y.operator,"onUpdate:modelValue":l[1]||(l[1]=e=>y.operator=e),style:{width:"120px"}},{default:i((()=>[(a(),o(r,null,m(k,(e=>s(t,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),s(h,{modelValue:y.threshold,"onUpdate:modelValue":l[2]||(l[2]=e=>y.threshold=e),min:0,step:1,"step-strictly":"",class:"ml-2"},null,8,["modelValue"])])])),_:1}),s(b,{label:"时间窗口"},{default:i((()=>[s(h,{modelValue:y.time_window,"onUpdate:modelValue":l[3]||(l[3]=e=>y.time_window=e),min:1,step:10,"step-strictly":""},null,8,["modelValue"]),l[4]||(l[4]=n("span",{class:"ml-2 text-sm text-gray-500"},"秒",-1))])),_:1,__:[4]})])),_:1})])}}});export{_ as default};
