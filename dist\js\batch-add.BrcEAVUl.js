import{d as e,r as a,S as l,e as t,f as o,w as s,m as i,g as r,P as n,Q as u,C as m,h as d,Z as p,$ as f,az as c}from"./index.Dk5pbsTU.js";import{E as v}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as j,a as _}from"./el-form-item.Bw6Zyv_7.js";import{E as g}from"./el-button.CXI119n4.js";import{E as y}from"./el-input.DiGatoux.js";import{T as x}from"./tags.B175GojK.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./use-form-item.DzRJVC1I.js";import"./index.C9UdVphc.js";import"./index.DEKElSOG.js";import"./index.Vn8pbgQR.js";const h={class:"flex items-center"},k={class:"dialog-footer"},b=e({name:"BatchAddTag",__name:"batch-add",emits:["success"],setup(e,{expose:b,emit:C}){const V=C,U=a(!1),w=a(!1),A=a(),E=a([{value:""}]),N=l({tagNames:""});function P(){E.value.push({value:""})}function T(){O()}function L(){var e;null==(e=A.value)||e.validate((e=>{if(e){const e=E.value.map((e=>e.value.trim())).filter((e=>e)).join(",");if(!e)return void c.warning("请至少输入一个标签名称");w.value=!0,N.tagNames=e,x.batchAdd(N).then((()=>{c.success("操作成功"),V("success"),O()})).finally((()=>{w.value=!1}))}}))}function O(){U.value=!1,E.value=[{value:""}],N.tagNames=""}return b({open:function(){O(),U.value=!0}}),(e,a)=>{const l=y,c=g,x=_,b=j,C=v;return o(),t(C,{title:"批量添加标签",modelValue:U.value,"onUpdate:modelValue":a[0]||(a[0]=e=>U.value=e),width:"500px","append-to-body":""},{footer:s((()=>[m("div",k,[i(c,{type:"primary",onClick:L,loading:w.value},{default:s((()=>a[3]||(a[3]=[f("确 定")]))),_:1,__:[3]},8,["loading"]),i(c,{onClick:T},{default:s((()=>a[4]||(a[4]=[f("取 消")]))),_:1,__:[4]})])])),default:s((()=>[i(b,{ref_key:"formRef",ref:A,model:N,rules:e.rules,"label-width":"80px"},{default:s((()=>[(o(!0),r(n,null,u(E.value,((e,r)=>(o(),t(x,{key:r,label:0===r?"标签名称":"",prop:"tagList."+r+".value",rules:{required:!0,message:"请输入标签名称",trigger:"blur"}},{default:s((()=>[m("div",h,[i(l,{modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,placeholder:"请输入标签名称",onKeyup:p(P,["enter"])},null,8,["modelValue","onUpdate:modelValue"]),r===E.value.length-1?(o(),t(c,{key:0,type:"primary",link:"",icon:"plus",onClick:P},{default:s((()=>a[1]||(a[1]=[f(" 添加 ")]))),_:1,__:[1]})):d("",!0),E.value.length>1?(o(),t(c,{key:1,type:"danger",link:"",icon:"delete",onClick:e=>function(e){E.value.splice(e,1)}(r)},{default:s((()=>a[2]||(a[2]=[f(" 删除 ")]))),_:2,__:[2]},1032,["onClick"])):d("",!0)])])),_:2},1032,["label","prop"])))),128))])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])}}});export{b as default};
