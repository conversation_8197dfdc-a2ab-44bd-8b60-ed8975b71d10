import{t as e,z as t,_ as a,d as s,b as l,c as o,cQ as r,cR as n,bA as i,cL as u,Y as c,br as d,L as p,g as f,f as v,h as y,n as m,i as g,C as h,k as b,l as k,F as w,e as x,w as $,D as F,E,q as R,a9 as S,M as T,ba as L,ad as C,x as _,r as U,P,Q as D,Z as j,j as B,m as O,cS as q,cg as A,aR as N,c8 as M,A as H,s as I,cx as z,cT as K,cU as W,I as X,a6 as Q,y as G,ae as J,aL as V,U as Y}from"./index.Dk5pbsTU.js";import{t as Z,d as ee}from"./error.D_Dr4eZ1.js";import{a as te}from"./use-form-common-props.CQPDkY7k.js";import{c as ae}from"./cloneDeep.DcCMo0F4.js";import{i as se}from"./isEqual.C0S6DIiJ.js";const le=e({type:{type:String,default:"line",values:["line","circle","dashboard"]},percentage:{type:Number,default:0,validator:e=>e>=0&&e<=100},status:{type:String,default:"",values:["","success","exception","warning"]},indeterminate:Boolean,duration:{type:Number,default:3},strokeWidth:{type:Number,default:6},strokeLinecap:{type:t(String),default:"round"},textInside:Boolean,width:{type:Number,default:126},showText:{type:Boolean,default:!0},color:{type:t([String,Array,Function]),default:""},striped:Boolean,stripedFlow:Boolean,format:{type:t(Function),default:e=>`${e}%`}}),oe=s({name:"ElProgress"});const re=R(a(s({...oe,props:le,setup(e){const t=e,a={success:"#13ce66",exception:"#ff4949",warning:"#e6a23c",default:"#20a0ff"},s=l("progress"),R=o((()=>{const e={width:`${t.percentage}%`,animationDuration:`${t.duration}s`},a=A(t.percentage);return a.includes("gradient")?e.background=a:e.backgroundColor=a,e})),S=o((()=>(t.strokeWidth/t.width*100).toFixed(1))),T=o((()=>["circle","dashboard"].includes(t.type)?Number.parseInt(""+(50-Number.parseFloat(S.value)/2),10):0)),L=o((()=>{const e=T.value,a="dashboard"===t.type;return`\n          M 50 50\n          m 0 ${a?"":"-"}${e}\n          a ${e} ${e} 0 1 1 0 ${a?"-":""}${2*e}\n          a ${e} ${e} 0 1 1 0 ${a?"":"-"}${2*e}\n          `})),C=o((()=>2*Math.PI*T.value)),_=o((()=>"dashboard"===t.type?.75:1)),U=o((()=>`${-1*C.value*(1-_.value)/2}px`)),P=o((()=>({strokeDasharray:`${C.value*_.value}px, ${C.value}px`,strokeDashoffset:U.value}))),D=o((()=>({strokeDasharray:`${C.value*_.value*(t.percentage/100)}px, ${C.value}px`,strokeDashoffset:U.value,transition:"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s"}))),j=o((()=>{let e;return e=t.color?A(t.percentage):a[t.status]||a.default,e})),B=o((()=>"warning"===t.status?r:"line"===t.type?"success"===t.status?n:i:"success"===t.status?u:c)),O=o((()=>"line"===t.type?12+.4*t.strokeWidth:.111111*t.width+2)),q=o((()=>t.format(t.percentage)));const A=e=>{var a;const{color:s}=t;if(d(s))return s(e);if(p(s))return s;{const t=function(e){const t=100/e.length;return e.map(((e,a)=>p(e)?{color:e,percentage:(a+1)*t}:e)).sort(((e,t)=>e.percentage-t.percentage))}(s);for(const a of t)if(a.percentage>e)return a.color;return null==(a=t[t.length-1])?void 0:a.color}};return(e,t)=>(v(),f("div",{class:m([g(s).b(),g(s).m(e.type),g(s).is(e.status),{[g(s).m("without-text")]:!e.showText,[g(s).m("text-inside")]:e.textInside}]),role:"progressbar","aria-valuenow":e.percentage,"aria-valuemin":"0","aria-valuemax":"100"},["line"===e.type?(v(),f("div",{key:0,class:m(g(s).b("bar"))},[h("div",{class:m(g(s).be("bar","outer")),style:b({height:`${e.strokeWidth}px`})},[h("div",{class:m([g(s).be("bar","inner"),{[g(s).bem("bar","inner","indeterminate")]:e.indeterminate},{[g(s).bem("bar","inner","striped")]:e.striped},{[g(s).bem("bar","inner","striped-flow")]:e.stripedFlow}]),style:b(g(R))},[(e.showText||e.$slots.default)&&e.textInside?(v(),f("div",{key:0,class:m(g(s).be("bar","innerText"))},[k(e.$slots,"default",{percentage:e.percentage},(()=>[h("span",null,w(g(q)),1)]))],2)):y("v-if",!0)],6)],6)],2)):(v(),f("div",{key:1,class:m(g(s).b("circle")),style:b({height:`${e.width}px`,width:`${e.width}px`})},[(v(),f("svg",{viewBox:"0 0 100 100"},[h("path",{class:m(g(s).be("circle","track")),d:g(L),stroke:`var(${g(s).cssVarName("fill-color-light")}, #e5e9f2)`,"stroke-linecap":e.strokeLinecap,"stroke-width":g(S),fill:"none",style:b(g(P))},null,14,["d","stroke","stroke-linecap","stroke-width"]),h("path",{class:m(g(s).be("circle","path")),d:g(L),stroke:g(j),fill:"none",opacity:e.percentage?1:0,"stroke-linecap":e.strokeLinecap,"stroke-width":g(S),style:b(g(D))},null,14,["d","stroke","opacity","stroke-linecap","stroke-width"])]))],6)),!e.showText&&!e.$slots.default||e.textInside?y("v-if",!0):(v(),f("div",{key:2,class:m(g(s).e("text")),style:b({fontSize:`${g(O)}px`})},[k(e.$slots,"default",{percentage:e.percentage},(()=>[e.status?(v(),x(g(E),{key:1},{default:$((()=>[(v(),x(F(g(B))))])),_:1})):(v(),f("span",{key:0},w(g(q)),1))]))],6))],10,["aria-valuenow"]))}}),[["__file","progress.vue"]])),ne=Symbol("uploadContextKey");class ie extends Error{constructor(e,t,a,s){super(e),this.name="UploadAjaxError",this.status=t,this.method=a,this.url=s}}function ue(e,t,a){let s;return s=a.response?`${a.response.error||a.response}`:a.responseText?`${a.responseText}`:`fail to ${t.method} ${e} ${a.status}`,new ie(s,a.status,t.method,e)}const ce=["text","picture","picture-card"];let de=1;const pe=()=>Date.now()+de++,fe=e({action:{type:String,default:"#"},headers:{type:t(Object)},method:{type:String,default:"post"},data:{type:t([Object,Function,Promise]),default:()=>C({})},multiple:Boolean,name:{type:String,default:"file"},drag:Boolean,withCredentials:Boolean,showFileList:{type:Boolean,default:!0},accept:{type:String,default:""},fileList:{type:t(Array),default:()=>C([])},autoUpload:{type:Boolean,default:!0},listType:{type:String,values:ce,default:"text"},httpRequest:{type:t(Function),default:e=>{"undefined"==typeof XMLHttpRequest&&Z("ElUpload","XMLHttpRequest is undefined");const t=new XMLHttpRequest,a=e.action;t.upload&&t.upload.addEventListener("progress",(t=>{const a=t;a.percent=t.total>0?t.loaded/t.total*100:0,e.onProgress(a)}));const s=new FormData;if(e.data)for(const[o,r]of Object.entries(e.data))S(r)&&r.length?s.append(o,...r):s.append(o,r);s.append(e.filename,e.file,e.file.name),t.addEventListener("error",(()=>{e.onError(ue(a,e,t))})),t.addEventListener("load",(()=>{if(t.status<200||t.status>=300)return e.onError(ue(a,e,t));e.onSuccess(function(e){const t=e.responseText||e.response;if(!t)return t;try{return JSON.parse(t)}catch(a){return t}}(t))})),t.open(e.method,a,!0),e.withCredentials&&"withCredentials"in t&&(t.withCredentials=!0);const l=e.headers||{};if(l instanceof Headers)l.forEach(((e,a)=>t.setRequestHeader(a,e)));else for(const[o,r]of Object.entries(l))T(r)||t.setRequestHeader(o,String(r));return t.send(s),t}},disabled:Boolean,limit:Number}),ve=e({...fe,beforeUpload:{type:t(Function),default:L},beforeRemove:{type:t(Function)},onRemove:{type:t(Function),default:L},onChange:{type:t(Function),default:L},onPreview:{type:t(Function),default:L},onSuccess:{type:t(Function),default:L},onProgress:{type:t(Function),default:L},onError:{type:t(Function),default:L},onExceed:{type:t(Function),default:L},crossorigin:{type:t(String)}}),ye=e({files:{type:t(Array),default:()=>C([])},disabled:{type:Boolean,default:!1},handlePreview:{type:t(Function),default:L},listType:{type:String,values:ce,default:"text"},crossorigin:{type:t(String)}}),me=s({name:"ElUploadList"});var ge=a(s({...me,props:ye,emits:{remove:e=>!!e},setup(e,{emit:t}){const a=e,{t:s}=_(),r=l("upload"),i=l("icon"),d=l("list"),p=te(),F=U(!1),R=o((()=>[r.b("list"),r.bm("list",a.listType),r.is("disabled",a.disabled)])),S=e=>{t("remove",e)};return(e,t)=>(v(),x(M,{tag:"ul",class:m(g(R)),name:g(d).b()},{default:$((()=>[(v(!0),f(P,null,D(e.files,((t,a)=>(v(),f("li",{key:t.uid||t.name,class:m([g(r).be("list","item"),g(r).is(t.status),{focusing:F.value}]),tabindex:"0",onKeydown:j((e=>!g(p)&&S(t)),["delete"]),onFocus:e=>F.value=!0,onBlur:e=>F.value=!1,onClick:e=>F.value=!1},[k(e.$slots,"default",{file:t,index:a},(()=>["picture"===e.listType||"uploading"!==t.status&&"picture-card"===e.listType?(v(),f("img",{key:0,class:m(g(r).be("list","item-thumbnail")),src:t.url,crossorigin:e.crossorigin,alt:""},null,10,["src","crossorigin"])):y("v-if",!0),"uploading"===t.status||"picture-card"!==e.listType?(v(),f("div",{key:1,class:m(g(r).be("list","item-info"))},[h("a",{class:m(g(r).be("list","item-name")),onClick:B((a=>e.handlePreview(t)),["prevent"])},[O(g(E),{class:m(g(i).m("document"))},{default:$((()=>[O(g(q))])),_:1},8,["class"]),h("span",{class:m(g(r).be("list","item-file-name")),title:t.name},w(t.name),11,["title"])],10,["onClick"]),"uploading"===t.status?(v(),x(g(re),{key:0,type:"picture-card"===e.listType?"circle":"line","stroke-width":"picture-card"===e.listType?6:2,percentage:Number(t.percentage),style:b("picture-card"===e.listType?"":"margin-top: 0.5rem")},null,8,["type","stroke-width","percentage","style"])):y("v-if",!0)],2)):y("v-if",!0),h("label",{class:m(g(r).be("list","item-status-label"))},["text"===e.listType?(v(),x(g(E),{key:0,class:m([g(i).m("upload-success"),g(i).m("circle-check")])},{default:$((()=>[O(g(n))])),_:1},8,["class"])):["picture-card","picture"].includes(e.listType)?(v(),x(g(E),{key:1,class:m([g(i).m("upload-success"),g(i).m("check")])},{default:$((()=>[O(g(u))])),_:1},8,["class"])):y("v-if",!0)],2),g(p)?y("v-if",!0):(v(),x(g(E),{key:2,class:m(g(i).m("close")),onClick:e=>S(t)},{default:$((()=>[O(g(c))])),_:2},1032,["class","onClick"])),y(" Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn"),y(" This is a bug which needs to be fixed "),y(" TODO: Fix the incorrect navigation interaction "),g(p)?y("v-if",!0):(v(),f("i",{key:3,class:m(g(i).m("close-tip"))},w(g(s)("el.upload.deleteTip")),3)),"picture-card"===e.listType?(v(),f("span",{key:4,class:m(g(r).be("list","item-actions"))},[h("span",{class:m(g(r).be("list","item-preview")),onClick:a=>e.handlePreview(t)},[O(g(E),{class:m(g(i).m("zoom-in"))},{default:$((()=>[O(g(A))])),_:1},8,["class"])],10,["onClick"]),g(p)?y("v-if",!0):(v(),f("span",{key:0,class:m(g(r).be("list","item-delete")),onClick:e=>S(t)},[O(g(E),{class:m(g(i).m("delete"))},{default:$((()=>[O(g(N))])),_:1},8,["class"])],10,["onClick"]))],2)):y("v-if",!0)]))],42,["onKeydown","onFocus","onBlur","onClick"])))),128)),k(e.$slots,"append")])),_:3},8,["class","name"]))}}),[["__file","upload-list.vue"]]);const he=e({disabled:{type:Boolean,default:!1}}),be={file:e=>S(e)},ke="ElUploadDrag",we=s({name:ke});var xe=a(s({...we,props:he,emits:be,setup(e,{emit:t}){H(ne)||Z(ke,"usage: <el-upload><el-upload-dragger /></el-upload>");const a=l("upload"),s=U(!1),o=te(),r=e=>{if(o.value)return;s.value=!1,e.stopPropagation();const a=Array.from(e.dataTransfer.files),l=e.dataTransfer.items||[];a.forEach(((e,t)=>{var a;const s=l[t],o=null==(a=null==s?void 0:s.webkitGetAsEntry)?void 0:a.call(s);o&&(e.isDirectory=o.isDirectory)})),t("file",a)},n=()=>{o.value||(s.value=!0)};return(e,t)=>(v(),f("div",{class:m([g(a).b("dragger"),g(a).is("dragover",s.value)]),onDrop:B(r,["prevent"]),onDragover:B(n,["prevent"]),onDragleave:B((e=>s.value=!1),["prevent"])},[k(e.$slots,"default")],42,["onDrop","onDragover","onDragleave"]))}}),[["__file","upload-dragger.vue"]]);const $e=e({...fe,beforeUpload:{type:t(Function),default:L},onRemove:{type:t(Function),default:L},onStart:{type:t(Function),default:L},onSuccess:{type:t(Function),default:L},onProgress:{type:t(Function),default:L},onError:{type:t(Function),default:L},onExceed:{type:t(Function),default:L}}),Fe=s({name:"ElUploadContent",inheritAttrs:!1});var Ee=a(s({...Fe,props:$e,setup(e,{expose:t}){const a=e,s=l("upload"),o=te(),r=I({}),n=I(),i=e=>{if(0===e.length)return;const{autoUpload:t,limit:s,fileList:l,multiple:o,onStart:r,onExceed:n}=a;if(s&&l.length+e.length>s)n(e,l);else{o||(e=e.slice(0,1));for(const a of e){const e=a;e.uid=pe(),r(e),t&&u(e)}}},u=async e=>{if(n.value.value="",!a.beforeUpload)return c(e);let t,s={};try{const l=a.data,o=a.beforeUpload(e);s=z(a.data)?ae(a.data):a.data,t=await o,z(a.data)&&se(l,s)&&(s=ae(a.data))}catch(o){t=!1}if(!1===t)return void a.onRemove(e);let l=e;t instanceof Blob&&(l=t instanceof File?t:new File([t],e.name,{type:e.type})),c(Object.assign(l,{uid:e.uid}),s)},c=async(e,t)=>{const{headers:s,data:l,method:o,withCredentials:n,name:i,action:u,onProgress:c,onSuccess:p,onError:f,httpRequest:v}=a;try{t=await(async(e,t)=>d(e)?e(t):e)(null!=t?t:l,e)}catch(h){return void a.onRemove(e)}const{uid:y}=e,m={headers:s||{},withCredentials:n,file:e,data:t,method:o,filename:i,action:u,onProgress:t=>{c(t,e)},onSuccess:t=>{p(t,e),delete r.value[y]},onError:t=>{f(t,e),delete r.value[y]}},g=v(m);r.value[y]=g,g instanceof Promise&&g.then(m.onSuccess,m.onError)},p=e=>{const t=e.target.files;t&&i(Array.from(t))},y=()=>{o.value||(n.value.value="",n.value.click())},b=()=>{y()};return t({abort:e=>{K(r.value).filter(e?([t])=>String(e.uid)===t:()=>!0).forEach((([e,t])=>{t instanceof XMLHttpRequest&&t.abort(),delete r.value[e]}))},upload:u}),(e,t)=>(v(),f("div",{class:m([g(s).b(),g(s).m(e.listType),g(s).is("drag",e.drag),g(s).is("disabled",g(o))]),tabindex:g(o)?"-1":"0",onClick:y,onKeydown:j(B(b,["self"]),["enter","space"])},[e.drag?(v(),x(xe,{key:0,disabled:g(o),onFile:i},{default:$((()=>[k(e.$slots,"default")])),_:3},8,["disabled"])):k(e.$slots,"default",{key:1}),h("input",{ref_key:"inputRef",ref:n,class:m(g(s).e("input")),name:e.name,disabled:g(o),multiple:e.multiple,accept:e.accept,type:"file",onChange:p,onClick:B((()=>{}),["stop"])},null,42,["name","disabled","multiple","accept","onClick"])],42,["tabindex","onKeydown"]))}}),[["__file","upload-content.vue"]]);const Re="ElUpload",Se=e=>{var t;(null==(t=e.url)?void 0:t.startsWith("blob:"))&&URL.revokeObjectURL(e.url)},Te=s({name:"ElUpload"});const Le=R(a(s({...Te,props:ve,setup(e,{expose:t}){const a=e,s=te(),l=I(),{abort:r,submit:n,clearFiles:i,uploadFiles:u,handleStart:c,handleError:d,handleRemove:p,handleSuccess:m,handleProgress:h,revokeFileObjectURL:b}=((e,t)=>{const a=W(e,"fileList",void 0,{passive:!0}),s=e=>a.value.find((t=>t.uid===e.uid));function l(e){var a;null==(a=t.value)||a.abort(e)}function o(e){a.value=a.value.filter((t=>t.uid!==e.uid))}return X((()=>e.listType),(t=>{"picture-card"!==t&&"picture"!==t||(a.value=a.value.map((t=>{const{raw:s,url:l}=t;if(!l&&s)try{t.url=URL.createObjectURL(s)}catch(o){e.onError(o,t,a.value)}return t})))})),X(a,(e=>{for(const t of e)t.uid||(t.uid=pe()),t.status||(t.status="success")}),{immediate:!0,deep:!0}),{uploadFiles:a,abort:l,clearFiles:function(e=["ready","uploading","success","fail"]){a.value=a.value.filter((t=>!e.includes(t.status)))},handleError:(t,l)=>{const r=s(l);r&&(r.status="fail",o(r),e.onError(t,r,a.value),e.onChange(r,a.value))},handleProgress:(t,l)=>{const o=s(l);o&&(e.onProgress(t,o,a.value),o.status="uploading",o.percentage=Math.round(t.percent))},handleStart:t=>{T(t.uid)&&(t.uid=pe());const s={name:t.name,percentage:0,status:"ready",size:t.size,raw:t,uid:t.uid};if("picture-card"===e.listType||"picture"===e.listType)try{s.url=URL.createObjectURL(t)}catch(l){ee(Re,l.message),e.onError(l,s,a.value)}a.value=[...a.value,s],e.onChange(s,a.value)},handleSuccess:(t,l)=>{const o=s(l);o&&(o.status="success",o.response=t,e.onSuccess(t,o,a.value),e.onChange(o,a.value))},handleRemove:async t=>{const r=t instanceof File?s(t):t;r||Z(Re,"file to be removed not found");const n=t=>{l(t),o(t),e.onRemove(t,a.value),Se(t)};e.beforeRemove?!1!==await e.beforeRemove(r,a.value)&&n(r):n(r)},submit:function(){a.value.filter((({status:e})=>"ready"===e)).forEach((({raw:e})=>{var a;return e&&(null==(a=t.value)?void 0:a.upload(e))}))},revokeFileObjectURL:Se}})(a,l),w=o((()=>"picture-card"===a.listType)),F=o((()=>({...a,fileList:u.value,onStart:c,onProgress:h,onSuccess:m,onError:d,onRemove:p})));return Q((()=>{u.value.forEach(b)})),G(ne,{accept:J(a,"accept")}),t({abort:r,submit:n,clearFiles:i,handleStart:c,handleRemove:p}),(e,t)=>(v(),f("div",null,[g(w)&&e.showFileList?(v(),x(ge,{key:0,disabled:g(s),"list-type":e.listType,files:g(u),crossorigin:e.crossorigin,"handle-preview":e.onPreview,onRemove:g(p)},V({append:$((()=>[O(Ee,Y({ref_key:"uploadRef",ref:l},g(F)),{default:$((()=>[e.$slots.trigger?k(e.$slots,"trigger",{key:0}):y("v-if",!0),!e.$slots.trigger&&e.$slots.default?k(e.$slots,"default",{key:1}):y("v-if",!0)])),_:3},16)])),_:2},[e.$slots.file?{name:"default",fn:$((({file:t,index:a})=>[k(e.$slots,"file",{file:t,index:a})]))}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):y("v-if",!0),!g(w)||g(w)&&!e.showFileList?(v(),x(Ee,Y({key:1,ref_key:"uploadRef",ref:l},g(F)),{default:$((()=>[e.$slots.trigger?k(e.$slots,"trigger",{key:0}):y("v-if",!0),!e.$slots.trigger&&e.$slots.default?k(e.$slots,"default",{key:1}):y("v-if",!0)])),_:3},16)):y("v-if",!0),e.$slots.trigger?k(e.$slots,"default",{key:2}):y("v-if",!0),k(e.$slots,"tip"),!g(w)&&e.showFileList?(v(),x(ge,{key:3,disabled:g(s),"list-type":e.listType,files:g(u),crossorigin:e.crossorigin,"handle-preview":e.onPreview,onRemove:g(p)},V({_:2},[e.$slots.file?{name:"default",fn:$((({file:t,index:a})=>[k(e.$slots,"file",{file:t,index:a})]))}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):y("v-if",!0)]))}}),[["__file","upload.vue"]]));export{Le as E,re as a,pe as g};
