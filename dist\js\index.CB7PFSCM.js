import{d as e,r as l,S as a,o as s,g as i,m as o,w as r,i as t,aO as d,C as m,aP as n,$ as u,F as p,E as c,ap as f,e as b,h as _,az as v,f as g}from"./index.Dk5pbsTU.js";import{E as y}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{a as w,E as j}from"./el-form-item.Bw6Zyv_7.js";import{_ as k}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as h}from"./el-input.DiGatoux.js";import{E as x,a as V}from"./el-tab-pane.CXnN_Izo.js";import{E as P,a as C}from"./el-col.Cfu8vZQ4.js";import{E}from"./el-card.DwLhVNHW.js";import{E as z,a as U}from"./el-descriptions-item.BlvmJIy_.js";import{E as I}from"./el-button.CXI119n4.js";import{E as q}from"./el-avatar.DtvYzXsq.js";import{F as A}from"./index.WzKGworL.js";import{_ as F}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./el-checkbox.DDYarIkn.js";import"./index.C9UdVphc.js";import"./use-form-item.DzRJVC1I.js";import"./isEqual.C0S6DIiJ.js";import"./el-radio.w2rep3_A.js";/* empty css               */import"./el-select.CRWkm-it.js";import"./el-popper.Dbn4MgsT.js";import"./index.ybpLT-bz.js";import"./index.L2DVy5yq.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./index.Vn8pbgQR.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";/* empty css                     */import"./index.DEKElSOG.js";import"./index.C_BbqFDa.js";const Z={class:"app-container"},$={class:"w-full"},M={class:"relative w-100px h-100px flex-center"},O={class:"mt-5"},R={class:"text-14px mt-2"},B={class:"mt-5"},T={class:"text-14px mt-2"},H={key:0},N={key:1},S={class:"mt-5"},D={class:"text-14px mt-2"},G={key:0},J={key:1},K={class:"dialog-footer"},L=F(e({__name:"index",setup(e){const F=l({}),L=a({visible:!1,title:"",type:""}),Q=a({}),Y=a({}),W=a({}),X=a({}),ee=l(0),le=l(),ae=l(0),se=l(),ie={oldPassword:[{required:!0,message:"请输入原密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"}]},oe={mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"}]},re={email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{pattern:/\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/,message:"请输入正确的邮箱地址",trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"}]},te=e=>{switch(L.type=e,L.visible=!0,e){case"account":L.title="账号资料",Q.id=F.value.id,Q.nickname=F.value.nickname,Q.gender=F.value.gender;break;case"password":L.title="修改密码";break;case"mobile":L.title="绑定手机";break;case"email":L.title="绑定邮箱"}};function de(){if(!W.mobile)return void v.error("请输入手机号");/^1[3-9]\d{9}$/.test(W.mobile)?d.sendMobileCode(W.mobile).then((()=>{v.success("验证码发送成功"),ee.value=60,le.value=setInterval((()=>{ee.value>0?ee.value-=1:clearInterval(le.value)}),1e3)})):v.error("手机号格式不正确")}function me(){if(!X.email)return void v.error("请输入邮箱");/\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/.test(X.email)?d.sendEmailCode(X.email).then((()=>{v.success("验证码发送成功"),ae.value=60,se.value=setInterval((()=>{ae.value>0?ae.value-=1:clearInterval(se.value)}),1e3)})):v.error("邮箱格式不正确")}const ne=async()=>{if("account"===L.type)d.updateProfile(Q).then((()=>{v.success("账号资料修改成功"),L.visible=!1,fe()}));else if("password"===L.type){if(Y.newPassword!==Y.confirmPassword)return void v.error("两次输入的密码不一致");d.changePassword(Y).then((()=>{v.success("密码修改成功"),L.visible=!1}))}else"mobile"===L.type?d.bindOrChangeMobile(W).then((()=>{v.success("手机号绑定成功"),L.visible=!1,fe()})):"email"===L.type&&d.bindOrChangeEmail(X).then((()=>{v.success("邮箱绑定成功"),L.visible=!1,fe()}))},ue=l(null),pe=()=>{var e;null==(e=ue.value)||e.click()},ce=async e=>{const l=e.target,a=l.files?l.files[0]:null;if(a)try{const e=await A.uploadFile(a);F.value.avatar=e.url,await d.updateProfile({avatar:e.url})}catch(s){v.error("头像上传失败")}},fe=async()=>{const e=await d.getProfile();F.value=e};return s((async()=>{le.value&&clearInterval(le.value),se.value&&clearInterval(se.value),await fe()})),(e,l)=>{const a=q,s=I,d=f("Edit"),v=c,A=f("User"),le=f("Male"),se=f("Female"),fe=U,be=f("Phone"),_e=f("Message"),ve=f("Timer"),ge=z,ye=E,we=V,je=C,ke=P,he=x,xe=h,Ve=w,Pe=k,Ce=j,Ee=y;return g(),i("div",Z,[o(he,{"tab-position":"left"},{default:r((()=>[o(we,{label:"账号信息"},{default:r((()=>[m("div",$,[o(ye,null,{default:r((()=>[m("div",M,[o(a,{src:t(F).avatar,size:100},null,8,["src"]),o(s,{type:"info",class:"absolute bottom-0 right-0 cursor-pointer",circle:"",icon:t(n),size:"small",onClick:pe},null,8,["icon"]),m("input",{ref_key:"fileInput",ref:ue,type:"file",style:{display:"none"},onChange:ce},null,544)]),m("div",O,[u(p(t(F).nickname)+" ",1),o(v,{class:"align-middle cursor-pointer",onClick:l[0]||(l[0]=e=>te("account"))},{default:r((()=>[o(d)])),_:1})]),o(ge,{column:1,class:"mt-10"},{default:r((()=>[o(fe,null,{label:r((()=>[o(v,{class:"align-middle"},{default:r((()=>[o(A)])),_:1}),l[17]||(l[17]=u(" 用户名 "))])),default:r((()=>[u(" "+p(t(F).username)+" ",1),1===t(F).gender?(g(),b(v,{key:0,class:"align-middle color-blue"},{default:r((()=>[o(le)])),_:1})):(g(),b(v,{key:1,class:"align-middle color-pink"},{default:r((()=>[o(se)])),_:1}))])),_:1}),o(fe,null,{label:r((()=>[o(v,{class:"align-middle"},{default:r((()=>[o(be)])),_:1}),l[18]||(l[18]=u(" 手机号码 "))])),default:r((()=>[u(" "+p(t(F).mobile),1)])),_:1}),o(fe,null,{label:r((()=>[o(v,{class:"align-middle"},{default:r((()=>[o(_e)])),_:1}),l[19]||(l[19]=u(" 邮箱 "))])),default:r((()=>[u(" "+p(t(F).email),1)])),_:1}),o(fe,null,{label:r((()=>l[20]||(l[20]=[m("div",{class:"i-svg:tree"},null,-1),u(" 部门 ")]))),default:r((()=>[u(" "+p(t(F).deptName),1)])),_:1}),o(fe,null,{label:r((()=>l[21]||(l[21]=[m("div",{class:"i-svg:role"},null,-1),u(" 角色 ")]))),default:r((()=>[u(" "+p(t(F).roleNames),1)])),_:1}),o(fe,null,{label:r((()=>[o(v,{class:"align-middle"},{default:r((()=>[o(ve)])),_:1}),l[22]||(l[22]=u(" 创建日期 "))])),default:r((()=>[u(" "+p(t(F).createTime),1)])),_:1})])),_:1})])),_:1})])])),_:1}),o(we,{label:"安全设置"},{default:r((()=>[o(ye,null,{default:r((()=>[o(ke,null,{default:r((()=>[o(je,{span:16},{default:r((()=>[l[25]||(l[25]=m("div",{class:"font-bold"},"账户密码",-1)),m("div",R,[l[24]||(l[24]=u(" 定期修改密码有助于保护账户安全 ")),o(s,{type:"primary",plain:"",size:"small",class:"ml-5",onClick:l[1]||(l[1]=()=>te("password"))},{default:r((()=>l[23]||(l[23]=[u(" 修改 ")]))),_:1,__:[23]})])])),_:1,__:[25]})])),_:1}),m("div",B,[l[28]||(l[28]=m("div",{class:"font-bold"},"绑定手机",-1)),m("div",T,[t(F).mobile?(g(),i("span",H,"已绑定手机号："+p(t(F).mobile),1)):(g(),i("span",N,"未绑定手机")),t(F).mobile?(g(),b(s,{key:2,type:"primary",plain:"",size:"small",class:"ml-5",onClick:l[2]||(l[2]=()=>te("mobile"))},{default:r((()=>l[26]||(l[26]=[u(" 更换 ")]))),_:1,__:[26]})):(g(),b(s,{key:3,type:"primary",plain:"",size:"small",class:"ml-5",onClick:l[3]||(l[3]=()=>te("mobile"))},{default:r((()=>l[27]||(l[27]=[u(" 绑定 ")]))),_:1,__:[27]}))])]),m("div",S,[l[31]||(l[31]=m("div",{class:"font-bold"},"绑定邮箱",-1)),m("div",D,[t(F).email?(g(),i("span",G,"已绑定邮箱："+p(t(F).email),1)):(g(),i("span",J,"未绑定邮箱")),t(F).email?(g(),b(s,{key:2,type:"primary",plain:"",size:"small",class:"ml-5",onClick:l[4]||(l[4]=()=>te("email"))},{default:r((()=>l[29]||(l[29]=[u(" 更换 ")]))),_:1,__:[29]})):(g(),b(s,{key:3,type:"primary",plain:"",size:"small",class:"ml-5",onClick:l[5]||(l[5]=()=>te("email"))},{default:r((()=>l[30]||(l[30]=[u(" 绑定 ")]))),_:1,__:[30]}))])])])),_:1})])),_:1})])),_:1}),o(Ee,{modelValue:t(L).visible,"onUpdate:modelValue":l[16]||(l[16]=e=>t(L).visible=e),title:t(L).title,width:500},{footer:r((()=>[m("span",K,[o(s,{onClick:l[15]||(l[15]=e=>t(L).visible=!1)},{default:r((()=>l[32]||(l[32]=[u("取消")]))),_:1,__:[32]}),o(s,{type:"primary",onClick:ne},{default:r((()=>l[33]||(l[33]=[u("确定")]))),_:1,__:[33]})])])),default:r((()=>["account"===t(L).type?(g(),b(Ce,{key:0,ref:"userProfileFormRef",model:t(Q),"label-width":100},{default:r((()=>[o(Ve,{label:"昵称"},{default:r((()=>[o(xe,{modelValue:t(Q).nickname,"onUpdate:modelValue":l[6]||(l[6]=e=>t(Q).nickname=e)},null,8,["modelValue"])])),_:1}),o(Ve,{label:"性别"},{default:r((()=>[o(Pe,{modelValue:t(Q).gender,"onUpdate:modelValue":l[7]||(l[7]=e=>t(Q).gender=e),code:"gender"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])):_("",!0),"password"===t(L).type?(g(),b(Ce,{key:1,ref:"passwordChangeFormRef",model:t(Y),rules:ie,"label-width":100},{default:r((()=>[o(Ve,{label:"原密码",prop:"oldPassword"},{default:r((()=>[o(xe,{modelValue:t(Y).oldPassword,"onUpdate:modelValue":l[8]||(l[8]=e=>t(Y).oldPassword=e),type:"password","show-password":""},null,8,["modelValue"])])),_:1}),o(Ve,{label:"新密码",prop:"newPassword"},{default:r((()=>[o(xe,{modelValue:t(Y).newPassword,"onUpdate:modelValue":l[9]||(l[9]=e=>t(Y).newPassword=e),type:"password","show-password":""},null,8,["modelValue"])])),_:1}),o(Ve,{label:"确认密码",prop:"confirmPassword"},{default:r((()=>[o(xe,{modelValue:t(Y).confirmPassword,"onUpdate:modelValue":l[10]||(l[10]=e=>t(Y).confirmPassword=e),type:"password","show-password":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])):"mobile"===t(L).type?(g(),b(Ce,{key:2,ref:"mobileBindingFormRef",model:t(W),rules:oe,"label-width":100},{default:r((()=>[o(Ve,{label:"手机号码",prop:"mobile"},{default:r((()=>[o(xe,{modelValue:t(W).mobile,"onUpdate:modelValue":l[11]||(l[11]=e=>t(W).mobile=e),style:{width:"250px"}},null,8,["modelValue"])])),_:1}),o(Ve,{label:"验证码",prop:"code"},{default:r((()=>[o(xe,{modelValue:t(W).code,"onUpdate:modelValue":l[12]||(l[12]=e=>t(W).code=e),style:{width:"250px"}},{append:r((()=>[o(s,{class:"ml-5",disabled:t(ee)>0,onClick:de},{default:r((()=>[u(p(t(ee)>0?`${t(ee)}s后重新发送`:"发送验证码"),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])):"email"===t(L).type?(g(),b(Ce,{key:3,ref:"emailBindingFormRef",model:t(X),rules:re,"label-width":100},{default:r((()=>[o(Ve,{label:"邮箱",prop:"email"},{default:r((()=>[o(xe,{modelValue:t(X).email,"onUpdate:modelValue":l[13]||(l[13]=e=>t(X).email=e),style:{width:"250px"}},null,8,["modelValue"])])),_:1}),o(Ve,{label:"验证码",prop:"code"},{default:r((()=>[o(xe,{modelValue:t(X).code,"onUpdate:modelValue":l[14]||(l[14]=e=>t(X).code=e),style:{width:"250px"}},{append:r((()=>[o(s,{class:"ml-5",disabled:t(ae)>0,onClick:me},{default:r((()=>[u(p(t(ae)>0?`${t(ae)}s后重新发送`:"发送验证码"),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])):_("",!0)])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-0c067bf8"]]);export{L as default};
