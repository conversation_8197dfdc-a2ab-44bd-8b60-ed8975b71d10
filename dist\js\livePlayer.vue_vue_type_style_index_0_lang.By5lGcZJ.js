import{d as e,S as l,r as s,e as i,f as a,w as t,C as r,g as o,h as d,i as m}from"./index.Dk5pbsTU.js";import{E as n}from"./el-drawer.Df_TzNjH.js";import"./el-overlay.DpVCS8zG.js";const u={style:{width:"100%",height:"calc(100% - 10px)"}},v=["src"],c=e({__name:"livePlayer",setup(e,{expose:c}){const p=l({visible:!1,title:"查看直播"}),f=s("");return c({open:e=>{f.value=e,p.visible=!0,p.title="观看直播"}}),(e,l)=>{const s=n;return a(),i(s,{modelValue:m(p).visible,"onUpdate:modelValue":l[0]||(l[0]=e=>m(p).visible=e),class:"look-live-drawer",title:m(p).title,size:"455"},{default:t((()=>[r("div",u,[m(p).visible?(a(),o("iframe",{key:0,src:m(f),width:"100%",height:"100%",frameborder:"0",allowfullscreen:""},null,8,v)):d("",!0)])])),_:1},8,["modelValue","title"])}}});export{c as _};
