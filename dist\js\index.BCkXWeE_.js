import{d as e,r as t,aQ as s,e as r,f as i,w as o,m as a,V as n,i as m,C as l,$ as p,F as d}from"./index.Dk5pbsTU.js";import{v as j}from"./el-loading.Dqi-qL7c.js";import{E as c,b as f}from"./el-main.CclDHmVj.js";import{E as u}from"./el-card.DwLhVNHW.js";import{a as _,E as g}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{E as x}from"./el-button.CXI119n4.js";/* empty css               */import{_ as h}from"./edit.vue_vue_type_script_setup_true_lang.Bveef4KT.js";import{a as v}from"./commonSetup.Dm-aByKQ.js";import{a as b}from"./grade.D-4Kz6mr.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./_Uint8Array.n_j8oILW.js";import"./_initCloneObject.BN1anLuC.js";import"./_baseIteratee.CPRpgrLu.js";import"./isEqual.C0S6DIiJ.js";import"./debounce.DJJTSR8O.js";import"./index.wZTqlYZ6.js";import"./use-form-common-props.CQPDkY7k.js";import"./event.BwRzfsZt.js";import"./use-form-item.DzRJVC1I.js";import"./index.D6CER_Ot.js";import"./index.DuiNpp1i.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import"./vnode.Cbclzz8S.js";import"./scroll.CVc-P3_z.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./el-input.DiGatoux.js";import"./index.DEKElSOG.js";import"./index.Vn8pbgQR.js";const y={class:"text-align-center"},w=e({name:"VipLevelConfig",__name:"index",setup(e){const w=t(),{sendInfo:C,onSend:E}=v(b.configList);return E(),(e,t)=>{const v=g,b=x,L=_,P=u,S=c,I=f,k=s("hasPerm"),O=j;return i(),r(I,{class:"app-container"},{default:o((()=>[a(S,{style:{padding:"0",height:"100%"}},{default:o((()=>[a(P,{shadow:"never"},{default:o((()=>[n((i(),r(L,{ref:"dataTableRef",data:m(C).data||[],"highlight-current-row":"",border:""},{default:o((()=>[a(v,{label:"序号",align:"center",width:"55",type:"index"}),a(v,{label:"等级名称",align:"center",prop:"levelName","min-width":"120"}),a(v,{label:"积分值",align:"center","min-width":"100"},{header:o((()=>[l("div",y,[t[2]||(t[2]=p(" 积分值 ")),n((i(),r(b,{type:"primary",style:{right:"0",top:"50%",transform:"translateY(-50%)"},class:"pos-absolute",icon:"edit",size:"small",onClick:t[0]||(t[0]=e=>{var t;return null==(t=m(w))?void 0:t.open(m(C).data||[])})},{default:o((()=>t[1]||(t[1]=[p(" 编辑 ")]))),_:1,__:[1]})),[[k,["vipLevel:config:edit"]]])])])),default:o((({row:e})=>[l("div",null,[p(d(e.startPoints)+" ",1),t[3]||(t[3]=l("span",{class:"m-l-1 m-r-1"},"至",-1)),p(" "+d(e.endPoints),1)])])),_:1})])),_:1},8,["data"])),[[O,m(C).loading]])])),_:1})])),_:1}),a(h,{ref_key:"editModelRef",ref:w,onSuccess:m(E)},null,8,["onSuccess"])])),_:1})}}});export{w as default};
