<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="700px">
    <el-tabs v-model="tabIndex">
      <el-tab-pane name="clone" label="快捷克隆">
        <el-form
          v-if="tabIndex === 'clone'"
          ref="editFormRef"
          :model="cloneFormData"
          :rules="rules"
          label-width="140px"
        >
          <el-form-item label="选择复用的直播间" prop="selectRoomName">
            <el-popover
              v-model:visible="popoverVisible"
              placement="bottom"
              trigger="click"
              width="500px"
            >
              <template #reference>
                <el-input
                  v-model="cloneFormData.selectRoomName"
                  show-word-limit
                  readonly
                  placeholder="选择复用的直播间"
                />
              </template>
              <el-form ref="queryFormRef" :model="page.query" :inline="true">
                <el-form-item prop="keywords" label="">
                  <el-input
                    v-model="page.query.keywords"
                    placeholder="直播间名称检索"
                    clearable
                    @keyup.enter="getPage"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
                </el-form-item>
              </el-form>
              <el-table
                ref="dataTableRef"
                v-loading="page.loading"
                height="400px"
                :data="page.data.records"
                highlight-current-row
                border
                @current-change="handleCurrentChange"
              >
                <el-table-column label="序号" align="center" width="55" type="index" />
                <el-table-column
                  label="直播间名称"
                  align="center"
                  prop="roomName"
                  min-width="120"
                />
                <el-table-column
                  label="累计开播次数"
                  align="center"
                  prop="liveCount"
                  min-width="100"
                />
                <el-table-column label="直播间状态" align="center" width="120">
                  <template #default="{ row }">
                    <dict-label v-model="row.status" code="live_status" />
                  </template>
                </el-table-column>
              </el-table>

              <pagination
                v-if="page.data.totalRow"
                v-model:total="page.data.totalRow"
                v-model:page="page.query.pageNum"
                v-model:limit="page.query.pageSize"
                @pagination="getPage"
              />
            </el-popover>
          </el-form-item>
          <el-alert
            title="您可以在这里快捷创建直播，同步复刻该直播间的快捷语、马甲"
            :closable="false"
            type="primary"
          />
          <div class="new-body">
            <div class="new-body-title">请设置直播间基础数据</div>
            <el-form-item label="直播间名称" prop="roomName">
              <el-input
                v-model="cloneFormData.roomName"
                :maxlength="50"
                show-word-limit
                placeholder="请输入直播间名称"
              />
            </el-form-item>
            <el-form-item label="封面图" prop="coverUrl">
              <SingleImageUpload
                v-model="cloneFormData.coverUrl"
                :style="{ width: '100px', height: '100px' }"
              />
            </el-form-item>
            <el-form-item label="在线用户显示设置">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="主播" label-width="80px">
                    <el-switch
                      v-model="cloneFormData.anchorShowUserCount"
                      inline-prompt
                      :active-value="1"
                      :inactive-value="0"
                      active-text="开"
                      inactive-text="关"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="助理" label-width="80px">
                    <el-switch
                      v-model="cloneFormData.assistantShowUserCount"
                      inline-prompt
                      :active-value="1"
                      :inactive-value="0"
                      active-text="开"
                      inactive-text="关"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="销售" label-width="80px">
                    <el-switch
                      v-model="cloneFormData.saleShowUserCount"
                      inline-prompt
                      :active-value="1"
                      :inactive-value="0"
                      active-text="开"
                      inactive-text="关"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="用户" label-width="80px">
                    <el-switch
                      v-model="cloneFormData.userShowUserCount"
                      inline-prompt
                      active-text="开"
                      :active-value="1"
                      :inactive-value="0"
                      inactive-text="关"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </div>
          <div class="new-body">
            <div class="new-body-title">同步克隆：</div>
            <div style="padding-left: 50px">
              <el-checkbox v-model="cloneFormData.cloneQuickReply" :false-label="0" :true-label="1">
                快捷语
              </el-checkbox>
            </div>
            <div style="padding-left: 50px">
              <el-checkbox v-model="cloneFormData.cloneQuickVest" :false-label="0" :true-label="1">
                马甲
              </el-checkbox>
            </div>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane name="new" label="手动新增">
        <el-form
          v-if="tabIndex === 'new'"
          ref="editFormRef"
          :model="formData"
          :rules="rules"
          label-width="140px"
        >
          <el-form-item label="直播间名称" prop="roomName">
            <el-input
              v-model="formData.roomName"
              :maxlength="50"
              show-word-limit
              placeholder="请输入直播间名称"
            />
          </el-form-item>
          <el-form-item label="封面图" prop="coverUrl">
            <SingleImageUpload
              v-model="formData.coverUrl"
              :style="{ width: '100px', height: '100px' }"
            />
          </el-form-item>

          <el-form-item label="直播间介绍" prop="description">
            <el-input
              v-model="formData.description"
              :maxlength="200"
              show-word-limit
              type="textarea"
              :rows="5"
              placeholder="请输入直播间介绍"
            />
          </el-form-item>

          <el-form-item label="直播间公告" prop="announcement">
            <el-input
              v-model="formData.announcement"
              :maxlength="200"
              show-word-limit
              type="textarea"
              :rows="4"
              placeholder="请输入直播间公告"
            />
          </el-form-item>

          <el-form-item label="温馨提示" prop="notice">
            <el-input
              v-model="formData.notice"
              :maxlength="80"
              show-word-limit
              type="textarea"
              :rows="4"
              placeholder="请输入温馨提示"
            />
          </el-form-item>

          <el-form-item label="在线用户显示设置">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="主播" label-width="80px">
                  <el-switch
                    v-model="formData.anchorShowUserCount"
                    inline-prompt
                    active-text="开"
                    inactive-text="关"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="助理" label-width="80px">
                  <el-switch
                    v-model="formData.assistantShowUserCount"
                    inline-prompt
                    active-text="开"
                    inactive-text="关"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="销售" label-width="80px">
                  <el-switch
                    v-model="formData.saleShowUserCount"
                    inline-prompt
                    active-text="开"
                    inactive-text="关"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用户" label-width="80px">
                  <el-switch
                    v-model="formData.userShowUserCount"
                    inline-prompt
                    active-text="开"
                    inactive-text="关"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form-item>
          <el-row :gutter="20" style="margin: 0">
            <el-col :span="12">
              <el-form-item label="直播间默认字号" prop="fontSize">
                <el-select v-model="formData.fontSize" placeholder="请选择字号">
                  <el-option label="14px" value="14" />
                  <el-option label="15px" value="15" />
                  <el-option label="16px" value="16" />
                  <el-option label="17px" value="17" />
                  <el-option label="18px" value="18" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否允许普通用户回复" label-width="160px" prop="fontSize">
                <el-switch v-model="formData.userReply" :active-value="1" :inactive-value="0" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="直播暂停自动结束" prop="stopLiveMinutes">
            <el-input-number
              v-model="formData.stopLiveMinutes"
              :min="1"
              :max="999"
              placeholder="请输入分钟数"
            />
            <span style="margin-left: 10px">分钟</span>
          </el-form-item>
          <div class="el-form-item-msg">当直播间暂停时长等于该值，则自动结束</div>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import LiveRoomApi, {
  CloneRoomDTO,
  LiveRoomDto,
  LiveRoomPageDto,
  LiveRoomPageVo,
} from "@/api/live/liveRoom";
import { usePage } from "@/utils/commonSetup";

const tabIndex = ref<string>("clone");
const dialog = reactive({
  visible: false,
  title: "",
});

interface ExtendedLiveRoomPageVo extends LiveRoomPageVo {
  loading?: boolean;
}

const { page, getPage } = usePage<LiveRoomPageDto, ExtendedLiveRoomPageVo>(
  new LiveRoomPageDto(),
  LiveRoomApi.page,
  false
);
const cloneFormData = ref<CloneRoomDTO>(new CloneRoomDTO());
const formData = ref(new LiveRoomDto());
const editFormRef = ref();
const emits = defineEmits(["success"]);
const loading = ref(false);
const popoverVisible = ref(false);
const rules = {
  selectRoomName: [{ required: true, message: "请选择复用直播间", trigger: "change" }],
  coverUrl: [{ required: true, message: "请上传封面图", trigger: "blur" }],
  roomName: [{ required: true, message: "请输入直播间名称", trigger: "blur" }],
};

function handleCurrentChange(_row: LiveRoomPageVo) {
  cloneFormData.value.selectRoomName = _row.roomName;
  cloneFormData.value.roomId = String(_row.id);
  popoverVisible.value = false;
}

function handleSubmit() {
  editFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      if (tabIndex.value === "clone") {
        LiveRoomApi.cloneRoom(cloneFormData.value)
          .then(() => {
            ElMessage.success("保存成功");
            dialog.visible = false;
            emits("success");
          })
          .finally(() => (loading.value = false));
      } else {
        LiveRoomApi.save(formData.value.toAPI() as any)
          .then(() => {
            ElMessage.success("保存成功");
            dialog.visible = false;
            emits("success");
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

defineExpose({
  open: () => {
    popoverVisible.value = false;
    formData.value = new LiveRoomDto();
    cloneFormData.value = new CloneRoomDTO();
    getPage();
    dialog.title = "新增直播间";
    dialog.visible = true;
  },
});
</script>

<style lang="scss" scoped>
.new-body {
  padding: 10px;
  border: 1px solid var(--el-border-color);
  margin-top: 10px;

  &-title {
    font-weight: bold;
    margin-bottom: 10px;
  }
}
</style>
