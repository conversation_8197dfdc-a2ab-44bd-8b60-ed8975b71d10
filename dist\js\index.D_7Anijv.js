import{d as e,r as a,aQ as t,g as o,f as l,C as s,m as r,w as i,Z as n,i as d,P as p,Q as m,e as u,$ as c,h as f,F as _,V as g,a_ as y,az as j}from"./index.Dk5pbsTU.js";import{v as w}from"./el-loading.Dqi-qL7c.js";import{E as v}from"./el-card.DwLhVNHW.js";import k from"./index.Cywy93e7.js";import{a as x,E as h}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{_ as b}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as V}from"./el-switch.kQ5v4arH.js";/* empty css               */import{a as q,E as C}from"./el-form-item.Bw6Zyv_7.js";import{E}from"./el-button.CXI119n4.js";import{E as U,a as K}from"./el-select.CRWkm-it.js";import{_ as S}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as T}from"./el-input.DiGatoux.js";import{_ as P}from"./edit.vue_vue_type_script_setup_true_lang.Ddi9qyBq.js";import{a as N,u as R}from"./commonSetup.Dm-aByKQ.js";import{a as B,A as I,K as L,b as O}from"./knowledgeType.DrndLWHa.js";/* empty css                       */import"./el-overlay.DpVCS8zG.js";import{E as z}from"./index.BcMfjWDS.js";import"./el-pagination.C5FHY27u.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./event.BwRzfsZt.js";import"./index.DuiNpp1i.js";import"./error.D_Dr4eZ1.js";import"./index.ybpLT-bz.js";import"./index.C9UdVphc.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./validator.HGn2BZtD.js";import"./el-radio.w2rep3_A.js";import"./el-drawer.Df_TzNjH.js";import"./el-col.Cfu8vZQ4.js";import"./index.vue_vue_type_script_setup_true_lang.BOVTspW2.js";import"./index.WzKGworL.js";import"./index.DFyomGhz.js";const $={class:"app-container"},A={class:"search-bar"},M={key:0,style:{color:"var(--el-color-danger)"}},Q={class:"mb-10px"},F={key:0},W=e({name:"MaskKnowledgeType",__name:"index",setup(e){const W=a(),{sendInfo:G,onSend:H}=N(B.allKnowledgeType,new L);H();const{page:J,getPage:Z,resetQuery:D}=R(new O,B.aiKnowledgePage);return(e,a)=>{const N=T,R=q,L=S,O=K,H=U,Y=E,X=C,ee=h,ae=V,te=b,oe=x,le=k,se=v,re=t("hasPerm"),ie=w;return l(),o("div",$,[s("div",A,[r(X,{ref:"queryFormRef",model:d(J).query,inline:!0},{default:i((()=>[r(R,{prop:"keywords",label:""},{default:i((()=>[r(N,{modelValue:d(J).query.keyWord,"onUpdate:modelValue":a[0]||(a[0]=e=>d(J).query.keyWord=e),placeholder:"类型名称检索",clearable:"",onKeyup:n(d(Z),["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),r(R,{prop:"status",label:"启用状态"},{default:i((()=>[r(L,{modelValue:d(J).query.status,"onUpdate:modelValue":a[1]||(a[1]=e=>d(J).query.status=e),code:"disable_status"},null,8,["modelValue"])])),_:1}),r(R,{prop:"fileType",label:"文件类型"},{default:i((()=>[r(H,{modelValue:d(J).query.typeId,"onUpdate:modelValue":a[2]||(a[2]=e=>d(J).query.typeId=e),clearable:"",style:{width:"200px"}},{default:i((()=>[(l(!0),o(p,null,m(d(G).data,(e=>(l(),u(O,{key:e.id,value:String(e.id)},{default:i((()=>[c(_(e.name)+" ",1),e.status?f("",!0):(l(),o("span",M,"（已停用）"))])),_:2},1032,["value"])))),128))])),_:1},8,["modelValue"])])),_:1}),r(R,null,{default:i((()=>[r(Y,{type:"primary",icon:"search",onClick:d(Z)},{default:i((()=>a[7]||(a[7]=[c("搜索")]))),_:1,__:[7]},8,["onClick"]),r(Y,{icon:"refresh",onClick:d(D)},{default:i((()=>a[8]||(a[8]=[c("重置")]))),_:1,__:[8]},8,["onClick"])])),_:1})])),_:1},8,["model"])]),r(se,{shadow:"never"},{default:i((()=>[s("div",Q,[g((l(),u(Y,{type:"success",icon:"plus",onClick:a[3]||(a[3]=e=>{var a;return null==(a=d(W))?void 0:a.open()})},{default:i((()=>a[9]||(a[9]=[c(" 新增 ")]))),_:1,__:[9]})),[[re,["sys:aimask:knowledge:file:addOrEdit"]]])]),g((l(),u(oe,{ref:"dataTableRef",data:d(J).data.records,"highlight-current-row":"",border:""},{default:i((()=>[r(ee,{label:"序号",align:"center",width:"55",type:"index"}),r(ee,{label:"文件名称",align:"center",prop:"fileName","min-width":"120"}),r(ee,{label:"文件类型",align:"center",prop:"typeName","min-width":"120"},{default:i((({row:e})=>[c(_(e.typeName)+" ",1),e.typeStatus?f("",!0):(l(),o("span",F,a[10]||(a[10]=[c(" （ "),s("span",{style:{color:"var(--el-color-danger)"}},"已停用",-1),c(" ） ")])))])),_:1}),r(ee,{label:"启用状态",align:"center",width:"120"},{default:i((({row:e})=>[d(y)("sys:aimask:knowledge:file:addOrEdit")?(l(),u(ae,{key:0,modelValue:e.status,"onUpdate:modelValue":a=>e.status=a,"inline-prompt":"","active-text":"是","inactive-text":"否","inactive-value":0,"active-value":1,loading:e.statusLoading,"before-change":()=>{return a=e,new Promise(((e,t)=>{z.confirm(`确定要${a.status?"停用":"启用"}知识库文件《${a.fileName}》吗？`,(a.status?"停用":"启用")+"知识库文件",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((()=>{const o=new I(a);o.status?o.status=0:o.status=1,a.statusLoading=!0,B.aiKnowledgeSave(o).then((()=>{j.success("操作成功"),e(!1),D()})).finally((()=>{a.statusLoading=!1,t(!1)}))})).catch((()=>{j.info("已取消"),t(!1)}))}));var a}},null,8,["modelValue","onUpdate:modelValue","loading","before-change"])):(l(),u(te,{key:1,modelValue:e.status,"onUpdate:modelValue":a=>e.status=a,code:"disable_status"},null,8,["modelValue","onUpdate:modelValue"]))])),_:1}),r(ee,{fixed:"right",align:"center",label:"操作",width:"180"},{default:i((e=>[g((l(),u(Y,{type:"warning",size:"small",link:"",loading:e.row.loading,onClick:a=>{var t;return null==(t=d(W))?void 0:t.open(e.row)}},{default:i((()=>a[11]||(a[11]=[c(" 编辑 ")]))),_:2,__:[11]},1032,["loading","onClick"])),[[re,["sys:aimask:knowledge:file:addOrEdit"]]]),!e.row.isChoose&&d(y)("sys:aimask:knowledge:file:delete")?(l(),u(Y,{key:0,type:"danger",size:"small",link:"",loading:e.row.loading,onClick:a=>{return t=e.row,void z.confirm(`确定删除知识库文件《${t.typeName}》吗？`,"删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{t.loading=!0,B.removeKnowledge(t.id).then((()=>{j.success("删除成功"),D()})).finally((()=>t.loading=!1))})).catch((()=>j.info("已取消")));var t}},{default:i((()=>a[12]||(a[12]=[c(" 删除 ")]))),_:2,__:[12]},1032,["loading","onClick"])):f("",!0)])),_:1})])),_:1},8,["data"])),[[ie,d(J).loading]]),d(J).data.totalRow?(l(),u(le,{key:0,total:d(J).data.totalRow,"onUpdate:total":a[4]||(a[4]=e=>d(J).data.totalRow=e),page:d(J).query.pageNum,"onUpdate:page":a[5]||(a[5]=e=>d(J).query.pageNum=e),limit:d(J).query.pageSize,"onUpdate:limit":a[6]||(a[6]=e=>d(J).query.pageSize=e),onPagination:d(Z)},null,8,["total","page","limit","onPagination"])):f("",!0)])),_:1}),r(P,{ref_key:"editModelRef",ref:W,onSuccess:d(D)},null,8,["onSuccess"])])}}});export{W as default};
