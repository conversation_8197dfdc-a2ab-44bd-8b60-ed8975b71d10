<script setup lang="ts">
import SessionList from "./components/SessionList.vue";
import livePreview, { PagePreviewSessionVO, PreviewSessionInfoVO } from "@/api/live/livePreview";
import { useApi } from "@/utils/commonSetup";
import { dayjs } from "element-plus";
import SessionInfo from "@/views/live/livePreview/components/SessionInfo.vue";

const { sendInfo, onSend } = useApi<number, PreviewSessionInfoVO>(livePreview.info as any);
const selectInfo = ref<PagePreviewSessionVO>({} as PagePreviewSessionVO);
const timerStr = computed(() => {
  if (!sendInfo.data?.actualStartTime) {
    return "-";
  }
  const start = dayjs(sendInfo.data?.actualStartTime);
  const end = dayjs();
  const totalSeconds = end.diff(start, "second");

  // 计算各个时间单位
  const days = Math.floor(totalSeconds / (3600 * 24));
  const hours = Math.floor((totalSeconds % (3600 * 24)) / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  // 按条件构建时间字符串
  const parts = [];
  if (days > 0) parts.push(`${days}天`);
  if (hours > 0) parts.push(`${hours}小时`);
  if (minutes > 0) parts.push(`${minutes}分`);
  if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`);

  return parts.join("");
});

function setSelectId(id: number) {
  sendInfo.params = id;
  onSend();
}

watch(
  () => selectInfo.value.id,
  (_newVal: number) => {
    if (_newVal) {
      setSelectId(_newVal);
    }
  },
  { deep: true }
);
</script>

<template>
  <el-container class="live-preview">
    <el-aside class="live-preview-aside">
      <session-list v-model="selectInfo" />
    </el-aside>
    <el-main
      v-loading="sendInfo.loading"
      element-loading-text="加载中..."
      class="live-preview-main"
    >
      <el-container style="padding: 0; height: 100%">
        <el-header class="base-info">
          <div class="base-info-item" style="flex: 1">
            <span>助理：</span>
            <span
              v-for="(item, index) in sendInfo.data?.assist || []"
              :key="index"
              class="user-item"
            >
              <el-icon
                size="12"
                style="
                  color: var(--el-color-primary);
                  margin-right: 2px;
                  transform: translateY(2px);
                "
              >
                <Avatar />
              </el-icon>
              {{ item }}
            </span>
          </div>
          <div class="base-info-item">
            <span>开播时长：</span>
            <span style="color: var(--el-color-primary)">{{ timerStr }}</span>
            <span style="margin-left: 15px">{{ sendInfo.data?.actualStartTime }}开播</span>
          </div>
          <div class="base-info-item" style="display: flex; align-items: center">
            <span>直播观众：</span>
            <div class="base-info-item-tag">{{ sendInfo.data?.watchCount }}</div>
          </div>
          <div class="base-info-item" style="display: flex; align-items: center">
            <span>直播间销售：</span>
            <div class="base-info-item-tag" style="background: #42c858">
              {{ sendInfo.data?.watchCount }}
            </div>
          </div>
        </el-header>
        <el-main style="padding: 0; height: 100%">
          <el-container style="height: 100%; width: 100%; padding: 0">
            <el-main style="height: 100%; width: 100%; padding: 0">
              <session-info :info="selectInfo" />
            </el-main>
            <el-aside style="padding: 0; width: 399px; overflow: hidden">
              <iframe
                v-if="sendInfo.data?.h5Url"
                :src="sendInfo.data?.h5Url"
                width="100%"
                height="100%"
                frameborder="0"
                allowfullscreen
              />
            </el-aside>
          </el-container>
        </el-main>
      </el-container>
    </el-main>
  </el-container>
</template>

<style scoped lang="scss">
.base-info {
  padding: 23px 31px;
  display: flex;
  height: auto;
  align-items: flex-start;
  justify-content: space-between;
  border-bottom: 1px solid var(--el-border-color);
  gap: 20px;

  &-item {
    font-weight: 400;
    font-size: 14px;
    color: #666666;

    &-tag {
      background: #ff7d21;
      height: 17px;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      line-height: 18px;
      padding: 0 6px;
      border-radius: 9px;
    }

    .user-item {
      margin-right: 24px;
    }
  }
}

.live-preview {
  width: 100%;
  height: 100%;
  background: #f8f9fa;

  &-aside {
    width: 196px;
    padding: 0;
    height: 100%;
    border-right: 1px solid #e4e7ed;
  }

  &-main {
    background: #ffffff;
    padding: 0;
    height: 100%;
    width: 100%;
  }
}
</style>
