import{d as e,r as l,S as t,o as a,aQ as i,g as o,f as r,C as s,m as n,w as u,Z as p,i as d,$ as m,V as c,e as _,h as f,F as y,ak as v,P as g,Q as b,aO as h,az as j}from"./index.Dk5pbsTU.js";import{v as w}from"./el-loading.Dqi-qL7c.js";import{_ as k,N as V}from"./NoticeDetail.Bi21lh4O.js";import{E as x}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as U,a as C}from"./el-radio.w2rep3_A.js";import{_ as S}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{_ as T}from"./index.vue_vue_type_script_setup_true_lang.BOVTspW2.js";import{E}from"./el-card.DwLhVNHW.js";import z from"./index.Cywy93e7.js";import{a as N,E as F}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     *//* empty css               */import{_ as I}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{a as q,E as O}from"./el-form-item.Bw6Zyv_7.js";import{E as P}from"./el-button.CXI119n4.js";import{E as R,a as A}from"./el-select.CRWkm-it.js";import{E as B}from"./el-input.DiGatoux.js";/* empty css                       */import{E as D}from"./index.L2DVy5yq.js";import{E as L}from"./index.BcMfjWDS.js";import"./el-descriptions-item.BlvmJIy_.js";import"./vnode.Cbclzz8S.js";import"./use-form-common-props.CQPDkY7k.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./use-form-item.DzRJVC1I.js";import"./index.WzKGworL.js";import"./el-pagination.C5FHY27u.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.ybpLT-bz.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./index.Vn8pbgQR.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./validator.HGn2BZtD.js";const K={class:"app-container"},Q={class:"search-bar"},H={class:"flex-x-start"},J={key:0,class:"flex-x-start"},M={key:1,class:"flex-x-start"},Z={class:"dialog-footer"},$=e({name:"Notice",inheritAttrs:!1,__name:"index",setup(e){const $=l(),G=l(),Y=l(),W=l(!1),X=l([]),ee=l(0),le=t({pageNum:1,pageSize:10}),te=l([]),ae=l([]),ie=t({title:"",visible:!1}),oe=t({level:"L",targetType:1}),re=t({title:[{required:!0,message:"请输入通知标题",trigger:"blur"}],content:[{required:!0,message:"请输入通知内容",trigger:"blur",validator:(e,l,t)=>{l.replace(/<[^>]+>/g,"").trim()?t():t(new Error("请输入通知内容"))}}],type:[{required:!0,message:"请选择通知类型",trigger:"change"}]});function se(){W.value=!0,V.getPage(le).then((e=>{ae.value=e.list,ee.value=e.total})).finally((()=>{W.value=!1}))}function ne(){$.value.resetFields(),le.pageNum=1,se()}function ue(e){X.value=e.map((e=>e.id))}function pe(e){h.getOptions().then((e=>{te.value=e})),ie.visible=!0,e?(ie.title="修改公告",V.getFormData(e).then((e=>{Object.assign(oe,e)}))):(Object.assign(oe,{level:0,targetType:0}),ie.title="新增公告")}function de(){ie.visible=!1,G.value.resetFields(),G.value.clearValidate(),oe.id=void 0,oe.targetType=1}function me(e){const l=[e||X.value].join(",");l?L.confirm("确认删除已选中的数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{W.value=!0,V.deleteByIds(l).then((()=>{j.success("删除成功"),ne()})).finally((()=>W.value=!1))}),(()=>{j.info("已取消删除")})):j.warning("请勾选删除项")}return a((()=>{se()})),(e,l)=>{const t=B,a=q,h=A,L=R,ce=P,_e=O,fe=F,ye=I,ve=D,ge=N,be=z,he=E,je=T,we=S,ke=C,Ve=U,xe=x,Ue=k,Ce=i("hasPerm"),Se=w;return r(),o("div",K,[s("div",Q,[n(_e,{ref_key:"queryFormRef",ref:$,model:d(le),inline:!0,"label-suffix":":"},{default:u((()=>[n(a,{label:"标题",prop:"title"},{default:u((()=>[n(t,{modelValue:d(le).title,"onUpdate:modelValue":l[0]||(l[0]=e=>d(le).title=e),placeholder:"标题",clearable:"",onKeyup:l[1]||(l[1]=p((e=>se()),["enter"]))},null,8,["modelValue"])])),_:1}),n(a,{label:"发布状态",prop:"publishStatus"},{default:u((()=>[n(L,{modelValue:d(le).publishStatus,"onUpdate:modelValue":l[2]||(l[2]=e=>d(le).publishStatus=e),class:"!w-[100px]",clearable:"",placeholder:"全部"},{default:u((()=>[n(h,{value:0,label:"未发布"}),n(h,{value:1,label:"已发布"}),n(h,{value:-1,label:"已撤回"})])),_:1},8,["modelValue"])])),_:1}),n(a,null,{default:u((()=>[n(ce,{type:"primary",icon:"search",onClick:l[3]||(l[3]=e=>se())},{default:u((()=>l[20]||(l[20]=[m("搜索")]))),_:1,__:[20]}),n(ce,{icon:"refresh",onClick:l[4]||(l[4]=e=>ne())},{default:u((()=>l[21]||(l[21]=[m("重置")]))),_:1,__:[21]})])),_:1})])),_:1},8,["model"])]),n(he,{shadow:"never",class:"table-wrapper"},{header:u((()=>[c((r(),_(ce,{type:"success",icon:"plus",onClick:l[5]||(l[5]=e=>pe())},{default:u((()=>l[22]||(l[22]=[m(" 新增通知 ")]))),_:1,__:[22]})),[[Ce,["sys:notice:add"]]]),c((r(),_(ce,{type:"danger",disabled:0===d(X).length,icon:"delete",onClick:l[6]||(l[6]=e=>me())},{default:u((()=>l[23]||(l[23]=[m(" 删除 ")]))),_:1,__:[23]},8,["disabled"])),[[Ce,["sys:notice:delete"]]])])),default:u((()=>[c((r(),_(ge,{ref:"dataTableRef",data:d(ae),"highlight-current-row":"",onSelectionChange:ue},{default:u((()=>[n(fe,{type:"selection",width:"55",align:"center"}),n(fe,{type:"index",label:"序号",width:"60"}),n(fe,{label:"通知标题",prop:"title","min-width":"200"}),n(fe,{align:"center",label:"通知类型",width:"150"},{default:u((e=>[n(ye,{modelValue:e.row.type,"onUpdate:modelValue":l=>e.row.type=l,code:"notice_type"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),n(fe,{align:"center",label:"发布人",prop:"publisherName",width:"150"}),n(fe,{align:"center",label:"通知等级",width:"100"},{default:u((e=>[n(ye,{modelValue:e.row.level,"onUpdate:modelValue":l=>e.row.level=l,code:"notice_level"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),n(fe,{align:"center",label:"通告目标类型",prop:"targetType","min-width":"100"},{default:u((e=>[1==e.row.targetType?(r(),_(ve,{key:0,type:"warning"},{default:u((()=>l[24]||(l[24]=[m("全体")]))),_:1,__:[24]})):f("",!0),2==e.row.targetType?(r(),_(ve,{key:1,type:"success"},{default:u((()=>l[25]||(l[25]=[m("指定")]))),_:1,__:[25]})):f("",!0)])),_:1}),n(fe,{align:"center",label:"发布状态","min-width":"100"},{default:u((e=>[0==e.row.publishStatus?(r(),_(ve,{key:0,type:"info"},{default:u((()=>l[26]||(l[26]=[m("未发布")]))),_:1,__:[26]})):f("",!0),1==e.row.publishStatus?(r(),_(ve,{key:1,type:"success"},{default:u((()=>l[27]||(l[27]=[m("已发布")]))),_:1,__:[27]})):f("",!0),-1==e.row.publishStatus?(r(),_(ve,{key:2,type:"warning"},{default:u((()=>l[28]||(l[28]=[m("已撤回")]))),_:1,__:[28]})):f("",!0)])),_:1}),n(fe,{label:"操作时间",width:"250"},{default:u((e=>[s("div",H,[l[29]||(l[29]=s("span",null,"创建时间：",-1)),s("span",null,y(e.row.createdAt||"-"),1)]),1===e.row.publishStatus?(r(),o("div",J,[l[30]||(l[30]=s("span",null,"发布时间：",-1)),s("span",null,y(e.row.publishTime||"-"),1)])):-1===e.row.publishStatus?(r(),o("div",M,[l[31]||(l[31]=s("span",null,"撤回时间：",-1)),s("span",null,y(e.row.revokeTime||"-"),1)])):f("",!0)])),_:1}),n(fe,{align:"center",fixed:"right",label:"操作",width:"150"},{default:u((e=>[n(ce,{type:"primary",size:"small",link:"",onClick:l=>{return t=e.row.id,void Y.value.openNotice(t);var t}},{default:u((()=>l[32]||(l[32]=[m(" 查看 ")]))),_:2,__:[32]},1032,["onClick"]),1!=e.row.publishStatus?c((r(),_(ce,{key:0,type:"primary",size:"small",link:"",onClick:l=>{return t=e.row.id,void V.publish(t).then((()=>{j.success("发布成功"),se()}));var t}},{default:u((()=>l[33]||(l[33]=[m(" 发布 ")]))),_:2,__:[33]},1032,["onClick"])),[[Ce,["sys:notice:publish"]]]):f("",!0),1==e.row.publishStatus?c((r(),_(ce,{key:1,type:"primary",size:"small",link:"",onClick:l=>{return t=e.row.id,void V.revoke(t).then((()=>{j.success("撤回成功"),se()}));var t}},{default:u((()=>l[34]||(l[34]=[m(" 撤回 ")]))),_:2,__:[34]},1032,["onClick"])),[[Ce,["sys:notice:revoke"]]]):f("",!0),1!=e.row.publishStatus?c((r(),_(ce,{key:2,type:"primary",size:"small",link:"",onClick:l=>pe(e.row.id)},{default:u((()=>l[35]||(l[35]=[m(" 编辑 ")]))),_:2,__:[35]},1032,["onClick"])),[[Ce,["sys:notice:edit"]]]):f("",!0),1!=e.row.publishStatus?c((r(),_(ce,{key:3,type:"danger",size:"small",link:"",onClick:l=>me(e.row.id)},{default:u((()=>l[36]||(l[36]=[m(" 删除 ")]))),_:2,__:[36]},1032,["onClick"])),[[Ce,["sys:notice:delete"]]]):f("",!0)])),_:1})])),_:1},8,["data"])),[[Se,d(W)]]),d(ee)>0?(r(),_(be,{key:0,total:d(ee),"onUpdate:total":l[7]||(l[7]=e=>v(ee)?ee.value=e:null),page:d(le).pageNum,"onUpdate:page":l[8]||(l[8]=e=>d(le).pageNum=e),limit:d(le).pageSize,"onUpdate:limit":l[9]||(l[9]=e=>d(le).pageSize=e),onPagination:l[10]||(l[10]=e=>se())},null,8,["total","page","limit"])):f("",!0)])),_:1}),n(xe,{modelValue:d(ie).visible,"onUpdate:modelValue":l[19]||(l[19]=e=>d(ie).visible=e),title:d(ie).title,top:"4vh",width:"1250",onClose:de},{footer:u((()=>[s("div",Z,[n(ce,{type:"primary",onClick:l[17]||(l[17]=e=>{G.value.validate((e=>{if(e){W.value=!0;const e=oe.id;e?V.update(e,oe).then((()=>{j.success("修改成功"),de(),ne()})).finally((()=>W.value=!1)):V.add(oe).then((()=>{j.success("新增成功"),de(),ne()})).finally((()=>W.value=!1))}}))})},{default:u((()=>l[39]||(l[39]=[m("确定")]))),_:1,__:[39]}),n(ce,{onClick:l[18]||(l[18]=e=>de())},{default:u((()=>l[40]||(l[40]=[m("取消")]))),_:1,__:[40]})])])),default:u((()=>[n(_e,{ref_key:"dataFormRef",ref:G,model:d(oe),rules:d(re),"label-width":"100px"},{default:u((()=>[n(a,{label:"通知标题",prop:"title"},{default:u((()=>[n(t,{modelValue:d(oe).title,"onUpdate:modelValue":l[11]||(l[11]=e=>d(oe).title=e),placeholder:"通知标题",clearable:""},null,8,["modelValue"])])),_:1}),n(a,{label:"通知内容",prop:"content"},{default:u((()=>[n(je,{modelValue:d(oe).content,"onUpdate:modelValue":l[12]||(l[12]=e=>d(oe).content=e)},null,8,["modelValue"])])),_:1}),n(a,{label:"通知类型",prop:"type"},{default:u((()=>[n(we,{modelValue:d(oe).type,"onUpdate:modelValue":l[13]||(l[13]=e=>d(oe).type=e),code:"notice_type"},null,8,["modelValue"])])),_:1}),n(a,{label:"通知等级",prop:"level"},{default:u((()=>[n(we,{modelValue:d(oe).level,"onUpdate:modelValue":l[14]||(l[14]=e=>d(oe).level=e),code:"notice_level"},null,8,["modelValue"])])),_:1}),n(a,{label:"目标类型",prop:"targetType"},{default:u((()=>[n(Ve,{modelValue:d(oe).targetType,"onUpdate:modelValue":l[15]||(l[15]=e=>d(oe).targetType=e)},{default:u((()=>[n(ke,{value:1},{default:u((()=>l[37]||(l[37]=[m("全体")]))),_:1,__:[37]}),n(ke,{value:2},{default:u((()=>l[38]||(l[38]=[m("指定")]))),_:1,__:[38]})])),_:1},8,["modelValue"])])),_:1}),2==d(oe).targetType?(r(),_(a,{key:0,label:"指定用户",prop:"targetUserIds"},{default:u((()=>[n(L,{modelValue:d(oe).targetUserIds,"onUpdate:modelValue":l[16]||(l[16]=e=>d(oe).targetUserIds=e),multiple:"",search:"",placeholder:"请选择指定用户"},{default:u((()=>[(r(!0),o(g,null,b(d(te),(e=>(r(),_(h,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})):f("",!0)])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),n(Ue,{ref_key:"noticeDetailRef",ref:Y},null,512)])}}});export{$ as default};
