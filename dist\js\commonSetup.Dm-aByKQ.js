import{S as a,o as n}from"./index.Dk5pbsTU.js";function e(e,t,o=!0){const r=a({loading:!1,data:{records:[],pageNumber:0,pageSize:20,totalPage:0,totalRow:0},query:JSON.parse(JSON.stringify(e))});function i(){r.loading=!0,t(r.query).then((a=>{r.data=a})).finally((()=>{r.loading=!1}))}return n((()=>{o&&i()})),{page:r,getPage:i,resetQuery:function(){r.query=JSON.parse(JSON.stringify(e)),i()}}}function t(n,e,t){const o=a({loading:!1,data:void 0,params:e});return{sendInfo:o,onSend:function(){return o.loading=!0,new Promise(((a,e)=>{n(o.params).then((n=>{o.data=n,t&&t(n),a(n)})).catch((a=>{e(a)})).finally((()=>{o.loading=!1}))}))}}}export{t as a,e as u};
