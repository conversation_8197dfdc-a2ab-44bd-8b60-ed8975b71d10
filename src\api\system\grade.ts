import request from "@/utils/request";

// 分页查询
export class GradeUserPageQuery implements PageQuery {
  pageNum = 1;
  pageSize = 10;
  // 等级名称
  level: string = "";
  // 开始时间
  startTime: string = "";
  // 结束时间
  endTime: string = "";
}

export class PointsPageQueryDto {
  pageNum = 1;
  pageSize = 10;
  // 类型
  type: string = "";
  // 开始时间
  startTime: string = "";
  // 结束时间
  endTime: string = "";
  id: number = 0;
}

export interface GradeUserVo {
  id: number; // 用户ID
  currentLevel: string; // 用户等级
  currentPoints: number; // 用户积分
  nickname: string; // 用户名称
  avatar: string; // 用户头像
  updatedAt: string; // 最近登录时间
}

export interface GradeConfigVo {
  id: number; // ID
  tenantId: number; // 租户id
  editUserId: number; // 编辑者的租户用户id
  startPoints: number; // 区间开始积分(含)
  endPoints: number; // 区间结束积分(不含)
  levelName: string; // 等级名称
}

export class GradeConfigLevelDto {
  // 	区间开始积分(含)
  startPoints: string = "";
  // 区间结束积分(不含)
  endPoints: string = "";
  // 等级名称
  levelName: string = "";

  constructor(e?: GradeConfigVo) {
    if (e) {
      this.startPoints = e.startPoints.toString();
      this.endPoints = (e.endPoints || "").toString();
      this.levelName = e.levelName;
    }
  }
}

export interface PointRecordVO {
  id: number; // 积分记录ID
  appTenantUserId: number; // 用户id
  tenantId: number; // 租户ID
  changePoints: number; // 积分变化,正为获取/负为消耗
  reason: string; // 变化原因
  beforeChange: number; // 变化前积分
  afterChange: number; // 变化后积分
  completeTime: string; // 入账时间
  remark: string; // 备注
  relationId: string; // 加分关联业务id或其他标识
  type: string; // 操作类型标识
}

const GradeApi = {
  /** 配置列表*/
  configList() {
    return request<any, GradeConfigVo[]>({
      url: `/grade/config`,
      method: "get",
    });
  },
  /**
   * 编辑配置
   *
   * @param list 列表
   */
  editConfig(list: GradeConfigLevelDto[]) {
    return request<any, null>({
      url: `/grade/edit/config`,
      method: "put",
      data: { leveConfigs: list },
    });
  },

  /**
   * 会员总览
   * @param data 查询
   */
  userList(data: GradeUserPageQuery) {
    return request<GradeUserPageQuery, any>({
      url: `/grade/userList`,
      method: "get",
      params: data,
    });
  },
  /*积分明细*/
  pointsPage(data: PointsPageQueryDto) {
    return request<PointsPageQueryDto, PageResult<PointRecordVO[]>>({
      url: `/grade/detail`,
      method: "get",
      params: data,
    });
  },
  /*积分类型列表*/
  pointTypeList() {
    return request<any, SelectVo[]>({
      url: `/grade/point/type`,
      method: "get",
    });
  },
};

export default GradeApi;
