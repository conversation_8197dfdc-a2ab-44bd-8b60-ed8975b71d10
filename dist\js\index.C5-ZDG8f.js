import{d as e,r as l,S as a,o,aQ as t,g as i,f as r,C as s,m as n,w as p,Z as d,i as m,$ as u,V as f,e as c,h as _,db as g,ak as j,az as y}from"./index.Dk5pbsTU.js";import{v as h}from"./el-loading.Dqi-qL7c.js";import{E as v}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{_ as b}from"./index.vue_vue_type_script_setup_true_lang.BOVTspW2.js";import{E as V,a as x}from"./el-col.Cfu8vZQ4.js";import{_ as k}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as w}from"./el-card.DwLhVNHW.js";import C from"./index.Cywy93e7.js";import{a as U,E}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{_ as N}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";/* empty css               */import{a as K,E as q}from"./el-form-item.Bw6Zyv_7.js";import{E as z}from"./el-button.CXI119n4.js";import{E as F}from"./el-input.DiGatoux.js";import{C as S}from"./config.DWdYQVGn.js";/* empty css                       */import{E as I}from"./index.BcMfjWDS.js";import{_ as P}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./index.WzKGworL.js";import"./el-radio.w2rep3_A.js";import"./use-form-common-props.CQPDkY7k.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./use-form-item.DzRJVC1I.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./index.L2DVy5yq.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.Vn8pbgQR.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./el-pagination.C5FHY27u.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./validator.HGn2BZtD.js";const R={class:"app-container"},A={class:"search-bar"},B={class:"mb-10px"},L={class:"dialog-footer"},T=P(e({name:"Config",inheritAttrs:!1,__name:"index",setup(e){const P=l(),T=l(),O=l(!1),D=l([]),H=l(0),J=a({pageNum:1,pageSize:10,keywords:""}),M=l([]),Q=a({title:"",visible:!1}),Z=a({id:void 0,configName:"",configKey:"",configValue:"",remark:"",type:"1"}),$=a({configName:[{required:!0,message:"请输入系统配置名称",trigger:"blur"}],configKey:[{required:!0,message:"请输入系统配置编码",trigger:"blur"}],configValue:[{required:!0,message:"请输入系统配置值",trigger:"blur"}],type:[{required:!0,message:"请选择值类型",trigger:"blur"}]});function G(){O.value=!0,S.getPage(J).then((e=>{M.value=e.list,H.value=e.total})).finally((()=>{O.value=!1}))}function Y(){P.value.resetFields(),J.pageNum=1,G()}function W(e){D.value=e.map((e=>e.id))}function X(e){Q.visible=!0,e?(Q.title="修改系统配置",S.getFormData(e).then((e=>{Object.assign(Z,e)}))):(Q.title="新增系统配置",Z.id=void 0)}const ee=g((()=>{S.refreshCache().then((()=>{y.success("刷新成功")}))}),1e3);function le(){T.value.validate((e=>{if(e){O.value=!0;const e=Z.id;e?S.update(e,Z).then((()=>{y.success("修改成功"),ae(),Y()})).finally((()=>O.value=!1)):S.add(Z).then((()=>{y.success("新增成功"),ae(),Y()})).finally((()=>O.value=!1))}}))}function ae(){Q.visible=!1,T.value.resetFields(),T.value.clearValidate(),Z.id=void 0}return o((()=>{G()})),(e,l)=>{const a=F,o=K,g=z,D=q,oe=E,te=N,ie=U,re=C,se=w,ne=x,pe=k,de=V,me=b,ue=v,fe=t("hasPerm"),ce=h;return r(),i("div",R,[s("div",A,[n(D,{ref_key:"queryFormRef",ref:P,model:m(J),inline:!0},{default:p((()=>[n(o,{label:"关键字",prop:"keywords"},{default:p((()=>[n(a,{modelValue:m(J).keywords,"onUpdate:modelValue":l[0]||(l[0]=e=>m(J).keywords=e),placeholder:"请输入配置键\\配置名称",clearable:"",onKeyup:d(G,["enter"])},null,8,["modelValue"])])),_:1}),n(o,null,{default:p((()=>[n(g,{type:"primary",icon:"search",onClick:G},{default:p((()=>l[12]||(l[12]=[u("搜索")]))),_:1,__:[12]}),n(g,{icon:"refresh",onClick:Y},{default:p((()=>l[13]||(l[13]=[u("重置")]))),_:1,__:[13]})])),_:1})])),_:1},8,["model"])]),n(se,{shadow:"never"},{default:p((()=>[s("div",B,[f((r(),c(g,{type:"success",icon:"plus",onClick:l[1]||(l[1]=e=>X())},{default:p((()=>l[14]||(l[14]=[u(" 新增 ")]))),_:1,__:[14]})),[[fe,["sys:config:add"]]]),f((r(),c(g,{color:"#626aef",icon:"RefreshLeft",onClick:m(ee)},{default:p((()=>l[15]||(l[15]=[u(" 刷新缓存 ")]))),_:1,__:[15]},8,["onClick"])),[[fe,["sys:config:refresh"]]])]),f((r(),c(ie,{ref:"dataTableRef",data:m(M),"highlight-current-row":"",onSelectionChange:W},{default:p((()=>[n(oe,{type:"index",label:"序号",width:"60"}),n(oe,{label:"配置名称",prop:"configName","min-width":"100"}),n(oe,{label:"配置键",prop:"configKey","min-width":"100"}),n(oe,{label:"值类型",width:"100"},{default:p((({row:e})=>[n(te,{modelValue:e.type,"onUpdate:modelValue":l=>e.type=l,code:"sys_config_value_type"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),n(oe,{"show-overflow-tooltip":"","tooltip-options":{"popper-class":"content-popper"},prop:"configValue","min-width":"100"}),n(oe,{key:"remark",label:"描述",prop:"remark","min-width":"100"}),n(oe,{fixed:"right",label:"操作",width:"220"},{default:p((e=>[f((r(),c(g,{type:"primary",size:"small",link:"",icon:"edit",onClick:l=>X(e.row.id)},{default:p((()=>l[16]||(l[16]=[u(" 编辑 ")]))),_:2,__:[16]},1032,["onClick"])),[[fe,["sys:config:update"]]]),f((r(),c(g,{type:"danger",size:"small",link:"",icon:"delete",onClick:l=>{return a=e.row.id,void I.confirm("确认删除该项配置?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{O.value=!0,S.deleteById(a).then((()=>{y.success("删除成功"),Y()})).finally((()=>O.value=!1))}));var a}},{default:p((()=>l[17]||(l[17]=[u(" 删除 ")]))),_:2,__:[17]},1032,["onClick"])),[[fe,["sys:config:delete"]]])])),_:1})])),_:1},8,["data"])),[[ce,m(O)]]),m(H)>0?(r(),c(re,{key:0,total:m(H),"onUpdate:total":l[2]||(l[2]=e=>j(H)?H.value=e:null),page:m(J).pageNum,"onUpdate:page":l[3]||(l[3]=e=>m(J).pageNum=e),limit:m(J).pageSize,"onUpdate:limit":l[4]||(l[4]=e=>m(J).pageSize=e),onPagination:G},null,8,["total","page","limit"])):_("",!0)])),_:1}),n(ue,{modelValue:m(Q).visible,"onUpdate:modelValue":l[11]||(l[11]=e=>m(Q).visible=e),title:m(Q).title,width:"1250",onClose:ae},{footer:p((()=>[s("div",L,[n(g,{type:"primary",onClick:le},{default:p((()=>l[18]||(l[18]=[u("确定")]))),_:1,__:[18]}),n(g,{onClick:ae},{default:p((()=>l[19]||(l[19]=[u("取消")]))),_:1,__:[19]})])])),default:p((()=>[n(D,{ref_key:"dataFormRef",ref:T,model:m(Z),rules:m($),"label-suffix":":","label-width":"100px"},{default:p((()=>[n(de,{gutter:20},{default:p((()=>[n(ne,{span:8},{default:p((()=>[n(o,{label:"配置名称",prop:"configName"},{default:p((()=>[n(a,{modelValue:m(Z).configName,"onUpdate:modelValue":l[5]||(l[5]=e=>m(Z).configName=e),placeholder:"请输入配置名称",maxlength:50},null,8,["modelValue"])])),_:1})])),_:1}),n(ne,{span:8},{default:p((()=>[n(o,{label:"配置键",prop:"configKey"},{default:p((()=>[n(a,{modelValue:m(Z).configKey,"onUpdate:modelValue":l[6]||(l[6]=e=>m(Z).configKey=e),placeholder:"请输入配置键",maxlength:50},null,8,["modelValue"])])),_:1})])),_:1}),n(ne,{span:8},{default:p((()=>[n(o,{label:"值类型",prop:"type"},{default:p((()=>[n(pe,{modelValue:m(Z).type,"onUpdate:modelValue":l[7]||(l[7]=e=>m(Z).type=e),code:"sys_config_value_type"},null,8,["modelValue"])])),_:1})])),_:1}),n(ne,{span:24},{default:p((()=>[n(o,{label:"描述",prop:"remark"},{default:p((()=>[n(a,{modelValue:m(Z).remark,"onUpdate:modelValue":l[8]||(l[8]=e=>m(Z).remark=e),rows:4,maxlength:100,"show-word-limit":"",placeholder:"请输入描述"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(o,{label:"配置值",prop:"configValue"},{default:p((()=>["1"==m(Z).type?(r(),c(a,{key:0,modelValue:m(Z).configValue,"onUpdate:modelValue":l[9]||(l[9]=e=>m(Z).configValue=e),placeholder:"请输入配置值",maxlength:100},null,8,["modelValue"])):"2"===m(Z).type?(r(),c(me,{key:1,modelValue:m(Z).configValue,"onUpdate:modelValue":l[10]||(l[10]=e=>m(Z).configValue=e)},null,8,["modelValue"])):_("",!0)])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-1cbda8c2"]]);export{T as default};
