import{aT as e}from"./index.Dk5pbsTU.js";const t="/api/v1/roles",d={getPage:d=>e({url:`${t}/page`,method:"get",params:d}),getOptions:()=>e({url:`${t}/options`,method:"get"}),getRoleMenuIds:d=>e({url:`${t}/${d}/menuIds`,method:"get"}),updateRoleMenus:(d,o)=>e({url:`${t}/${d}/menus`,method:"put",data:o}),getFormData:d=>e({url:`${t}/${d}/form`,method:"get"}),add:d=>e({url:`${t}`,method:"post",data:d}),update:(d,o)=>e({url:`${t}/${d}`,method:"put",data:o}),deleteByIds:d=>e({url:`${t}/${d}`,method:"delete"})};export{d as R};
