<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px">
    <el-form ref="editFormRef" :model="formData" :rules="rules" label-width="110px">
      <el-form-item label="公司名称" prop="name">
        <el-input
          v-model="formData.name"
          :maxlength="50"
          show-word-limit
          placeholder="请输入公司名称"
        />
      </el-form-item>

      <el-form-item label="公司logo" prop="logo">
        <SingleImageUpload :style="{ width: '100px', height: '100px' }" v-model="formData.logo" />
      </el-form-item>
      <el-form-item label="负责人姓名" prop="nickName">
        <el-input
          v-model="formData.nickName"
          :maxlength="25"
          show-word-limit
          placeholder="请输入负责人姓名"
        />
      </el-form-item>
      <el-form-item label="负责人电话" prop="phone">
        <el-input
          v-model="formData.phone"
          :maxlength="11"
          show-word-limit
          placeholder="请输入负责人电话"
        />
      </el-form-item>
      <el-form-item label="部署方式" prop="deployWay">
        <dict v-model="formData.deployWay" type="radio" code="tenant_deploy_way" />
      </el-form-item>
      <el-form-item label="短信签名" prop="smsSignature">
        <dict
          v-model="formData.smsSignature"
          style="width: 100%"
          :clearable="false"
          code="sms_signature"
        />
      </el-form-item>
      <el-form-item label="可用房间数" prop="maxRoom">
        <el-input-number
          v-model="formData.maxRoom"
          style="width: 100%"
          controls-position="right"
          :min="1"
        />
      </el-form-item>
      <el-form-item label="租户私有域名" prop="privateDomain">
        <el-input v-model="formData.privateDomain" show-word-limit />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import TenantApi, { TenantSaveDto, TenantPageVO } from "@/api/system/tenant";
import { formValidator, isPhone } from "@/utils/validate";

const dialog = reactive({
  visible: false,
  title: "",
});
const formData = ref(new TenantSaveDto());
const editFormRef = ref();
const emits = defineEmits(["success"]);
const loading = ref(false);
const rules = {
  name: [{ required: true, message: "请输入公司名称", trigger: "blur" }],
  maxRoom: [{ required: true, message: "请输入可用房间数", trigger: "blur" }],
  logo: [{ required: true, message: "请上传公司logo", trigger: "blur" }],
  nickName: [{ required: true, message: "请输入负责人名称", trigger: "blur" }],
  smsSignature: [{ required: true, message: "请选择短信签名", trigger: "blur" }],
  phone: [
    { required: true, message: "请输入负责人电话", trigger: "blur" },
    formValidator(isPhone, "手机号错误"),
  ],
  deployWay: [{ required: true, message: "请选择部署方式", trigger: "blur" }],
};

function handleSubmit() {
  editFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      TenantApi.save(formData.value)
        .then(() => {
          ElMessage.success("保存成功");
          dialog.visible = false;
          emits("success");
        })
        .finally(() => (loading.value = false));
    }
  });
}

defineExpose({
  open: (_row?: TenantPageVO) => {
    dialog.visible = true;
    dialog.title = _row?.id ? "编辑授权公司" : "授权新公司";
    formData.value = new TenantSaveDto(_row);
  },
});
</script>

<style scoped lang="scss"></style>
