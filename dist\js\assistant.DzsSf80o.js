var t=Object.defineProperty,e=(e,s,a)=>((e,s,a)=>s in e?t(e,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[s]=a)(e,"symbol"!=typeof s?s+"":s,a);import{aT as s}from"./index.Dk5pbsTU.js";class a{constructor(t){e(this,"id"),e(this,"appUserId",0),e(this,"userName",""),e(this,"nickName",""),e(this,"avatar",""),e(this,"wechatName",""),t&&(t instanceof a?(this.id=t.id,this.appUserId=t.appUserId,this.userName=t.userName,this.nickName=t.nickName,this.avatar=t.avatar):(this.avatar=t.avatar,this.wechatName=t.nickname,this.appUserId=t.appUserId))}}class i{constructor(t){e(this,"id",0),e(this,"status",0),t&&(this.id=t.id,this.status=t.status)}}const r={remove:t=>s({url:`/webcaster/removeAssistant?id=${t}&type=1`,method:"delete"}),changeUserType:t=>s({url:`/webcaster/changeUserType/${t}`,method:"post"}),getUserByPhone:t=>s({url:`/webcaster/getUserByPhone?uids=${t}&type=1`,method:"get"}),save:t=>s({url:"/webcaster/saveAssistant",method:"post",data:{list:t,type:1}}),editStatus:t=>s({url:"/webcaster/editAssistantStatus",method:"post",data:{...t,type:1}})};export{r as A,a as S,i as U};
