var e=Object.defineProperty,a=(a,t,l)=>((a,t,l)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[t]=l)(a,"symbol"!=typeof t?t+"":t,l);import{aT as t,d as l,S as s,r as i,e as r,f as o,w as d,C as n,m as u,g as p,h as m,ak as c,i as v,$ as h,E as f,aR as b,az as g}from"./index.Dk5pbsTU.js";import{E as w}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{a as _,E as y}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{E as U}from"./el-image-viewer.BH897zgF.js";/* empty css               */import{E as j,a as N}from"./el-form-item.Bw6Zyv_7.js";import{E as x}from"./el-button.CXI119n4.js";import{E as k}from"./el-input.DiGatoux.js";import{_ as V}from"./_plugin-vue_export-helper.BCo6x5W8.js";class I{constructor(e){a(this,"id"),a(this,"appUserId",0),a(this,"mobile",""),a(this,"userName",""),a(this,"nickName",""),a(this,"avatar",""),a(this,"wechatName",""),e&&(e instanceof I?(this.id=e.id,this.appUserId=e.appUserId,this.userName=e.userName,this.nickName=e.nickName,this.avatar=e.avatar):(this.avatar=e.avatar,this.wechatName=e.nickname,this.appUserId=e.appUserId,this.userName=e.userName))}}class E{constructor(e){a(this,"id",0),a(this,"status",0),e&&(this.id=e.id,this.status=e.status)}}const C={remove:e=>t({url:`/webcaster/removeAssistant?id=${e}&type=2`,method:"delete"}),getUserByPhone:e=>t({url:`/webcaster/getUserByPhone?uids=${e}&type=2`,method:"get"}),save:e=>t({url:"/webcaster/saveAssistant",method:"post",data:{list:e,type:2}}),editStatus:e=>t({url:"/webcaster/editAssistantStatus",method:"post",data:{...e,type:2}})},S={style:{"text-align":"right",width:"100%"}},P={key:0},A={class:"dialog-footer"},$=V(l({__name:"edit",emits:["success"],setup(e,{expose:a,emit:t}){const l=s({visible:!1,title:"新增销售"}),V=i(""),E=i([]),$=t,B=i(!1);function D(){if(!V.value)return void g.warning("请输入手机号");const e=V.value.replaceAll("，",",");if(e)return B.value=!0,C.getUserByPhone(e).then((e=>{e.forEach((e=>{const a=E.value.findIndex((a=>e.appUserId===a.appUserId)),t=new I(e);a>=0?E.value[a]=t:E.value.push(t)}))})).finally((()=>B.value=!1));g.warning("请输入有效的uid")}function O(){if(0!==E.value.length){for(const e of E.value){if(!e.userName)return void g.warning("请填写姓名");if(!e.nickName)return void g.warning("请填写昵称")}B.value=!0,C.save(E.value).then((()=>{g.success("保存成功"),l.visible=!1,$("success")})).finally((()=>{B.value=!1}))}else g.warning("请至少添加一个销售")}function z(){V.value="",E.value=[]}return a({open:()=>{l.visible=!0,l.title="新增销售",V.value="",E.value=[]}}),(e,a)=>{const t=k,s=N,i=x,g=j,I=y,C=U,$=f,B=_,L=w;return o(),r(L,{modelValue:v(l).visible,"onUpdate:modelValue":a[2]||(a[2]=e=>v(l).visible=e),title:v(l).title,width:"800px",onClosed:z},{footer:d((()=>[n("div",A,[u(i,{onClick:a[1]||(a[1]=e=>v(l).visible=!1)},{default:d((()=>a[5]||(a[5]=[h("取 消")]))),_:1,__:[5]}),u(i,{type:"primary",disabled:0===v(E).length,onClick:O},{default:d((()=>a[6]||(a[6]=[h(" 确认添加 ")]))),_:1,__:[6]},8,["disabled"])])])),default:d((()=>[n("div",null,[u(g,{class:"mb-20px"},{default:d((()=>[u(s,{label:"输入UID",required:""},{default:d((()=>[u(t,{modelValue:v(V),"onUpdate:modelValue":a[0]||(a[0]=e=>c(V)?V.value=e:null),clearable:"",type:"textarea",placeholder:"请输入UID，若多个UID，请用'逗号'隔开"},null,8,["modelValue"])])),_:1}),u(s,null,{default:d((()=>[n("div",S,[u(i,{type:"primary",disabled:!v(V),onClick:D},{default:d((()=>a[3]||(a[3]=[h(" 识别 ")]))),_:1,__:[3]},8,["disabled"])])])),_:1})])),_:1}),v(E).length>0?(o(),p("div",P,[a[4]||(a[4]=n("div",{class:"mb-10px"},"成功识别如下用户信息：",-1)),u(B,{data:v(E),border:""},{default:d((()=>[u(I,{label:"序号",align:"center",width:"55",type:"index"}),u(I,{label:"头像",align:"center",width:"100"},{default:d((({row:e})=>[u(C,{style:{width:"30px",height:"30px","border-radius":"30px"},"preview-src-list":[e.avatar],"preview-teleported":"",src:e.avatar},null,8,["preview-src-list","src"])])),_:1}),u(I,{label:"微信昵称",align:"center",prop:"wechatName","min-width":"120"}),u(I,{label:"姓名",align:"center","min-width":"120"},{default:d((({row:e})=>[u(t,{modelValue:e.userName,"onUpdate:modelValue":a=>e.userName=a,placeholder:"请输入姓名"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),u(I,{label:"销售昵称",align:"center","min-width":"120"},{default:d((({row:e})=>[u(t,{modelValue:e.nickName,"onUpdate:modelValue":a=>e.nickName=a,placeholder:"请输入昵称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),u(I,{label:"操作",align:"center",width:"80"},{default:d((({$index:e})=>[u(i,{type:"danger",circle:"",onClick:a=>{return t=e,void E.value.splice(t,1);var t}},{default:d((()=>[u($,null,{default:d((()=>[u(v(b))])),_:1})])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])):m("",!0)])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-93b5387d"]]),B=Object.freeze(Object.defineProperty({__proto__:null,default:$},Symbol.toStringTag,{value:"Module"}));export{$ as E,C as S,E as U,B as e};
