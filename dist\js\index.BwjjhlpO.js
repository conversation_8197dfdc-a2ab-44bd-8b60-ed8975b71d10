import{d as e,aC as a,am as s,aI as o,ax as r,r as l,ao as t,c as i,o as n,g as p,C as m,m as d,ak as c,i as u,w as g,aJ as f,ah as h,ap as j,f as v,F as _,aw as w,E as x,Z as y,$ as b,j as C,aB as V}from"./index.Dk5pbsTU.js";import{E}from"./el-text.6kaKYQ9U.js";import{E as k,a as K}from"./el-form-item.Bw6Zyv_7.js";import{E as U}from"./el-button.CXI119n4.js";import{E as $}from"./el-link.qHYW6llJ.js";import{E as q}from"./el-checkbox.DDYarIkn.js";import{E as R}from"./el-image-viewer.BH897zgF.js";import"./el-tooltip.l0sNRNKZ.js";import{E as A}from"./el-popper.Dbn4MgsT.js";import{E as I}from"./el-input.DiGatoux.js";import{_ as z}from"./index.vue_vue_type_script_setup_true_lang.ZmSkfcBe.js";import{E as D}from"./el-switch.kQ5v4arH.js";import{_ as L}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./error.D_Dr4eZ1.js";import"./index.D6CER_Ot.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./index.DuiNpp1i.js";import"./use-form-item.DzRJVC1I.js";import"./index.C9UdVphc.js";import"./event.BwRzfsZt.js";import"./isEqual.C0S6DIiJ.js";import"./index.C6NthMtN.js";import"./debounce.DJJTSR8O.js";import"./position.DfR5znly.js";import"./index.DEKElSOG.js";import"./scroll.CVc-P3_z.js";import"./isUndefined.DgmxjSXK.js";import"./index.Vn8pbgQR.js";/* empty css                        *//* empty css                     */import"./index.ybpLT-bz.js";import"./dropdown.B_OfpyL_.js";import"./refs.BzVvuxps.js";import"./validator.HGn2BZtD.js";const M={class:"login"},P={class:"login-header"},S={class:"flex-y-center"},T={class:"login-form"},B={class:"form-title"},F={class:"input-wrapper"},J={class:"input-wrapper"},O={class:"input-wrapper"},Z={class:"flex-x-between w-full py-1"},G={class:"login-footer"},H=L(e({__name:"index",setup(e){const L=a(),H=s(),N=o();h();const{t:W}=r(),Y=l(),Q=l(H.theme===t.DARK),X=l(!1),ee=l(!1),ae=l(),se=l({username:"",password:"",captchaKey:"",captchaCode:""}),oe=i((()=>({username:[{required:!0,trigger:"blur",message:W("login.message.username.required")}],password:[{required:!0,trigger:"blur",message:W("login.message.password.required")},{min:6,message:W("login.message.password.min"),trigger:"blur"}],captchaCode:[{required:!0,trigger:"blur",message:W("login.message.captchaCode.required")}]})));function re(){f.getCaptcha().then((e=>{se.value.captchaKey=e.captchaKey,ae.value=e.captchaBase64}))}async function le(){var e;null==(e=Y.value)||e.validate((e=>{e&&(X.value=!0,L.login(se.value).then((async()=>{await L.getUserInfo(),await N.loadDictionaries(),await V.replace({name:"Dashboard"})})).catch((()=>{re()})).finally((()=>{X.value=!1})))}))}const te=()=>{const e=H.theme===t.DARK?t.LIGHT:t.DARK;H.changeTheme(e)};function ie(e){e instanceof KeyboardEvent&&(ee.value=e.getModifierState("CapsLock"))}return n((()=>{re()})),(e,a)=>{const s=D,o=z,r=j("User"),l=x,t=I,i=K,n=j("Lock"),f=A,h=R,V=q,L=$,H=U,N=k,W=E;return v(),p("div",M,[m("div",P,[m("div",S,[d(s,{modelValue:u(Q),"onUpdate:modelValue":a[0]||(a[0]=e=>c(Q)?Q.value=e:null),"inline-prompt":"","active-icon":"Moon","inactive-icon":"Sunny",onChange:te},null,8,["modelValue"]),d(o,{class:"ml-2 cursor-pointer"})])]),m("div",T,[d(N,{ref_key:"loginFormRef",ref:Y,model:u(se),rules:u(oe)},{default:g((()=>[m("div",B,[m("h2",null,_(u(w).title),1)]),d(i,{prop:"username"},{default:g((()=>[m("div",F,[d(l,{class:"mx-2"},{default:g((()=>[d(r)])),_:1}),d(t,{ref:"username",modelValue:u(se).username,"onUpdate:modelValue":a[1]||(a[1]=e=>u(se).username=e),placeholder:e.$t("login.username"),name:"username",size:"large",class:"h-[48px]"},null,8,["modelValue","placeholder"])])])),_:1}),d(f,{visible:u(ee),content:e.$t("login.capsLock"),placement:"right"},{default:g((()=>[d(i,{prop:"password"},{default:g((()=>[m("div",J,[d(l,{class:"mx-2"},{default:g((()=>[d(n)])),_:1}),d(t,{modelValue:u(se).password,"onUpdate:modelValue":a[2]||(a[2]=e=>u(se).password=e),placeholder:e.$t("login.password"),type:"password",name:"password",size:"large",class:"h-[48px] pr-2","show-password":"",onKeyup:[ie,y(le,["enter"])]},null,8,["modelValue","placeholder"])])])),_:1})])),_:1},8,["visible","content"]),d(i,{prop:"captchaCode"},{default:g((()=>[m("div",O,[a[4]||(a[4]=m("div",{class:"i-svg:captcha mx-2"},null,-1)),d(t,{modelValue:u(se).captchaCode,"onUpdate:modelValue":a[3]||(a[3]=e=>u(se).captchaCode=e),"auto-complete":"off",size:"large",class:"flex-1",placeholder:e.$t("login.captchaCode"),onKeyup:y(le,["enter"])},null,8,["modelValue","placeholder"]),d(h,{src:u(ae),class:"captcha-img",onClick:re},null,8,["src"])])])),_:1}),m("div",Z,[d(V,null,{default:g((()=>[b(_(e.$t("login.rememberMe")),1)])),_:1}),d(L,{type:"primary",href:"/forget-password"},{default:g((()=>[b(_(e.$t("login.forgetPassword")),1)])),_:1})]),d(H,{loading:u(X),type:"primary",size:"large",class:"w-full",onClick:C(le,["prevent"])},{default:g((()=>[b(_(e.$t("login.login")),1)])),_:1},8,["loading"])])),_:1},8,["model","rules"])]),m("div",G,[d(W,{size:"small"},{default:g((()=>a[5]||(a[5]=[b(" Copyright © 2021 - 2025 youlai.tech All Rights Reserved. "),m("a",{href:"http://beian.miit.gov.cn/",target:"_blank"},"皖ICP备20006496号-2",-1)]))),_:1,__:[5]})])])}}}),[["__scopeId","data-v-1d8b9898"]]);export{H as default};
