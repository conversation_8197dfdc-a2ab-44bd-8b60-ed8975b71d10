import{d as a,r as s,c as e,I as t,ap as i,e as l,f as r,w as o,m as n,V as d,ak as p,i as m,C as u,g as f,P as c,Q as v,$ as j,E as h,F as g,h as _}from"./index.Dk5pbsTU.js";import{v as y}from"./el-loading.Dqi-qL7c.js";import{c as x,E as b,b as w,d as S}from"./el-main.CclDHmVj.js";import L from"./SessionList.Nuu8OjFV.js";import{L as U}from"./index.CgJfYQHq.js";import{a as k}from"./commonSetup.Dm-aByKQ.js";import $ from"./SessionInfo.CGDv9rMP.js";import{d as C}from"./dayjs.min.D7B9fRnU.js";import{_ as I}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./el-divider.2VNIZioN.js";import"./el-empty.Dee0wMKK.js";import"./index.D6CER_Ot.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./el-tab-pane.CXnN_Izo.js";import"./strings.MqEQKtyI.js";import"./event.BwRzfsZt.js";import"./index.C_BbqFDa.js";import"./vnode.Cbclzz8S.js";import"./DrainageList.C3a8wXiP.js";import"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";/* empty css               */import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./ph3.gbMn8L6h.js";import"./UserList.DqCXDuec.js";import"./dayjs.min.Bj2GcQlV.js";import"./index.CcWFbuMM.js";import"./SpeechesRanking.D9mNPq2y.js";/* empty css                     */import"./AllUserList.gs1HNLuT.js";const V={class:"base-info-item",style:{flex:"1"}},A={class:"base-info-item"},D={style:{color:"var(--el-color-primary)"}},E={style:{"margin-left":"15px"}},M={class:"base-info-item",style:{display:"flex","align-items":"center"}},P={class:"base-info-item-tag"},T={class:"base-info-item",style:{display:"flex","align-items":"center"}},z={class:"base-info-item-tag",style:{background:"#42c858"}},O=["src"],Y=I(a({__name:"index",setup(a){const{sendInfo:I,onSend:Y}=k(U.info),q=s({}),B=e((()=>{var a,s;if(!(null==(a=I.data)?void 0:a.actualStartTime))return"-";const e=C(null==(s=I.data)?void 0:s.actualStartTime),t=C().diff(e,"second"),i=Math.floor(t/86400),l=Math.floor(t%86400/3600),r=Math.floor(t%3600/60),o=t%60,n=[];return i>0&&n.push(`${i}天`),l>0&&n.push(`${l}小时`),r>0&&n.push(`${r}分`),(o>0||0===n.length)&&n.push(`${o}秒`),n.join("")}));return t((()=>q.value.id),(a=>{var s;a&&(s=a,I.params=s,Y())}),{deep:!0}),(a,s)=>{const e=x,t=i("Avatar"),U=h,k=S,C=b,Y=w,F=y;return r(),l(Y,{class:"live-preview"},{default:o((()=>[n(e,{class:"live-preview-aside"},{default:o((()=>[n(L,{modelValue:m(q),"onUpdate:modelValue":s[0]||(s[0]=a=>p(q)?q.value=a:null)},null,8,["modelValue"])])),_:1}),d((r(),l(C,{"element-loading-text":"加载中...",class:"live-preview-main"},{default:o((()=>[n(Y,{style:{padding:"0",height:"100%"}},{default:o((()=>[n(k,{class:"base-info"},{default:o((()=>{var a,e,i,l;return[u("div",V,[s[1]||(s[1]=u("span",null,"助理：",-1)),(r(!0),f(c,null,v((null==(a=m(I).data)?void 0:a.assist)||[],((a,s)=>(r(),f("span",{key:s,class:"user-item"},[n(U,{size:"12",style:{color:"var(--el-color-primary)","margin-right":"2px",transform:"translateY(2px)"}},{default:o((()=>[n(t)])),_:1}),j(" "+g(a),1)])))),128))]),u("div",A,[s[2]||(s[2]=u("span",null,"开播时长：",-1)),u("span",D,g(m(B)),1),u("span",E,g(null==(e=m(I).data)?void 0:e.actualStartTime)+"开播",1)]),u("div",M,[s[3]||(s[3]=u("span",null,"直播观众：",-1)),u("div",P,g(null==(i=m(I).data)?void 0:i.watchCount),1)]),u("div",T,[s[4]||(s[4]=u("span",null,"直播间销售：",-1)),u("div",z,g(null==(l=m(I).data)?void 0:l.watchCount),1)])]})),_:1}),n(C,{style:{padding:"0",height:"100%"}},{default:o((()=>[n(Y,{style:{height:"100%",width:"100%",padding:"0"}},{default:o((()=>[n(C,{style:{height:"100%",width:"100%",padding:"0"}},{default:o((()=>[n($,{info:m(q)},null,8,["info"])])),_:1}),n(e,{style:{padding:"0",width:"399px",overflow:"hidden"}},{default:o((()=>{var a,s;return[(null==(a=m(I).data)?void 0:a.h5Url)?(r(),f("iframe",{key:0,src:null==(s=m(I).data)?void 0:s.h5Url,width:"100%",height:"100%",frameborder:"0",allowfullscreen:""},null,8,O)):_("",!0)]})),_:1})])),_:1})])),_:1})])),_:1})])),_:1})),[[F,m(I).loading]])])),_:1})}}}),[["__scopeId","data-v-c8a95092"]]);export{Y as default};
