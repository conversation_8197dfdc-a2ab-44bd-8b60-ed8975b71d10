import{O as e,bb as t,a9 as n,a5 as d,S as o,br as a,L as r,aa as s,c9 as i,a0 as l,_ as c,d as h,b as u,A as p,l as f,a7 as g,y,r as k,a3 as N,a1 as C,ap as v,V as x,X as b,g as m,f as E,C as K,m as D,e as w,h as S,w as A,D as L,n as B,j as T,k as O,P as $,Q as _,bh as I,E as M,I as q,cJ as z,B as j,o as F,b8 as R,u as H,K as P,F as U,v as W,x as J,c as Q,q as V}from"./index.Dk5pbsTU.js";import{s as X}from"./token.DWNpOE8r.js";import{E as Y}from"./index.DLOxQT-M.js";import{E as G}from"./el-checkbox.DDYarIkn.js";import{E as Z}from"./el-text.6kaKYQ9U.js";import{f as ee}from"./use-form-common-props.CQPDkY7k.js";const te="$treeNodeId",ne=function(e,t){t&&!t[te]&&Object.defineProperty(t,te,{value:e.id,enumerable:!1,configurable:!1,writable:!1})},de=(e,t)=>null==t?void 0:t[e||te],oe=(e,t,n)=>{const d=e.value.currentNode;n();const o=e.value.currentNode;d!==o&&t("current-change",o?o.data:null,o)},ae=e=>{let t=!0,n=!0,d=!0;for(let o=0,a=e.length;o<a;o++){const a=e[o];(!0!==a.checked||a.indeterminate)&&(t=!1,a.disabled||(d=!1)),(!1!==a.checked||a.indeterminate)&&(n=!1)}return{all:t,none:n,allWithoutDisable:d,half:!t&&!n}},re=function(e){if(0===e.childNodes.length||e.loading)return;const{all:t,none:n,half:d}=ae(e.childNodes);t?(e.checked=!0,e.indeterminate=!1):d?(e.checked=!1,e.indeterminate=!0):n&&(e.checked=!1,e.indeterminate=!1);const o=e.parent;o&&0!==o.level&&(e.store.checkStrictly||re(o))},se=function(e,t){const n=e.store.props,o=e.data||{},s=n[t];if(a(s))return s(o,e);if(r(s))return o[s];if(d(s)){const e=o[t];return d(e)?"":e}};let ie=0;class le{constructor(t){this.id=ie++,this.text=null,this.checked=!1,this.indeterminate=!1,this.data=null,this.expanded=!1,this.parent=null,this.visible=!0,this.isCurrent=!1,this.canFocus=!1;for(const n in t)e(t,n)&&(this[n]=t[n]);this.level=0,this.loaded=!1,this.childNodes=[],this.loading=!1,this.parent&&(this.level=this.parent.level+1)}initialize(){const e=this.store;if(!e)throw new Error("[Node]store is required!");e.registerNode(this);const d=e.props;if(d&&void 0!==d.isLeaf){const e=se(this,"isLeaf");t(e)&&(this.isLeafByUser=e)}if(!0!==e.lazy&&this.data?(this.setData(this.data),e.defaultExpandAll&&(this.expanded=!0,this.canFocus=!0)):this.level>0&&e.lazy&&e.defaultExpandAll&&!this.isLeafByUser&&this.expand(),n(this.data)||ne(this,this.data),!this.data)return;const o=e.defaultExpandedKeys,a=e.key;a&&o&&o.includes(this.key)&&this.expand(null,e.autoExpandParent),a&&void 0!==e.currentNodeKey&&this.key===e.currentNodeKey&&(e.currentNode=this,e.currentNode.isCurrent=!0),e.lazy&&e._initDefaultCheckedNode(this),this.updateLeafState(),!this.parent||1!==this.level&&!0!==this.parent.expanded||(this.canFocus=!0)}setData(e){let t;n(e)||ne(this,e),this.data=e,this.childNodes=[],t=0===this.level&&n(this.data)?this.data:se(this,"children")||[];for(let n=0,d=t.length;n<d;n++)this.insertChild({data:t[n]})}get label(){return se(this,"label")}get key(){const e=this.store.key;return this.data?this.data[e]:null}get disabled(){return se(this,"disabled")}get nextSibling(){const e=this.parent;if(e){const t=e.childNodes.indexOf(this);if(t>-1)return e.childNodes[t+1]}return null}get previousSibling(){const e=this.parent;if(e){const t=e.childNodes.indexOf(this);if(t>-1)return t>0?e.childNodes[t-1]:null}return null}contains(e,t=!0){return(this.childNodes||[]).some((n=>n===e||t&&n.contains(e)))}remove(){const e=this.parent;e&&e.removeChild(this)}insertChild(e,t,n){if(!e)throw new Error("InsertChild error: child is required.");if(!(e instanceof le)){if(!n){const n=this.getChildren(!0);n.includes(e.data)||(d(t)||t<0?n.push(e.data):n.splice(t,0,e.data))}Object.assign(e,{parent:this,store:this.store}),(e=o(new le(e)))instanceof le&&e.initialize()}e.level=this.level+1,d(t)||t<0?this.childNodes.push(e):this.childNodes.splice(t,0,e),this.updateLeafState()}insertBefore(e,t){let n;t&&(n=this.childNodes.indexOf(t)),this.insertChild(e,n)}insertAfter(e,t){let n;t&&(n=this.childNodes.indexOf(t),-1!==n&&(n+=1)),this.insertChild(e,n)}removeChild(e){const t=this.getChildren()||[],n=t.indexOf(e.data);n>-1&&t.splice(n,1);const d=this.childNodes.indexOf(e);d>-1&&(this.store&&this.store.deregisterNode(e),e.parent=null,this.childNodes.splice(d,1)),this.updateLeafState()}removeChildByData(e){let t=null;for(let n=0;n<this.childNodes.length;n++)if(this.childNodes[n].data===e){t=this.childNodes[n];break}t&&this.removeChild(t)}expand(e,t){const d=()=>{if(t){let e=this.parent;for(;e.level>0;)e.expanded=!0,e=e.parent}this.expanded=!0,e&&e(),this.childNodes.forEach((e=>{e.canFocus=!0}))};this.shouldLoadData()?this.loadData((e=>{n(e)&&(this.checked?this.setChecked(!0,!0):this.store.checkStrictly||re(this),d())})):d()}doCreateChildren(e,t={}){e.forEach((e=>{this.insertChild(Object.assign({data:e},t),void 0,!0)}))}collapse(){this.expanded=!1,this.childNodes.forEach((e=>{e.canFocus=!1}))}shouldLoadData(){return!0===this.store.lazy&&this.store.load&&!this.loaded}updateLeafState(){if(!0===this.store.lazy&&!0!==this.loaded&&void 0!==this.isLeafByUser)return void(this.isLeaf=this.isLeafByUser);const e=this.childNodes;!this.store.lazy||!0===this.store.lazy&&!0===this.loaded?this.isLeaf=!e||0===e.length:this.isLeaf=!1}setChecked(e,t,n,d){if(this.indeterminate="half"===e,this.checked=!0===e,this.store.checkStrictly)return;if(!this.shouldLoadData()||this.store.checkDescendants){const{all:n,allWithoutDisable:o}=ae(this.childNodes);this.isLeaf||n||!o||(this.checked=!1,e=!1);const a=()=>{if(t){const n=this.childNodes;for(let r=0,s=n.length;r<s;r++){const o=n[r];d=d||!1!==e;const a=o.disabled?o.checked:d;o.setChecked(a,t,!0,d)}const{half:o,all:a}=ae(n);a||(this.checked=a,this.indeterminate=o)}};if(this.shouldLoadData())return void this.loadData((()=>{a(),re(this)}),{checked:!1!==e});a()}const o=this.parent;o&&0!==o.level&&(n||re(o))}getChildren(e=!1){if(0===this.level)return this.data;const t=this.data;if(!t)return null;const n=this.store.props;let o="children";return n&&(o=n.children||"children"),d(t[o])&&(t[o]=null),e&&!t[o]&&(t[o]=[]),t[o]}updateChildren(){const e=this.getChildren()||[],t=this.childNodes.map((e=>e.data)),n={},d=[];e.forEach(((e,o)=>{const a=e[te];!!a&&t.findIndex((e=>e[te]===a))>=0?n[a]={index:o,data:e}:d.push({index:o,data:e})})),this.store.lazy||t.forEach((e=>{n[e[te]]||this.removeChildByData(e)})),d.forEach((({index:e,data:t})=>{this.insertChild({data:t},e)})),this.updateLeafState()}loadData(e,t={}){if(!0!==this.store.lazy||!this.store.load||this.loaded||this.loading&&!Object.keys(t).length)e&&e.call(this);else{this.loading=!0;const n=n=>{this.childNodes=[],this.doCreateChildren(n,t),this.loaded=!0,this.loading=!1,this.updateLeafState(),e&&e.call(this,n)},d=()=>{this.loading=!1};this.store.load(this,n,d)}}eachNode(e){const t=[this];for(;t.length;){const n=t.shift();t.unshift(...n.childNodes),e(n)}}reInitChecked(){this.store.checkStrictly||re(this)}}class ce{constructor(t){this.currentNode=null,this.currentNodeKey=null;for(const n in t)e(t,n)&&(this[n]=t[n]);this.nodesMap={}}initialize(){if(this.root=new le({data:this.data,store:this}),this.root.initialize(),this.lazy&&this.load){(0,this.load)(this.root,(e=>{this.root.doCreateChildren(e),this._initDefaultCheckedNodes()}))}else this._initDefaultCheckedNodes()}filter(e){const t=this.filterNodeMethod,n=this.lazy,d=async function(o){const a=o.root?o.root.childNodes:o.childNodes;for(const[n,r]of a.entries())r.visible=t.call(r,e,r.data,r),n%80==0&&n>0&&await l(),await d(r);if(!o.visible&&a.length){let e=!0;e=!a.some((e=>e.visible)),o.root?o.root.visible=!1===e:o.visible=!1===e}e&&o.visible&&!o.isLeaf&&(n&&!o.loaded||o.expand())};d(this)}setData(e){e!==this.root.data?(this.nodesMap={},this.root.setData(e),this._initDefaultCheckedNodes(),this.setCurrentNodeKey(this.currentNodeKey)):this.root.updateChildren()}getNode(e){if(e instanceof le)return e;const t=s(e)?de(this.key,e):e;return this.nodesMap[t]||null}insertBefore(e,t){const n=this.getNode(t);n.parent.insertBefore({data:e},n)}insertAfter(e,t){const n=this.getNode(t);n.parent.insertAfter({data:e},n)}remove(e){const t=this.getNode(e);t&&t.parent&&(t===this.currentNode&&(this.currentNode=null),t.parent.removeChild(t))}append(e,t){const n=i(t)?this.root:this.getNode(t);n&&n.insertChild({data:e})}_initDefaultCheckedNodes(){const e=this.defaultCheckedKeys||[],t=this.nodesMap;e.forEach((e=>{const n=t[e];n&&n.setChecked(!0,!this.checkStrictly)}))}_initDefaultCheckedNode(e){(this.defaultCheckedKeys||[]).includes(e.key)&&e.setChecked(!0,!this.checkStrictly)}setDefaultCheckedKey(e){e!==this.defaultCheckedKeys&&(this.defaultCheckedKeys=e,this._initDefaultCheckedNodes())}registerNode(e){const t=this.key;if(e&&e.data)if(t){void 0!==e.key&&(this.nodesMap[e.key]=e)}else this.nodesMap[e.id]=e}deregisterNode(e){this.key&&e&&e.data&&(e.childNodes.forEach((e=>{this.deregisterNode(e)})),delete this.nodesMap[e.key])}getCheckedNodes(e=!1,t=!1){const n=[],d=function(o){(o.root?o.root.childNodes:o.childNodes).forEach((o=>{(o.checked||t&&o.indeterminate)&&(!e||e&&o.isLeaf)&&n.push(o.data),d(o)}))};return d(this),n}getCheckedKeys(e=!1){return this.getCheckedNodes(e).map((e=>(e||{})[this.key]))}getHalfCheckedNodes(){const e=[],t=function(n){(n.root?n.root.childNodes:n.childNodes).forEach((n=>{n.indeterminate&&e.push(n.data),t(n)}))};return t(this),e}getHalfCheckedKeys(){return this.getHalfCheckedNodes().map((e=>(e||{})[this.key]))}_getAllNodes(){const t=[],n=this.nodesMap;for(const d in n)e(n,d)&&t.push(n[d]);return t}updateChildren(e,t){const n=this.nodesMap[e];if(!n)return;const d=n.childNodes;for(let o=d.length-1;o>=0;o--){const e=d[o];this.remove(e.data)}for(let o=0,a=t.length;o<a;o++){const e=t[o];this.append(e,n.data)}}_setCheckedKeys(e,t=!1,n){const d=this._getAllNodes().sort(((e,t)=>e.level-t.level)),o=Object.create(null),a=Object.keys(n);d.forEach((e=>e.setChecked(!1,!1)));const r=t=>{t.childNodes.forEach((t=>{var n;o[t.data[e]]=!0,(null==(n=t.childNodes)?void 0:n.length)&&r(t)}))};for(let s=0,i=d.length;s<i;s++){const n=d[s],i=n.data[e].toString();if(a.includes(i)){if(n.childNodes.length&&r(n),n.isLeaf||this.checkStrictly)n.setChecked(!0,!1);else if(n.setChecked(!0,!0),t){n.setChecked(!1,!1);const e=function(t){t.childNodes.forEach((t=>{t.isLeaf||t.setChecked(!1,!1),e(t)}))};e(n)}}else n.checked&&!o[i]&&n.setChecked(!1,!1)}}setCheckedNodes(e,t=!1){const n=this.key,d={};e.forEach((e=>{d[(e||{})[n]]=!0})),this._setCheckedKeys(n,t,d)}setCheckedKeys(e,t=!1){this.defaultCheckedKeys=e;const n=this.key,d={};e.forEach((e=>{d[e]=!0})),this._setCheckedKeys(n,t,d)}setDefaultExpandedKeys(e){e=e||[],this.defaultExpandedKeys=e,e.forEach((e=>{const t=this.getNode(e);t&&t.expand(null,this.autoExpandParent)}))}setChecked(e,t,n){const d=this.getNode(e);d&&d.setChecked(!!t,n)}getCurrentNode(){return this.currentNode}setCurrentNode(e){const t=this.currentNode;t&&(t.isCurrent=!1),this.currentNode=e,this.currentNode.isCurrent=!0}setUserCurrentNode(e,t=!0){const n=e[this.key],d=this.nodesMap[n];this.setCurrentNode(d),t&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0)}setCurrentNodeKey(e,t=!0){if(this.currentNodeKey=e,i(e))return this.currentNode&&(this.currentNode.isCurrent=!1),void(this.currentNode=null);const n=this.getNode(e);n&&(this.setCurrentNode(n),t&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0))}}var he=c(h({name:"ElTreeNodeContent",props:{node:{type:Object,required:!0},renderContent:Function},setup(e){const t=u("tree"),n=p("NodeInstance"),d=p("RootTree");return()=>{const o=e.node,{data:a,store:r}=o;return e.renderContent?e.renderContent(g,{_self:n,node:o,data:a,store:r}):f(d.ctx.slots,"default",{node:o,data:a},(()=>[g(Z,{tag:"span",truncated:!0,class:t.be("node","label")},(()=>[o.label]))]))}}}),[["__file","tree-node-content.vue"]]);function ue(e){const t=p("TreeNodeMap",null),n={treeNodeExpand:t=>{e.node!==t&&e.node.collapse()},children:[]};return t&&t.children.push(n),y("TreeNodeMap",n),{broadcastExpanded:t=>{if(e.accordion)for(const e of n.children)e.treeNodeExpand(t)}}}const pe=Symbol("dragEvents");const fe=h({name:"ElTreeNode",components:{ElCollapseTransition:Y,ElCheckbox:G,NodeContent:he,ElIcon:M,Loading:I},props:{node:{type:le,default:()=>({})},props:{type:Object,default:()=>({})},accordion:Boolean,renderContent:Function,renderAfterExpand:Boolean,showCheckbox:{type:Boolean,default:!1}},emits:["node-expand"],setup(e,t){const n=u("tree"),{broadcastExpanded:d}=ue(e),o=p("RootTree"),s=k(!1),i=k(!1),c=k(),h=k(),f=k(),g=p(pe),N=j();y("NodeInstance",N),e.node.expanded&&(s.value=!0,i.value=!0);const C=o.props.props.children||"children";q((()=>{var t;const n=null==(t=e.node.data)?void 0:t[C];return n&&[...n]}),(()=>{e.node.updateChildren()})),q((()=>e.node.indeterminate),(t=>{x(e.node.checked,t)})),q((()=>e.node.checked),(t=>{x(t,e.node.indeterminate)})),q((()=>e.node.childNodes.length),(()=>e.node.reInitChecked())),q((()=>e.node.expanded),(e=>{l((()=>s.value=e)),e&&(i.value=!0)}));const v=e=>de(o.props.nodeKey,e.data),x=(t,n)=>{c.value===t&&h.value===n||o.ctx.emit("check-change",e.node.data,t,n),c.value=t,h.value=n},b=()=>{e.node.isLeaf||(s.value?(o.ctx.emit("node-collapse",e.node.data,e.node,N),e.node.collapse()):e.node.expand((()=>{t.emit("node-expand",e.node.data,e.node,N)})))},m=t=>{e.node.setChecked(t,!(null==o?void 0:o.props.checkStrictly)),l((()=>{const t=o.store.value;o.ctx.emit("check",e.node.data,{checkedNodes:t.getCheckedNodes(),checkedKeys:t.getCheckedKeys(),halfCheckedNodes:t.getHalfCheckedNodes(),halfCheckedKeys:t.getHalfCheckedKeys()})}))};return{ns:n,node$:f,tree:o,expanded:s,childNodeRendered:i,oldChecked:c,oldIndeterminate:h,getNodeKey:v,getNodeClass:t=>{const n=e.props.class;if(!n)return{};let d;if(a(n)){const{data:e}=t;d=n(e,t)}else d=n;return r(d)?{[d]:!0}:d},handleSelectChange:x,handleClick:t=>{oe(o.store,o.ctx.emit,(()=>{var t;if(null==(t=null==o?void 0:o.props)?void 0:t.nodeKey){const t=v(e.node);o.store.value.setCurrentNodeKey(t)}else o.store.value.setCurrentNode(e.node)})),o.currentNode.value=e.node,o.props.expandOnClickNode&&b(),(o.props.checkOnClickNode||e.node.isLeaf&&o.props.checkOnClickLeaf&&e.showCheckbox)&&!e.node.disabled&&m(!e.node.checked),o.ctx.emit("node-click",e.node.data,e.node,N,t)},handleContextMenu:t=>{var n;(null==(n=o.instance.vnode.props)?void 0:n.onNodeContextmenu)&&(t.stopPropagation(),t.preventDefault()),o.ctx.emit("node-contextmenu",t,e.node.data,e.node,N)},handleExpandIconClick:b,handleCheckChange:m,handleChildNodeExpand:(e,t,n)=>{d(t),o.ctx.emit("node-expand",e,t,n)},handleDragStart:t=>{o.props.draggable&&g.treeNodeDragStart({event:t,treeNode:e})},handleDragOver:t=>{t.preventDefault(),o.props.draggable&&g.treeNodeDragOver({event:t,treeNode:{$el:f.value,node:e.node}})},handleDrop:e=>{e.preventDefault()},handleDragEnd:e=>{o.props.draggable&&g.treeNodeDragEnd(e)},CaretRight:z}}});const ge=V(c(h({name:"ElTree",components:{ElTreeNode:c(fe,[["render",function(e,t,n,d,o,a){const r=v("el-icon"),s=v("el-checkbox"),i=v("loading"),l=v("node-content"),c=v("el-tree-node"),h=v("el-collapse-transition");return x((E(),m("div",{ref:"node$",class:B([e.ns.b("node"),e.ns.is("expanded",e.expanded),e.ns.is("current",e.node.isCurrent),e.ns.is("hidden",!e.node.visible),e.ns.is("focusable",!e.node.disabled),e.ns.is("checked",!e.node.disabled&&e.node.checked),e.getNodeClass(e.node)]),role:"treeitem",tabindex:"-1","aria-expanded":e.expanded,"aria-disabled":e.node.disabled,"aria-checked":e.node.checked,draggable:e.tree.props.draggable,"data-key":e.getNodeKey(e.node),onClick:T(e.handleClick,["stop"]),onContextmenu:e.handleContextMenu,onDragstart:T(e.handleDragStart,["stop"]),onDragover:T(e.handleDragOver,["stop"]),onDragend:T(e.handleDragEnd,["stop"]),onDrop:T(e.handleDrop,["stop"])},[K("div",{class:B(e.ns.be("node","content")),style:O({paddingLeft:(e.node.level-1)*e.tree.props.indent+"px"})},[e.tree.props.icon||e.CaretRight?(E(),w(r,{key:0,class:B([e.ns.be("node","expand-icon"),e.ns.is("leaf",e.node.isLeaf),{expanded:!e.node.isLeaf&&e.expanded}]),onClick:T(e.handleExpandIconClick,["stop"])},{default:A((()=>[(E(),w(L(e.tree.props.icon||e.CaretRight)))])),_:1},8,["class","onClick"])):S("v-if",!0),e.showCheckbox?(E(),w(s,{key:1,"model-value":e.node.checked,indeterminate:e.node.indeterminate,disabled:!!e.node.disabled,onClick:T((()=>{}),["stop"]),onChange:e.handleCheckChange},null,8,["model-value","indeterminate","disabled","onClick","onChange"])):S("v-if",!0),e.node.loading?(E(),w(r,{key:2,class:B([e.ns.be("node","loading-icon"),e.ns.is("loading")])},{default:A((()=>[D(i)])),_:1},8,["class"])):S("v-if",!0),D(l,{node:e.node,"render-content":e.renderContent},null,8,["node","render-content"])],6),D(h,null,{default:A((()=>[!e.renderAfterExpand||e.childNodeRendered?x((E(),m("div",{key:0,class:B(e.ns.be("node","children")),role:"group","aria-expanded":e.expanded,onClick:T((()=>{}),["stop"])},[(E(!0),m($,null,_(e.node.childNodes,(t=>(E(),w(c,{key:e.getNodeKey(t),"render-content":e.renderContent,"render-after-expand":e.renderAfterExpand,"show-checkbox":e.showCheckbox,node:t,accordion:e.accordion,props:e.props,onNodeExpand:e.handleChildNodeExpand},null,8,["render-content","render-after-expand","show-checkbox","node","accordion","props","onNodeExpand"])))),128))],10,["aria-expanded","onClick"])),[[b,e.expanded]]):S("v-if",!0)])),_:1})],42,["aria-expanded","aria-disabled","aria-checked","draggable","data-key","onClick","onContextmenu","onDragstart","onDragover","onDragend","onDrop"])),[[b,e.node.visible]])}],["__file","tree-node.vue"]])},props:{data:{type:Array,default:()=>[]},emptyText:{type:String},renderAfterExpand:{type:Boolean,default:!0},nodeKey:String,checkStrictly:Boolean,defaultExpandAll:Boolean,expandOnClickNode:{type:Boolean,default:!0},checkOnClickNode:Boolean,checkOnClickLeaf:{type:Boolean,default:!0},checkDescendants:{type:Boolean,default:!1},autoExpandParent:{type:Boolean,default:!0},defaultCheckedKeys:Array,defaultExpandedKeys:Array,currentNodeKey:[String,Number],renderContent:Function,showCheckbox:{type:Boolean,default:!1},draggable:{type:Boolean,default:!1},allowDrag:Function,allowDrop:Function,props:{type:Object,default:()=>({children:"children",label:"label",disabled:"disabled"})},lazy:{type:Boolean,default:!1},highlightCurrent:Boolean,load:Function,filterNodeMethod:Function,accordion:Boolean,indent:{type:Number,default:18},icon:{type:W}},emits:["check-change","current-change","node-click","node-contextmenu","node-collapse","node-expand","check","node-drag-start","node-drag-end","node-drop","node-drag-leave","node-drag-enter","node-drag-over"],setup(e,t){const{t:n}=J(),d=u("tree"),o=p(X,null),r=k(new ce({key:e.nodeKey,data:e.data,lazy:e.lazy,props:e.props,load:e.load,currentNodeKey:e.currentNodeKey,checkStrictly:e.checkStrictly,checkDescendants:e.checkDescendants,defaultCheckedKeys:e.defaultCheckedKeys,defaultExpandedKeys:e.defaultExpandedKeys,autoExpandParent:e.autoExpandParent,defaultExpandAll:e.defaultExpandAll,filterNodeMethod:e.filterNodeMethod}));r.value.initialize();const s=k(r.value.root),i=k(null),l=k(null),c=k(null),{broadcastExpanded:h}=ue(e),{dragState:f}=function({props:e,ctx:t,el$:n,dropIndicator$:d,store:o}){const r=u("tree"),s=k({showDropIndicator:!1,draggingNode:null,dropNode:null,allowDrop:!0,dropType:null});return y(pe,{treeNodeDragStart:({event:n,treeNode:d})=>{if(a(e.allowDrag)&&!e.allowDrag(d.node))return n.preventDefault(),!1;n.dataTransfer.effectAllowed="move";try{n.dataTransfer.setData("text/plain","")}catch(o){}s.value.draggingNode=d,t.emit("node-drag-start",d.node,n)},treeNodeDragOver:({event:o,treeNode:i})=>{const l=i,c=s.value.dropNode;c&&c.node.id!==l.node.id&&N(c.$el,r.is("drop-inner"));const h=s.value.draggingNode;if(!h||!l)return;let u=!0,p=!0,f=!0,g=!0;a(e.allowDrop)&&(u=e.allowDrop(h.node,l.node,"prev"),g=p=e.allowDrop(h.node,l.node,"inner"),f=e.allowDrop(h.node,l.node,"next")),o.dataTransfer.dropEffect=p||u||f?"move":"none",(u||p||f)&&(null==c?void 0:c.node.id)!==l.node.id&&(c&&t.emit("node-drag-leave",h.node,c.node,o),t.emit("node-drag-enter",h.node,l.node,o)),s.value.dropNode=u||p||f?l:null,l.node.nextSibling===h.node&&(f=!1),l.node.previousSibling===h.node&&(u=!1),l.node.contains(h.node,!1)&&(p=!1),(h.node===l.node||h.node.contains(l.node))&&(u=!1,p=!1,f=!1);const y=l.$el.querySelector(`.${r.be("node","content")}`).getBoundingClientRect(),k=n.value.getBoundingClientRect();let v;const x=u?p?.25:f?.45:1:-1,b=f?p?.75:u?.55:0:1;let m=-9999;const E=o.clientY-y.top;v=E<y.height*x?"before":E>y.height*b?"after":p?"inner":"none";const K=l.$el.querySelector(`.${r.be("node","expand-icon")}`).getBoundingClientRect(),D=d.value;"before"===v?m=K.top-k.top:"after"===v&&(m=K.bottom-k.top),D.style.top=`${m}px`,D.style.left=K.right-k.left+"px","inner"===v?C(l.$el,r.is("drop-inner")):N(l.$el,r.is("drop-inner")),s.value.showDropIndicator="before"===v||"after"===v,s.value.allowDrop=s.value.showDropIndicator||g,s.value.dropType=v,t.emit("node-drag-over",h.node,l.node,o)},treeNodeDragEnd:e=>{const{draggingNode:n,dropType:d,dropNode:a}=s.value;if(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="move"),n&&a){const s={data:n.node.data};"none"!==d&&n.node.remove(),"before"===d?a.node.parent.insertBefore(s,a.node):"after"===d?a.node.parent.insertAfter(s,a.node):"inner"===d&&a.node.insertChild(s),"none"!==d&&(o.value.registerNode(s),o.value.key&&n.node.eachNode((e=>{var t;null==(t=o.value.nodesMap[e.data[o.value.key]])||t.setChecked(e.checked,!o.value.checkStrictly)}))),N(a.$el,r.is("drop-inner")),t.emit("node-drag-end",n.node,a.node,d,e),"none"!==d&&t.emit("node-drop",n.node,a.node,d,e)}n&&!a&&t.emit("node-drag-end",n.node,null,d,e),s.value.showDropIndicator=!1,s.value.draggingNode=null,s.value.dropNode=null,s.value.allowDrop=!0}}),{dragState:s}}({props:e,ctx:t,el$:l,dropIndicator$:c,store:r});!function({el$:e},t){const n=u("tree");F((()=>{d()})),R((()=>{Array.from(e.value.querySelectorAll("input[type=checkbox]")).forEach((e=>{e.setAttribute("tabindex","-1")}))})),H(e,"keydown",(d=>{const o=d.target;if(!o.className.includes(n.b("node")))return;const a=d.code,r=Array.from(e.value.querySelectorAll(`.${n.is("focusable")}[role=treeitem]`)),s=r.indexOf(o);let i;if([P.up,P.down].includes(a)){if(d.preventDefault(),a===P.up){i=-1===s?0:0!==s?s-1:r.length-1;const e=i;for(;!t.value.getNode(r[i].dataset.key).canFocus;){if(i--,i===e){i=-1;break}i<0&&(i=r.length-1)}}else{i=-1===s?0:s<r.length-1?s+1:0;const e=i;for(;!t.value.getNode(r[i].dataset.key).canFocus;){if(i++,i===e){i=-1;break}i>=r.length&&(i=0)}}-1!==i&&r[i].focus()}[P.left,P.right].includes(a)&&(d.preventDefault(),o.click());const l=o.querySelector('[type="checkbox"]');[P.enter,P.numpadEnter,P.space].includes(a)&&l&&(d.preventDefault(),l.click())}));const d=()=>{var t;const d=Array.from(e.value.querySelectorAll(`.${n.is("focusable")}[role=treeitem]`));Array.from(e.value.querySelectorAll("input[type=checkbox]")).forEach((e=>{e.setAttribute("tabindex","-1")}));const o=e.value.querySelectorAll(`.${n.is("checked")}[role=treeitem]`);o.length?o[0].setAttribute("tabindex","0"):null==(t=d[0])||t.setAttribute("tabindex","0")}}({el$:l},r);const g=Q((()=>{const{childNodes:e}=s.value,t=!!o&&0!==o.hasFilteredOptions;return(!e||0===e.length||e.every((({visible:e})=>!e)))&&!t}));q((()=>e.currentNodeKey),(e=>{r.value.setCurrentNodeKey(e)})),q((()=>e.defaultCheckedKeys),(e=>{r.value.setDefaultCheckedKey(e)})),q((()=>e.defaultExpandedKeys),(e=>{r.value.setDefaultExpandedKeys(e)})),q((()=>e.data),(e=>{r.value.setData(e)}),{deep:!0}),q((()=>e.checkStrictly),(e=>{r.value.checkStrictly=e}));const v=()=>{const e=r.value.getCurrentNode();return e?e.data:null};return y("RootTree",{ctx:t,props:e,store:r,root:s,currentNode:i,instance:j()}),y(ee,void 0),{ns:d,store:r,root:s,currentNode:i,dragState:f,el$:l,dropIndicator$:c,isEmpty:g,filter:t=>{if(!e.filterNodeMethod)throw new Error("[Tree] filterNodeMethod is required when filter");r.value.filter(t)},getNodeKey:t=>de(e.nodeKey,t.data),getNodePath:t=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in getNodePath");const n=r.value.getNode(t);if(!n)return[];const d=[n.data];let o=n.parent;for(;o&&o!==s.value;)d.push(o.data),o=o.parent;return d.reverse()},getCheckedNodes:(e,t)=>r.value.getCheckedNodes(e,t),getCheckedKeys:e=>r.value.getCheckedKeys(e),getCurrentNode:v,getCurrentKey:()=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in getCurrentKey");const t=v();return t?t[e.nodeKey]:null},setCheckedNodes:(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedNodes");r.value.setCheckedNodes(t,n)},setCheckedKeys:(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedKeys");r.value.setCheckedKeys(t,n)},setChecked:(e,t,n)=>{r.value.setChecked(e,t,n)},getHalfCheckedNodes:()=>r.value.getHalfCheckedNodes(),getHalfCheckedKeys:()=>r.value.getHalfCheckedKeys(),setCurrentNode:(n,d=!0)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentNode");oe(r,t.emit,(()=>{h(n),r.value.setUserCurrentNode(n,d)}))},setCurrentKey:(n,d=!0)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentKey");oe(r,t.emit,(()=>{h(),r.value.setCurrentNodeKey(n,d)}))},t:n,getNode:e=>r.value.getNode(e),remove:e=>{r.value.remove(e)},append:(e,t)=>{r.value.append(e,t)},insertBefore:(e,t)=>{r.value.insertBefore(e,t)},insertAfter:(e,t)=>{r.value.insertAfter(e,t)},handleNodeExpand:(e,n,d)=>{h(n),t.emit("node-expand",e,n,d)},updateKeyChildren:(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in updateKeyChild");r.value.updateChildren(t,n)}}}}),[["render",function(e,t,n,d,o,a){const r=v("el-tree-node");return E(),m("div",{ref:"el$",class:B([e.ns.b(),e.ns.is("dragging",!!e.dragState.draggingNode),e.ns.is("drop-not-allow",!e.dragState.allowDrop),e.ns.is("drop-inner","inner"===e.dragState.dropType),{[e.ns.m("highlight-current")]:e.highlightCurrent}]),role:"tree"},[(E(!0),m($,null,_(e.root.childNodes,(t=>(E(),w(r,{key:e.getNodeKey(t),node:t,props:e.props,accordion:e.accordion,"render-after-expand":e.renderAfterExpand,"show-checkbox":e.showCheckbox,"render-content":e.renderContent,onNodeExpand:e.handleNodeExpand},null,8,["node","props","accordion","render-after-expand","show-checkbox","render-content","onNodeExpand"])))),128)),e.isEmpty?(E(),m("div",{key:0,class:B(e.ns.e("empty-block"))},[f(e.$slots,"empty",{},(()=>{var t;return[K("span",{class:B(e.ns.e("empty-text"))},U(null!=(t=e.emptyText)?t:e.t("el.tree.emptyText")),3)]}))],2)):S("v-if",!0),x(K("div",{ref:"dropIndicator$",class:B(e.ns.e("drop-indicator"))},null,2),[[b,e.dragState.showDropIndicator]])],2)}],["__file","tree.vue"]]));export{ge as E};
