import request from "@/utils/request";

// 分页查询
export class LiveRoomPageDto implements PageQuery {
  pageNum = 1;
  pageSize = 10;
  // 关键字
  keywords: string = "";
  // 状态
  status: string = "";
}

// 分页返回信息
export interface LiveRoomPageVo {
  id: number; // ID
  tenantId: number; // 租户ID
  roomName: string; // 直播间名称
  streamName: string; // 流名
  coverUrl: string; // 封面图URL
  description: string; // 直播间描述
  announcement?: string; // 直播间公告
  notice?: string; // 温馨提示
  anchorShowUserCount?: number; // 是否展示主播在线用户数
  assistantShowUserCount?: number; // 是否展示助理在线用户数
  saleShowUserCount?: number; // 是否展示销售在线用户数
  userShowUserCount?: number; // 是否展示普通用户在线用户数
  fontSize?: number; // 直播间默认字号
  stopLiveMinutes?: number; // 直播暂停自动结束时间(分钟)
  quickReply?: string; // 快捷回复JSON字符串
  status: number; // 0-未开播 1-直播中
  liveCount: number; // 开播次数
  createdAt: string; // 创建时间
  updatedAt: string; // 更新时间
  deletedAt: string; // 删除时间
  userReply: number;
}

export interface LiveRoomVo extends LiveRoomPageVo {
  anchorShowUserCount: number;
  assistantShowUserCount: number;
  saleShowUserCount: number;
  userShowUserCount: number;
}

export class LiveRoomDto {
  id?: number; // 直播间ID (可选)
  roomName: string = ""; // 直播间名称
  streamName: string = ""; // 流名
  coverUrl: string = ""; // 封面图URL
  description: string = ""; // 直播间介绍
  announcement: string = ""; // 直播间公告
  notice: string = ""; // 温馨提示
  private _anchorShowUserCount: boolean = false; // 是否展示主播在线用户数
  private _assistantShowUserCount: boolean = false; // 是否展示助理在线用户数
  private _saleShowUserCount: boolean = false; // 是否展示销售在线用户数
  private _userShowUserCount: boolean = false; // 是否展示普通用户在线用户数
  fontSize: number = 16; // 直播间默认字号
  stopLiveMinutes: number = 30; // 直播暂停自动结束时间(分钟)
  userReply: number = 0; // 是否允许普通用户回复
  constructor(e?: LiveRoomPageVo) {
    if (e) {
      this.id = e.id;
      this.roomName = e.roomName;
      this.streamName = e.streamName;
      this.coverUrl = e.coverUrl;
      this.description = e.description;
      this.announcement = e.announcement || "";
      this.notice = e.notice || "";
      this._anchorShowUserCount = e.anchorShowUserCount === 1;
      this._assistantShowUserCount = e.assistantShowUserCount === 1;
      this._saleShowUserCount = e.saleShowUserCount === 1;
      this._userShowUserCount = e.userShowUserCount === 1;
      this.fontSize = e.fontSize || 16;
      this.stopLiveMinutes = e.stopLiveMinutes || 30;
      this.userReply = e.userReply || 0;
    }
  }

  // Getters and setters for boolean/number conversion
  get anchorShowUserCount(): boolean {
    return this._anchorShowUserCount;
  }

  set anchorShowUserCount(value: boolean) {
    this._anchorShowUserCount = value;
  }

  get assistantShowUserCount(): boolean {
    return this._assistantShowUserCount;
  }

  set assistantShowUserCount(value: boolean) {
    this._assistantShowUserCount = value;
  }

  get saleShowUserCount(): boolean {
    return this._saleShowUserCount;
  }

  set saleShowUserCount(value: boolean) {
    this._saleShowUserCount = value;
  }

  get userShowUserCount(): boolean {
    return this._userShowUserCount;
  }

  set userShowUserCount(value: boolean) {
    this._userShowUserCount = value;
  }

  // Method to convert to API format
  toAPI(): Record<string, any> {
    return {
      id: this.id,
      roomName: this.roomName,
      streamName: this.streamName,
      coverUrl: this.coverUrl,
      description: this.description,
      announcement: this.announcement,
      notice: this.notice,
      anchorShowUserCount: this._anchorShowUserCount ? 1 : 0,
      assistantShowUserCount: this._assistantShowUserCount ? 1 : 0,
      saleShowUserCount: this._saleShowUserCount ? 1 : 0,
      userShowUserCount: this._userShowUserCount ? 1 : 0,
      fontSize: this.fontSize,
      stopLiveMinutes: this.stopLiveMinutes,
      userReply: this.userReply,
    };
  }
}

export class CloneRoomDTO {
  roomId: string = "";
  selectRoomName: string = "";
  roomName: string = "";
  coverUrl: string = "";
  saleShowUserCount: number = 0;
  anchorShowUserCount: number = 0;
  assistantShowUserCount: number = 0;
  userShowUserCount: number = 0;
  cloneQuickReply: number = 1;
  cloneQuickVest: number = 1;
}

const LiveRoomApi = {
  /*克隆*/
  cloneRoom(data: CloneRoomDTO) {
    return request<CloneRoomDTO, any>({
      url: `/tenant/live/room/cloneRoom`,
      method: "post",
      data: data,
    });
  },
  /** 分页*/
  page(query: LiveRoomPageDto) {
    return request<any, PageResult<LiveRoomPageVo[]>>({
      url: `/tenant/live/room/list`,
      method: "get",
      params: query,
    });
  },
  /**
   * 保存
   * @param data 表单数据
   */
  save(data: LiveRoomDto) {
    return request({
      url: `/tenant/live/room/saveOrEdit`,
      method: "post",
      data: data,
    });
  },
  /**
   * 删除
   * @param id ID
   */
  remove(id: number) {
    return request<any, null>({
      url: `/tenant/live/room/remove/${id}`,
      method: "delete",
    });
  },
  /**
   * 保存快捷回复
   * @param data 快捷回复数据
   */
  saveQuickReply(data: { id: number; quickReply: string[] }) {
    return request({
      url: `/tenant/live/room/quickReply`,
      method: "post",
      data: data,
    });
  },
};

export default LiveRoomApi;
