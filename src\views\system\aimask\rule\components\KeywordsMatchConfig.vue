<template>
  <div class="keywords-match-config">
    <el-form label-width="80px">
      <el-form-item label="匹配模式">
        <el-radio-group v-model="localConfig.match_mode">
          <el-radio value="exact">精确匹配</el-radio>
          <el-radio value="partial">部分匹配</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="关键词">
        <div class="mb-2 flex">
          <el-input
            v-model="newKeyword"
            placeholder="请输入关键词"
            @keyup.enter="addKeyword"
            class="mr-2"
          />
          <el-button type="primary" @click="addKeyword">添加</el-button>
        </div>
      </el-form-item>
      <div style="padding: 0 6px">
        <el-tag
          v-for="(keyword, index) in localConfig.keywords"
          :key="index"
          closable
          class="mr-1 mb-1"
          @close="removeKeyword(index)"
        >
          {{ keyword }}
        </el-tag>
      </div>
      <div v-if="localConfig.keywords.length === 0" class="text-gray-400 text-sm">
        暂无关键词，请添加
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from "vue";

const props = defineProps({
  config: {
    type: Object,
    default: () => ({ keywords: [], match_mode: "partial" }),
  },
});

const emit = defineEmits(["update"]);

const localConfig = reactive({
  keywords: props.config?.keywords || [],
  match_mode: props.config?.match_mode || "partial",
});

// 监听配置变化并向上传递
watch(
  localConfig,
  (newVal) => {
    emit("update", { ...newVal });
  },
  { deep: true }
);

// 新关键词输入
const newKeyword = ref("");

// 添加关键词
const addKeyword = () => {
  if (newKeyword.value && !localConfig.keywords.includes(newKeyword.value)) {
    localConfig.keywords.push(newKeyword.value);
    newKeyword.value = "";
  }
};

// 删除关键词
const removeKeyword = (index: number) => {
  localConfig.keywords.splice(index, 1);
};
</script>
