import{d as e,b2 as a,ai as t,c as l,e as r,f as s,i as d,ak as o}from"./index.Dk5pbsTU.js";import{E as p}from"./el-date-picker.B6TshyBV.js";import"./el-input.DiGatoux.js";/* empty css                     */import"./el-popper.Dbn4MgsT.js";import"./el-button.CXI119n4.js";const u=e({__name:"index",props:a({clearable:{type:Boolean,default:!0},type:{type:String,default:"datetimerange"},isSplit:{type:Boolean,default:!1}},{start:{type:String,required:!1,default:""},startModifiers:{},end:{type:String,required:!1,default:""},endModifiers:{},modelValue:{type:Array,required:!1,default:()=>[]},modelModifiers:{}}),emits:["update:start","update:end","update:modelValue"],setup(e){const a=e,u=t(e,"start"),i=t(e,"end"),n=t(e,"modelValue"),m=l({get:()=>a.isSplit?[u.value||"",i.value||""]:a.modelValue||[],set:e=>{a.isSplit?e&&e.length>0?(u.value=e[0]||"",i.value=e[1]||""):(u.value="",i.value=""):n.value=e}});return(a,t)=>{const l=p;return s(),r(l,{modelValue:d(m),"onUpdate:modelValue":t[0]||(t[0]=e=>o(m)?m.value=e:null),type:e.type,"range-separator":"~",clearable:e.clearable,"value-format":"YYYY-MM-DD HH:mm:ss","start-placeholder":"开始时间","end-placeholder":"结束时间"},null,8,["modelValue","type","clearable"])}}});export{u as _};
