import request from "@/utils/request";

// AI马甲配置列表查询参数类型
export interface AiVestConfigListQuery {
  vestId?: string;
  vestNickname?: string;
  type?: string;
  triggerMode?: string;
  ruleId?: number;
  status?: string;
  pageNum: number;
  pageSize: number;
}

// AI马甲配置VO类型
export interface AiVestConfigVo {
  id: number;
  vestId: number;
  vestNickname: string;
  modelName: string;
  temperature: number;
  promptTemplate: string;
  contextWindow: number;
  useRag: number;
  type: number;
  typeName: string;
  isDelay: number;
  fuzzinessRate: number;
  isAuto: number;
  triggerMode: number;
  triggerModeName: string;
  ruleId?: number;
  ruleName?: string;
  sessionRound: number;
  status: number;
  createdAt: string;
  updatedAt: string;
  activeState:number
}

// AI马甲配置DTO类型（用于新增/修改）
export interface AiVestConfigDto {
  id?: number;
  vestId: number;
  temperature?: number;
  promptTemplate?: string;
  contextWindow?: number;
  useRag?: number;
  type?: number;
  isDelay?: number;
  fuzzinessRate?: number;
  isAuto?: number;
  triggerMode?: number;
  ruleId?: number;
  sessionRound?: number;
  status?: number;
  knowledgeIds?: number[];
  sex?: string;
  age?: string;
  intro?: string;
}

// 分页返回类型
export interface PageAiVestConfigVo {
  records: AiVestConfigVo[];
  pageNumber: number;
  pageSize: number;
  totalPage: number;
  totalRow: number;
}

// 获取AI马甲配置列表
export function getAiVestConfigList(params: AiVestConfigListQuery) {
  return request<PageAiVestConfigVo>({
    url: "/system/aiVestConfig/list",
    method: "get",
    params,
  });
}

// 新增或修改AI马甲配置
export function saveOrUpdateAiVestConfig(data: AiVestConfigDto) {
  return request({
    url: "/system/aiVestConfig",
    method: "post",
    data,
  });
}

// 更新AI马甲配置状态
export function updateAiVestConfigStatus(id: number, status: number) {
  return request<boolean>({
    url: `/system/aiVestConfig/${id}/status`,
    method: "put",
    params: { status },
  });
}

// 删除AI马甲配置
export function deleteAiVestConfig(id: number) {
  return request<boolean>({
    url: `/system/aiVestConfig/${id}`,
    method: "delete",
  });
}
// 停用或激活AI马甲配置
export function updateAiVestConfigActiveState(id: number, activeState: number) {
  return request<boolean>({
    url: `/system/aiVestConfig/${id}/activeState`,
    method: "put",
    params: { activeState },
  });
}

// 根据马甲ID获取AI配置
export function getAiVestConfigByVestId(vestId: number) {
  return request<AiVestConfigVo>({
    url: `/system/aiVestConfig/vest/${vestId}`,
    method: "get",
  });
}

// AI马甲触发规则VO
export interface AiVestTriggerRulesVo {
  id: number;
  ruleName: string;
  description: string;
  conditionConfig: any;
  responseType: string;
  responseTypeName: string;
  staticResponse: string;
  dynamicPrompt: string;
  priority: number;
  cooldownSeconds: number;
  enabled: number;
  createdAt: string;
}

// 获取所有启用的触发规则列表
export function getEnabledTriggerRules() {
  return request<AiVestTriggerRulesVo[]>({
    url: "/system/aiVestConfig/enabled-trigger-rules",
    method: "get",
  });
}

// AI马甲触发规则查询参数
export interface AiVestTriggerRuleListQuery {
  ruleName?: string;
  description?: string;
  responseType?: string;
  priority?: string;
  enabled?: string;
  pageNum: number;
  pageSize: number;
}

// AI马甲触发规则VO
export interface AiVestTriggerRuleVo {
  id: number;
  ruleName: string;
  description: string;
  conditionConfig: any;
  responseType: string;
  responseTypeName: string;
  staticResponse: string;
  dynamicPrompt: string;
  priority: number;
  cooldownSeconds: number;
  enabled: number;
  createdAt: string;
}

// 条件配置DTO
export interface ConditionConfigDto {
  conditionTypeId: number; // 条件类型ID
  conditionConfig: any; // 具体条件配置
  logicalOperator?: string; // 逻辑关系
}

// AI马甲触发规则DTO（新增/修改）
export interface AiVestTriggerRuleDto {
  id?: number;
  ruleName: string;
  description?: string;
  responseType: string;
  staticResponse?: string;
  dynamicPrompt?: string;
  priority: number;
  cooldownSeconds: number;
  enabled: number;
  conditions?: ConditionConfigDto[]; // 条件配置列表
}

// 分页返回 - 直接代表API返回的数据结构
export interface PageResult {
  list: AiVestTriggerRuleVo[];
  total: number;
}

// 条件配置VO
export interface ConditionConfigVo {
  id: number;
  ruleId: number;
  conditionTypeId: number;
  conditionTypeName: string;
  conditionConfig: any;
  logicalOperator: string;
}

// AI马甲触发规则详情VO
export interface AiVestTriggerRuleDetailVo {
  id: number;
  ruleName: string;
  description: string;
  conditionConfig: any;
  responseType: string;
  responseTypeName: string;
  staticResponse: string;
  dynamicPrompt: string;
  priority: number;
  cooldownSeconds: number;
  enabled: number;
  createdAt: string;
  conditions: ConditionConfigVo[];
}

// 获取AI马甲触发规则列表
export function getAiVestTriggerRuleList(params: AiVestTriggerRuleListQuery) {
  return request<PageResult>({
    url: "/ai-vest/trigger-rules/list",
    method: "get",
    params,
  });
}

// 新增或修改AI马甲触发规则
export function saveOrUpdateAiVestTriggerRule(data: AiVestTriggerRuleDto) {
  return request<number>({
    url: "/ai-vest/trigger-rules/saveOrUpdate",
    method: "post",
    data,
  });
}

// 更新AI马甲触发规则状态
export function updateAiVestTriggerRuleStatus(id: number, enabled: number) {
  return request<boolean>({
    url: `/ai-vest/trigger-rules/status`,
    method: "post",
    data: { id, enabled },
  });
}

// 删除AI马甲触发规则
export function deleteAiVestTriggerRule(id: number) {
  return request<boolean>({
    url: `/ai-vest/trigger-rules/${id}`,
    method: "delete",
  });
}

// 获取AI马甲触发规则详情
export function getAiVestTriggerRuleDetail(id: number) {
  return request<AiVestTriggerRuleDetailVo>({
    url: `/ai-vest/trigger-rules/${id}/detail`,
    method: "get",
  });
}

// AI马甲条件类型VO
export interface AiVestConditionTypeVo {
  id: number;
  typeName: string;
  description: string;
  configSchema: any;
}

// 获取条件类型列表
export function getAiVestConditionTypes() {
  return request<AiVestConditionTypeVo[]>({
    url: "/ai-vest/trigger-rules/condition-types",
    method: "get",
  });
}

// AI马甲用户类型VO
export interface VestUserTypeVo {
  type: number;
  description: string;
}

// 获取AI马甲用户类型列表
export function getVestUserType() {
  return request<VestUserTypeVo[]>({
    url: "/system/aiVestConfig/getVestUserType",
    method: "get",
  });
}
