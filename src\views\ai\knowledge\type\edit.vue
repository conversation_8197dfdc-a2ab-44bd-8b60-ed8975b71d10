<template>
  <el-dialog
    v-model="dialog.visible"
    :title="dialog.title"
    width="800px"
    @closed="handleDialogClosed"
  >
    <div>
      <el-form class="mb-20px">
        <el-form-item label="输入UID" required>
          <el-input
            v-model="phoneInput"
            clearable
            type="textarea"
            placeholder="请输入UID，若多个UID，请用'逗号'隔开"
          />
        </el-form-item>
        <el-form-item>
          <div style="text-align: right; width: 100%">
            <el-button type="primary" :disabled="!phoneInput" @click="handleRecognize">
              识别
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <div v-if="assistantList.length > 0">
        <div class="mb-10px">成功识别如下用户信息：</div>
        <el-table :data="assistantList" border>
          <el-table-column label="序号" align="center" width="55" type="index" />
          <el-table-column label="头像" align="center" width="100">
            <template #default="{ row }">
              <el-image
                style="width: 30px; height: 30px; border-radius: 30px"
                :preview-src-list="[row.avatar]"
                preview-teleported
                :src="row.avatar"
              />
            </template>
          </el-table-column>
          <el-table-column label="微信昵称" align="center" prop="wechatName" min-width="120" />
          <el-table-column label="姓名" align="center" min-width="120">
            <template #default="{ row }">
              <el-input v-model="row.userName" placeholder="请输入姓名" />
            </template>
          </el-table-column>
          <el-table-column label="助理昵称" align="center" min-width="120">
            <template #default="{ row }">
              <el-input v-model="row.nickName" placeholder="请输入昵称" />
            </template>
          </el-table-column>
          <!-- <el-table-column label="手机号" align="center" min-width="120" prop="mobile" /> -->
          <el-table-column label="操作" align="center" width="80">
            <template #default="{ $index }">
              <el-button type="danger" circle @click="removeAssistant($index)">
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialog.visible = false">取 消</el-button>
        <el-button type="primary" :disabled="assistantList.length === 0" @click="handleSubmit">
          确认添加
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import AssistantApi, { SaveAssistantDto } from "@/api/webcasterAndAssistant/assistant";
import { Delete } from "@element-plus/icons-vue";

const dialog = reactive({
  visible: false,
  title: "新增助理",
});

const phoneInput = ref("");
const assistantList = ref<SaveAssistantDto[]>([]);
const emits = defineEmits(["success"]);
const loading = ref(false);

// 处理助理识别
function handleRecognize() {
  if (!phoneInput.value) {
    ElMessage.warning("请输入Uid");
    return;
  }

  // 支持多个uid，以逗号分隔
  const uids = phoneInput.value.replaceAll("，", ",");

  if (!uids) {
    ElMessage.warning("请输入有效的uid");
    return;
  }
  loading.value = true;

  return AssistantApi.getUserByPhone(uids)
    .then((res) => {
      res.forEach((item) => {
        // 检查是否已存在相同手机号的记录
        const existingIndex = assistantList.value.findIndex((f) => item.appUserId === f.appUserId);

        // 创建新的助理对象
        const newAssistant = new SaveAssistantDto(item);

        if (existingIndex >= 0) {
          // 替换已存在的记录
          assistantList.value[existingIndex] = newAssistant;
        } else {
          // 添加新记录
          assistantList.value.push(newAssistant);
        }
      });
    })
    .finally(() => (loading.value = false));
}

// 清空手机号输入
function clearPhoneInput() {
  phoneInput.value = "";
}

// 移除助理
function removeAssistant(index: number) {
  assistantList.value.splice(index, 1);
}

// 提交表单
function handleSubmit() {
  if (assistantList.value.length === 0) {
    ElMessage.warning("请至少添加一个助理");
    return;
  }

  // 验证每个助理的必填字段
  for (const assistant of assistantList.value) {
    if (!assistant.userName) {
      ElMessage.warning("请填写姓名");
      return;
    }
    if (!assistant.nickName) {
      ElMessage.warning("请填写昵称");
      return;
    }
  }

  loading.value = true;
  AssistantApi.save(assistantList.value)
    .then(() => {
      ElMessage.success("保存成功");
      dialog.visible = false;
      emits("success");
    })
    .finally(() => {
      loading.value = false;
    });
}

// 关闭对话框时清空数据
function handleDialogClosed() {
  phoneInput.value = "";
  assistantList.value = [];
}

defineExpose({
  open: () => {
    dialog.visible = true;
    dialog.title = "新增助理";
    // 清空数据
    phoneInput.value = "";
    assistantList.value = [];
  },
});
</script>

<style scoped>
.mb-20px {
  margin-bottom: 20px;
}

.mb-10px {
  margin-bottom: 10px;
}
</style>
