import{d as e,S as l,r as a,e as t,f as s,w as o,m as i,$ as r,F as u,i as m,C as n}from"./index.Dk5pbsTU.js";import{E as d}from"./el-drawer.Df_TzNjH.js";import"./el-overlay.DpVCS8zG.js";import{E as _,a as p}from"./el-descriptions-item.BlvmJIy_.js";import{_ as b}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as f}from"./el-image-viewer.BH897zgF.js";const v=["innerHTML"],c=e({__name:"info",setup(e,{expose:c}){const j=l({visible:!1,title:""}),w=a({});return c({open:e=>{j.visible=!0,j.title="主播详情",w.value=e}}),(e,l)=>{const a=p,c=f,x=b,V=_,h=d;return s(),t(h,{modelValue:m(j).visible,"onUpdate:modelValue":l[1]||(l[1]=e=>m(j).visible=e),title:m(j).title,size:"80vw"},{default:o((()=>[i(V,{column:1,border:"","label-width":"90px"},{default:o((()=>[i(a,{label:"姓名"},{default:o((()=>[r(u(m(w).userName),1)])),_:1}),i(a,{label:"昵称"},{default:o((()=>[r(u(m(w).nickName),1)])),_:1}),i(a,{label:"手机号"},{default:o((()=>[r(u(m(w).mobile),1)])),_:1}),i(a,{label:"头像"},{default:o((()=>[i(c,{src:m(w).avatar,style:{width:"100px",height:"100px"}},null,8,["src"])])),_:1}),i(a,{label:"自主开播"},{default:o((()=>[i(x,{modelValue:m(w).isAutonomous,"onUpdate:modelValue":l[0]||(l[0]=e=>m(w).isAutonomous=e),code:"yes_or_no"},null,8,["modelValue"])])),_:1}),i(a,{label:"主播介绍"},{default:o((()=>[n("div",{innerHTML:m(w).introduction},null,8,v)])),_:1})])),_:1})])),_:1},8,["modelValue","title"])}}});export{c as _};
