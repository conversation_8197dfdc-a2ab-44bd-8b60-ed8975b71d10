<template>
  <el-container>
    <el-main class="p-0 m-0">
      <el-card shadow="never" title="公司介绍">
        <wang-editor
          v-model="content"
          :style="{ height: '500px' }"
        />
      </el-card>
    </el-main>
    <el-footer class="text-align-center justify-center">
      <el-button type="primary" :loading="saveInfo.loading" @click="onSubmit">保&nbsp;存</el-button>
    </el-footer>
  </el-container>
</template>

<script setup lang="ts">
import { getCompanyIntro, saveCompanyIntro, type CompanyIntroVo } from '@/api/live/companyIntro'
import { useApi } from '@/utils/commonSetup'
import { ref, watch } from 'vue'

defineOptions({ name: 'CompanyIntro' })

const content = ref('')

// Get company introduction
const { sendInfo, onSend } = useApi<null, CompanyIntroVo>(getCompanyIntro)
onSend()

// Watch for data changes
watch(() => sendInfo.data, (newVal) => {
  if (newVal) {
    content.value = newVal.content
  }
}, { immediate: true })

// Save API
const { sendInfo: saveInfo, onSend: onSave } = useApi<string, any>(saveCompanyIntro as any)

// Submit handler
const onSubmit = async () => {
  if (!content.value) {
    ElMessage.warning('请输入公司介绍内容')
    return
  }

  saveInfo.params = content.value
  try {
    await onSave()
    ElMessage.success('保存成功')
    onSend() // Refresh data
  } catch (error) {
    console.error('Save failed:', error)
  }
}
</script> 