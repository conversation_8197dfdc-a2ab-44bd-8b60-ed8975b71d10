import request from "@/utils/request";
import AuthAPI from "@/api/auth";

export class TagPageQuery {
  pageNum = 1;
  pageSize = 10;
  name?: string;
  status?: number;

  constructor(pageSize = "10") {
    this.pageSize = parseInt(pageSize);
  }
}

export class TagUserPageQuery {
  pageNum = 1;
  pageSize = 10;
  keyword: string = "";
  startPoints: string = "";
  endPoints: string = "";
  tagId: string = "";

  constructor(pageSize = "10") {
    this.pageSize = parseInt(pageSize);
  }
}

export interface TagPageVO {
  id: number;
  name: string;
  status: number;
  loading?: boolean;
  count: string;
}

export interface BatchAddTagDto {
  tagNames: string;
}

export interface UpdateTagDto {
  id: number | string;
  name: string;
  status: number;
}

export interface PageTagUserVO {
  /*用户ID*/
  id: number;
  /*用户昵称*/
  nickName: string;
  /*当前积分*/
  currentPoints: number;
  /*当前用户等级*/
  currentLevel: string;
  /*标签列表*/
  tagList: string[];
}

const TagApi = {
  page(params: TagPageQuery) {
    return request.get("/api/basic/tag/list", { params });
  },
  userPage(params: TagUserPageQuery) {
    return request.get("/api/basic/tag/user", { params });
  },

  batchAdd(data: BatchAddTagDto) {
    return request.post("/api/basic/tag/batch", data);
  },

  remove(id: number) {
    return request.delete(`/api/basic/tag/${id}`);
  },

  update(data: UpdateTagDto) {
    return request.put("/api/basic/tag", data);
  },
};
export default TagApi;
