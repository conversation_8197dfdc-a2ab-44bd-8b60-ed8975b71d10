<template>
  <el-dialog
    title="批量添加标签"
    v-model="visible"
    width="500px"
    append-to-body
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item
        v-for="(tag, index) in tagList"
        :key="index"
        :label="index === 0 ? '标签名称' : ''"
        :prop="'tagList.' + index + '.value'"
        :rules="{ required: true, message: '请输入标签名称', trigger: 'blur' }"
      >
        <div class="flex items-center">
          <el-input
            v-model="tag.value"
            placeholder="请输入标签名称"
            @keyup.enter="addTag"
          />
          <el-button
            v-if="index === tagList.length - 1"
            type="primary"
            link
            icon="plus"
            @click="addTag"
          >
            添加
          </el-button>
          <el-button
            v-if="tagList.length > 1"
            type="danger"
            link
            icon="delete"
            @click="removeTag(index)"
          >
            删除
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import TagApi, { BatchAddTagDto } from '@/api/live/tags'

defineOptions({ name: 'BatchAddTag' })

const emit = defineEmits(['success'])
const visible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

interface TagItem {
  value: string
}

const tagList = ref<TagItem[]>([{ value: '' }])

const form = reactive<BatchAddTagDto>({
  tagNames: ''
})

/** 添加标签输入框 */
function addTag() {
  tagList.value.push({ value: '' })
}

/** 删除标签输入框 */
function removeTag(index: number) {
  tagList.value.splice(index, 1)
}

/** 取消按钮 */
function cancel() {
  reset()
}

/** 提交按钮 */
function submitForm() {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      // 过滤空值并用逗号拼接
      const tagNames = tagList.value
        .map(tag => tag.value.trim())
        .filter(value => value)
        .join(',')
      
      if (!tagNames) {
        ElMessage.warning('请至少输入一个标签名称')
        return
      }

      loading.value = true
      form.tagNames = tagNames
      TagApi.batchAdd(form)
        .then(() => {
          ElMessage.success('操作成功')
          emit('success')
          reset()
        })
        .finally(() => {
          loading.value = false
        })
    }
  })
}

/** 表单重置 */
function reset() {
  visible.value = false
  tagList.value = [{ value: '' }]
  form.tagNames = ''
}

/** 打开弹窗 */
function open() {
  reset()
  visible.value = true
}

defineExpose({
  open
})
</script> 