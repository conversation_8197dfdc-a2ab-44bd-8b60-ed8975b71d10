import{d as e,r as t,aQ as o,g as a,f as i,C as r,m as l,w as s,i as n,$ as p,V as d,e as m,h as u,a_ as c,az as _}from"./index.Dk5pbsTU.js";import{v as j}from"./el-loading.Dqi-qL7c.js";import{E as f}from"./el-card.DwLhVNHW.js";import g from"./index.Cywy93e7.js";import{a as y,E as v}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{_ as h}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";/* empty css               */import{a as w,E as b}from"./el-form-item.Bw6Zyv_7.js";import{E as x}from"./el-button.CXI119n4.js";import{_ as V}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{_ as k,V as C,A as U}from"./edit.vue_vue_type_script_setup_true_lang.dEGtdQmb.js";import{u as q}from"./commonSetup.Dm-aByKQ.js";/* empty css                       */import"./el-input.DiGatoux.js";import"./el-overlay.DpVCS8zG.js";import{E as T}from"./index.BcMfjWDS.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./event.BwRzfsZt.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.DuiNpp1i.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./el-radio.w2rep3_A.js";import"./el-dialog.Cnp8BitR.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./FileUpload.DqYUzwLQ.js";import"./el-progress.BQBUwu9o.js";import"./cloneDeep.DcCMo0F4.js";import"./index.WzKGworL.js";import"./el-switch.kQ5v4arH.js";import"./validator.HGn2BZtD.js";import"./validate.Bicq6Mu8.js";const B={class:"app-container"},E={class:"search-bar"},R={class:"mb-10px"},z=e({name:"AppVersion",__name:"index",setup(e){const z=t(),{page:P,getPage:S,resetQuery:A}=q(new U,C.page);return(e,t)=>{const U=V,q=w,H=x,N=b,F=v,I=h,L=y,M=g,O=f,Q=o("hasPerm"),$=j;return i(),a("div",B,[r("div",E,[l(N,{ref:"queryFormRef",model:n(P).query,inline:!0},{default:s((()=>[l(q,{prop:"status",label:"状态"},{default:s((()=>[l(U,{modelValue:n(P).query.status,"onUpdate:modelValue":t[0]||(t[0]=e=>n(P).query.status=e),code:"version_status"},null,8,["modelValue"])])),_:1}),l(q,{prop:"status",label:"操作系统"},{default:s((()=>[l(U,{modelValue:n(P).query.deviceType,"onUpdate:modelValue":t[1]||(t[1]=e=>n(P).query.deviceType=e),code:"version_device_type"},null,8,["modelValue"])])),_:1}),l(q,null,{default:s((()=>[l(H,{type:"primary",icon:"search",onClick:n(S)},{default:s((()=>t[6]||(t[6]=[p("搜索")]))),_:1,__:[6]},8,["onClick"]),l(H,{icon:"refresh",onClick:n(A)},{default:s((()=>t[7]||(t[7]=[p("重置")]))),_:1,__:[7]},8,["onClick"])])),_:1})])),_:1},8,["model"])]),l(O,{shadow:"never"},{default:s((()=>[r("div",R,[d((i(),m(H,{type:"success",icon:"plus",onClick:t[2]||(t[2]=e=>{var t;return null==(t=n(z))?void 0:t.open()})},{default:s((()=>t[8]||(t[8]=[p(" 新增 ")]))),_:1,__:[8]})),[[Q,["sys:version:save"]]])]),d((i(),m(L,{ref:"dataTableRef",data:n(P).data.records,"highlight-current-row":"",border:""},{default:s((()=>[l(F,{label:"序号",align:"center",width:"55",type:"index"}),l(F,{label:"版本号",align:"center",prop:"versionName","min-width":"100"}),l(F,{label:"操作系统",align:"center",width:"100"},{default:s((({row:e})=>[l(I,{modelValue:e.deviceType,"onUpdate:modelValue":t=>e.deviceType=t,code:"version_device_type"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),l(F,{label:"状态",align:"center",width:"90"},{default:s((({row:e})=>[l(I,{modelValue:e.status,"onUpdate:modelValue":t=>e.status=t,code:"version_status"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),l(F,{label:"是否热更新",align:"center",width:"80"},{default:s((({row:e})=>[l(I,{modelValue:e.isHotUpdate,"onUpdate:modelValue":t=>e.isHotUpdate=t,code:"yes_or_no"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),l(F,{label:"更新内容",align:"center",prop:"updateContent","min-width":"150","show-overflow-tooltip":"","tooltip-options":{"popper-class":"table-popper-class"}}),l(F,{label:"发版时间",align:"center",prop:"publishTime","min-width":"100"}),l(F,{label:"创建时间",align:"center",prop:"createdAt","min-width":"100"}),l(F,{fixed:"right",label:"操作",width:"180"},{default:s((e=>[0===e.row.status&&n(c)("sys:version:save")?(i(),m(H,{key:0,type:"primary",size:"small",link:"",icon:"edit",onClick:t=>{var o;return null==(o=n(z))?void 0:o.open(e.row)}},{default:s((()=>t[9]||(t[9]=[p(" 编辑 ")]))),_:2,__:[9]},1032,["onClick"])):u("",!0),0===e.row.status&&n(c)("sys:version:delete")?(i(),m(H,{key:1,type:"danger",size:"small",link:"",icon:"delete",loading:e.row.loading,onClick:t=>{return o=e.row,void T.confirm(`确定删除《${o.versionName}》版本吗？`,"删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{o.loading=!0,C.remove(o.id).then((()=>{_.success("删除成功"),A()})).finally((()=>o.loading=!1))})).catch((()=>_.info("已取消")));var o}},{default:s((()=>t[10]||(t[10]=[p(" 删除 ")]))),_:2,__:[10]},1032,["loading","onClick"])):u("",!0),0===e.row.status&&n(c)("sys:version:save")?(i(),m(H,{key:2,type:"warning",size:"small",link:"",loading:e.row.loading,onClick:t=>{return o=e.row,void T.confirm("确定要发布新版本吗？","版本发布",{confirmButtonText:"确定发布",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{o.loading=!0,C.publish(o.id).then((()=>{_.success("操作成功"),A()})).finally((()=>o.loading=!1))})).catch((()=>_.info("已取消")));var o}},{default:s((()=>t[11]||(t[11]=[p(" 发布 ")]))),_:2,__:[11]},1032,["loading","onClick"])):u("",!0)])),_:1})])),_:1},8,["data"])),[[$,n(P).loading]]),n(P).data.totalRow?(i(),m(M,{key:0,total:n(P).data.totalRow,"onUpdate:total":t[3]||(t[3]=e=>n(P).data.totalRow=e),page:n(P).query.pageNum,"onUpdate:page":t[4]||(t[4]=e=>n(P).query.pageNum=e),limit:n(P).query.pageSize,"onUpdate:limit":t[5]||(t[5]=e=>n(P).query.pageSize=e),onPagination:n(S)},null,8,["total","page","limit","onPagination"])):u("",!0)])),_:1}),l(k,{ref_key:"editModelRef",ref:z,onSuccess:n(A)},null,8,["onSuccess"])])}}});export{z as default};
