import{d as e,aI as l,r as a,I as t,o as d,e as u,h as o,k as s,i as r,ak as i,w as n,g as p,P as y,Q as m,f as b,$ as c,F as v}from"./index.Dk5pbsTU.js";import{a as f,E as V}from"./el-checkbox.DDYarIkn.js";import{E as h,a as g}from"./el-radio.w2rep3_A.js";/* empty css               */import{E as k,a as x}from"./el-select.CRWkm-it.js";/* empty css                     */import"./el-popper.Dbn4MgsT.js";const j=e({__name:"index",props:{clearable:{type:Boolean,default:!0},code:{type:String,required:!0},modelValue:{type:[String,Number,Array],required:!1},type:{type:String,default:"select",validator:e=>["select","radio","checkbox"].includes(e)},placeholder:{type:String,default:"请选择"},disabled:{type:Boolean,default:!1},style:{type:Object,default:()=>({width:"200px"})}},emits:["update:modelValue"],setup(e,{emit:j}){const _=l(),A=e,S=j,C=a([]),E=a("string"==typeof A.modelValue||"number"==typeof A.modelValue||Array.isArray(A.modelValue)?A.modelValue:void 0);function U(e){S("update:modelValue",e)}return t((()=>A.modelValue),(e=>{"checkbox"===A.type?E.value=Array.isArray(e)?e:[]:E.value=(null==e?void 0:e.toString())||""}),{immediate:!0}),t((()=>C.value),(e=>{if(e.length>0&&void 0!==E.value){e.find((e=>e.value===E.value))||"checkbox"===A.type||(E.value="")}})),d((()=>{C.value=_.getDictionary(A.code)})),(l,a)=>{const t=x,d=k,j=g,_=h,A=V,S=f;return"select"===e.type?(b(),u(d,{key:0,modelValue:r(E),"onUpdate:modelValue":a[0]||(a[0]=e=>i(E)?E.value=e:null),placeholder:e.placeholder,disabled:e.disabled,clearable:e.clearable,style:s(e.style),onChange:U},{default:n((()=>[(b(!0),p(y,null,m(r(C),(e=>(b(),u(t,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder","disabled","clearable","style"])):"radio"===e.type?(b(),u(_,{key:1,modelValue:r(E),"onUpdate:modelValue":a[1]||(a[1]=e=>i(E)?E.value=e:null),disabled:e.disabled,style:s(e.style),onChange:U},{default:n((()=>[(b(!0),p(y,null,m(r(C),(e=>(b(),u(j,{key:e.value,label:e.label,value:e.value},{default:n((()=>[c(v(e.label),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","disabled","style"])):"checkbox"===e.type?(b(),u(S,{key:2,modelValue:r(E),"onUpdate:modelValue":a[2]||(a[2]=e=>i(E)?E.value=e:null),disabled:e.disabled,style:s(e.style),onChange:U},{default:n((()=>[(b(!0),p(y,null,m(r(C),(e=>(b(),u(A,{key:e.value,label:e.label,value:e.value},{default:n((()=>[c(v(e.label),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","disabled","style"])):o("",!0)}}});export{j as _};
