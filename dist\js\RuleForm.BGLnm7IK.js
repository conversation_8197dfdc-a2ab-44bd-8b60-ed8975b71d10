const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/KeywordsMatchConfig.tFi5HhMq.js","js/index.Dk5pbsTU.js","css/index.DZ2Q3hhD.css","js/el-form-item.Bw6Zyv_7.js","js/use-form-common-props.CQPDkY7k.js","js/castArray.C4RhTg2c.js","js/error.D_Dr4eZ1.js","js/index.D6CER_Ot.js","js/_Uint8Array.n_j8oILW.js","js/_arrayPush.DSBJLlac.js","js/_initCloneObject.BN1anLuC.js","css/el-form-item.BqrJjMte.css","js/el-button.CXI119n4.js","js/index.DuiNpp1i.js","js/use-form-item.DzRJVC1I.js","css/el-button.DhOMi8yW.css","js/el-input.DiGatoux.js","js/index.C9UdVphc.js","js/event.BwRzfsZt.js","js/index.DEKElSOG.js","js/index.Vn8pbgQR.js","css/el-input.Cz--kClu.css","js/el-radio.w2rep3_A.js","css/el-radio.wSVBx8Lp.css","js/index.L2DVy5yq.js","css/el-tag.DljBBxJR.css","js/TimeTriggerConfig.BaNxn777.js","js/el-input-number.C02ig7uT.js","js/index.Cd8M2JyP.js","css/el-input-number.DUUPPWGj.css","js/MetricsThresholdConfig.Cu9_S2k4.js","js/el-select.CRWkm-it.js","js/el-popper.Dbn4MgsT.js","js/index.C6NthMtN.js","js/isUndefined.DgmxjSXK.js","css/el-popper.DG5wR-qi.css","js/index.ybpLT-bz.js","js/token.DWNpOE8r.js","js/strings.MqEQKtyI.js","js/isEqual.C0S6DIiJ.js","js/scroll.CVc-P3_z.js","js/debounce.DJJTSR8O.js","js/_baseIteratee.CPRpgrLu.js","js/index.wZTqlYZ6.js","js/vnode.Cbclzz8S.js","css/el-select.CvzM3W2w.css","css/el-scrollbar.BWxh-h6K.css","js/ConfidenceThresholdConfig.DKSgtAoq.js","js/el-slider.DLapdkRs.js","css/el-slider.DtISwLyR.css","js/el-tooltip.l0sNRNKZ.js"])))=>i.map(i=>d[i]);
import{d as e,r as o,S as a,I as t,o as i,g as n,f as l,m as r,C as s,w as d,h as m,i as p,c1 as c,$ as u,e as f,P as y,Q as g,F as _,d7 as v,aR as D,az as b,cX as h}from"./index.Dk5pbsTU.js";import{E as j}from"./el-input.DiGatoux.js";import{E as x}from"./el-input-number.C02ig7uT.js";import{E as V,a as k}from"./el-radio.w2rep3_A.js";import{E as w}from"./el-card.DwLhVNHW.js";import{E as C}from"./el-alert.CImT_8mr.js";/* empty css               */import{E as T,a as U}from"./el-select.CRWkm-it.js";/* empty css                     */import"./el-popper.Dbn4MgsT.js";import{E}from"./el-empty.Dee0wMKK.js";import{E as O}from"./el-button.CXI119n4.js";import{a as I,E as S}from"./el-form-item.Bw6Zyv_7.js";import{E as N}from"./el-slider.DLapdkRs.js";import"./el-tooltip.l0sNRNKZ.js";import{a as R,E as J}from"./el-step.6FNGwSz5.js";import{_ as P}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./event.BwRzfsZt.js";import"./index.DEKElSOG.js";import"./use-form-item.DzRJVC1I.js";import"./use-form-common-props.CQPDkY7k.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./error.D_Dr4eZ1.js";import"./index.Cd8M2JyP.js";import"./index.DuiNpp1i.js";import"./index.ybpLT-bz.js";import"./index.L2DVy5yq.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./_initCloneObject.BN1anLuC.js";import"./index.C_BbqFDa.js";const q={class:"mt-6"},A={key:0},F={key:1},L={class:"flex justify-center mb-3"},$={class:"condition-list-container"},M={class:"flex justify-between items-center"},K={class:"flex flex-col"},Q={class:"text-xs text-gray-500"},z={key:0},B={class:"mb-2"},G={class:"text-sm text-gray-500",style:{padding:"6px"}},H={key:0},X={key:1},Y={key:2},Z={key:3},W={key:4},ee={key:1,class:"text-gray-500"},oe={key:2},ae={class:"text-sm text-gray-500 mt-2"},te={key:3},ie={class:"dialog-footer mt-4 flex justify-center"},ne=P(e({__name:"RuleForm",props:{formData:{type:Object,required:!0},conditionTypes:{type:Array,default:()=>[]},submitLoading:{type:Boolean,default:!1}},emits:["submit","update:formData"],setup(e,{expose:P,emit:ne}){const le=v((()=>h((()=>import("./KeywordsMatchConfig.tFi5HhMq.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25])))),re=v((()=>h((()=>import("./TimeTriggerConfig.BaNxn777.js")),__vite__mapDeps([26,1,2,3,4,5,6,7,8,9,10,11,16,17,18,19,14,20,21,27,28,29])))),se=v((()=>h((()=>import("./MetricsThresholdConfig.Cu9_S2k4.js")),__vite__mapDeps([30,1,2,3,4,5,6,7,8,9,10,11,16,17,18,19,14,20,21,27,28,29,31,32,33,34,35,36,24,37,38,39,40,41,42,43,44,45,25,46])))),de=v((()=>h((()=>import("./ConfidenceThresholdConfig.DKSgtAoq.js")),__vite__mapDeps([47,1,2,3,4,5,6,7,8,9,10,11,16,17,18,19,14,20,21,48,27,28,29,32,33,34,35,41,49,50])))),me="keyword",pe="timing",ce="activity",ue="llm_judge",fe=e,ye=ne,ge=o(0),_e=o(),ve=o(),De=o(),be=o(),he=a({conditions:[]}),je=o([]),xe=a({ruleName:[{required:!0,message:"请输入规则名称",trigger:"blur"}],priority:[{required:!0,message:"请输入优先级",trigger:"blur"}]}),Ve=a({responseType:[{required:!0,message:"请选择响应类型",trigger:"change"}],staticResponse:[{required:!0,message:"请输入静态回复内容",trigger:"blur",validator:(e,o,a)=>{const{responseType:t}=fe.formData;["static","hybrid"].includes(t)&&!o?a(new Error("请输入静态回复内容")):a()}}],dynamicPrompt:[{required:!0,message:"请输入动态生成提示词",trigger:"blur",validator:(e,o,a)=>{const{responseType:t}=fe.formData;["dynamic","hybrid"].includes(t)&&!o?a(new Error("请输入动态生成提示词")):a()}}]}),ke=a({cooldownSeconds:[{required:!0,message:"请设置冷却时间",trigger:"blur"}],enabled:[{required:!0,message:"请选择是否启用",trigger:"change"}]});t((()=>fe.formData),(e=>{(null==e?void 0:e.conditions)?je.value=e.conditions.map((e=>{if("string"==typeof e.conditionConfig)try{e.conditionConfig=JSON.parse(e.conditionConfig)}catch(o){}return e.conditionConfig=e.conditionConfig||{},JSON.stringify(e.conditionConfig,null,2)})):je.value=[]}),{deep:!0,immediate:!0}),i((()=>{var e;fe.formData.id&&(null==(e=fe.formData.conditions)?void 0:e.length)&&setTimeout((()=>{(fe.formData.conditions||[]).forEach(((e,o)=>{e.conditionConfig&&0!==Object.keys(e.conditionConfig).length||Oe(o)}))}),200)}));const we=e=>fe.conditionTypes.find((o=>o.id===e)),Ce=e=>{const o=we(e.conditionTypeId);return o?o.typeName:""},Te=()=>{const e={conditionTypeId:fe.conditionTypes.length>0?fe.conditionTypes[0].id:1,conditionConfig:{},logicalOperator:fe.formData.conditions&&fe.formData.conditions.length>0?"AND":void 0};if(fe.formData.conditions)fe.formData.conditions.push(e),ye("update:formData",{...fe.formData});else{const o={...fe.formData,conditions:[e]};ye("update:formData",o)}const o=fe.formData.conditions?fe.formData.conditions.length-1:0;je.value.push(JSON.stringify({},null,2)),setTimeout((()=>{Oe(o)}),0)},Ue=(e,o)=>{fe.formData.conditions&&fe.formData.conditions[e]&&(fe.formData.conditions[e].conditionConfig=o,ye("update:formData",{...fe.formData}),je.value[e]=JSON.stringify(o,null,2))},Ee=()=>{ye("update:formData",{...fe.formData})},Oe=e=>{var o;if(!fe.formData.conditions||!fe.formData.conditions[e])return;const a=fe.formData.conditions[e].conditionTypeId,t=we(a);if(!t)return fe.formData.conditions[e].conditionConfig={},ye("update:formData",{...fe.formData}),void(je.value[e]=JSON.stringify({},null,2));const i=fe.formData.conditions[e],n=Ce(i);let l={};switch(n){case me:l={keywords:[],match_mode:"partial"};break;case pe:l={delay_time:0};break;case ce:l={metric:"message_count",operator:">",threshold:10,time_window:60};break;case ue:l={min_confidence:.7,prompt_template:""};break;default:if(null==(o=t.configSchema)?void 0:o.properties){const e=t.configSchema.properties;Object.keys(e).forEach((o=>{const a=e[o];"array"===a.type?l[o]=[]:"number"===a.type||"integer"===a.type?l[o]=0:"string"===a.type?l[o]="":"boolean"===a.type?l[o]=!1:"object"===a.type&&(l[o]={}),a.enum&&a.enum.length&&(l[o]=a.enum[0])}))}}fe.formData.conditions[e].conditionConfig=l,ye("update:formData",{...fe.formData}),je.value[e]=JSON.stringify(l,null,2)},Ie=async()=>{var e,o;try{0===ge.value?await(null==(e=_e.value)?void 0:e.validate()):1===ge.value||2===ge.value&&await(null==(o=De.value)?void 0:o.validate()),ge.value++}catch(a){}},Se=()=>{ge.value--},Ne=async()=>{var e;try{await(null==(e=be.value)?void 0:e.validate()),ye("submit",fe.formData)}catch(o){}};return P({resetSteps:()=>{ge.value=0}}),(o,a)=>{const t=R,i=J,v=j,h=I,P=N,ne=S,Re=O,Je=E,Pe=U,qe=T,Ae=C,Fe=w,Le=k,$e=V,Me=x;return l(),n("div",null,[r(i,{active:ge.value,"finish-status":"success",simple:""},{default:d((()=>[r(t,{title:"基本信息"}),r(t,{title:"触发条件"}),r(t,{title:"响应内容"}),r(t,{title:"高级设置"})])),_:1},8,["active"]),s("div",q,[0===ge.value?(l(),n("div",A,[r(ne,{ref_key:"basicFormRef",ref:_e,model:e.formData,rules:xe,"label-width":"120px"},{default:d((()=>[r(h,{label:"规则名称",prop:"ruleName"},{default:d((()=>[r(v,{modelValue:e.formData.ruleName,"onUpdate:modelValue":a[0]||(a[0]=o=>e.formData.ruleName=o),placeholder:"请输入规则名称"},null,8,["modelValue"])])),_:1}),r(h,{label:"规则描述",prop:"description"},{default:d((()=>[r(v,{modelValue:e.formData.description,"onUpdate:modelValue":a[1]||(a[1]=o=>e.formData.description=o),type:"textarea",rows:3,placeholder:"请输入规则描述"},null,8,["modelValue"])])),_:1}),r(h,{label:"优先级",prop:"priority"},{default:d((()=>[r(P,{modelValue:e.formData.priority,"onUpdate:modelValue":a[2]||(a[2]=o=>e.formData.priority=o),min:1,max:10,step:1,"show-input":"",style:{width:"300px"}},null,8,["modelValue"]),a[8]||(a[8]=s("span",{class:"text-sm text-gray-500 ml-2"},"数值越大优先级越高",-1))])),_:1,__:[8]})])),_:1},8,["model","rules"])])):1===ge.value?(l(),n("div",F,[r(ne,{ref_key:"conditionFormRef",ref:ve,model:he,"label-width":"120px"},{default:d((()=>{var o;return[s("div",L,[r(Re,{type:"primary",icon:p(c),onClick:Te},{default:d((()=>a[9]||(a[9]=[u("添加条件")]))),_:1,__:[9]},8,["icon"])]),s("div",$,[(null==(o=e.formData.conditions)?void 0:o.length)?(l(!0),n(y,{key:1},g(e.formData.conditions,((o,a)=>(l(),n("div",{key:a,class:"mb-4"},[r(Fe,{shadow:"hover",class:"condition-card condition-item"},{header:d((()=>[s("div",M,[s("span",null,"条件 #"+_(a+1),1),s("div",null,[a>0?(l(),f(qe,{key:0,modelValue:o.logicalOperator,"onUpdate:modelValue":e=>o.logicalOperator=e,style:{width:"80px","margin-right":"10px"}},{default:d((()=>[r(Pe,{label:"并且",value:"AND"}),r(Pe,{label:"或者",value:"OR"})])),_:2},1032,["modelValue","onUpdate:modelValue"])):m("",!0),r(Re,{type:"danger",icon:p(D),circle:"",onClick:e=>(e=>{fe.formData.conditions&&(fe.formData.conditions.splice(e,1),ye("update:formData",{...fe.formData}),je.value.splice(e,1))})(a)},null,8,["icon","onClick"])])])])),default:d((()=>[r(h,{label:"条件类型",prop:`conditions[${a}].conditionTypeId`},{default:d((()=>[r(qe,{modelValue:o.conditionTypeId,"onUpdate:modelValue":e=>o.conditionTypeId=e,placeholder:"请选择条件类型",style:{width:"100%"},onChange:()=>Oe(a)},{default:d((()=>[(l(!0),n(y,null,g(e.conditionTypes,(e=>(l(),f(Pe,{key:e.id,label:e.description,value:e.id},{default:d((()=>[s("div",K,[s("span",null,_(e.description),1),s("span",Q,_(e.description),1)])])),_:2},1032,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])),_:2},1032,["prop"]),r(h,{label:"配置",prop:`conditions[${a}].conditionConfig`},{default:d((()=>{var e;return[o.conditionTypeId?(l(),n("div",z,[s("div",B,[s("div",G,_((null==(e=we(o.conditionTypeId))?void 0:e.description)||"请配置参数"),1)]),Ce(o)===me?(l(),n("div",H,[r(p(le),{config:o.conditionConfig,onUpdate:e=>Ue(a,e)},null,8,["config","onUpdate"])])):Ce(o)===pe?(l(),n("div",X,[r(p(re),{config:o.conditionConfig,onUpdate:e=>Ue(a,e)},null,8,["config","onUpdate"])])):Ce(o)===ce?(l(),n("div",Y,[r(p(se),{config:o.conditionConfig,onUpdate:e=>Ue(a,e)},null,8,["config","onUpdate"])])):Ce(o)===ue?(l(),n("div",Z,[r(p(de),{config:o.conditionConfig,onUpdate:e=>Ue(a,e)},null,8,["config","onUpdate"])])):(l(),n("div",W,[r(Ae,{type:"warning",closable:!1,title:"当前条件类型暂无可视化配置界面，请使用JSON格式配置",class:"mb-2"}),r(v,{modelValue:je.value[a],"onUpdate:modelValue":e=>je.value[a]=e,type:"textarea",rows:3,placeholder:"请输入JSON格式的条件配置",onInput:e=>(e=>{try{if(!fe.formData.conditions||!fe.formData.conditions[e])return;const o=JSON.parse(je.value[e]);fe.formData.conditions[e].conditionConfig=o,ye("update:formData",{...fe.formData});const a=we(fe.formData.conditions[e].conditionTypeId);null==a||a.configSchema}catch(o){b.warning("JSON格式错误，请检查输入")}})(a)},null,8,["modelValue","onUpdate:modelValue","onInput"])]))])):(l(),n("div",ee,"请先选择条件类型"))]})),_:2},1032,["prop"])])),_:2},1024)])))),128)):(l(),f(Je,{key:0,description:"暂无触发条件，请添加"}))])]})),_:1},8,["model"])])):2===ge.value?(l(),n("div",oe,[r(ne,{ref_key:"responseFormRef",ref:De,model:e.formData,rules:Ve,"label-width":"120px"},{default:d((()=>[r(h,{label:"响应类型",prop:"responseType"},{default:d((()=>[r($e,{modelValue:e.formData.responseType,"onUpdate:modelValue":a[3]||(a[3]=o=>e.formData.responseType=o)},{default:d((()=>[r(Le,{value:"static"},{default:d((()=>a[10]||(a[10]=[u("静态回复")]))),_:1,__:[10]}),r(Le,{value:"dynamic"},{default:d((()=>a[11]||(a[11]=[u("动态生成")]))),_:1,__:[11]}),r(Le,{value:"hybrid"},{default:d((()=>a[12]||(a[12]=[u("混合模式")]))),_:1,__:[12]})])),_:1},8,["modelValue"])])),_:1}),["static","hybrid"].includes(e.formData.responseType)?(l(),f(h,{key:0,label:"静态回复内容",prop:"staticResponse"},{default:d((()=>[r(v,{modelValue:e.formData.staticResponse,"onUpdate:modelValue":a[4]||(a[4]=o=>e.formData.staticResponse=o),type:"textarea",rows:4,placeholder:"请输入静态回复内容",onInput:Ee},null,8,["modelValue"])])),_:1})):m("",!0),["dynamic","hybrid"].includes(e.formData.responseType)?(l(),f(h,{key:1,label:"动态生成提示词",prop:"dynamicPrompt"},{default:d((()=>[r(v,{modelValue:e.formData.dynamicPrompt,"onUpdate:modelValue":a[5]||(a[5]=o=>e.formData.dynamicPrompt=o),type:"textarea",rows:4,placeholder:"请输入动态生成提示词",onInput:Ee},null,8,["modelValue"]),s("div",ae," 提示：可以使用 "+_(o.变量)+" 语法引用触发条件中的变量 ",1)])),_:1})):m("",!0)])),_:1},8,["model","rules"])])):3===ge.value?(l(),n("div",te,[r(ne,{ref_key:"advancedFormRef",ref:be,model:e.formData,rules:ke,"label-width":"120px"},{default:d((()=>[r(h,{label:"冷却时间(秒)",prop:"cooldownSeconds"},{default:d((()=>[r(Me,{modelValue:e.formData.cooldownSeconds,"onUpdate:modelValue":a[6]||(a[6]=o=>e.formData.cooldownSeconds=o),min:0,precision:0,style:{width:"120px"}},null,8,["modelValue"]),a[13]||(a[13]=s("div",{class:"text-sm text-gray-500 mt-1"}," 同一规则在冷却时间内只会触发一次，设置为0表示无冷却限制 ",-1))])),_:1,__:[13]}),r(h,{label:"是否启用",prop:"enabled"},{default:d((()=>[r($e,{modelValue:e.formData.enabled,"onUpdate:modelValue":a[7]||(a[7]=o=>e.formData.enabled=o)},{default:d((()=>[r(Le,{value:1},{default:d((()=>a[14]||(a[14]=[u("启用")]))),_:1,__:[14]}),r(Le,{value:0},{default:d((()=>a[15]||(a[15]=[u("禁用")]))),_:1,__:[15]})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])):m("",!0)]),s("div",ie,[ge.value>0?(l(),f(Re,{key:0,onClick:Se},{default:d((()=>a[16]||(a[16]=[u("上一步")]))),_:1,__:[16]})):m("",!0),ge.value<3?(l(),f(Re,{key:1,type:"primary",onClick:Ie},{default:d((()=>a[17]||(a[17]=[u("下一步")]))),_:1,__:[17]})):(l(),f(Re,{key:2,type:"success",loading:e.submitLoading,onClick:Ne},{default:d((()=>a[18]||(a[18]=[u(" 提交 ")]))),_:1,__:[18]},8,["loading"]))])])}}}),[["__scopeId","data-v-b6ef7541"]]);export{ne as default};
