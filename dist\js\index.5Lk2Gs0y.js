import{d as e,r as o,aQ as r,g as t,f as i,C as l,m as s,w as a,Z as n,i as p,$ as m,V as d,e as u,h as c,a_ as j,az as f}from"./index.Dk5pbsTU.js";import{v as _}from"./el-loading.Dqi-qL7c.js";import{E as v}from"./el-card.DwLhVNHW.js";import g from"./index.Cywy93e7.js";import{a as y,E as w}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{_ as k}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as h}from"./el-image-viewer.BH897zgF.js";/* empty css               */import{a as x,E as b}from"./el-form-item.Bw6Zyv_7.js";import{E as C}from"./el-button.CXI119n4.js";import{_ as R}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as q}from"./el-input.DiGatoux.js";import U from"./edit.DqWGhE7O.js";import S from"./quick-reply.BUszv3kV.js";import V from"./vest.BH0B5JOA.js";import{u as E}from"./commonSetup.Dm-aByKQ.js";import{a as z,b as P}from"./liveRoom.B9NhOBdK.js";import N from"./cloneRoom.C57hBAo6.js";/* empty css                       */import"./el-overlay.DpVCS8zG.js";import{E as B}from"./index.BcMfjWDS.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./event.BwRzfsZt.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.DuiNpp1i.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./position.DfR5znly.js";import"./el-radio.w2rep3_A.js";import"./el-dialog.Cnp8BitR.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./el-input-number.C02ig7uT.js";import"./index.Cd8M2JyP.js";import"./el-col.Cfu8vZQ4.js";import"./el-switch.kQ5v4arH.js";import"./validator.HGn2BZtD.js";import"./SingleImageUpload.WGBxPB_4.js";import"./el-progress.BQBUwu9o.js";import"./cloneDeep.DcCMo0F4.js";import"./index.WzKGworL.js";import"./el-divider.2VNIZioN.js";import"./el-avatar.DtvYzXsq.js";import"./vest.j8NavXyg.js";import"./el-tab-pane.CXnN_Izo.js";import"./index.C_BbqFDa.js";import"./el-alert.CImT_8mr.js";import"./el-popover.Bo2lPKkO.js";import"./dropdown.B_OfpyL_.js";const I={class:"app-container"},K={class:"search-bar"},L={class:"mb-10px"},M=e({name:"LiveRoom",__name:"index",setup(e){const M=o(),{page:T,getPage:A,resetQuery:D}=E(new P,z.page),O=o(),Q=o(),$=o(),F=o(0);return(e,o)=>{const E=q,P=x,H=R,J=C,W=b,Z=w,G=h,X=k,Y=y,ee=g,oe=v,re=r("hasPerm"),te=_;return i(),t("div",I,[l("div",K,[s(W,{ref:"queryFormRef",model:p(T).query,inline:!0},{default:a((()=>[s(P,{prop:"keywords",label:""},{default:a((()=>[s(E,{modelValue:p(T).query.keywords,"onUpdate:modelValue":o[0]||(o[0]=e=>p(T).query.keywords=e),placeholder:"直播间名称检索",clearable:"",onKeyup:n(p(A),["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),s(P,{prop:"status",label:"直播间状态"},{default:a((()=>[s(H,{modelValue:p(T).query.status,"onUpdate:modelValue":o[1]||(o[1]=e=>p(T).query.status=e),code:"live_status"},null,8,["modelValue"])])),_:1}),s(P,null,{default:a((()=>[s(J,{type:"primary",icon:"search",onClick:p(A)},{default:a((()=>o[6]||(o[6]=[m("搜索")]))),_:1,__:[6]},8,["onClick"]),s(J,{icon:"refresh",onClick:p(D)},{default:a((()=>o[7]||(o[7]=[m("重置")]))),_:1,__:[7]},8,["onClick"])])),_:1})])),_:1},8,["model"])]),s(oe,{shadow:"never"},{default:a((()=>[l("div",L,[d((i(),u(J,{type:"success",icon:"plus",onClick:o[2]||(o[2]=e=>{var o;return null==(o=M.value)?void 0:o.open()})},{default:a((()=>o[8]||(o[8]=[m(" 新增 ")]))),_:1,__:[8]})),[[re,["live:room:save"]]])]),d((i(),u(Y,{ref:"dataTableRef",data:p(T).data.records,"highlight-current-row":"",border:""},{default:a((()=>[s(Z,{label:"序号",align:"center",width:"55",type:"index"}),s(Z,{label:"封面图",align:"center",prop:"name",width:"100"},{default:a((({row:e})=>[s(G,{style:{width:"30px",height:"30px"},"preview-src-list":[e.coverUrl],"preview-teleported":"",src:e.coverUrl},null,8,["preview-src-list","src"])])),_:1}),s(Z,{label:"直播间名称",align:"center",prop:"roomName","min-width":"120"}),s(Z,{label:"累计开播次数",align:"center",prop:"liveCount","min-width":"100"}),s(Z,{label:"直播间状态",align:"center",width:"120"},{default:a((({row:e})=>[s(X,{modelValue:e.status,"onUpdate:modelValue":o=>e.status=o,code:"live_status"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(Z,{fixed:"right",label:"操作",width:"280"},{default:a((e=>[d((i(),u(J,{type:"primary",size:"small",link:"",icon:"edit",onClick:o=>{var r;return null==(r=O.value)?void 0:r.open(e.row)}},{default:a((()=>o[9]||(o[9]=[m(" 编辑 ")]))),_:2,__:[9]},1032,["onClick"])),[[re,["live:room:save"]]]),d((i(),u(J,{type:"primary",size:"small",link:"",icon:"chat-line-round",onClick:o=>{var r;return null==(r=Q.value)?void 0:r.open(e.row)}},{default:a((()=>o[10]||(o[10]=[m(" 快捷语设置 ")]))),_:2,__:[10]},1032,["onClick"])),[[re,["live:quick-reply"]]]),d((i(),u(J,{type:"warning",size:"small",link:"",icon:"user",onClick:o=>{return r=e.row,F.value=r.id,void(null==(t=$.value)||t.open());var r,t}},{default:a((()=>o[11]||(o[11]=[m(" 马甲设置 ")]))),_:2,__:[11]},1032,["onClick"])),[[re,["live:vest"]]]),0===e.row.status&&e.row.liveCount<=0&&p(j)("live:room:delete")?(i(),u(J,{key:0,type:"danger",size:"small",link:"",icon:"delete",loading:e.row.loading,onClick:o=>{return r=e.row,void B.confirm(`确定删除直播间《${r.roomName}》吗？`,"删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{r.loading=!0,z.remove(r.id).then((()=>{f.success("删除成功"),D()})).finally((()=>r.loading=!1))})).catch((()=>f.info("已取消")));var r}},{default:a((()=>o[12]||(o[12]=[m(" 删除 ")]))),_:2,__:[12]},1032,["loading","onClick"])):c("",!0)])),_:1})])),_:1},8,["data"])),[[te,p(T).loading]]),p(T).data.totalRow?(i(),u(ee,{key:0,total:p(T).data.totalRow,"onUpdate:total":o[3]||(o[3]=e=>p(T).data.totalRow=e),page:p(T).query.pageNum,"onUpdate:page":o[4]||(o[4]=e=>p(T).query.pageNum=e),limit:p(T).query.pageSize,"onUpdate:limit":o[5]||(o[5]=e=>p(T).query.pageSize=e),onPagination:p(A)},null,8,["total","page","limit","onPagination"])):c("",!0)])),_:1}),s(U,{ref_key:"editModelRef",ref:O,onSuccess:p(D)},null,8,["onSuccess"]),s(S,{ref_key:"quickReplyRef",ref:Q,onSuccess:p(A)},null,8,["onSuccess"]),s(V,{ref_key:"vestModelRef",ref:$,"room-id":F.value,onSuccess:p(A)},null,8,["room-id","onSuccess"]),s(N,{ref_key:"cloneRoomRef",ref:M,onSuccess:p(D)},null,8,["onSuccess"])])}}});export{M as default};
