import{d as e,r as l,S as a,I as t,c as s,o,g as r,f as i,P as u,m as n,w as d,C as p,E as m,i as c,c$ as f,e as v,h as _,$ as g,F as y,d0 as V,d1 as b,Q as x,d2 as h,d3 as w,d4 as I,d5 as j,a0 as R}from"./index.Dk5pbsTU.js";import{E as C,a as k}from"./el-form-item.Bw6Zyv_7.js";import{E as A}from"./el-link.qHYW6llJ.js";import{E}from"./el-button.CXI119n4.js";import{E as N}from"./el-switch.kQ5v4arH.js";/* empty css               */import{E as z,a as T}from"./el-select.CRWkm-it.js";/* empty css                     */import{E as U}from"./el-popper.Dbn4MgsT.js";import"./el-tooltip.l0sNRNKZ.js";import{E as F}from"./el-slider.DLapdkRs.js";import{E as O}from"./el-input.DiGatoux.js";import"./el-input-number.C02ig7uT.js";import{E as $,a as q}from"./el-col.Cfu8vZQ4.js";import{_ as D}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as W}from"./el-cascader-panel.CXd5b5cH.js";import"./el-checkbox.DDYarIkn.js";import"./el-radio.w2rep3_A.js";import{E as G}from"./el-divider.2VNIZioN.js";import{g as L}from"./aiVestConfig.CW5InkHZ.js";import{V as S}from"./vest.j8NavXyg.js";import{b as P,a as B}from"./liveRoom.B9NhOBdK.js";import K from"./SetKnowledgeFile.QQOMNXXo.js";import{E as M}from"./index.L2DVy5yq.js";const J={class:"flex items-center"},Q={class:"flex items-center"},X={class:"el-form-item__description mt-2"},Y={class:"flex items-center"},H={class:"flex items-center"},Z={class:"w-full"},ee={class:"flex items-center justify-between"},le=["title"],ae={class:"mt-1"},te={class:"flex items-center"},se=e({__name:"AiVestConfigForm",props:{modelValue:{},triggerRuleOptions:{},aiVestConfigList:{},personaTypeOptions:{}},emits:["update:modelValue","goToRuleConfig"],setup(e,{expose:se,emit:oe}){const re=e,ie=oe,ue=l(),ne=l(),de=l(),pe=l(0),me=a({...re.modelValue});t((()=>re.modelValue),(e=>{Object.assign(me,e)}),{deep:!0}),t(me,(e=>{ie("update:modelValue",{...e})}),{deep:!0}),t((()=>me.vestId),(e=>{ne.value&&!me.id&&setTimeout((()=>{ne.value.validateField("vestCascader")}),100)}));const ce={value:"id",label:"label",children:"children",emitPath:!0,lazy:!0,lazyLoad:async(e,l)=>{const{level:a}=e;if(0===a)try{const e=new P;e.pageSize=100;const a=(await B.page(e)).records||[];l(a.map((e=>({id:e.id,label:e.roomName,leaf:!1}))))}catch{l([])}else if(1===a)try{const a=e.data.id,t=await S.getAllGroups(a),s=t.data||t||[];l(s.map((e=>({id:e.id,label:e.groupName,leaf:!1}))))}catch{l([])}else if(2===a)try{const a=e.data.id,t=await S.getVestsByGroupId(a),s=t.data||t||[];l(s.map((e=>({id:e.id,label:e.nickname,leaf:!0,vest:e}))))}catch{l([])}}},fe=s((()=>{if(me.id&&re.aiVestConfigList.length>0){const e=re.aiVestConfigList.find((e=>e.id===me.id));return e?`马甲ID: ${e.vestId} - ${e.vestNickname}`:""}return""})),ve=a({sex:[{required:!0,message:"请选择性别",trigger:"change"}],age:[{required:!0,message:"请输入年龄",trigger:"change"}],vestCascader:[{required:!0,message:"请选择马甲",trigger:"change",validator:(e,l,a)=>{me.id?a():l&&3===l.length?!me.vestId||me.vestId<=0?a(new Error("马甲ID获取失败，请重新选择")):a():a(new Error("请完整选择直播间、分组和马甲"))}}],ruleId:[{validator:(e,l,a)=>{null==l||""===l?(me.ruleId=0,a()):"number"==typeof l&&l>=0?a():"string"==typeof l&&/^\d+$/.test(l)&&Number(l)>=0?(me.ruleId=Number(l),a()):a(new Error("请选择有效的触发规则"))},trigger:"change"}],temperature:[{required:!0,message:"请设置生成温度",trigger:"change"},{validator:(e,l,a)=>{const t=Number(l);isNaN(t)||t<0||t>2?a(new Error("生成温度必须在0-2之间")):a()},trigger:"change"}],type:[{required:!0,message:"请选择人设类型",trigger:"change"},{validator:(e,l,a)=>{null==l||""===l?a(new Error("请选择人设类型")):"number"==typeof l||"string"==typeof l&&/^\d+$/.test(l)?(me.type=Number(l),a()):a(new Error("请选择有效的人设类型"))},trigger:"change"}]}),_e=l([]);function ge(e){if(e&&Array.isArray(e)&&3===e.length){const l=e[2],a=Number(l);isNaN(a)||a<=0?me.vestId=0:(me.vestId=a,setTimeout((()=>{ne.value&&ne.value.validateField("vestCascader")}),100))}else me.vestId=0}function ye(){me.vestCascader=[],me.vestId=0}function Ve(e){"string"==typeof e&&/^\d+$/.test(e)&&Number(e)>0&&(me.sessionRound=Number(e))}return o((()=>{!async function(){if(re.personaTypeOptions&&re.personaTypeOptions.length>0)_e.value=re.personaTypeOptions;else try{const e=await L();e&&e.data&&Array.isArray(e.data)?_e.value=e.data.map((e=>({label:e.description,value:e.type}))):Array.isArray(e)?_e.value=e.map((e=>({label:e.description,value:e.type}))):_e.value=[]}catch(e){_e.value=[]}}()})),t((()=>re.personaTypeOptions),(e=>{e&&e.length>0&&(_e.value=e)}),{immediate:!0}),se({resetForm:async function(){const e=_e.value.length>0?_e.value[0].value:0;Object.assign(me,{id:void 0,vestCascader:[],vestId:0,temperature:.7,promptTemplate:"",contextWindow:0,useRag:0,type:e,isDelay:0,fuzzinessRate:0,isAuto:0,triggerMode:0,ruleId:0,sessionRound:1,status:1,knowledgeIds:[],sex:"",age:"",intro:""}),pe.value++,await R(),de.value&&(de.value.clearCheckedNodes(),de.value.presentText="",de.value.inputValue=""),ne.value&&(ne.value.resetFields(),ne.value.clearValidate())},validate:function(){var e;return null==(e=ne.value)?void 0:e.validate()},clearValidate:function(){var e;null==(e=ne.value)||e.clearValidate()}}),(e,l)=>{const a=m,t=G,s=W,o=k,R=M,L=q,S=D,P=O,B=$,se=F,oe=U,re=T,ie=z,be=N,xe=E,he=A,we=C;return i(),r(u,null,[n(we,{ref_key:"formRef",ref:ne,model:me,rules:ve,"label-width":"120px","label-position":"top"},{default:d((()=>[n(t,{"content-position":"left"},{default:d((()=>[p("div",J,[n(a,null,{default:d((()=>[n(c(f))])),_:1}),l[18]||(l[18]=p("span",{class:"ml-2"},"基本信息",-1))])])),_:1}),n(B,{gutter:20},{default:d((()=>[n(L,{span:12},{default:d((()=>[me.id?_("",!0):(i(),v(o,{key:0,label:"选择马甲",prop:"vestCascader"},{default:d((()=>[(i(),v(s,{ref_key:"cascaderRef",ref:de,key:pe.value,modelValue:me.vestCascader,"onUpdate:modelValue":l[0]||(l[0]=e=>me.vestCascader=e),props:ce,placeholder:"请选择直播间/分组/马甲",filterable:"",style:{width:"100%"},onChange:ge,onClear:ye},null,8,["modelValue"]))])),_:1})),me.id?(i(),v(o,{key:1,label:"当前配置马甲"},{default:d((()=>[n(R,{type:"success",size:"large"},{default:d((()=>[g(y(fe.value),1)])),_:1})])),_:1})):_("",!0)])),_:1}),n(L,{span:6},{default:d((()=>[n(o,{label:"性别",prop:"sex"},{default:d((()=>[n(S,{modelValue:me.sex,"onUpdate:modelValue":l[1]||(l[1]=e=>me.sex=e),code:"gender"},null,8,["modelValue"])])),_:1})])),_:1}),n(L,{span:6},{default:d((()=>[n(o,{label:"年龄",prop:"age"},{default:d((()=>[n(P,{modelValue:me.age,"onUpdate:modelValue":l[2]||(l[2]=e=>me.age=e),oninput:"value=value.replace(/^(0+)|[^\\d]+/g,'')"},null,8,["modelValue"])])),_:1})])),_:1}),n(L,{span:24},{default:d((()=>[n(o,{label:"人物介绍",prop:"intro"},{default:d((()=>[n(P,{modelValue:me.intro,"onUpdate:modelValue":l[3]||(l[3]=e=>me.intro=e),maxlength:200,type:"textarea"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(t,{"content-position":"left"},{default:d((()=>[p("div",Q,[n(a,null,{default:d((()=>[n(c(V))])),_:1}),l[19]||(l[19]=p("span",{class:"ml-2"},"AI 模型配置",-1))])])),_:1}),n(B,{gutter:20},{default:d((()=>[n(L,{span:12},{default:d((()=>[n(o,{label:"生成温度 (Temperature)",prop:"temperature"},{default:d((()=>[n(se,{modelValue:me.temperature,"onUpdate:modelValue":l[4]||(l[4]=e=>me.temperature=e),min:0,max:2,step:.1,"show-input":""},null,8,["modelValue"]),p("div",X,[l[23]||(l[23]=g(" 值越低，输出越保守准确 (适合事实回答)；值越高，输出越随机发散 (适合创意写作)。 ")),n(oe,{content:"低温(0.2): 🎯 保守准确",placement:"top"},{default:d((()=>[n(R,{type:"info",size:"small",class:"mx-1"},{default:d((()=>l[20]||(l[20]=[g("保守")]))),_:1,__:[20]})])),_:1}),n(oe,{content:"中温(0.7): ⚖️ 平衡创造与逻辑",placement:"top"},{default:d((()=>[n(R,{type:"primary",size:"small",class:"mx-1"},{default:d((()=>l[21]||(l[21]=[g("平衡")]))),_:1,__:[21]})])),_:1}),n(oe,{content:"高温(>1.0): 🎨 随机发散",placement:"top"},{default:d((()=>[n(R,{type:"warning",size:"small",class:"mx-1"},{default:d((()=>l[22]||(l[22]=[g("创意")]))),_:1,__:[22]})])),_:1})])])),_:1})])),_:1}),n(L,{span:12},{default:d((()=>[n(o,{label:"上下文窗口 (Context Window)",prop:"contextWindow"},{default:d((()=>[n(se,{modelValue:me.contextWindow,"onUpdate:modelValue":l[5]||(l[5]=e=>me.contextWindow=e),min:0,max:10,step:1,"show-input":""},null,8,["modelValue"]),l[24]||(l[24]=p("div",{class:"el-form-item__description mt-2"}," 模型能记忆的对话轮次。0表示只基于当前输入回复。 ",-1))])),_:1,__:[24]})])),_:1})])),_:1}),n(o,{label:"提示词模板 (Prompt Template)",prop:"promptTemplate"},{default:d((()=>[n(P,{modelValue:me.promptTemplate,"onUpdate:modelValue":l[6]||(l[6]=e=>me.promptTemplate=e),type:"textarea",rows:3,placeholder:"定义AI的角色和回复风格。例如：你是一个幽默的助手，请用俏皮的风格回答问题。"},null,8,["modelValue"])])),_:1}),n(t,{"content-position":"left"},{default:d((()=>[p("div",Y,[n(a,null,{default:d((()=>[n(c(b))])),_:1}),l[25]||(l[25]=p("span",{class:"ml-2"},"行为与人设",-1))])])),_:1}),n(B,{gutter:20},{default:d((()=>[n(L,{span:12},{default:d((()=>[n(o,{label:"人设类型",prop:"type"},{default:d((()=>[n(ie,{modelValue:me.type,"onUpdate:modelValue":l[7]||(l[7]=e=>me.type=e),placeholder:"请选择人设类型",style:{width:"100%"}},{default:d((()=>[(i(!0),r(u,null,x(_e.value,(e=>(i(),v(re,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),n(L,{span:12},{default:d((()=>[n(o,{label:"模糊话概率 (Fuzziness Rate)",prop:"fuzzinessRate"},{default:d((()=>[n(se,{modelValue:me.fuzzinessRate,"onUpdate:modelValue":l[8]||(l[8]=e=>me.fuzzinessRate=e),min:0,max:10,step:1,"show-input":""},null,8,["modelValue"]),l[26]||(l[26]=p("div",{class:"el-form-item__description mt-2"},"AI回复不确定性或“打哈哈”的概率。",-1))])),_:1,__:[26]})])),_:1})])),_:1}),n(B,{gutter:20},{default:d((()=>[n(L,{span:8},{default:d((()=>[n(o,{label:"使用RAG增强",prop:"useRag"},{default:d((()=>[n(be,{modelValue:me.useRag,"onUpdate:modelValue":l[9]||(l[9]=e=>me.useRag=e),"active-value":1,"inactive-value":0,"inline-prompt":"","active-text":"是","inactive-text":"否"},null,8,["modelValue"]),n(oe,{content:"Retrieval-Augmented Generation，使用知识库增强回复的准确性",placement:"top"},{default:d((()=>[n(a,{class:"ml-2 cursor-help text-gray-400"},{default:d((()=>[n(c(h))])),_:1})])),_:1}),me.useRag?(i(),v(xe,{key:0,type:"primary",link:"",style:{"margin-left":"10px"},onClick:l[10]||(l[10]=e=>{var l;return null==(l=ue.value)?void 0:l.open(me.knowledgeIds)})},{default:d((()=>l[27]||(l[27]=[g(" 设置知识库文件 ")]))),_:1,__:[27]})):_("",!0)])),_:1})])),_:1}),n(L,{span:8},{default:d((()=>[n(o,{label:"随机延迟回复",prop:"isDelay"},{default:d((()=>[n(be,{modelValue:me.isDelay,"onUpdate:modelValue":l[11]||(l[11]=e=>me.isDelay=e),"active-value":1,"inactive-value":0,"inline-prompt":"","active-text":"是","inactive-text":"否"},null,8,["modelValue"]),n(oe,{content:"模拟真人的思考和打字时间",placement:"top"},{default:d((()=>[n(a,{class:"ml-2 cursor-help text-gray-400"},{default:d((()=>[n(c(h))])),_:1})])),_:1})])),_:1})])),_:1}),n(L,{span:8},{default:d((()=>[n(o,{label:"自动发送回复",prop:"isAuto"},{default:d((()=>[n(be,{modelValue:me.isAuto,"onUpdate:modelValue":l[12]||(l[12]=e=>me.isAuto=e),"active-value":1,"inactive-value":0,"inline-prompt":"","active-text":"是","inactive-text":"否"},null,8,["modelValue"]),n(oe,{content:"AI生成回复后是否需要人工审核再发送",placement:"top"},{default:d((()=>[n(a,{class:"ml-2 cursor-help text-gray-400"},{default:d((()=>[n(c(h))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),n(t,{"content-position":"left"},{default:d((()=>[p("div",H,[n(a,null,{default:d((()=>[n(c(w))])),_:1}),l[28]||(l[28]=p("span",{class:"ml-2"},"触发与会话",-1))])])),_:1}),n(B,{gutter:20},{default:d((()=>[n(L,{span:12},{default:d((()=>[n(o,{label:"触发规则",prop:"ruleId"},{default:d((()=>[p("div",Z,[n(ie,{modelValue:me.ruleId,"onUpdate:modelValue":l[13]||(l[13]=e=>me.ruleId=e),placeholder:"默认（无特定规则）",class:"w-full",filterable:"",clearable:""},{default:d((()=>[n(re,{label:"无特定规则",value:0}),(i(!0),r(u,null,x(e.triggerRuleOptions,(e=>(i(),v(re,{key:e.id,label:e.ruleName,value:e.id},{default:d((()=>[p("div",ee,[p("span",{title:e.ruleName,class:"overflow-hidden text-ellipsis whitespace-nowrap"},y(e.ruleName),9,le),e.description?(i(),v(oe,{key:0,content:e.description,placement:"left"},{default:d((()=>[n(a,{class:"text-gray-400"},{default:d((()=>[n(c(f))])),_:1})])),_:2},1032,["content"])):_("",!0)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),p("div",ae,[n(he,{type:"primary",onClick:l[14]||(l[14]=l=>e.$emit("goToRuleConfig"))},{default:d((()=>[n(a,{class:"mr-1"},{default:d((()=>[n(c(I))])),_:1}),l[29]||(l[29]=g(" 管理触发规则 "))])),_:1,__:[29]})])])])),_:1})])),_:1}),n(L,{span:12},{default:d((()=>[n(o,{label:"会话轮次 (Session Round)",prop:"sessionRound"},{default:d((()=>[n(ie,{modelValue:me.sessionRound,"onUpdate:modelValue":l[15]||(l[15]=e=>me.sessionRound=e),placeholder:"请选择会话轮次",class:"w-full",filterable:"","allow-create":"",onChange:Ve},{default:d((()=>[n(re,{label:"无限制（需手动停止）",value:-1}),n(re,{label:"随机",value:0}),(i(),r(u,null,x(20,(e=>n(re,{key:e,label:e+"轮后终止",value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),l[30]||(l[30]=p("div",{class:"el-form-item__description mt-2"},"一次完整的对话交互持续的轮次。",-1))])),_:1,__:[30]})])),_:1})])),_:1}),n(t,{"content-position":"left"},{default:d((()=>[p("div",te,[n(a,null,{default:d((()=>[n(c(j))])),_:1}),l[31]||(l[31]=p("span",{class:"ml-2"},"状态",-1))])])),_:1}),n(o,{label:"配置状态",prop:"status"},{default:d((()=>[n(be,{modelValue:me.status,"onUpdate:modelValue":l[16]||(l[16]=e=>me.status=e),"active-value":1,"inactive-value":0,"inline-prompt":"","active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"]),l[32]||(l[32]=p("div",{class:"el-form-item__description ml-4"},"禁用后，该AI马甲配置将不会生效。",-1))])),_:1,__:[32]})])),_:1},8,["model","rules"]),n(K,{ref_key:"setKnowledgeFileRef",ref:ue,onSuccess:l[17]||(l[17]=e=>me.knowledgeIds=e)},null,512)],64)}}});export{se as _};
