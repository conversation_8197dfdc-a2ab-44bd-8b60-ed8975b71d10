<template>
  <el-date-picker
    v-model="dataPickVal"
    :type="type"
    range-separator="~"
    :clearable="clearable"
    value-format="YYYY-MM-DD HH:mm:ss"
    start-placeholder="开始时间"
    end-placeholder="结束时间"
  />
</template>
<script setup lang="ts">
const props = defineProps({
  clearable: { type: Boolean, default: true },
  type: { type: String, default: "datetimerange" },
  isSplit: { type: Boolean, default: false },
});
const start = defineModel("start", {
  type: String,
  required: false,
  default: "",
});
const end = defineModel("end", {
  type: String,
  required: false,
  default: "",
});
const modelValue = defineModel("modelValue", {
  type: Array,
  required: false,
  default: () => [],
});
const dataPickVal = computed({
  get: () => {
    if (props.isSplit) {
      return [start.value || "", end.value || ""];
    }
    return props.modelValue || [];
  },
  set: (_val) => {
    if (props.isSplit) {
      if (_val && _val.length > 0) {
        start.value = _val[0] || "";
        end.value = _val[1] || "";
      } else {
        start.value = "";
        end.value = "";
      }
    } else {
      modelValue.value = _val;
    }
  },
});
</script>
