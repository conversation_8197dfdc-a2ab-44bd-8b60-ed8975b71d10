import{bR as e,b6 as a,t as s,z as t,ad as l,_ as o,d as n,bV as i,cc as r,cd as c,x as u,b as d,be as v,r as f,ce as p,s as m,c as g,I as y,o as b,u as w,e as k,f as h,w as x,m as z,T as I,C,k as _,n as O,i as S,h as N,g as L,l as $,j as E,E as T,Y as R,P as A,c0 as j,a4 as P,$ as B,F as Y,cf as M,cg as X,D as F,ch as D,ci as V,Q as q,cj as H,K,a0 as W,q as Z,bw as G,bo as Q,H as U,a9 as J,U as ee,aL as ae,au as se,av as te,bm as le,L as oe,a as ne}from"./index.Dk5pbsTU.js";import{E as ie,a as re}from"./index.C6NthMtN.js";import{d as ce}from"./debounce.DJJTSR8O.js";import{i as ue}from"./position.DfR5znly.js";import{u as de}from"./index.DEKElSOG.js";import{a as ve}from"./scroll.CVc-P3_z.js";function fe(a,s,t){var l=!0,o=!0;if("function"!=typeof a)throw new TypeError("Expected a function");return e(t)&&(l="leading"in t?!!t.leading:l,o="trailing"in t?!!t.trailing:o),ce(a,s,{leading:l,maxWait:s,trailing:o})}const pe=s({urlList:{type:t(Array),default:()=>l([])},zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},hideOnClickModal:Boolean,teleported:Boolean,closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7},showProgress:{type:Boolean,default:!1},crossorigin:{type:t(String)}}),me={close:()=>!0,switch:e=>a(e),rotate:e=>a(e)},ge=n({name:"ElImageViewer"});const ye=Z(o(n({...ge,props:pe,emits:me,setup(e,{expose:a,emit:s}){var t;const l=e,o={CONTAIN:{name:"contain",icon:i(c)},ORIGINAL:{name:"original",icon:i(r)}};let n,Z="";const{t:G}=u(),Q=d("image-viewer"),{nextZIndex:U}=v(),J=f(),ee=f([]),ae=p(),se=f(!0),te=f(l.initialIndex),le=m(o.CONTAIN),oe=f({scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}),ne=f(null!=(t=l.zIndex)?t:U()),ce=g((()=>{const{urlList:e}=l;return e.length<=1})),ue=g((()=>0===te.value)),de=g((()=>te.value===l.urlList.length-1)),ve=g((()=>l.urlList[te.value])),pe=g((()=>[Q.e("btn"),Q.e("prev"),Q.is("disabled",!l.infinite&&ue.value)])),me=g((()=>[Q.e("btn"),Q.e("next"),Q.is("disabled",!l.infinite&&de.value)])),ge=g((()=>{const{scale:e,deg:a,offsetX:s,offsetY:t,enableTransition:l}=oe.value;let n=s/e,i=t/e;const r=a*Math.PI/180,c=Math.cos(r),u=Math.sin(r);n=n*c+i*u,i=i*c-s/e*u;const d={transform:`scale(${e}) rotate(${a}deg) translate(${n}px, ${i}px)`,transition:l?"transform .3s":""};return le.value.name===o.CONTAIN.name&&(d.maxWidth=d.maxHeight="100%"),d})),ye=g((()=>`${te.value+1} / ${l.urlList.length}`));function be(){ae.stop(),null==n||n(),document.body.style.overflow=Z,s("close")}function we(){se.value=!1}function ke(e){se.value=!1,e.target.alt=G("el.image.error")}function he(e){if(se.value||0!==e.button||!J.value)return;oe.value.enableTransition=!1;const{offsetX:a,offsetY:s}=oe.value,t=e.pageX,l=e.pageY,o=fe((e=>{oe.value={...oe.value,offsetX:a+e.pageX-t,offsetY:s+e.pageY-l}})),n=w(document,"mousemove",o);w(document,"mouseup",(()=>{n()})),e.preventDefault()}function xe(){oe.value={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}}function ze(){if(se.value)return;const e=H(o),a=Object.values(o),s=le.value.name,t=(a.findIndex((e=>e.name===s))+1)%e.length;le.value=o[e[t]],xe()}function Ie(e){const a=l.urlList.length;te.value=(e+a)%a}function Ce(){ue.value&&!l.infinite||Ie(te.value-1)}function _e(){de.value&&!l.infinite||Ie(te.value+1)}function Oe(e,a={}){if(se.value)return;const{minScale:t,maxScale:o}=l,{zoomRate:n,rotateDeg:i,enableTransition:r}={zoomRate:l.zoomRate,rotateDeg:90,enableTransition:!0,...a};switch(e){case"zoomOut":oe.value.scale>t&&(oe.value.scale=Number.parseFloat((oe.value.scale/n).toFixed(3)));break;case"zoomIn":oe.value.scale<o&&(oe.value.scale=Number.parseFloat((oe.value.scale*n).toFixed(3)));break;case"clockwise":oe.value.deg+=i,s("rotate",oe.value.deg);break;case"anticlockwise":oe.value.deg-=i,s("rotate",oe.value.deg)}oe.value.enableTransition=r}function Se(e){var a;"pointer"===(null==(a=e.detail)?void 0:a.focusReason)&&e.preventDefault()}function Ne(){l.closeOnPressEscape&&be()}function Le(e){if(e.ctrlKey)return e.deltaY<0||e.deltaY>0?(e.preventDefault(),!1):void 0}return y(ve,(()=>{W((()=>{const e=ee.value[0];(null==e?void 0:e.complete)||(se.value=!0)}))})),y(te,(e=>{xe(),s("switch",e)})),b((()=>{!function(){const e=fe((e=>{switch(e.code){case K.esc:l.closeOnPressEscape&&be();break;case K.space:ze();break;case K.left:Ce();break;case K.up:Oe("zoomIn");break;case K.right:_e();break;case K.down:Oe("zoomOut")}})),a=fe((e=>{Oe((e.deltaY||e.deltaX)<0?"zoomIn":"zoomOut",{zoomRate:l.zoomRate,enableTransition:!1})}));ae.run((()=>{w(document,"keydown",e),w(document,"wheel",a)}))}(),n=w("wheel",Le,{passive:!1}),Z=document.body.style.overflow,document.body.style.overflow="hidden"})),a({setActiveItem:Ie}),(e,a)=>(h(),k(S(re),{to:"body",disabled:!e.teleported},{default:x((()=>[z(I,{name:"viewer-fade",appear:""},{default:x((()=>[C("div",{ref_key:"wrapper",ref:J,tabindex:-1,class:O(S(Q).e("wrapper")),style:_({zIndex:ne.value})},[z(S(ie),{loop:"",trapped:"","focus-trap-el":J.value,"focus-start-el":"container",onFocusoutPrevented:Se,onReleaseRequested:Ne},{default:x((()=>[C("div",{class:O(S(Q).e("mask")),onClick:E((a=>e.hideOnClickModal&&be()),["self"])},null,10,["onClick"]),N(" CLOSE "),C("span",{class:O([S(Q).e("btn"),S(Q).e("close")]),onClick:be},[z(S(T),null,{default:x((()=>[z(S(R))])),_:1})],2),N(" ARROW "),S(ce)?N("v-if",!0):(h(),L(A,{key:0},[C("span",{class:O(S(pe)),onClick:Ce},[z(S(T),null,{default:x((()=>[z(S(j))])),_:1})],2),C("span",{class:O(S(me)),onClick:_e},[z(S(T),null,{default:x((()=>[z(S(P))])),_:1})],2)],64)),e.$slots.progress||e.showProgress?(h(),L("div",{key:1,class:O([S(Q).e("btn"),S(Q).e("progress")])},[$(e.$slots,"progress",{activeIndex:te.value,total:e.urlList.length},(()=>[B(Y(S(ye)),1)]))],2)):N("v-if",!0),N(" ACTIONS "),C("div",{class:O([S(Q).e("btn"),S(Q).e("actions")])},[C("div",{class:O(S(Q).e("actions__inner"))},[$(e.$slots,"toolbar",{actions:Oe,prev:Ce,next:_e,reset:ze,activeIndex:te.value,setActiveItem:Ie},(()=>[z(S(T),{onClick:e=>Oe("zoomOut")},{default:x((()=>[z(S(M))])),_:1},8,["onClick"]),z(S(T),{onClick:e=>Oe("zoomIn")},{default:x((()=>[z(S(X))])),_:1},8,["onClick"]),C("i",{class:O(S(Q).e("actions__divider"))},null,2),z(S(T),{onClick:ze},{default:x((()=>[(h(),k(F(S(le).icon)))])),_:1}),C("i",{class:O(S(Q).e("actions__divider"))},null,2),z(S(T),{onClick:e=>Oe("anticlockwise")},{default:x((()=>[z(S(D))])),_:1},8,["onClick"]),z(S(T),{onClick:e=>Oe("clockwise")},{default:x((()=>[z(S(V))])),_:1},8,["onClick"])]))],2)],2),N(" CANVAS "),C("div",{class:O(S(Q).e("canvas"))},[(h(!0),L(A,null,q(e.urlList,((a,s)=>(h(),L(A,{key:s},[s===te.value?(h(),L("img",{key:0,ref_for:!0,ref:e=>ee.value[s]=e,src:a,style:_(S(ge)),class:O(S(Q).e("img")),crossorigin:e.crossorigin,onLoad:we,onError:ke,onMousedown:he},null,46,["src","crossorigin"])):N("v-if",!0)],64)))),128))],2),$(e.$slots,"default")])),_:3},8,["focus-trap-el"])],6)])),_:3})])),_:3},8,["disabled"]))}}),[["__file","image-viewer.vue"]])),be=s({hideOnClickModal:Boolean,src:{type:String,default:""},fit:{type:String,values:["","contain","cover","fill","none","scale-down"],default:""},loading:{type:String,values:["eager","lazy"]},lazy:Boolean,scrollContainer:{type:t([String,Object])},previewSrcList:{type:t(Array),default:()=>l([])},previewTeleported:Boolean,zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7},showProgress:{type:Boolean,default:!1},crossorigin:{type:t(String)}}),we={load:e=>e instanceof Event,error:e=>e instanceof Event,switch:e=>a(e),close:()=>!0,show:()=>!0},ke=n({name:"ElImage",inheritAttrs:!1});const he=Z(o(n({...ke,props:be,emits:we,setup(e,{expose:a,emit:s}){const t=e,{t:l}=u(),o=d("image"),n=G(),i=g((()=>Q(Object.entries(n).filter((([e])=>/^(data-|on[A-Z])/i.test(e)||["id","style"].includes(e)))))),r=de({excludeListeners:!0,excludeKeys:g((()=>Object.keys(i.value)))}),c=f(),v=f(!1),p=f(!0),m=f(!1),z=f(),I=f(),_=U&&"loading"in HTMLImageElement.prototype;let E;const T=g((()=>[o.e("inner"),j.value&&o.e("preview"),p.value&&o.is("loading")])),R=g((()=>{const{fit:e}=t;return U&&e?{objectFit:e}:{}})),j=g((()=>{const{previewSrcList:e}=t;return J(e)&&e.length>0})),P=g((()=>{const{previewSrcList:e,initialIndex:a}=t;let s=a;return a>e.length-1&&(s=0),s})),B=g((()=>"eager"!==t.loading&&(!_&&"lazy"===t.loading||t.lazy))),M=()=>{U&&(p.value=!0,v.value=!1,c.value=t.src)};function X(e){p.value=!1,v.value=!1,s("load",e)}function F(e){p.value=!1,v.value=!0,s("error",e)}function D(){ue(z.value,I.value)&&(M(),H())}const V=ne(D,200,!0);async function q(){var e;if(!U)return;await W();const{scrollContainer:a}=t;le(a)?I.value=a:oe(a)&&""!==a?I.value=null!=(e=document.querySelector(a))?e:void 0:z.value&&(I.value=ve(z.value)),I.value&&(E=w(I,"scroll",V),setTimeout((()=>D()),100))}function H(){U&&I.value&&V&&(null==E||E(),I.value=void 0)}function K(){j.value&&(m.value=!0,s("show"))}function Z(){m.value=!1,s("close")}function ie(e){s("switch",e)}return y((()=>t.src),(()=>{B.value?(p.value=!0,v.value=!1,H(),q()):M()})),b((()=>{B.value?q():M()})),a({showPreview:K}),(e,a)=>(h(),L("div",ee({ref_key:"container",ref:z},S(i),{class:[S(o).b(),e.$attrs.class]}),[v.value?$(e.$slots,"error",{key:0},(()=>[C("div",{class:O(S(o).e("error"))},Y(S(l)("el.image.error")),3)])):(h(),L(A,{key:1},[void 0!==c.value?(h(),L("img",ee({key:0},S(r),{src:c.value,loading:e.loading,style:S(R),class:S(T),crossorigin:e.crossorigin,onClick:K,onLoad:X,onError:F}),null,16,["src","loading","crossorigin"])):N("v-if",!0),p.value?(h(),L("div",{key:1,class:O(S(o).e("wrapper"))},[$(e.$slots,"placeholder",{},(()=>[C("div",{class:O(S(o).e("placeholder"))},null,2)]))],2)):N("v-if",!0)],64)),S(j)?(h(),L(A,{key:2},[m.value?(h(),k(S(ye),{key:0,"z-index":e.zIndex,"initial-index":S(P),infinite:e.infinite,"zoom-rate":e.zoomRate,"min-scale":e.minScale,"max-scale":e.maxScale,"show-progress":e.showProgress,"url-list":e.previewSrcList,crossorigin:e.crossorigin,"hide-on-click-modal":e.hideOnClickModal,teleported:e.previewTeleported,"close-on-press-escape":e.closeOnPressEscape,onClose:Z,onSwitch:ie},ae({toolbar:x((a=>[$(e.$slots,"toolbar",se(te(a)))])),default:x((()=>[e.$slots.viewer?(h(),L("div",{key:0},[$(e.$slots,"viewer")])):N("v-if",!0)])),_:2},[e.$slots.progress?{name:"progress",fn:x((a=>[$(e.$slots,"progress",se(te(a)))]))}:void 0]),1032,["z-index","initial-index","infinite","zoom-rate","min-scale","max-scale","show-progress","url-list","crossorigin","hide-on-click-modal","teleported","close-on-press-escape"])):N("v-if",!0)],64)):N("v-if",!0)],16))}}),[["__file","image.vue"]]));export{he as E,ye as a};
