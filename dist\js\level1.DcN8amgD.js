import{ap as e,g as t,f as r,m as s,w as a,$ as l}from"./index.Dk5pbsTU.js";import{E as o}from"./el-alert.CImT_8mr.js";import{E as i}from"./el-link.qHYW6llJ.js";import{_ as m}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.DuiNpp1i.js";const n={style:{padding:"30px"}};const p=m({},[["render",function(m,p){const d=i,u=e("router-view"),f=o;return r(),t("div",n,[s(d,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/multi-level/level1.vue",type:"primary",target:"_blank",class:"mb-10"},{default:a((()=>p[0]||(p[0]=[l(" 示例源码 请点击>>>> ")]))),_:1,__:[0]}),s(f,{closable:!1,title:"菜单一级"},{default:a((()=>[s(u)])),_:1})])}]]);export{p as default};
