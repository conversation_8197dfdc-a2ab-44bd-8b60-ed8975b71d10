import{d as e,S as l,r as a,e as o,f as t,w as r,m as s,i,C as u,$ as d,az as m}from"./index.Dk5pbsTU.js";import{E as n}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as p}from"./el-button.CXI119n4.js";import{E as c,a as g}from"./el-form-item.Bw6Zyv_7.js";import{E as f}from"./el-switch.kQ5v4arH.js";import{_ as b}from"./SingleImageUpload.WGBxPB_4.js";import{E as v}from"./el-input.DiGatoux.js";import{f as V,i as h}from"./validate.Bicq6Mu8.js";import{S as w,a as _}from"./webcaster.CYqw_lYq.js";const x={class:"dialog-footer"},j=e({__name:"edit",emits:["success"],setup(e,{expose:j,emit:N}){const U=l({visible:!1,title:""}),k=a(new w),q=a(),y=N,E=a(!1),A={password:[{required:!0,message:"请输入密码",trigger:"blur"}],account:[{required:!0,message:"请输入账号",trigger:"blur"}],userName:[{required:!0,message:"请输入姓名",trigger:"blur"}],nickName:[{required:!0,message:"请输入昵称",trigger:"blur"}],avatar:[{required:!0,message:"请上传头像",trigger:"blur"}],mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},V(h,"手机号错误")]};function C(){q.value.validate((e=>{e&&(E.value=!0,_.save(k.value).then((()=>{m.success("保存成功"),U.visible=!1,y("success")})).finally((()=>E.value=!1)))}))}return j({open:e=>{U.visible=!0,U.title=(null==e?void 0:e.id)?"编辑主播信息":"新增主播",k.value=new w(e)}}),(e,l)=>{const a=v,m=g,V=b,h=f,w=c,_=p,j=n;return t(),o(j,{modelValue:i(U).visible,"onUpdate:modelValue":l[8]||(l[8]=e=>i(U).visible=e),title:i(U).title,width:"500px"},{footer:r((()=>[u("div",x,[s(_,{loading:i(E),type:"primary",onClick:C},{default:r((()=>l[9]||(l[9]=[d("确 定")]))),_:1,__:[9]},8,["loading"]),s(_,{loading:i(E),onClick:l[7]||(l[7]=e=>i(U).visible=!1)},{default:r((()=>l[10]||(l[10]=[d("取 消")]))),_:1,__:[10]},8,["loading"])])])),default:r((()=>[s(w,{ref_key:"editFormRef",ref:q,model:i(k),rules:A,"label-width":"100px"},{default:r((()=>[s(m,{label:"姓名",prop:"userName"},{default:r((()=>[s(a,{modelValue:i(k).userName,"onUpdate:modelValue":l[0]||(l[0]=e=>i(k).userName=e),maxlength:25,"show-word-limit":"",placeholder:"请输入姓名"},null,8,["modelValue"])])),_:1}),s(m,{label:"昵称",prop:"nickName"},{default:r((()=>[s(a,{modelValue:i(k).nickName,"onUpdate:modelValue":l[1]||(l[1]=e=>i(k).nickName=e),maxlength:25,"show-word-limit":"",placeholder:"请输入昵称"},null,8,["modelValue"])])),_:1}),s(m,{label:"手机号",prop:"mobile"},{default:r((()=>[s(a,{modelValue:i(k).mobile,"onUpdate:modelValue":l[2]||(l[2]=e=>i(k).mobile=e),maxlength:11,"show-word-limit":"",placeholder:"请输入手机号"},null,8,["modelValue"])])),_:1}),s(m,{label:"头像",prop:"avatar"},{default:r((()=>[s(V,{modelValue:i(k).avatar,"onUpdate:modelValue":l[3]||(l[3]=e=>i(k).avatar=e),style:{width:"100px",height:"100px"}},null,8,["modelValue"])])),_:1}),s(m,{label:"自主开播",prop:"isAutonomous"},{default:r((()=>[s(h,{modelValue:i(k).isAutonomous,"onUpdate:modelValue":l[4]||(l[4]=e=>i(k).isAutonomous=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1}),s(m,{label:"登录账号",prop:"account"},{default:r((()=>[s(a,{modelValue:i(k).account,"onUpdate:modelValue":l[5]||(l[5]=e=>i(k).account=e),maxlength:25,"show-word-limit":"",placeholder:"请输入账号"},null,8,["modelValue"])])),_:1}),s(m,{label:"登录密码",prop:i(k).id?"":"password"},{default:r((()=>[s(a,{modelValue:i(k).password,"onUpdate:modelValue":l[6]||(l[6]=e=>i(k).password=e),maxlength:25,"show-word-limit":"",placeholder:"如需修改，请输入即可"},null,8,["modelValue"])])),_:1},8,["prop"])])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}});export{j as _};
