import{d as e,r as l,S as t,b1 as a,c as o,ap as r,g as n,f as i,k as s,i as p,m as u,U as d,w as m,C as c,V as f,e as v,h as g,P as y,Q as _,Z as b,$ as j,n as h,l as k,F as x,E as V,az as w,aO as C,aI as U}from"./index.Dk5pbsTU.js";import{v as S}from"./el-loading.Dqi-qL7c.js";import"./el-popper.Dbn4MgsT.js";import{E}from"./el-popover.Bo2lPKkO.js";import A from"./index.Cywy93e7.js";import{a as $,E as I}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";/* empty css                     *//* empty css               */import{E as N,a as R}from"./el-form-item.Bw6Zyv_7.js";import{E as z}from"./el-button.CXI119n4.js";import{E as K}from"./el-date-picker.B6TshyBV.js";import{E as M}from"./el-input.DiGatoux.js";import{E as D,a as P}from"./el-select.CRWkm-it.js";import"./el-tree.ChWw39qP.js";import"./el-text.6kaKYQ9U.js";import{E as T}from"./el-tree-select.HrmCEXac.js";import{_ as Y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as L}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as O}from"./el-link.qHYW6llJ.js";import{E as Q}from"./index.L2DVy5yq.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.C6NthMtN.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./use-form-common-props.CQPDkY7k.js";import"./dropdown.B_OfpyL_.js";import"./el-pagination.C5FHY27u.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./event.BwRzfsZt.js";import"./index.DuiNpp1i.js";import"./error.D_Dr4eZ1.js";import"./index.ybpLT-bz.js";import"./_initCloneObject.BN1anLuC.js";import"./_baseIteratee.CPRpgrLu.js";import"./debounce.DJJTSR8O.js";import"./index.wZTqlYZ6.js";import"./use-form-item.DzRJVC1I.js";import"./castArray.C4RhTg2c.js";import"./dayjs.min.D7B9fRnU.js";import"./arrays.CygeFE-H.js";import"./index.Cd8M2JyP.js";import"./index.DEKElSOG.js";import"./index.Vn8pbgQR.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./scroll.CVc-P3_z.js";import"./vnode.Cbclzz8S.js";import"./index.DLOxQT-M.js";const q={class:"feedback"},F=Y(e({__name:"index",props:{selectConfig:{},text:{default:""}},emits:["confirmClick"],setup(e,{emit:C}){const U=e,Y=C,L=U.selectConfig.pk??"id",O=!0===U.selectConfig.multiple,Q=U.selectConfig.width??"100%",F=U.selectConfig.placeholder??"请选择",J=l(!1),Z=l(!1),G=l(0),H=l([]),W=t({pageNum:1,pageSize:10}),B=l(),X=l(Q);a(B,(e=>{X.value=`${e[0].contentRect.width}px`}));const ee=l();for(const l of U.selectConfig.formItems)W[l.prop]=l.initialValue??"";function le(){var e;null==(e=ee.value)||e.resetFields(),ae(!0)}function te(){ae(!0)}function ae(e=!1){Z.value=!0,e&&(W.pageNum=1,W.pageSize=10),U.selectConfig.indexAction(W).then((e=>{G.value=e.total,H.value=e.list})).finally((()=>{Z.value=!1}))}const oe=l();for(const l of U.selectConfig.tableColumns)if("selection"===l.type){l.reserveSelection=!0;break}const re=l([]),ne=o((()=>re.value.length>0?`已选(${re.value.length})`:"确 定"));function ie(e,l){var t,a,o;O||0===e.length?re.value=e:(re.value=[e[e.length-1]],null==(t=oe.value)||t.clearSelection(),null==(a=oe.value)||a.toggleRowSelection(re.value[0],!0),null==(o=oe.value)||o.setCurrentRow(re.value[0]))}function se(e){O&&(re.value=e)}function pe(){ae()}const ue=l(!1);function de(){!1===ue.value&&(ue.value=!0,ae())}function me(){0!==re.value.length?(J.value=!1,Y("confirmClick",re.value)):w.error("请选择数据")}function ce(){var e;null==(e=oe.value)||e.clearSelection(),re.value=[]}function fe(){J.value=!1}const ve=l();return(e,l)=>{const t=r("ArrowDown"),a=V,o=M,w=P,C=D,U=T,Y=K,ae=R,re=z,ue=N,ge=I,ye=$,_e=A,be=E,je=S;return i(),n("div",{ref_key:"tableSelectRef",ref:B,style:s("width:"+p(Q))},[u(be,d({visible:J.value,width:X.value,placement:"bottom-end"},e.selectConfig.popover,{onShow:de}),{reference:m((()=>[c("div",{onClick:l[0]||(l[0]=e=>J.value=!J.value)},[k(e.$slots,"default",{},(()=>[u(o,{class:"reference","model-value":e.text,readonly:!0,placeholder:p(F)},{suffix:m((()=>[u(a,{style:s({transform:J.value?"rotate(180deg)":"rotate(0)",transition:"transform .5s"})},{default:m((()=>[u(t)])),_:1},8,["style"])])),_:1},8,["model-value","placeholder"])]),!0)])])),default:m((()=>[c("div",{ref_key:"popoverContentRef",ref:ve},[u(ue,{ref_key:"formRef",ref:ee,model:W,inline:!0},{default:m((()=>[(i(!0),n(y,null,_(e.selectConfig.formItems,(e=>(i(),v(ae,{key:e.prop,label:e.label,prop:e.prop},{default:m((()=>{var l,t;return["input"===e.type?(i(),n(y,{key:0},["number"===(null==(l=e.attrs)?void 0:l.type)?(i(),v(o,d({key:0,modelValue:W[e.prop],"onUpdate:modelValue":l=>W[e.prop]=l,modelModifiers:{number:!0}},{ref_for:!0},e.attrs,{onKeyup:b(te,["enter"])}),null,16,["modelValue","onUpdate:modelValue"])):(i(),v(o,d({key:1,modelValue:W[e.prop],"onUpdate:modelValue":l=>W[e.prop]=l},{ref_for:!0},e.attrs,{onKeyup:b(te,["enter"])}),null,16,["modelValue","onUpdate:modelValue"]))],64)):"select"===e.type?(i(),v(C,d({key:1,modelValue:W[e.prop],"onUpdate:modelValue":l=>W[e.prop]=l},{ref_for:!0},e.attrs),{default:m((()=>[(i(!0),n(y,null,_(e.options,(e=>(i(),v(w,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1040,["modelValue","onUpdate:modelValue"])):"tree-select"===e.type?(i(),v(U,d({key:2,modelValue:W[e.prop],"onUpdate:modelValue":l=>W[e.prop]=l},{ref_for:!0},e.attrs),null,16,["modelValue","onUpdate:modelValue"])):"date-picker"===e.type?(i(),v(Y,d({key:3,modelValue:W[e.prop],"onUpdate:modelValue":l=>W[e.prop]=l},{ref_for:!0},e.attrs),null,16,["modelValue","onUpdate:modelValue"])):(i(),n(y,{key:4},["number"===(null==(t=e.attrs)?void 0:t.type)?(i(),v(o,d({key:0,modelValue:W[e.prop],"onUpdate:modelValue":l=>W[e.prop]=l,modelModifiers:{number:!0}},{ref_for:!0},e.attrs,{onKeyup:b(te,["enter"])}),null,16,["modelValue","onUpdate:modelValue"])):(i(),v(o,d({key:1,modelValue:W[e.prop],"onUpdate:modelValue":l=>W[e.prop]=l},{ref_for:!0},e.attrs,{onKeyup:b(te,["enter"])}),null,16,["modelValue","onUpdate:modelValue"]))],64))]})),_:2},1032,["label","prop"])))),128)),u(ae,null,{default:m((()=>[u(re,{type:"primary",icon:"search",onClick:te},{default:m((()=>l[4]||(l[4]=[j("搜索")]))),_:1,__:[4]}),u(re,{icon:"refresh",onClick:le},{default:m((()=>l[5]||(l[5]=[j("重置")]))),_:1,__:[5]})])),_:1})])),_:1},8,["model"]),f((i(),v(ye,{ref_key:"tableRef",ref:oe,data:H.value,border:!0,"max-height":250,"row-key":p(L),"highlight-current-row":!0,class:h({radio:!O}),onSelect:ie,onSelectAll:se},{default:m((()=>[(i(!0),n(y,null,_(e.selectConfig.tableColumns,(l=>(i(),n(y,{key:l.prop},["custom"===l.templet?(i(),v(ge,d({key:0,ref_for:!0},l),{default:m((t=>[k(e.$slots,l.slotName??l.prop,d({prop:l.prop},{ref_for:!0},t),void 0,!0)])),_:2},1040)):(i(),v(ge,d({key:1,ref_for:!0},l),null,16))],64)))),128))])),_:3},8,["data","row-key","class"])),[[je,Z.value]]),G.value>0?(i(),v(_e,{key:0,total:G.value,"onUpdate:total":l[1]||(l[1]=e=>G.value=e),page:W.pageNum,"onUpdate:page":l[2]||(l[2]=e=>W.pageNum=e),limit:W.pageSize,"onUpdate:limit":l[3]||(l[3]=e=>W.pageSize=e),onPagination:pe},null,8,["total","page","limit"])):g("",!0),c("div",q,[u(re,{type:"primary",size:"small",onClick:me},{default:m((()=>[j(x(ne.value),1)])),_:1}),u(re,{size:"small",onClick:ce},{default:m((()=>l[6]||(l[6]=[j("清 空")]))),_:1,__:[6]}),u(re,{size:"small",onClick:fe},{default:m((()=>l[7]||(l[7]=[j("关 闭")]))),_:1,__:[7]})])],512)])),_:3},16,["visible","width"])],4)}}}),[["__scopeId","data-v-283070f5"]]),J={pk:"id",width:"70%",placeholder:"请选择用户",formItems:[{type:"input",label:"关键字",prop:"keywords",attrs:{placeholder:"用户名/昵称/手机号",clearable:!0,style:{width:"200px"}}},{type:"tree-select",label:"部门",prop:"deptId",attrs:{placeholder:"请选择",data:[{value:1,label:"有来技术",children:[{value:2,label:"研发部门"},{value:3,label:"测试部门"}]}],filterable:!0,"check-strictly":!0,"render-after-expand":!1,clearable:!0,style:{width:"150px"}}},{type:"select",label:"状态",prop:"status",attrs:{placeholder:"全部",clearable:!0,style:{width:"100px"}},options:[{label:"启用",value:1},{label:"禁用",value:0}]},{type:"date-picker",label:"创建时间",prop:"createAt",attrs:{type:"daterange","range-separator":"~","start-placeholder":"开始时间","end-placeholder":"截止时间","value-format":"YYYY-MM-DD",style:{width:"240px"}}}],indexAction:function(e){if("createAt"in e){const l=e.createAt;(null==l?void 0:l.length)>1&&(e.startTime=l[0],e.endTime=l[1]),delete e.createAt}return C.getPage(e)},tableColumns:[{type:"selection",width:50,align:"center"},{label:"编号",align:"center",prop:"id",width:100},{label:"用户名",align:"center",prop:"username"},{label:"用户昵称",align:"center",prop:"nickname",width:120},{label:"性别",align:"center",prop:"gender",width:100,templet:"custom",slotName:"gender"},{label:"部门",align:"center",prop:"deptName",width:120},{label:"手机号码",align:"center",prop:"mobile",width:120},{label:"状态",align:"center",prop:"status",templet:"custom",slotName:"status"},{label:"创建时间",align:"center",prop:"createTime",width:180}]},Z={class:"app-container"},G=e({__name:"index",setup(e){const t=U(),a=l();function r(e){a.value=e[0]}const s=o((()=>{var e;const l=null==(e=t.getDictionary("gender").find((e=>{var l;return e.value==(null==(l=a.value)?void 0:l.gender)})))?void 0:e.label;return a.value?`${a.value.username} - ${l} - ${a.value.deptName}`:""}));return(e,l)=>{const t=O,a=Q,o=L,d=F;return i(),n("div",Z,[u(t,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/table-select/index.vue",type:"primary",target:"_blank",class:"mb-10"},{default:m((()=>l[0]||(l[0]=[j(" 示例源码 请点击>>>> ")]))),_:1,__:[0]}),u(d,{text:p(s),"select-config":p(J),onConfirmClick:r},{status:m((e=>[u(a,{type:1==e.row[e.prop]?"success":"info"},{default:m((()=>[j(x(1==e.row[e.prop]?"启用":"禁用"),1)])),_:2},1032,["type"])])),gender:m((e=>[u(o,{modelValue:e.row.gender,"onUpdate:modelValue":l=>e.row.gender=l,code:"gender"},null,8,["modelValue","onUpdate:modelValue"])])),_:1},8,["text","select-config"])])}}});export{G as default};
