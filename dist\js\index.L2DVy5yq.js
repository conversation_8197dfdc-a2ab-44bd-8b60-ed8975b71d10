import{t as s,bP as e,_ as a,d as o,b as l,c as n,g as t,e as c,f as i,C as r,h as u,l as p,n as m,i as d,w as f,m as b,Y as k,j as v,E as g,k as y,T as C,q as h}from"./index.Dk5pbsTU.js";import{u as T}from"./use-form-common-props.CQPDkY7k.js";const _=s({type:{type:String,values:["primary","success","info","warning","danger"],default:"primary"},closable:Boolean,disableTransitions:<PERSON><PERSON>an,hit:Boolean,color:String,size:{type:String,values:e},effect:{type:String,values:["dark","light","plain"],default:"light"},round:Boolean}),E={close:s=>s instanceof MouseEvent,click:s=>s instanceof MouseEvent},S=o({name:"ElTag"});const B=h(a(o({...S,props:_,emits:E,setup(s,{emit:e}){const a=s,o=T(),h=l("tag"),_=n((()=>{const{type:s,hit:e,effect:l,closable:n,round:t}=a;return[h.b(),h.is("closable",n),h.m(s||"primary"),h.m(o.value),h.m(l),h.is("hit",e),h.is("round",t)]})),E=s=>{e("close",s)},S=s=>{e("click",s)},B=s=>{var e,a,o;(null==(o=null==(a=null==(e=null==s?void 0:s.component)?void 0:e.subTree)?void 0:a.component)?void 0:o.bum)&&(s.component.subTree.component.bum=null)};return(s,e)=>s.disableTransitions?(i(),t("span",{key:0,class:m(d(_)),style:y({backgroundColor:s.color}),onClick:S},[r("span",{class:m(d(h).e("content"))},[p(s.$slots,"default")],2),s.closable?(i(),c(d(g),{key:0,class:m(d(h).e("close")),onClick:v(E,["stop"])},{default:f((()=>[b(d(k))])),_:1},8,["class","onClick"])):u("v-if",!0)],6)):(i(),c(C,{key:1,name:`${d(h).namespace.value}-zoom-in-center`,appear:"",onVnodeMounted:B},{default:f((()=>[r("span",{class:m(d(_)),style:y({backgroundColor:s.color}),onClick:S},[r("span",{class:m(d(h).e("content"))},[p(s.$slots,"default")],2),s.closable?(i(),c(d(g),{key:0,class:m(d(h).e("close")),onClick:v(E,["stop"])},{default:f((()=>[b(d(k))])),_:1},8,["class","onClick"])):u("v-if",!0)],6)])),_:3},8,["name"]))}}),[["__file","tag.vue"]]));export{B as E,_ as t};
