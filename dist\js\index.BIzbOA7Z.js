import{aT as e,d as i,r as t,S as l,o as a,aQ as o,g as n,f as s,C as r,m as d,w as u,Z as p,i as m,$ as c,V as v,e as f,h as g,d6 as h,ak as b,az as y}from"./index.Dk5pbsTU.js";import{v as _}from"./el-loading.Dqi-qL7c.js";import{E as j}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as w}from"./el-card.DwLhVNHW.js";import S from"./index.Cywy93e7.js";import{a as W,E as x}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{E as k}from"./el-switch.kQ5v4arH.js";/* empty css               */import{a as C,E}from"./el-form-item.Bw6Zyv_7.js";import{E as V}from"./el-button.CXI119n4.js";import{E as U,a as T}from"./el-select.CRWkm-it.js";import{E as B}from"./el-input.DiGatoux.js";/* empty css                       */import{E as I}from"./index.BcMfjWDS.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./el-pagination.C5FHY27u.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./index.ybpLT-bz.js";import"./index.C9UdVphc.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./validator.HGn2BZtD.js";const A={getSensitiveWordPage:function(i){return e({url:"/sensitiveWord/page",method:"get",params:i})},addSensitiveWord:function(i){return e({url:"/sensitiveWord/save",method:"post",data:i})},deleteSensitiveWordById:function(i){return e({url:`/sensitiveWord/remove/${i}`,method:"delete"})},updateSensitiveWord:function(i){return e({url:"/sensitiveWord/update",method:"put",data:i})},getSensitiveWordById:function(i){return e({url:`/sensitiveWord/getInfo/${i}`,method:"get"})},refreshSensitiveWord:function(){return e({url:"/sensitiveWord/refresh",method:"post"})},importSensitiveWords:function(i){const t=new FormData;return t.append("file",i),e({url:"/sensitiveWord/importWords",method:"post",headers:{"Content-Type":"multipart/form-data"},data:t})},downloadSensitiveWordTemplate:function(){return e({url:"/sensitiveWord/template",method:"get",responseType:"blob"})},enableSensitiveWord:function(i){return e({url:"/sensitiveWord/enable",method:"put",data:i})},refreshSensitiveWordByTenant:function(){return e({url:"/sensitiveWord/refreshByTenant",method:"post",data:{}})}},R={class:"app-container"},N={class:"search-bar"},z={class:"mb-10px"},F={class:"dialog-footer"},L=i({name:"SensitiveWord",inheritAttrs:!1,__name:"index",setup(e){const i=t(),L=t(),P=t(!1),q=t(!1),O=t(!1),$=t([]),K=t(0),H=l({pageNumber:1,pageSize:10,word:"",tags:"",isSensitive:""}),J=t([]),M=l({title:"",visible:!1}),Q=l({id:void 0,word:"",tags:"",isSensitive:1,isEnable:1,createdAt:"",updatedAt:""}),Z=l({word:[{required:!0,message:"请输入敏感词",trigger:"blur"}],isSensitive:[{required:!0,message:"请选择是否敏感词",trigger:"blur"}]}),D=t([]),G={id:void 0,word:"",tags:"",isSensitive:0,isEnable:1,createdAt:"",updatedAt:""},Y=t(void 0);function X(){P.value=!0,A.getSensitiveWordPage(H).then((e=>{J.value=e.list,K.value=e.total})).finally((()=>{P.value=!1}))}function ee(){i.value.resetFields(),H.pageNumber=1,X()}function ie(e){$.value=e.map((e=>e.id))}function te(e){M.visible=!0,e?(M.title="修改敏感词",A.getSensitiveWordById(e).then((e=>{e=e||{...G},Object.assign(Q,e),Q.isSensitive=Number(e.isSensitive)||1,D.value=e.tags?e.tags.split(","):[]}))):(M.title="新增敏感词",D.value=[],Q.isSensitive=1)}function le(){L.value.validate((e=>{if(e){P.value=!0;const e=Q.id,i={...Q,tags:D.value.join(",")};e?A.updateSensitiveWord(i).then((()=>{y.success("修改成功"),ae(),ee()})).finally((()=>P.value=!1)):A.addSensitiveWord(i).then((()=>{y.success("新增成功"),ae(),ee()})).finally((()=>P.value=!1))}}))}function ae(){M.visible=!1,L.value.resetFields(),L.value.clearValidate(),Q.id=void 0,Q.isSensitive=0}function oe(e){let i=[];e?i=[e]:$.value.length>0&&(i=$.value),0!==i.length?I.confirm("确认删除已选中的数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{P.value=!0;try{for(const e of i)await A.deleteSensitiveWordById(e);y.success("删除成功"),ee()}finally{P.value=!1}}),(()=>{y.info("已取消删除")})):y.warning("请勾选删除项")}function ne(){P.value=!0,A.refreshSensitiveWord().then((()=>{y.success("刷新成功")})).finally((()=>{P.value=!1}))}function se(){P.value=!0,A.refreshSensitiveWordByTenant().then((()=>{y.success("敏感词库缓存刷新成功")})).catch((()=>{})).finally((()=>{P.value=!1}))}const re=t(null);function de(){re.value&&re.value.click()}function ue(e){const i=e.target.files;if(!i||0===i.length)return;const t=i[0];P.value=!0,A.importSensitiveWords(t).then((()=>{y.success("导入成功"),ee()})).catch((()=>{})).finally((()=>{P.value=!1,re.value&&(re.value.value="")}))}function pe(){q.value=!0,A.downloadSensitiveWordTemplate().then((e=>{const i=e instanceof Blob?e:e.data,t=window.URL.createObjectURL(i),l=document.createElement("a");l.href=t,l.download="敏感词导入模板.xlsx",document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(t)})).catch((()=>{y.error("模板下载失败")})).finally((()=>{q.value=!1}))}return a((()=>{X()})),(e,t)=>{const l=B,a=C,I=T,D=U,G=V,me=E,ce=x,ve=k,fe=W,ge=S,he=w,be=j,ye=o("hasPerm"),_e=_;return s(),n("div",R,[r("div",N,[d(me,{ref_key:"queryFormRef",ref:i,model:m(H),inline:!0},{default:u((()=>[d(a,{prop:"word",label:"敏感词"},{default:u((()=>[d(l,{modelValue:m(H).word,"onUpdate:modelValue":t[0]||(t[0]=e=>m(H).word=e),placeholder:"请输入敏感词",clearable:"",onKeyup:p(X,["enter"])},null,8,["modelValue"])])),_:1}),d(a,{prop:"isEnable",label:"校验状态"},{default:u((()=>[d(D,{modelValue:m(H).isEnable,"onUpdate:modelValue":t[1]||(t[1]=e=>m(H).isEnable=e),style:{"min-width":"100px"},placeholder:"请选择"},{default:u((()=>[d(I,{label:"是",value:"1"}),d(I,{label:"否",value:"0"})])),_:1},8,["modelValue"])])),_:1}),d(a,null,{default:u((()=>[d(G,{type:"primary",icon:"search",onClick:X},{default:u((()=>t[10]||(t[10]=[c("搜索")]))),_:1,__:[10]}),d(G,{icon:"refresh",onClick:ee},{default:u((()=>t[11]||(t[11]=[c("重置")]))),_:1,__:[11]})])),_:1})])),_:1},8,["model"])]),d(he,{shadow:"never"},{default:u((()=>[r("div",z,[v((s(),f(G,{type:"success",icon:"plus",loading:m(P),onClick:t[2]||(t[2]=e=>te())},{default:u((()=>t[12]||(t[12]=[c(" 新增 ")]))),_:1,__:[12]},8,["loading"])),[[ye,["sys:sensitive:add"]]]),v((s(),f(G,{type:"danger",disabled:0===m($).length||m(P),loading:m(P),icon:"delete",onClick:t[3]||(t[3]=e=>oe())},{default:u((()=>t[13]||(t[13]=[c(" 删除 ")]))),_:1,__:[13]},8,["disabled","loading"])),[[ye,["sys:sensitive:del"]]]),v((s(),f(G,{type:"primary",icon:"refresh",loading:m(P),onClick:ne},{default:u((()=>t[14]||(t[14]=[c(" 刷新敏感词缓存 ")]))),_:1,__:[14]},8,["loading"])),[[ye,["sys:sensitive:refresh"]]]),v((s(),f(G,{type:"warning",icon:"refresh",loading:m(P),onClick:se},{default:u((()=>t[15]||(t[15]=[c(" 刷新敏感词缓存 ")]))),_:1,__:[15]},8,["loading"])),[[ye,["sys:sensitive:refresh:tenant"]]]),v((s(),f(G,{type:"primary",icon:"upload",loading:m(P),onClick:de},{default:u((()=>t[16]||(t[16]=[c(" 导入 ")]))),_:1,__:[16]},8,["loading"])),[[ye,["sys:sensitive:import"]]]),v((s(),f(G,{type:"info",icon:"download",loading:m(q),onClick:pe},{default:u((()=>t[17]||(t[17]=[c(" 下载模板 ")]))),_:1,__:[17]},8,["loading"])),[[ye,["sys:sensitive:import"]]]),r("input",{ref_key:"importFileInput",ref:re,type:"file",accept:".xls,.xlsx",style:{display:"none"},onChange:ue},null,544)]),v((s(),f(fe,{ref:"dataTableRef",data:m(J),"highlight-current-row":"",border:"",onSelectionChange:ie},{default:u((()=>[d(ce,{type:"selection",width:"55",align:"center"}),d(ce,{label:"敏感词",prop:"word","min-width":"120"}),d(ce,{label:"校验状态",prop:"isEnable","min-width":"100"},{default:u((e=>[d(ve,{modelValue:e.row.isEnable,"onUpdate:modelValue":i=>e.row.isEnable=i,disabled:!m(h)(["sys:sensitive:edit"])||m(O),loading:m(O)&&e.row.id===m(Y),"active-value":1,"inactive-value":0,onChange:i=>function(e,i){if(O.value)return void(e.isEnable=1===Number(i)?0:1);O.value=!0,Y.value=e.id;const t=Number(i);A.enableSensitiveWord({id:e.id,word:e.word,isSensitive:e.isSensitive,isEnable:t}).then((()=>{y.success(1===t?"已启用":"已禁用")})).catch((()=>{e.isEnable=1===t?0:1})).finally((()=>{O.value=!1,Y.value=void 0}))}(e.row,i)},null,8,["modelValue","onUpdate:modelValue","disabled","loading","onChange"])])),_:1}),d(ce,{label:"添加时间",prop:"createdAt","min-width":"160"}),d(ce,{fixed:"right",label:"操作",width:"180"},{default:u((e=>[v((s(),f(G,{type:"primary",size:"small",link:"",icon:"edit",loading:m(P),onClick:i=>te(e.row.id)},{default:u((()=>t[18]||(t[18]=[c(" 编辑 ")]))),_:2,__:[18]},1032,["loading","onClick"])),[[ye,["sys:sensitive:edit"]]]),v((s(),f(G,{type:"danger",size:"small",link:"",icon:"delete",loading:m(P),onClick:i=>oe(e.row.id)},{default:u((()=>t[19]||(t[19]=[c(" 删除 ")]))),_:2,__:[19]},1032,["loading","onClick"])),[[ye,["sys:sensitive:del"]]])])),_:1})])),_:1},8,["data"])),[[_e,m(P)]]),m(K)>0?(s(),f(ge,{key:0,total:m(K),"onUpdate:total":t[4]||(t[4]=e=>b(K)?K.value=e:null),page:m(H).pageNumber,"onUpdate:page":t[5]||(t[5]=e=>m(H).pageNumber=e),limit:m(H).pageSize,"onUpdate:limit":t[6]||(t[6]=e=>m(H).pageSize=e),onPagination:X},null,8,["total","page","limit"])):g("",!0)])),_:1}),d(be,{modelValue:m(M).visible,"onUpdate:modelValue":t[9]||(t[9]=e=>m(M).visible=e),title:m(M).title,width:"500px",onClose:ae},{footer:u((()=>[r("div",F,[d(G,{type:"primary",loading:m(P),onClick:le},{default:u((()=>t[20]||(t[20]=[c("确 定")]))),_:1,__:[20]},8,["loading"]),d(G,{disabled:m(P),onClick:ae},{default:u((()=>t[21]||(t[21]=[c("取 消")]))),_:1,__:[21]},8,["disabled"])])])),default:u((()=>[d(me,{ref_key:"sensitiveWordFormRef",ref:L,model:m(Q),rules:m(Z),"label-width":"100px"},{default:u((()=>[d(a,{label:"敏感词",prop:"word"},{default:u((()=>[d(l,{modelValue:m(Q).word,"onUpdate:modelValue":t[7]||(t[7]=e=>m(Q).word=e),placeholder:"请输入敏感词"},null,8,["modelValue"])])),_:1}),d(a,{label:"校验状态",prop:"isSensitive"},{default:u((()=>[d(D,{modelValue:m(Q).isEnable,"onUpdate:modelValue":t[8]||(t[8]=e=>m(Q).isEnable=e),style:{width:"120px"},placeholder:"请选择"},{default:u((()=>[d(I,{label:"是",value:1}),d(I,{label:"否",value:0})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}});export{L as default};
