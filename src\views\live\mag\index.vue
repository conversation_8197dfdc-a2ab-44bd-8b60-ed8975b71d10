<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="keywords" label="">
          <el-input
            v-model="page.query.keywords"
            placeholder="主播检索"
            clearable
            @keyup.enter="getPage"
          />
        </el-form-item>
        <el-form-item prop="status" label="直播间状态">
          <dict v-model="page.query.status" code="live_status" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          v-hasPerm="['live:mag:save']"
          type="success"
          icon="plus"
          @click="editModelRef?.open()"
        >
          新增
        </el-button>
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="封面图" align="center" prop="name" width="100">
          <template #default="{ row }">
            <el-image
              style="width: 30px; height: 30px"
              :preview-src-list="[row.coverUrl]"
              preview-teleported
              :src="row.coverUrl"
            />
          </template>
        </el-table-column>
        <el-table-column label="主播" align="center" prop="name" width="150">
          <template #default="{ row }">
            <div class="flex-center">
              <el-image
                style="width: 30px; height: 30px; border-radius: 30px"
                :preview-src-list="[row.anchorAvatar]"
                preview-teleported
                :src="row.anchorAvatar"
              />
              <div class="m-l-1" style="flex: 1">
                <div class="color-primary cursor-pointer" @click="infoModelRef?.open(row)">
                  {{ row.anchorName }}
                </div>
                <div class="m-t-1">{{ row.anchorPhone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <!--        <el-table-column label="助理" align="center" prop="assistNames" min-width="100" />-->
        <el-table-column label="直播间" align="center" prop="roomName" min-width="120" />
        <el-table-column label="直播名称" align="center" prop="title" min-width="120" />
        <el-table-column label="是否私密" align="center" width="60">
          <template #default="{ row }">
            <dict-label v-model="row.isSecret" code="yes_or_no" />
          </template>
        </el-table-column>

        <el-table-column label="直播类型" align="center" width="100">
          <template #default="{ row }">
            <dict-label v-model="row.liveType" code="live_type" />
          </template>
        </el-table-column>
        <el-table-column label="直播状态" align="center" width="100">
          <template #default="{ row }">
            <dict-label v-model="row.status" code="live_session_status" />
          </template>
        </el-table-column>
        <el-table-column label="允许回放" align="center" width="60">
          <template #default="{ row }">
            <dict-label v-model="row.allowReplay" code="yes_or_no" />
          </template>
        </el-table-column>
        <el-table-column label="开播时间（计划）" align="center" prop="startTime" min-width="120" />
        <el-table-column
          label="开播时间（实际）"
          align="center"
          prop="actualStartTime"
          min-width="120"
        />
        <el-table-column label="开播时长" align="center" prop="durationStr" min-width="100" />
        <el-table-column
          label="观看用户数（累计）"
          align="center"
          prop="watchCount"
          min-width="100"
        />
        <el-table-column label="发言数" align="center" prop="commentCount" min-width="100" />
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button
              v-if="scope.row.status <= 1"
              type="warning"
              size="small"
              link
              @click="sharedUrlRef?.open(scope.row)"
            >
              分享
            </el-button>
            <el-button
              v-if="scope.row.status === 1"
              type="primary"
              size="small"
              link
              @click="livePlayerRef?.open(scope.row.sharedUrl)"
            >
              观看直播
            </el-button>
            <el-button
              v-if="scope.row.status === 0 && hasAuth('live:mag:save')"
              type="primary"
              size="small"
              link
              @click="editModelRef?.open(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="scope.row.status === 0 && hasAuth('live:mag:delete')"
              type="danger"
              size="small"
              link
              :loading="scope.row.loading"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
            <el-button
              v-if="hasAuth('live:mag:save')"
              type="warning"
              size="small"
              link
              @click="editModelRef?.open(scope.row, true)"
            >
              场次复刻
            </el-button>
            <el-button
              v-if="hasAuth('live:mag:save')"
              :type="scope.row.allowReplay === 1 ? 'danger' : 'primary'"
              size="small"
              link
              @click="onEditAllowReplay(scope.row)"
            >
              {{ scope.row.allowReplay === 1 ? "取消回放" : "允许回放" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
    <edit-model ref="editModelRef" @success="resetQuery" />
    <info-model ref="infoModelRef" />
    <shared-url ref="sharedUrlRef" />
    <live-player ref="livePlayerRef" />
  </div>
</template>

<script setup lang="ts">
import EditModel from "./edit.vue";
import InfoModel from "./info/index.vue";
import LivePlayer from "./livePlayer.vue";

defineOptions({ name: "LiveMag" });
import { usePage } from "@/utils/commonSetup";
import { hasAuth } from "@/plugins/permission";
import LiveSessionApi, { LiveSessionPageQueryDto, LiveSessionPageVo } from "@/api/live/liveSession";
import SharedUrl from "./sharedUrl.vue";

const livePlayerRef = ref<InstanceType<typeof LivePlayer>>();
const editModelRef = ref<InstanceType<typeof EditModel>>();
const infoModelRef = ref<InstanceType<typeof InfoModel>>();
const sharedUrlRef = ref<InstanceType<typeof SharedUrl>>();
const { page, getPage, resetQuery } = usePage<LiveSessionPageQueryDto, LiveSessionPageVo>(
  new LiveSessionPageQueryDto(),
  LiveSessionApi.page
);

function onEditAllowReplay(_row: LiveSessionPageVo) {
  const _info = JSON.parse(JSON.stringify(_row));
  _info.allowReplay = _row.allowReplay === 1 ? 0 : 1;
  ElMessageBox.confirm(
    `确定${_info.allowReplay === 1 ? "允许" : "取消"}回放直播场次吗？`,
    `${_info.allowReplay === 1 ? "允许" : "取消"}回放`,
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    }
  )
    .then(() => {
      _row.loading = true;
      LiveSessionApi.allowPlayback(_info.id)
        .then(() => {
          ElMessage.success("操作成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}

function handleDelete(_row: LiveSessionPageVo) {
  ElMessageBox.confirm(`确定删除《${_row.anchorName}》的直播场次吗？`, `删除`, {
    confirmButtonText: "确定删除",
    cancelButtonText: "取消",
    confirmButtonClass: "el-button--danger",
    type: "error",
  })
    .then(() => {
      _row.loading = true;
      LiveSessionApi.remove(_row.id)
        .then(() => {
          ElMessage.success("删除成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}
</script>
