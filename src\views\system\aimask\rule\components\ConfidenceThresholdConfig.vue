<template>
  <div class="confidence-threshold-config">
    <el-form label-width="120px">
      <el-form-item label="最小置信度">
        <el-slider
          v-model="localConfig.min_confidence"
          :min="0"
          :max="1"
          :step="0.05"
          :format-tooltip="(val) => (val * 100).toFixed(0) + '%'"
          show-input
          :show-input-controls="false"
          style="width: 300px"
        />
        <span class="ml-2 text-sm text-gray-500">越接近1要求越精确</span>
      </el-form-item>
      <el-form-item label="提示词模板">
        <el-input
          v-model="localConfig.prompt_template"
          type="textarea"
          :rows="4"
          placeholder="请输入AI判断的提示词模板"
        />
        <div class="text-sm text-gray-500 mt-1">提示：可以使用 {{ 变量 }} 语法引用上下文变量</div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";

const props = defineProps({
  config: {
    type: Object,
    default: () => ({ min_confidence: 0.7, prompt_template: "" }),
  },
});

const emit = defineEmits(["update"]);

const localConfig = reactive({
  min_confidence: props.config?.min_confidence ?? 0.7,
  prompt_template: props.config?.prompt_template || "",
});

// 监听配置变化并向上传递
watch(
  localConfig,
  (newVal) => {
    emit("update", { ...newVal });
  },
  { deep: true }
);
</script>
