var e=Object.defineProperty,l=(l,t,a)=>((l,t,a)=>t in l?e(l,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):l[t]=a)(l,"symbol"!=typeof t?t+"":t,a);import{aT as t,d as a,r as s,c as i,s as o,b7 as r,g as d,f as n,m as u,w as v,C as p,l as m,h as c,e as f,P as g,Q as h,i as y,$ as w,a0 as x}from"./index.Dk5pbsTU.js";import{E as I}from"./el-divider.2VNIZioN.js";import{E as b}from"./el-empty.Dee0wMKK.js";import{a as _}from"./commonSetup.Dm-aByKQ.js";import{E as k}from"./index.ybpLT-bz.js";import{_ as S}from"./_plugin-vue_export-helper.BCo6x5W8.js";class j{constructor(){l(this,"pageNum",1),l(this,"pageSize",30),l(this,"sessionId","")}}class R{constructor(){l(this,"pageNum",1),l(this,"pageSize",10),l(this,"sessionId",""),l(this,"inviterId",""),l(this,"type","")}}const $={stats:e=>t({url:`/tenant/live/preview/data/stats?sessionId=${e}`,method:"get"}),info:e=>t({url:`/tenant/live/preview/info?sessionId=${e}`,method:"get"}),inviterList:e=>t({url:"/tenant/live/preview/inviter/list",method:"get",params:e}),list:e=>t({url:"/tenant/live/preview/list",method:"get",params:e}),sessionUser:e=>t({url:"/tenant/live/preview/sessionUser",method:"get",params:e})},T={class:"i-scroll-list"},V={ref:"iScrollListBody"},z={key:0,class:"i-scroll-list-body"},B=S(a({__name:"index",props:{emptyTitle:{default:"暂无数据"},modelValue:{},list:{},hideItem:{type:Boolean,default:!1},auto:{type:Boolean,default:!1},api:{}},emits:["update:modelValue","update:list"],setup(e,{expose:l,emit:t}){const a=e,S=s(),j=t,R=i({get:()=>a.modelValue,set:e=>j("update:modelValue",e)}),$=o([]),{sendInfo:B,onSend:L}=_(a.api,a.modelValue,(e=>{$.value=$.value.concat($.value,e.records),j("update:list",$.value)})),N=s(0),E=i((()=>{var e;return!B.loading&&((null==(e=B.data)?void 0:e.totalRow)||0)>$.value.length})),P=({scrollTop:e})=>{var l,t,a,s;if(B.loading)return;const i=(null==(t=null==(l=S.value)?void 0:l.wrapRef)?void 0:t.scrollHeight)||0,o=(null==(s=null==(a=S.value)?void 0:a.wrapRef)?void 0:s.clientHeight)||0;N.value=e,i-o+100>=e&&E.value&&(R.value.pageNum+=1,L())};r((()=>{N.value>0&&x((()=>{var e;null==(e=S.value)||e.setScrollTop(N.value)}))})),a.auto&&L();return l({reset:()=>($.value=[],j("update:list",[]),R.value.pageNum=1,void L())}),(e,l)=>{const t=b,a=I;return n(),d("div",T,[u(y(k),{ref_key:"iScrollBodyRef",ref:S,height:"100%",onScroll:P},{default:v((()=>{var l,s,i,o;return[p("div",V,[m(e.$slots,"head",{},void 0,!0),e.hideItem?c("",!0):(n(),d("div",z,[(n(!0),d(g,null,h($.value,((l,t)=>(n(),d("div",{key:t,class:"i-scroll-list-li"},[m(e.$slots,"default",{item:l,itemIndex:t},void 0,!0)])))),128))])),m(e.$slots,"allList",{data:$.value},void 0,!0),y(B).loading||(null==(s=null==(l=y(B))?void 0:l.data)?void 0:s.totalRow)?((null==(o=null==(i=y(B))?void 0:i.data)?void 0:o.totalRow)||0)<=$.value.length?c("",!0):(n(),f(a,{key:2,class:"i-scroll-list-divider",style:{"max-width":"220px",margin:"50px auto",background:"none","font-size":"12px"}},{default:v((()=>{var e,l;return[y(B).loading?(n(),d(g,{key:0},[w("加载中...")],64)):((null==(l=null==(e=y(B))?void 0:e.data)?void 0:l.totalRow)||0)<=$.value.length?(n(),d(g,{key:1},[w(" 已经到底了 ")],64)):(n(),d(g,{key:2},[w("加载更多")],64))]})),_:1})):m(e.$slots,"empty",{key:1},(()=>[u(t,{"image-size":30,title:e.emptyTitle},null,8,["title"])]),!0)],512)]})),_:3},512)])}}}),[["__scopeId","data-v-da1b0baf"]]);export{B as I,$ as L,R as P,j as a};
