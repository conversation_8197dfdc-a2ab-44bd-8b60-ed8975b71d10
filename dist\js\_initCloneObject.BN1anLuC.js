import{bR as r,cr as t,c5 as e,bS as n}from"./index.Dk5pbsTU.js";import{e as o,f as u,h as a,o as c,U as f}from"./_Uint8Array.n_j8oILW.js";var s=Object.create,i=function(){function t(){}return function(e){if(!r(e))return{};if(s)return s(e);t.prototype=e;var n=new t;return t.prototype=void 0,n}}();function p(r,t){var e=-1,n=r.length;for(t||(t=Array(n));++e<n;)t[e]=r[e];return t}function v(r,n,o,u){var a=!o;o||(o={});for(var c=-1,f=n.length;++c<f;){var s=n[c],i=void 0;void 0===i&&(i=r[s]),a?t(o,s,i):e(o,s,i)}return o}var l=Object.prototype.hasOwnProperty;function y(t){if(!r(t))return function(r){var t=[];if(null!=r)for(var e in Object(r))t.push(e);return t}(t);var e=o(t),n=[];for(var u in t)("constructor"!=u||!e&&l.call(t,u))&&n.push(u);return n}function b(r){return a(r)?u(r,!0):y(r)}var d=c(Object.getPrototypeOf,Object),h="object"==typeof exports&&exports&&!exports.nodeType&&exports,j=h&&"object"==typeof module&&module&&!module.nodeType&&module,m=j&&j.exports===h?n.Buffer:void 0,w=m?m.allocUnsafe:void 0;function O(r,t){if(t)return r.slice();var e=r.length,n=w?w(e):new r.constructor(e);return r.copy(n),n}function g(r){var t=new r.constructor(r.byteLength);return new f(t).set(new f(r)),t}function x(r,t){var e=t?g(r.buffer):r.buffer;return new r.constructor(e,r.byteOffset,r.length)}function U(r){return"function"!=typeof r.constructor||o(r)?{}:i(d(r))}export{g as a,x as b,v as c,p as d,O as e,d as g,U as i,b as k};
