import{d as e,r as a,S as t,ag as l,o as s,aQ as i,g as o,f as r,m as d,w as n,P as u,Q as p,e as m,C as c,E as v,i as f,d2 as g,$ as y,V as j,h as b,F as h,d6 as _,az as w}from"./index.Dk5pbsTU.js";import{v as x}from"./el-loading.Dqi-qL7c.js";import{E as I}from"./el-drawer.Df_TzNjH.js";import"./el-overlay.DpVCS8zG.js";import k from"./index.Cywy93e7.js";import{a as C,E as V}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import{E as A}from"./el-popper.Dbn4MgsT.js";/* empty css                     */import{E as N}from"./el-switch.kQ5v4arH.js";/* empty css               */import{E as R}from"./el-card.DwLhVNHW.js";import{E as z,a as U}from"./el-form-item.Bw6Zyv_7.js";import{E}from"./el-button.CXI119n4.js";import{E as T,a as S}from"./el-select.CRWkm-it.js";import{E as F}from"./el-cascader-panel.CXd5b5cH.js";import"./el-input.DiGatoux.js";import"./el-radio.w2rep3_A.js";import{_ as M}from"./AiVestConfigForm.vue_vue_type_script_setup_true_lang.B7yYlWHh.js";import{a as P,b as $,c as O,s as W,u as D,d as G,g as B}from"./aiVestConfig.CW5InkHZ.js";import{V as q}from"./vest.j8NavXyg.js";import{b as K,a as L}from"./liveRoom.B9NhOBdK.js";import{E as Q}from"./index.BcMfjWDS.js";import"./index.C6NthMtN.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./el-pagination.C5FHY27u.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./index.ybpLT-bz.js";import"./index.C9UdVphc.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./validator.HGn2BZtD.js";import"./arrays.CygeFE-H.js";import"./cloneDeep.DcCMo0F4.js";import"./el-link.qHYW6llJ.js";import"./el-slider.DLapdkRs.js";import"./el-input-number.C02ig7uT.js";import"./index.Cd8M2JyP.js";import"./el-col.Cfu8vZQ4.js";import"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import"./el-divider.2VNIZioN.js";import"./SetKnowledgeFile.QQOMNXXo.js";import"./el-dialog.Cnp8BitR.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./el-popover.Bo2lPKkO.js";import"./dropdown.B_OfpyL_.js";import"./el-empty.Dee0wMKK.js";import"./el-main.CclDHmVj.js";import"./commonSetup.Dm-aByKQ.js";import"./knowledgeType.DrndLWHa.js";const H={class:"app-container"},J={style:{display:"flex","align-items":"center"}},X={class:"flex items-center justify-between"},Y={class:"dialog-footer"},Z=e({name:"AiVestConfig",inheritAttrs:!1,__name:"index",setup(e){const Z=a(),ee=a(),ae=a(!1),te=a(!1),le=a(0),se=a(void 0),ie=a([]),oe=a([]),re={value:"id",label:"label",children:"children",emitPath:!0,lazy:!0,lazyLoad:async(e,a)=>{const{level:t}=e;if(0===t)try{const e=new K;e.pageSize=100;const t=(await L.page(e)).records||[];a(t.map((e=>({id:e.id,label:e.roomName,leaf:!1}))))}catch{a([])}else if(1===t)try{const t=e.data.id,l=await q.getAllGroups(t),s=l.data||l||[];a(s.map((e=>({id:e.id,label:e.groupName,leaf:!1}))))}catch{a([])}else if(2===t)try{const t=e.data.id,l=await q.getVestsByGroupId(t),s=l.data||l||[];a(s.map((e=>({id:e.id,label:e.nickname,leaf:!0,vest:e}))))}catch{a([])}}},de=a([]),ne=t({vestCascader:[],vestId:"",vestNickname:"",type:"",ruleId:void 0,status:"",pageNum:1,pageSize:10}),ue=a([]),pe=t({title:"",visible:!1}),me=t({id:void 0,vestCascader:[],vestId:0,temperature:.7,promptTemplate:"",contextWindow:0,useRag:0,type:0,isDelay:0,fuzzinessRate:0,isAuto:0,triggerMode:0,ruleId:0,sessionRound:1,status:1,knowledgeIds:[],sex:"",age:"",intro:""});async function ce(){try{ae.value=!0;const e=await P();e&&e.data&&Array.isArray(e.data)?ie.value=e.data:Array.isArray(e)?ie.value=e:ie.value=[]}catch(e){w.error("获取触发规则列表失败"),ie.value=[]}finally{ae.value=!1}}function ve(){ae.value=!0;const e={...ne};null!==e.ruleId&&void 0!==e.ruleId||delete e.ruleId,null===e.type||void 0===e.type||""===e.type?delete e.type:e.type=e.type.toString(),$(e).then((e=>{ue.value=e.records||e.data&&e.data.records||[],le.value=e.totalRow||e.data&&e.data.totalRow||0})).finally((()=>{ae.value=!1}))}function fe(){Z.value.resetFields(),ne.pageNum=1,ne.vestCascader=[],ne.vestId="",ne.type="",ne.ruleId=void 0,ne.status="",ve()}function ge(e){if(e&&Array.isArray(e)&&3===e.length){const a=e[2];"number"==typeof a||"string"==typeof a&&/^\d+$/.test(a)?ne.vestId=a.toString():ne.vestId=""}else ne.vestId=""}async function ye(e){if(0===ie.value.length&&(ae.value=!0,await ce(),ae.value=!1),e&&e.id){pe.title="修改AI马甲配置",ae.value=!0;try{const a=await O(e.vestId),t=a.data||a;Object.assign(me,{id:t.id,vestId:t.vestId,modelName:t.modelName,temperature:t.temperature,promptTemplate:t.promptTemplate,contextWindow:t.contextWindow,useRag:t.useRag,type:t.type,isDelay:t.isDelay,fuzzinessRate:t.fuzzinessRate,isAuto:t.isAuto,triggerMode:t.triggerMode,ruleId:t.ruleId?Number(t.ruleId):0,sessionRound:t.sessionRound,status:t.status,sex:t.sex,age:t.age,intro:t.intro,vestCascader:[]}),pe.visible=!0}catch(a){w.error("获取配置信息失败")}finally{ae.value=!1}}else{pe.title="新增AI马甲配置";const e=oe.value.length>0?oe.value[0].value:0;Object.assign(me,{id:void 0,vestCascader:[],vestId:0,temperature:.7,promptTemplate:"",contextWindow:0,useRag:0,type:e,isDelay:0,fuzzinessRate:0,isAuto:0,triggerMode:0,ruleId:0,sessionRound:1,status:1,knowledgeIds:[],sex:"",age:"",intro:""}),pe.visible=!0,setTimeout((()=>{ee.value&&ee.value.resetForm()}),100)}}async function je(){try{if(!(await ee.value.validate()))return void w.error("请完善表单信息");ae.value=!0;const e={...me};if(delete e.vestCascader,e.vestId=Number(e.vestId),e.ruleId=Number(e.ruleId)||0,e.temperature=Number(e.temperature)||.7,e.type=Number(e.type),e.ruleId>0&&(e.triggerMode=2),!me.id&&(!e.vestId||e.vestId<=0))return w.error("请选择有效的马甲"),void(ae.value=!1);if(e.temperature<0||e.temperature>2)return w.error("生成温度必须在0-2之间"),void(ae.value=!1);W(e).then((e=>{w.success(me.id?"修改成功":"新增成功"),be(),fe()})).catch((e=>{})).finally((()=>ae.value=!1))}catch(e){w.error("表单验证失败，请检查输入"),ae.value=!1}}function be(){pe.visible=!1,ee.value&&ee.value.resetForm()}const he=l();function _e(){he.push("/aimask/maskRuleConfig")}async function we(){try{ae.value=!0;const e=await B();e&&e.data&&Array.isArray(e.data)?oe.value=e.data.map((e=>({label:e.description,value:e.type}))):Array.isArray(e)?oe.value=e.map((e=>({label:e.description,value:e.type}))):oe.value=[]}catch(e){w.error("获取人设类型列表失败"),oe.value=[]}finally{ae.value=!1}}return s((async()=>{await Promise.all([ce(),we()]),ve()})),(e,a)=>{const t=F,l=U,s=S,P=T,$=v,O=A,W=E,B=z,q=R,K=V,L=N,ce=C,he=k,we=I,xe=i("hasPerm"),Ie=x;return r(),o("div",H,[d(q,{shadow:"never",class:"mb-4"},{default:n((()=>[d(B,{ref_key:"queryFormRef",ref:Z,model:ne,inline:!0},{default:n((()=>[d(l,{prop:"vestCascader",label:"选择马甲"},{default:n((()=>[d(t,{modelValue:ne.vestCascader,"onUpdate:modelValue":a[0]||(a[0]=e=>ne.vestCascader=e),options:de.value,props:re,placeholder:"请选择直播间/分组/马甲",clearable:"",filterable:"",style:{width:"300px"},onChange:ge},null,8,["modelValue","options"])])),_:1}),d(l,{prop:"type",label:"人设类型"},{default:n((()=>[d(P,{modelValue:ne.type,"onUpdate:modelValue":a[1]||(a[1]=e=>ne.type=e),placeholder:"请选择人设类型",clearable:"",style:{width:"150px"}},{default:n((()=>[(r(!0),o(u,null,p(oe.value,(e=>(r(),m(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),d(l,{prop:"ruleId",label:"触发规则"},{default:n((()=>[c("div",J,[d(P,{modelValue:ne.ruleId,"onUpdate:modelValue":a[2]||(a[2]=e=>ne.ruleId=e),placeholder:"请选择触发规则",clearable:"",filterable:"",style:{width:"200px"}},{default:n((()=>[(r(!0),o(u,null,p(ie.value,(e=>(r(),m(s,{key:e.id,label:e.ruleName,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),d(O,{content:"需要先去触发规则配置页面配置触发规则，再返回此处选择",placement:"top",effect:"light"},{default:n((()=>[d($,{class:"ml-2 text-gray-400 cursor-help"},{default:n((()=>[d(f(g))])),_:1})])),_:1})])])),_:1}),d(l,{prop:"status",label:"状态"},{default:n((()=>[d(P,{modelValue:ne.status,"onUpdate:modelValue":a[3]||(a[3]=e=>ne.status=e),style:{width:"100px"},placeholder:"请选择",clearable:""},{default:n((()=>[d(s,{label:"启用",value:"1"}),d(s,{label:"禁用",value:"0"})])),_:1},8,["modelValue"])])),_:1}),d(l,null,{default:n((()=>[d(W,{type:"primary",icon:"search",onClick:ve},{default:n((()=>a[10]||(a[10]=[y("搜索")]))),_:1,__:[10]}),d(W,{icon:"refresh",onClick:fe},{default:n((()=>a[11]||(a[11]=[y("重置")]))),_:1,__:[11]})])),_:1})])),_:1},8,["model"])])),_:1}),d(q,{shadow:"never"},{header:n((()=>[c("div",X,[a[13]||(a[13]=c("h3",{class:"text-lg font-semibold"},"AI马甲配置列表",-1)),j((r(),m(W,{type:"success",icon:"plus",loading:ae.value,onClick:a[4]||(a[4]=e=>ye())},{default:n((()=>a[12]||(a[12]=[y(" 新增配置 ")]))),_:1,__:[12]},8,["loading"])),[[xe,["sys:aimask:add"]]])])])),default:n((()=>[j((r(),m(ce,{ref:"dataTableRef",data:ue.value,"highlight-current-row":"",border:""},{default:n((()=>[d(K,{type:"selection",width:"55",align:"center"}),d(K,{label:"马甲昵称",prop:"vestNickname","min-width":"120"}),d(K,{label:"人设类型",prop:"typeName","min-width":"100"}),d(K,{label:"触发规则",prop:"ruleName","min-width":"100"},{default:n((e=>[y(h(e.row.ruleName||(e.row.triggerModeName?`${e.row.triggerModeName}（旧）`:"无")),1)])),_:1}),d(K,{label:"状态",prop:"status","min-width":"100"},{default:n((e=>[d(L,{modelValue:e.row.status,"onUpdate:modelValue":a=>e.row.status=a,disabled:!f(_)(["sys:aimask:add"])||te.value,loading:te.value&&e.row.id===se.value,"active-value":1,"inactive-value":0,onChange:a=>function(e,a){if(te.value)return void(e.status=1===Number(a)?0:1);te.value=!0,se.value=e.id;const t=Number(a);D(e.id,t).then((()=>{w.success(1===t?"已启用":"已禁用")})).catch((()=>{e.status=1===t?0:1})).finally((()=>{te.value=!1,se.value=void 0}))}(e.row,a)},null,8,["modelValue","onUpdate:modelValue","disabled","loading","onChange"])])),_:1}),d(K,{label:"创建时间",prop:"createdAt","min-width":"160"}),d(K,{label:"更新时间",prop:"updatedAt","min-width":"160"}),d(K,{fixed:"right",label:"操作",width:"120"},{default:n((e=>[j((r(),m(W,{type:"primary",size:"small",link:"",loading:ae.value,onClick:a=>ye(e.row)},{default:n((()=>a[14]||(a[14]=[y(" 编辑 ")]))),_:2,__:[14]},1032,["loading","onClick"])),[[xe,["sys:aimask:add"]]]),j((r(),m(W,{type:"danger",size:"small",link:"",loading:ae.value,onClick:a=>{return t=e.row,void Q.confirm(`确认删除马甲"${t.vestNickname}"的AI配置吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{ae.value=!0,G(t.id).then((()=>{w.success("删除成功"),ve()})).finally((()=>{ae.value=!1}))})).catch((()=>{}));var t}},{default:n((()=>a[15]||(a[15]=[y(" 删除 ")]))),_:2,__:[15]},1032,["loading","onClick"])),[[xe,["sys:aimask:del"]]])])),_:1})])),_:1},8,["data"])),[[Ie,ae.value]]),le.value>0?(r(),m(he,{key:0,total:le.value,"onUpdate:total":a[5]||(a[5]=e=>le.value=e),page:ne.pageNum,"onUpdate:page":a[6]||(a[6]=e=>ne.pageNum=e),limit:ne.pageSize,"onUpdate:limit":a[7]||(a[7]=e=>ne.pageSize=e),onPagination:ve},null,8,["total","page","limit"])):b("",!0)])),_:1}),d(we,{modelValue:pe.visible,"onUpdate:modelValue":a[9]||(a[9]=e=>pe.visible=e),title:pe.title,size:"850px",onClose:be},{footer:n((()=>[c("div",Y,[d(W,{type:"primary",loading:ae.value,onClick:je},{default:n((()=>a[16]||(a[16]=[y("确 定")]))),_:1,__:[16]},8,["loading"]),d(W,{disabled:ae.value,onClick:be},{default:n((()=>a[17]||(a[17]=[y("取 消")]))),_:1,__:[17]},8,["disabled"])])])),default:n((()=>[d(M,{ref_key:"aiVestConfigFormRef",ref:ee,"model-value":me,"trigger-rule-options":ie.value,"ai-vest-config-list":ue.value,"persona-type-options":oe.value,"onUpdate:modelValue":a[8]||(a[8]=e=>Object.assign(me,e)),onGoToRuleConfig:_e},null,8,["model-value","trigger-rule-options","ai-vest-config-list","persona-type-options"])])),_:1},8,["modelValue","title"])])}}});export{Z as default};
