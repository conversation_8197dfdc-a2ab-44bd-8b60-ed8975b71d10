import{t as s,z as a,v as e,b6 as t,bP as r,_ as c,d as i,b as n,r as l,c as o,L as u,J as p,I as v,g as f,f as d,e as m,l as y,k as S,i as g,w as b,D as h,E as k,n as z,q as E}from"./index.Dk5pbsTU.js";const _=s({size:{type:[Number,String],values:r,default:"",validator:s=>t(s)},shape:{type:String,values:["circle","square"],default:"circle"},icon:{type:e},src:{type:String,default:""},alt:String,srcSet:String,fit:{type:a(String),default:"cover"}}),j={error:s=>s instanceof Event},q=i({name:"ElAvatar"});const x=E(c(i({...q,props:_,emits:j,setup(s,{emit:a}){const e=s,r=n("avatar"),c=l(!1),i=o((()=>{const{size:s,icon:a,shape:t}=e,c=[r.b()];return u(s)&&c.push(r.m(s)),a&&c.push(r.m("icon")),t&&c.push(r.m(t)),c})),E=o((()=>{const{size:s}=e;return t(s)?r.cssVarBlock({size:p(s)||""}):void 0})),_=o((()=>({objectFit:e.fit})));function j(s){c.value=!0,a("error",s)}return v((()=>e.src),(()=>c.value=!1)),(s,a)=>(d(),f("span",{class:z(g(i)),style:S(g(E))},[!s.src&&!s.srcSet||c.value?s.icon?(d(),m(g(k),{key:1},{default:b((()=>[(d(),m(h(s.icon)))])),_:1})):y(s.$slots,"default",{key:2}):(d(),f("img",{key:0,src:s.src,alt:s.alt,srcset:s.srcSet,style:S(g(_)),onError:j},null,44,["src","alt","srcset"]))],6))}}),[["__file","avatar.vue"]]));export{x as E};
