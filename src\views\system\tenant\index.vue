<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="keywords" label="">
          <el-input
            v-model="page.query.keywords"
            placeholder="公司名称、负责人名称检索"
            clearable
            @keyup.enter="getPage"
          />
        </el-form-item>
        <el-form-item prop="status" label="公司状态">
          <dict v-model="page.query.status" code="enable_status" />
        </el-form-item>
        <el-form-item prop="keywords" label="部署方式">
          <dict v-model="page.query.deployWay" code="tenant_deploy_way" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          v-hasPerm="['sys:tenant:save']"
          type="success"
          icon="plus"
          @click="editModelRef?.open()"
        >
          新增
        </el-button>
        <el-button
          v-hasPerm="['sys:tenant:export']"
          type="primary"
          icon="download"
          @click="TenantApi.export(page.query)"
        >
          导出
        </el-button>
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="公司名称" align="center" prop="name" min-width="120" />
        <el-table-column label="公司logo" align="center" prop="name" width="100">
          <template #default="{ row }">
            <el-image
              style="width: 30px; height: 30px; border-radius: 30px"
              :preview-src-list="[row.logo]"
              preview-teleported
              :src="row.logo"
            />
          </template>
        </el-table-column>
        <el-table-column label="公司负责人" align="center" prop="nickName" min-width="100" />
        <el-table-column label="负责人电话" align="center" prop="phone" min-width="100" />
        <el-table-column label="状态" align="center" width="80">
          <template #default="{ row }">
            <dict-label v-model="row.status" code="enable_status" />
          </template>
        </el-table-column>
        <el-table-column label="部署方式" align="center" width="100">
          <template #default="{ row }">
            <dict-label v-model="row.deployWay" code="tenant_deploy_way" />
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="160">
          <template #default="scope">
            <el-button
              v-hasPerm="['sys:tenant:save']"
              type="primary"
              size="small"
              link
              @click="editModelRef?.open(scope.row)"
            >
              编辑
            </el-button>
            <!--            <el-button-->
            <!--              v-hasPerm="['sys:tenant:delete']"-->
            <!--              type="danger"-->
            <!--              size="small"-->
            <!--              link-->
            <!--              :loading="scope.row.loading"-->
            <!--              @click="handleDelete(scope.row)"-->
            <!--            >-->
            <!--              删除-->
            <!--            </el-button>-->
            <el-button
              v-hasPerm="['sys:tenant:save']"
              type="warning"
              size="small"
              link
              :loading="scope.row.loading"
              @click="editStatus(scope.row)"
            >
              {{ scope.row.status ? "停用" : "启用" }}
            </el-button>
            <el-button
              v-hasPerm="['sys:tenant:save']"
              type="info"
              size="small"
              link
              :loading="scope.row.loading"
              @click="editAiRef.open(scope.row)"
            >
              AI授权
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
    <edit-model ref="editModelRef" @success="resetQuery" />
    <edit-ai ref="editAiRef" @success="resetQuery" />
  </div>
</template>

<script setup lang="ts">
import EditModel from "./edit.vue";
// import { h } from "vue";
import TenantApi, {
  TenantPageListVO,
  TenantPageQuery,
  TenantPageVO,
  TenantSaveDto,
} from "@/api/system/tenant";

defineOptions({ name: "Tenant" });
import { usePage } from "@/utils/commonSetup";
import EditAi from "@/views/system/tenant/editAi.vue";

const editModelRef = ref<InstanceType<typeof EditModel>>();
const editAiRef = ref<InstanceType<typeof EditAi>>();
const { page, getPage, resetQuery } = usePage<TenantPageQuery, TenantPageVO>(
  new TenantPageQuery(),
  TenantApi.page
);

// function handleDelete(_row: TenantPageListVO) {
//   ElMessageBox.confirm(
//     h("p", null, [
//       h("span", null, "确定删除"),
//       h("span", { style: { color: "var(--el-color-primary)" } }, _row.name),
//       h("span", null, "公司吗？"),
//     ]),
//     `删除`,
//     {
//       confirmButtonText: "确定删除",
//       cancelButtonText: "取消",
//       confirmButtonClass: "el-button--danger",
//       type: "error",
//     }
//   )
//     .then(() => {
//       _row.loading = true;
//       TenantApi.remove(_row.id)
//         .then(() => {
//           ElMessage.success("删除成功");
//           resetQuery();
//         })
//         .finally(() => (_row.loading = false));
//     })
//     .catch(() => ElMessage.info("已取消"));
// }

function editStatus(_row: TenantPageListVO) {
  ElMessageBox.confirm(
    `确定要${_row.status ? "停用" : "启用"}租户《${_row.name}》吗？`,
    `${_row.status ? "停用" : "启用"}授权`,
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      confirmButtonClass: "el-button--danger",
      type: "error",
    }
  )
    .then(() => {
      const formData = new TenantSaveDto(_row);
      if (formData.status) formData.status = 0;
      else formData.status = 1;
      _row.loading = true;
      TenantApi.save(formData)
        .then(() => {
          ElMessage.success("操作成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}
</script>
