import{d as e,r as l,S as t,o as a,aQ as o,g as s,f as r,C as i,m as d,w as n,Z as p,i as m,$ as u,V as c,e as f,j as _,az as j}from"./index.Dk5pbsTU.js";import{v as h}from"./el-loading.Dqi-qL7c.js";import{E as y}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as b,a as v}from"./el-radio.w2rep3_A.js";import{E as g}from"./el-input.DiGatoux.js";import{E as x}from"./el-input-number.C02ig7uT.js";/* empty css               */import{E as V,a as k}from"./el-select.CRWkm-it.js";/* empty css                     */import"./el-popper.Dbn4MgsT.js";import"./el-tree.ChWw39qP.js";import"./el-checkbox.DDYarIkn.js";import"./el-text.6kaKYQ9U.js";import{E as w}from"./el-tree-select.HrmCEXac.js";import{E as C}from"./el-card.DwLhVNHW.js";import{a as E,E as I}from"./el-table-column.DRgE6Qqc.js";import"./el-tooltip.l0sNRNKZ.js";import{a as U,E as q}from"./el-form-item.Bw6Zyv_7.js";import{E as F}from"./el-button.CXI119n4.js";import{D as z}from"./dept.DtjbtSjb.js";/* empty css                       */import{E as O}from"./index.L2DVy5yq.js";import{E as R}from"./index.BcMfjWDS.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./use-form-common-props.CQPDkY7k.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./use-form-item.DzRJVC1I.js";import"./index.DEKElSOG.js";import"./index.Vn8pbgQR.js";import"./index.Cd8M2JyP.js";import"./index.ybpLT-bz.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./index.DLOxQT-M.js";import"./_initCloneObject.BN1anLuC.js";import"./validator.HGn2BZtD.js";const A={class:"app-container"},B={class:"search-bar"},D={class:"mb-10px"},L={class:"dialog-footer"},P=e({name:"Dept",inheritAttrs:!1,__name:"index",setup(e){const P=l(),Q=l(),S=l(!1),T=l([]),H=t({}),J=t({title:"",visible:!1}),Y=l(),Z=l(),$=t({status:1,parentId:"0",sort:1}),G=t({parentId:[{required:!0,message:"上级部门不能为空",trigger:"change"}],name:[{required:!0,message:"部门名称不能为空",trigger:"blur"}],code:[{required:!0,message:"部门编号不能为空",trigger:"blur"}],sort:[{required:!0,message:"显示排序不能为空",trigger:"blur"}]});function K(){S.value=!0,z.getList(H).then((e=>{Y.value=e,S.value=!1}))}function M(){P.value.resetFields(),K()}function N(e){T.value=e.map((e=>e.id))}async function W(e,l){const t=await z.getOptions();Z.value=[{value:"0",label:"顶级部门",children:t}],J.visible=!0,l?(J.title="修改部门",z.getFormData(l).then((e=>{Object.assign($,e)}))):(J.title="新增部门",$.parentId=e||"0")}function X(){Q.value.validate((e=>{if(e){S.value=!0;const e=$.id;e?z.update(e,$).then((()=>{j.success("修改成功"),le(),K()})).finally((()=>S.value=!1)):z.add($).then((()=>{j.success("新增成功"),le(),K()})).finally((()=>S.value=!1))}}))}function ee(e){const l=[e||T.value].join(",");l?R.confirm("确认删除已选中的数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{S.value=!0,z.deleteByIds(l).then((()=>{j.success("删除成功"),M()})).finally((()=>S.value=!1))}),(()=>{j.info("已取消删除")})):j.warning("请勾选删除项")}function le(){J.visible=!1,Q.value.resetFields(),Q.value.clearValidate(),$.id=void 0,$.parentId="0",$.status=1,$.sort=1}return a((()=>{K()})),(e,l)=>{const t=g,a=U,j=k,z=V,R=F,te=q,ae=I,oe=O,se=E,re=C,ie=w,de=x,ne=v,pe=b,me=y,ue=o("hasPerm"),ce=h;return r(),s("div",A,[i("div",B,[d(te,{ref_key:"queryFormRef",ref:P,model:m(H),inline:!0},{default:n((()=>[d(a,{label:"关键字",prop:"keywords"},{default:n((()=>[d(t,{modelValue:m(H).keywords,"onUpdate:modelValue":l[0]||(l[0]=e=>m(H).keywords=e),placeholder:"部门名称",onKeyup:p(K,["enter"])},null,8,["modelValue"])])),_:1}),d(a,{label:"部门状态",prop:"status"},{default:n((()=>[d(z,{modelValue:m(H).status,"onUpdate:modelValue":l[1]||(l[1]=e=>m(H).status=e),placeholder:"全部",clearable:"",class:"!w-[100px]"},{default:n((()=>[d(j,{value:1,label:"正常"}),d(j,{value:0,label:"禁用"})])),_:1},8,["modelValue"])])),_:1}),d(a,null,{default:n((()=>[d(R,{class:"filter-item",type:"primary",icon:"search",onClick:K},{default:n((()=>l[10]||(l[10]=[u(" 搜索 ")]))),_:1,__:[10]}),d(R,{icon:"refresh",onClick:M},{default:n((()=>l[11]||(l[11]=[u("重置")]))),_:1,__:[11]})])),_:1})])),_:1},8,["model"])]),d(re,{shadow:"never"},{default:n((()=>[i("div",D,[c((r(),f(R,{type:"success",icon:"plus",onClick:l[2]||(l[2]=e=>W())},{default:n((()=>l[12]||(l[12]=[u(" 新增 ")]))),_:1,__:[12]})),[[ue,["sys:dept:add"]]]),c((r(),f(R,{type:"danger",disabled:0===m(T).length,icon:"delete",onClick:l[3]||(l[3]=e=>ee())},{default:n((()=>l[13]||(l[13]=[u(" 删除 ")]))),_:1,__:[13]},8,["disabled"])),[[ue,["sys:dept:delete"]]])]),c((r(),f(se,{data:m(Y),"row-key":"id","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"},onSelectionChange:N},{default:n((()=>[d(ae,{type:"selection",width:"55",align:"center"}),d(ae,{prop:"name",label:"部门名称","min-width":"200"}),d(ae,{prop:"code",label:"部门编号",width:"200"}),d(ae,{prop:"status",label:"状态",width:"100"},{default:n((e=>[1==e.row.status?(r(),f(oe,{key:0,type:"success"},{default:n((()=>l[14]||(l[14]=[u("正常")]))),_:1,__:[14]})):(r(),f(oe,{key:1,type:"info"},{default:n((()=>l[15]||(l[15]=[u("禁用")]))),_:1,__:[15]}))])),_:1}),d(ae,{prop:"sort",label:"排序",width:"100"}),d(ae,{label:"操作",fixed:"right",align:"left",width:"200"},{default:n((e=>[c((r(),f(R,{type:"primary",link:"",size:"small",icon:"plus",onClick:_((l=>W(e.row.id,void 0)),["stop"])},{default:n((()=>l[16]||(l[16]=[u(" 新增 ")]))),_:2,__:[16]},1032,["onClick"])),[[ue,["sys:dept:add"]]]),c((r(),f(R,{type:"primary",link:"",size:"small",icon:"edit",onClick:_((l=>W(e.row.parentId,e.row.id)),["stop"])},{default:n((()=>l[17]||(l[17]=[u(" 编辑 ")]))),_:2,__:[17]},1032,["onClick"])),[[ue,["sys:dept:edit"]]]),c((r(),f(R,{type:"danger",link:"",size:"small",icon:"delete",onClick:_((l=>ee(e.row.id)),["stop"])},{default:n((()=>l[18]||(l[18]=[u(" 删除 ")]))),_:2,__:[18]},1032,["onClick"])),[[ue,["sys:dept:delete"]]])])),_:1})])),_:1},8,["data"])),[[ce,m(S)]])])),_:1}),d(me,{modelValue:m(J).visible,"onUpdate:modelValue":l[9]||(l[9]=e=>m(J).visible=e),title:m(J).title,width:"600px",onClosed:le},{footer:n((()=>[i("div",L,[d(R,{type:"primary",onClick:X},{default:n((()=>l[21]||(l[21]=[u("确 定")]))),_:1,__:[21]}),d(R,{onClick:le},{default:n((()=>l[22]||(l[22]=[u("取 消")]))),_:1,__:[22]})])])),default:n((()=>[d(te,{ref_key:"deptFormRef",ref:Q,model:m($),rules:m(G),"label-width":"80px"},{default:n((()=>[d(a,{label:"上级部门",prop:"parentId"},{default:n((()=>[d(ie,{modelValue:m($).parentId,"onUpdate:modelValue":l[4]||(l[4]=e=>m($).parentId=e),placeholder:"选择上级部门",data:m(Z),filterable:"","check-strictly":"","render-after-expand":!1},null,8,["modelValue","data"])])),_:1}),d(a,{label:"部门名称",prop:"name"},{default:n((()=>[d(t,{modelValue:m($).name,"onUpdate:modelValue":l[5]||(l[5]=e=>m($).name=e),placeholder:"请输入部门名称"},null,8,["modelValue"])])),_:1}),d(a,{label:"部门编号",prop:"code"},{default:n((()=>[d(t,{modelValue:m($).code,"onUpdate:modelValue":l[6]||(l[6]=e=>m($).code=e),placeholder:"请输入部门编号"},null,8,["modelValue"])])),_:1}),d(a,{label:"显示排序",prop:"sort"},{default:n((()=>[d(de,{modelValue:m($).sort,"onUpdate:modelValue":l[7]||(l[7]=e=>m($).sort=e),"controls-position":"right",style:{width:"100px"},min:0},null,8,["modelValue"])])),_:1}),d(a,{label:"部门状态"},{default:n((()=>[d(pe,{modelValue:m($).status,"onUpdate:modelValue":l[8]||(l[8]=e=>m($).status=e)},{default:n((()=>[d(ne,{value:1},{default:n((()=>l[19]||(l[19]=[u("正常")]))),_:1,__:[19]}),d(ne,{value:0},{default:n((()=>l[20]||(l[20]=[u("禁用")]))),_:1,__:[20]})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}});export{P as default};
