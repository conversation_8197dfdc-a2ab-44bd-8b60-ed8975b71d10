import{t as s,b6 as e,_ as a,d as t,b as i,I as l,g as r,f as u,l as n,n as o,i as p,B as c,y as v,v as d,r as f,A as m,o as S,a6 as y,S as h,c as g,h as x,C as $,k as w,e as k,w as E,D as _,E as b,m as C,cL as B,Y as j,F as I,$ as P,G as z,q as D}from"./index.Dk5pbsTU.js";import{C as N}from"./event.BwRzfsZt.js";import{u as W}from"./index.C_BbqFDa.js";const q=s({space:{type:[Number,String],default:""},active:{type:Number,default:0},direction:{type:String,default:"horizontal",values:["horizontal","vertical"]},alignCenter:{type:Boolean},simple:{type:Boolean},finishStatus:{type:String,values:["wait","process","finish","error","success"],default:"finish"},processStatus:{type:String,values:["wait","process","finish","error","success"],default:"process"}}),A={[N]:(s,a)=>[s,a].every(e)},F=t({name:"ElSteps"});var G=a(t({...F,props:q,emits:A,setup(s,{emit:e}){const a=s,t=i("steps"),{children:d,addChild:f,removeChild:m}=W(c(),"ElStep");return l(d,(()=>{d.value.forEach(((s,e)=>{s.setIndex(e)}))})),v("ElSteps",{props:a,steps:d,addStep:f,removeStep:m}),l((()=>a.active),((s,a)=>{e(N,s,a)})),(s,e)=>(u(),r("div",{class:o([p(t).b(),p(t).m(s.simple?"simple":s.direction)])},[n(s.$slots,"default")],2))}}),[["__file","steps.vue"]]);const L=s({title:{type:String,default:""},icon:{type:d},description:{type:String,default:""},status:{type:String,values:["","wait","process","finish","error","success"],default:""}}),Y=t({name:"ElStep"});var H=a(t({...Y,props:L,setup(s){const a=s,t=i("step"),v=f(-1),d=f({}),z=f(""),D=m("ElSteps"),N=c();S((()=>{l([()=>D.props.active,()=>D.props.processStatus,()=>D.props.finishStatus],(([s])=>{M(s)}),{immediate:!0})})),y((()=>{D.removeStep(O.uid)}));const W=g((()=>a.status||z.value)),q=g((()=>{const s=D.steps.value[v.value-1];return s?s.currentStatus:"wait"})),A=g((()=>D.props.alignCenter)),F=g((()=>"vertical"===D.props.direction)),G=g((()=>D.props.simple)),L=g((()=>D.steps.value.length)),Y=g((()=>{var s;return(null==(s=D.steps.value[L.value-1])?void 0:s.uid)===(null==N?void 0:N.uid)})),H=g((()=>G.value?"":D.props.space)),J=g((()=>[t.b(),t.is(G.value?"simple":D.props.direction),t.is("flex",Y.value&&!H.value&&!A.value),t.is("center",A.value&&!F.value&&!G.value)])),K=g((()=>{const s={flexBasis:e(H.value)?`${H.value}px`:H.value?H.value:100/(L.value-(A.value?0:1))+"%"};return F.value||Y.value&&(s.maxWidth=100/L.value+"%"),s})),M=s=>{s>v.value?z.value=D.props.finishStatus:s===v.value&&"error"!==q.value?z.value=D.props.processStatus:z.value="wait";const e=D.steps.value[v.value-1];e&&e.calcProgress(z.value)},O=h({uid:N.uid,currentStatus:W,setIndex:s=>{v.value=s},calcProgress:s=>{const e="wait"===s,a={transitionDelay:`${e?"-":""}${150*v.value}ms`},t=s===D.props.processStatus||e?0:100;a.borderWidth=t&&!G.value?"1px":0,a["vertical"===D.props.direction?"height":"width"]=`${t}%`,d.value=a}});return D.addStep(O),(s,e)=>(u(),r("div",{style:w(p(K)),class:o(p(J))},[x(" icon & line "),$("div",{class:o([p(t).e("head"),p(t).is(p(W))])},[p(G)?x("v-if",!0):(u(),r("div",{key:0,class:o(p(t).e("line"))},[$("i",{class:o(p(t).e("line-inner")),style:w(d.value)},null,6)],2)),$("div",{class:o([p(t).e("icon"),p(t).is(s.icon||s.$slots.icon?"icon":"text")])},[n(s.$slots,"icon",{},(()=>[s.icon?(u(),k(p(b),{key:0,class:o(p(t).e("icon-inner"))},{default:E((()=>[(u(),k(_(s.icon)))])),_:1},8,["class"])):"success"===p(W)?(u(),k(p(b),{key:1,class:o([p(t).e("icon-inner"),p(t).is("status")])},{default:E((()=>[C(p(B))])),_:1},8,["class"])):"error"===p(W)?(u(),k(p(b),{key:2,class:o([p(t).e("icon-inner"),p(t).is("status")])},{default:E((()=>[C(p(j))])),_:1},8,["class"])):p(G)?x("v-if",!0):(u(),r("div",{key:3,class:o(p(t).e("icon-inner"))},I(v.value+1),3))]))],2)],2),x(" title & description "),$("div",{class:o(p(t).e("main"))},[$("div",{class:o([p(t).e("title"),p(t).is(p(W))])},[n(s.$slots,"title",{},(()=>[P(I(s.title),1)]))],2),p(G)?(u(),r("div",{key:0,class:o(p(t).e("arrow"))},null,2)):(u(),r("div",{key:1,class:o([p(t).e("description"),p(t).is(p(W))])},[n(s.$slots,"description",{},(()=>[P(I(s.description),1)]))],2))],2)],6))}}),[["__file","item.vue"]]);const J=D(G,{Step:H}),K=z(H);export{J as E,K as a};
