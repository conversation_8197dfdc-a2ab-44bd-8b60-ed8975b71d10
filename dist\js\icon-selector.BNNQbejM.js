import{_ as e}from"./index.B_Z-un7A.js";import{d as o,r,g as s,f as i,m as t,w as p,$ as m,ak as n,i as a}from"./index.Dk5pbsTU.js";import{E as l}from"./el-link.qHYW6llJ.js";import"./el-popper.Dbn4MgsT.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.C6NthMtN.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./use-form-common-props.CQPDkY7k.js";import"./el-popover.Bo2lPKkO.js";import"./dropdown.B_OfpyL_.js";import"./el-tab-pane.CXnN_Izo.js";import"./error.D_Dr4eZ1.js";import"./strings.MqEQKtyI.js";import"./event.BwRzfsZt.js";import"./index.C_BbqFDa.js";import"./vnode.Cbclzz8S.js";/* empty css                     */import"./el-tooltip.l0sNRNKZ.js";import"./el-input.DiGatoux.js";import"./index.DEKElSOG.js";import"./use-form-item.DzRJVC1I.js";import"./index.Vn8pbgQR.js";import"./index.ybpLT-bz.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.DuiNpp1i.js";const d={class:"app-container"},j=o({__name:"icon-selector",setup(o){const j=r("el-icon-edit");return(o,r)=>{const u=l,c=e;return i(),s("div",d,[t(u,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/icon-selector.vue",type:"primary",target:"_blank",class:"mb-10"},{default:p((()=>r[1]||(r[1]=[m(" 示例源码 请点击>>>> ")]))),_:1,__:[1]}),t(c,{modelValue:a(j),"onUpdate:modelValue":r[0]||(r[0]=e=>n(j)?j.value=e:null)},null,8,["modelValue"])])}}});export{j as default};
