var e=Object.defineProperty,t=(t,s,a)=>((t,s,a)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[s]=a)(t,"symbol"!=typeof s?s+"":s,a);import{aT as s}from"./index.Dk5pbsTU.js";class a{constructor(e){t(this,"id"),t(this,"name",""),t(this,"status",1),e&&(this.id=e.id,this.name=e.name,this.status=e.status)}}class i{constructor(){t(this,"pageNum",1),t(this,"pageSize",10),t(this,"keyWord",""),t(this,"status","")}}class o{constructor(){t(this,"pageNum",1),t(this,"pageSize",10),t(this,"keyWord",""),t(this,"status",""),t(this,"typeId",""),t(this,"includeIds",[]),t(this,"excludeIds",[])}}class d{constructor(e){t(this,"id"),t(this,"fileName",""),t(this,"typeId",""),t(this,"status",1),t(this,"content",""),e&&(this.id=e.id,this.fileName=e.fileName,this.typeId=String(e.typeId),this.status=e.status,this.content=e.content)}}const l={allKnowledgeType:e=>s({url:"/system/aiKnowledge/typeAll",method:"get",params:e}),aiKnowledgePage:e=>s({url:"/system/aiKnowledge/aiKnowledgePage",method:"get",params:e}),removeKnowledge:e=>s({url:`/system/aiKnowledge/removeKnowledge/${e}`,method:"delete"}),aiKnowledgeSave:e=>s({url:"/system/aiKnowledge/saveOrUpdate",method:"post",data:e}),page:e=>s({url:"/system/aiKnowledge/typePage",method:"get",params:e}),remove:e=>s({url:`/system/aiKnowledge/removeType?id=${e}`,method:"delete"}),saveOrEdit:e=>s({url:"/system/aiKnowledge/saveOrEditType",method:"post",data:e})};export{d as A,i as K,a as S,l as a,o as b};
