import{d as e,S as l,r as t,e as o,f as a,w as i,m as r,i as s,C as m,$ as p,az as d}from"./index.Dk5pbsTU.js";import{E as n}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as u}from"./el-button.CXI119n4.js";import{E as f,a as c}from"./el-form-item.Bw6Zyv_7.js";import{E as j}from"./el-input.DiGatoux.js";import{E as v}from"./el-input-number.C02ig7uT.js";/* empty css               */import{E as x,a as _}from"./el-select.CRWkm-it.js";/* empty css                     */import"./el-popper.Dbn4MgsT.js";import{E as V,a as h}from"./el-col.Cfu8vZQ4.js";import{E as b}from"./el-switch.kQ5v4arH.js";import{_ as w}from"./SingleImageUpload.WGBxPB_4.js";import{L as U,a as g}from"./liveRoom.B9NhOBdK.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./use-form-item.DzRJVC1I.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./index.C9UdVphc.js";import"./index.DEKElSOG.js";import"./index.Vn8pbgQR.js";import"./index.Cd8M2JyP.js";import"./index.ybpLT-bz.js";import"./index.L2DVy5yq.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./isEqual.C0S6DIiJ.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./validator.HGn2BZtD.js";import"./el-progress.BQBUwu9o.js";import"./cloneDeep.DcCMo0F4.js";import"./el-image-viewer.BH897zgF.js";import"./position.DfR5znly.js";import"./index.WzKGworL.js";const S={class:"dialog-footer"},C=y(e({__name:"edit",emits:["success"],setup(e,{expose:y,emit:C}){const E=l({visible:!1,title:""}),I=t(new U),L=t(),N=C,R=t(!1),k={coverUrl:[{required:!0,message:"请上传封面图",trigger:"blur"}],roomName:[{required:!0,message:"请输入直播间名称",trigger:"blur"}]};function q(){L.value.validate((e=>{e&&(R.value=!0,g.save(I.value.toAPI()).then((()=>{d.success("保存成功"),E.visible=!1,N("success")})).finally((()=>R.value=!1)))}))}return y({open:e=>{E.visible=!0,E.title=(null==e?void 0:e.id)?"编辑直播间":"新增直播间",I.value=new U(e)}}),(e,l)=>{const t=j,d=c,U=w,g=b,y=h,C=V,N=_,R=x,z=v,A=f,M=u,P=n;return a(),o(P,{modelValue:s(E).visible,"onUpdate:modelValue":l[13]||(l[13]=e=>s(E).visible=e),title:s(E).title,width:"700px"},{footer:i((()=>[m("div",S,[r(M,{type:"primary",onClick:q},{default:i((()=>l[16]||(l[16]=[p("确 定")]))),_:1,__:[16]}),r(M,{onClick:l[12]||(l[12]=e=>s(E).visible=!1)},{default:i((()=>l[17]||(l[17]=[p("取 消")]))),_:1,__:[17]})])])),default:i((()=>[r(A,{ref_key:"editFormRef",ref:L,model:s(I),rules:k,"label-width":"140px"},{default:i((()=>[r(d,{label:"直播间名称",prop:"roomName"},{default:i((()=>[r(t,{modelValue:s(I).roomName,"onUpdate:modelValue":l[0]||(l[0]=e=>s(I).roomName=e),maxlength:50,"show-word-limit":"",placeholder:"请输入直播间名称"},null,8,["modelValue"])])),_:1}),r(d,{label:"封面图",prop:"coverUrl"},{default:i((()=>[r(U,{modelValue:s(I).coverUrl,"onUpdate:modelValue":l[1]||(l[1]=e=>s(I).coverUrl=e),style:{width:"100px",height:"100px"}},null,8,["modelValue"])])),_:1}),r(d,{label:"直播间介绍",prop:"description"},{default:i((()=>[r(t,{modelValue:s(I).description,"onUpdate:modelValue":l[2]||(l[2]=e=>s(I).description=e),maxlength:200,"show-word-limit":"",type:"textarea",rows:5,placeholder:"请输入直播间介绍"},null,8,["modelValue"])])),_:1}),r(d,{label:"直播间公告",prop:"announcement"},{default:i((()=>[r(t,{modelValue:s(I).announcement,"onUpdate:modelValue":l[3]||(l[3]=e=>s(I).announcement=e),maxlength:200,"show-word-limit":"",type:"textarea",rows:4,placeholder:"请输入直播间公告"},null,8,["modelValue"])])),_:1}),r(d,{label:"温馨提示",prop:"notice"},{default:i((()=>[r(t,{modelValue:s(I).notice,"onUpdate:modelValue":l[4]||(l[4]=e=>s(I).notice=e),maxlength:80,"show-word-limit":"",type:"textarea",rows:4,placeholder:"请输入温馨提示"},null,8,["modelValue"])])),_:1}),r(d,{label:"在线用户显示设置"},{default:i((()=>[r(C,{gutter:20},{default:i((()=>[r(y,{span:12},{default:i((()=>[r(d,{label:"主播","label-width":"80px"},{default:i((()=>[r(g,{modelValue:s(I).anchorShowUserCount,"onUpdate:modelValue":l[5]||(l[5]=e=>s(I).anchorShowUserCount=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1}),r(y,{span:12},{default:i((()=>[r(d,{label:"助理","label-width":"80px"},{default:i((()=>[r(g,{modelValue:s(I).assistantShowUserCount,"onUpdate:modelValue":l[6]||(l[6]=e=>s(I).assistantShowUserCount=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(C,{gutter:20},{default:i((()=>[r(y,{span:12},{default:i((()=>[r(d,{label:"销售","label-width":"80px"},{default:i((()=>[r(g,{modelValue:s(I).saleShowUserCount,"onUpdate:modelValue":l[7]||(l[7]=e=>s(I).saleShowUserCount=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1}),r(y,{span:12},{default:i((()=>[r(d,{label:"用户","label-width":"80px"},{default:i((()=>[r(g,{modelValue:s(I).userShowUserCount,"onUpdate:modelValue":l[8]||(l[8]=e=>s(I).userShowUserCount=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),r(C,{gutter:20,style:{margin:"0"}},{default:i((()=>[r(y,{span:12},{default:i((()=>[r(d,{label:"直播间默认字号",prop:"fontSize"},{default:i((()=>[r(R,{modelValue:s(I).fontSize,"onUpdate:modelValue":l[9]||(l[9]=e=>s(I).fontSize=e),placeholder:"请选择字号"},{default:i((()=>[r(N,{label:"14px",value:"14"}),r(N,{label:"15px",value:"15"}),r(N,{label:"16px",value:"16"}),r(N,{label:"17px",value:"17"}),r(N,{label:"18px",value:"18"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),r(y,{span:12},{default:i((()=>[r(d,{label:"是否允许普通用户回复","label-width":"160px",prop:"formData"},{default:i((()=>[r(g,{modelValue:s(I).userReply,"onUpdate:modelValue":l[10]||(l[10]=e=>s(I).userReply=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(d,{label:"直播暂停自动结束",prop:"stopLiveMinutes"},{default:i((()=>[r(z,{modelValue:s(I).stopLiveMinutes,"onUpdate:modelValue":l[11]||(l[11]=e=>s(I).stopLiveMinutes=e),min:1,max:999,placeholder:"请输入分钟数"},null,8,["modelValue"]),l[14]||(l[14]=m("span",{style:{"margin-left":"10px"}},"分钟",-1))])),_:1,__:[14]}),l[15]||(l[15]=m("div",{class:"el-form-item-msg"},"当直播间暂停时长等于该值，则自动结束",-1))])),_:1,__:[15]},8,["model"])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-c7346779"]]);export{C as default};
