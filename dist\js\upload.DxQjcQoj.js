import{d as e,b2 as l,r as t,ai as a,o as s,ap as i,g as o,f as r,P as u,m as n,e as m,h as p,i as d,ak as c,w as f,E as j,C as v,az as _,$ as g}from"./index.Dk5pbsTU.js";import{a as y,E as x}from"./el-form-item.Bw6Zyv_7.js";import{_ as h}from"./FileUpload.DqYUzwLQ.js";import{_ as b}from"./SingleImageUpload.WGBxPB_4.js";import{E as V}from"./el-link.qHYW6llJ.js";import{a as w}from"./el-image-viewer.BH897zgF.js";import{E as C}from"./el-progress.BQBUwu9o.js";import{F}from"./index.WzKGworL.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./error.D_Dr4eZ1.js";import"./index.D6CER_Ot.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./el-button.CXI119n4.js";import"./index.DuiNpp1i.js";import"./use-form-item.DzRJVC1I.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./debounce.DJJTSR8O.js";import"./position.DfR5znly.js";import"./index.DEKElSOG.js";import"./scroll.CVc-P3_z.js";import"./cloneDeep.DcCMo0F4.js";import"./isEqual.C0S6DIiJ.js";const k={style:{width:"100%"}},U=["src"],M={class:"el-upload-list__item-actions"},z=["onClick"],E=["onClick"],S=e({__name:"MultiImageUpload",props:l({data:{type:Object,default:()=>({})},name:{type:String,default:"file"},limit:{type:Number,default:10},maxFileSize:{type:Number,default:10},accept:{type:String,default:"image/*"}},{modelValue:{type:[Array],required:!0,default:()=>[]},modelModifiers:{}}),emits:["update:modelValue"],setup(e){const l=e,g=t(!1),y=t(0),x=a(e,"modelValue"),h=t([]);function b(e){return l.accept.split(",").map((e=>e.trim())).some((l=>"image/*"===l?e.type.startsWith("image/"):l.startsWith(".")?e.name.toLowerCase().endsWith(l):e.type===l))?!(e.size>1024*l.maxFileSize*1024)||(_.warning("上传图片不能大于"+l.maxFileSize+"M"),!1):(_.warning(`上传文件的格式不正确，仅支持：${l.accept}`),!1)}function V(e){return new Promise(((t,a)=>{const s=e.file,i=new FormData;i.append(l.name,s),Object.keys(l.data).forEach((e=>{i.append(e,l.data[e])})),F.upload(i).then((e=>{t(e)})).catch((e=>{a(e)}))}))}function S(){_.warning("最多只能上传"+l.limit+"张图片")}const N=(e,l)=>{_.success("上传成功");const t=h.value.findIndex((e=>e.uid===l.uid));-1!==t&&(h.value[t].url=e.url,h.value[t].status="success",x.value[t]=e.url)},I=e=>{_.error("上传失败: "+e.message)},L=()=>{g.value=!1};return s((()=>{h.value=x.value.map((e=>({url:e})))})),(e,t)=>{const a=i("Plus"),s=j,_=i("zoom-in"),q=i("Delete"),H=C,K=w;return r(),o(u,null,[n(H,{"file-list":d(h),"onUpdate:fileList":t[0]||(t[0]=e=>c(h)?h.value=e:null),"list-type":"picture-card","before-upload":b,"http-request":V,"on-success":N,"on-error":I,"on-exceed":S,accept:l.accept,limit:l.limit,multiple:""},{file:f((({file:e})=>[v("div",k,[v("img",{class:"el-upload-list__item-thumbnail",src:e.url},null,8,U),v("span",M,[v("span",{onClick:l=>{return t=e.url,y.value=x.value.findIndex((e=>e===t)),void(g.value=!0);var t}},[n(s,null,{default:f((()=>[n(_)])),_:1})],8,z),v("span",{onClick:l=>{return t=e.url,void F.delete(t).then((()=>{const e=x.value.indexOf(t);-1!==e&&(x.value.splice(e,1),h.value.splice(e,1))}));var t}},[n(s,null,{default:f((()=>[n(q)])),_:1})],8,E)])])])),default:f((()=>[n(s,null,{default:f((()=>[n(a)])),_:1})])),_:1},8,["file-list","accept","limit"]),d(g)?(r(),m(K,{key:0,"zoom-rate":1.2,"initial-index":d(y),"url-list":x.value,onClose:L},null,8,["initial-index","url-list"])):p("",!0)],64)}}}),N={class:"app-container"},I=e({__name:"upload",setup(e){const l=t("https://s2.loli.net/2023/05/24/yNsxFC8rLHMZQcK.jpg"),a=t(["https://s2.loli.net/2023/05/24/yNsxFC8rLHMZQcK.jpg"]),s=t(["https://s2.loli.net/2023/05/24/yNsxFC8rLHMZQcK.jpg","https://s2.loli.net/2023/05/24/RuHFMwW4rG5lIqs.jpg"]);return(e,t)=>{const i=V,u=b,m=y,p=h,j=x;return r(),o("div",N,[n(i,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/upload.vue",type:"primary",target:"_blank",class:"mb-10"},{default:f((()=>t[3]||(t[3]=[g(" 示例源码 请点击>>>> ")]))),_:1,__:[3]}),n(j,null,{default:f((()=>[n(m,{label:"单图上传"},{default:f((()=>[n(u,{modelValue:d(l),"onUpdate:modelValue":t[0]||(t[0]=e=>c(l)?l.value=e:null)},null,8,["modelValue"])])),_:1}),n(m,{label:"多图上传"},{default:f((()=>[n(S,{modelValue:d(a),"onUpdate:modelValue":t[1]||(t[1]=e=>c(a)?a.value=e:null)},null,8,["modelValue"])])),_:1}),n(m,{label:"文件上传"},{default:f((()=>[n(p,{modelValue:d(s),"onUpdate:modelValue":t[2]||(t[2]=e=>c(s)?s.value=e:null)},null,8,["modelValue"])])),_:1})])),_:1})])}}});export{I as default};
