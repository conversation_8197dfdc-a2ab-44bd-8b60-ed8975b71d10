import{E as e,a as t}from"./el-select.CRWkm-it.js";import{E as l}from"./el-tree.ChWw39qP.js";import{b as a,I as s,c as o,bX as d,a0 as r,d as n,B as c,a9 as i,cK as u,M as p,br as h,A as v,H as f,_ as k,r as m,S as y,o as C,a7 as b,q as N}from"./index.Dk5pbsTU.js";import{U as g}from"./event.BwRzfsZt.js";import{p as K}from"./index.C9UdVphc.js";import{e as x}from"./strings.MqEQKtyI.js";import{i as O}from"./isEqual.C0S6DIiJ.js";import{s as E}from"./token.DWNpOE8r.js";const j=n({extends:t,setup(e,l){const a=t.setup(e,l);delete a.selectOptionClick;const o=c().proxy;return r((()=>{a.select.states.cachedOptions.get(o.value)||a.select.onOptionCreate(o)})),s((()=>l.attrs.visible),(e=>{r((()=>{a.states.visible=e}))}),{immediate:!0}),a},methods:{selectOptionClick(){this.$el.parentElement.click()}}});function V(e){return e||0===e}function M(e){return i(e)&&e.length}function S(e){return i(e)?e:V(e)?[e]:[]}function A(e,t,l,a,s){for(let o=0;o<e.length;o++){const d=e[o];if(t(d,o,e,s))return a?a(d,o,e,s):d;{const e=l(d);if(M(e)){const s=A(e,t,l,a,d);if(s)return s}}}}function H(e,t,l,a){for(let s=0;s<e.length;s++){const o=e[s];t(o,s,e,a);const d=l(o);M(d)&&H(d,t,l,o)}}var w=n({props:{data:{type:Array,default:()=>[]}},setup(e){const t=v(E);return s((()=>e.data),(()=>{var l;e.data.forEach((e=>{t.states.cachedOptions.has(e.value)||t.states.cachedOptions.set(e.value,e)}));const a=(null==(l=t.selectRef)?void 0:l.querySelectorAll("input"))||[];f&&!Array.from(a).includes(document.activeElement)&&t.setSelected()}),{flush:"post",immediate:!0}),()=>{}}});const L=N(k(n({name:"ElTreeSelect",inheritAttrs:!1,props:{...e.props,...l.props,cacheData:{type:Array,default:()=>[]}},setup(t,n){const{slots:c,expose:i}=n,v=m(),f=m(),k=o((()=>t.nodeKey||t.valueKey||"value")),N=((t,{attrs:l,emit:n},{select:c,tree:i,key:u})=>{const p=a("tree-select");return s((()=>t.data),(()=>{t.filterable&&r((()=>{var e,t;null==(t=i.value)||t.filter(null==(e=c.value)?void 0:e.states.inputValue)}))}),{flush:"post"}),{...K(d(t),Object.keys(e.props)),...l,class:o((()=>l.class)),style:o((()=>l.style)),"onUpdate:modelValue":e=>n(g,e),valueKey:u,popperClass:o((()=>{const e=[p.e("popper")];return t.popperClass&&e.push(t.popperClass),e.join(" ")})),filterMethod:(e="")=>{var l;t.filterMethod?t.filterMethod(e):t.remoteMethod?t.remoteMethod(e):null==(l=i.value)||l.filter(e)}}})(t,n,{select:v,tree:f,key:k}),{cacheOptions:E,...L}=((e,{attrs:t,slots:a,emit:n},{select:c,tree:i,key:v})=>{s((()=>e.modelValue),(()=>{e.showCheckbox&&r((()=>{const t=i.value;t&&!O(t.getCheckedKeys(),S(e.modelValue))&&t.setCheckedKeys(S(e.modelValue))}))}),{immediate:!0,deep:!0});const f=o((()=>({value:v.value,label:"label",children:"children",disabled:"disabled",isLeaf:"isLeaf",...e.props}))),k=(e,t)=>{var l;const a=f.value[e];return h(a)?a(t,null==(l=i.value)?void 0:l.getNode(k("value",t))):t[a]},m=S(e.modelValue).map((t=>A(e.data||[],(e=>k("value",e)===t),(e=>k("children",e)),((e,t,l,a)=>a&&k("value",a))))).filter((e=>V(e))),y=o((()=>{if(!e.renderAfterExpand&&!e.lazy)return[];const t=[];return H(e.data.concat(e.cacheData),(e=>{const l=k("value",e);t.push({value:l,currentLabel:k("label",e),isDisabled:k("disabled",e)})}),(e=>k("children",e))),t})),C=()=>{var e;return null==(e=i.value)?void 0:e.getCheckedKeys().filter((e=>{var t;const l=null==(t=i.value)?void 0:t.getNode(e);return!p(l)&&u(l.childNodes)}))};return{...K(d(e),Object.keys(l.props)),...t,nodeKey:v,expandOnClickNode:o((()=>!e.checkStrictly&&e.expandOnClickNode)),defaultExpandedKeys:o((()=>e.defaultExpandedKeys?e.defaultExpandedKeys.concat(m):m)),renderContent:(t,{node:l,data:s,store:o})=>t(j,{value:k("value",s),label:k("label",s),disabled:k("disabled",s),visible:l.visible},e.renderContent?()=>e.renderContent(t,{node:l,data:s,store:o}):a.default?()=>a.default({node:l,data:s,store:o}):void 0),filterNodeMethod:(t,l,a)=>e.filterNodeMethod?e.filterNodeMethod(t,l,a):!t||new RegExp(x(t),"i").test(k("label",l)||""),onNodeClick:(l,a,s)=>{var o,d,r,n;if(null==(o=t.onNodeClick)||o.call(t,l,a,s),!e.showCheckbox||!e.checkOnClickNode){if(e.showCheckbox||!e.checkStrictly&&!a.isLeaf)e.expandOnClickNode&&s.proxy.handleExpandIconClick();else if(!k("disabled",l)){const e=null==(d=c.value)?void 0:d.states.options.get(k("value",l));null==(r=c.value)||r.handleOptionSelect(e)}null==(n=c.value)||n.focus()}},onCheck:(l,a)=>{var s;if(!e.showCheckbox)return;const o=k("value",l),d={};H([i.value.store.root],(e=>d[e.key]=e),(e=>e.childNodes));const u=a.checkedKeys,p=e.multiple?S(e.modelValue).filter((e=>!(e in d)&&!u.includes(e))):[],h=p.concat(u);if(e.checkStrictly)n(g,e.multiple?h:h.includes(o)?o:void 0);else if(e.multiple){const e=C();n(g,p.concat(e))}else{const t=A([l],(e=>!M(k("children",e))&&!k("disabled",e)),(e=>k("children",e))),a=t?k("value",t):void 0,s=V(e.modelValue)&&!!A([l],(t=>k("value",t)===e.modelValue),(e=>k("children",e)));n(g,a===e.modelValue||s?void 0:a)}r((()=>{var a;const s=S(e.modelValue);i.value.setCheckedKeys(s),null==(a=t.onCheck)||a.call(t,l,{checkedKeys:i.value.getCheckedKeys(),checkedNodes:i.value.getCheckedNodes(),halfCheckedKeys:i.value.getHalfCheckedKeys(),halfCheckedNodes:i.value.getHalfCheckedNodes()})})),null==(s=c.value)||s.focus()},onNodeExpand:(l,a,s)=>{var o;null==(o=t.onNodeExpand)||o.call(t,l,a,s),r((()=>{if(!e.checkStrictly&&e.lazy&&e.multiple&&a.checked){const t={},l=i.value.getCheckedKeys();H([i.value.store.root],(e=>t[e.key]=e),(e=>e.childNodes));const a=S(e.modelValue).filter((e=>!(e in t)&&!l.includes(e))),s=C();n(g,a.concat(s))}}))},cacheOptions:y}})(t,n,{select:v,tree:f,key:k}),q=y({});return i(q),C((()=>{Object.assign(q,{...K(f.value,["filter","updateKeyChildren","getCheckedNodes","setCheckedNodes","getCheckedKeys","setCheckedKeys","setChecked","getHalfCheckedNodes","getHalfCheckedKeys","getCurrentKey","getCurrentNode","setCurrentKey","setCurrentNode","getNode","remove","append","insertBefore","insertAfter"]),...K(v.value,["focus","blur","selectedLabel"])})})),()=>b(e,y({...N,ref:e=>v.value=e}),{...c,default:()=>[b(w,{data:E.value}),b(l,y({...L,ref:e=>f.value=e}))]})}}),[["__file","tree-select.vue"]]));export{L as E};
