<template>
  <div class="time-trigger-config">
    <el-form label-width="120px">
      <el-form-item label="开播后触发时间">
        <div class="flex items-center">
          <el-input-number
            v-model="localConfig.delay_time"
            placeholder="输入延迟分钟数"
            :min="0"
            :max="1440"
            :step="1"
            step-strictly
            style="width: 180px"
          />
          <span class="ml-2 text-sm text-gray-500">
            {{ formatDelayTime(localConfig.delay_time) }}
          </span>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { ElMessage } from "element-plus";

const props = defineProps({
  config: {
    type: Object,
    default: () => ({ delay_time: 0 }),
  },
});

const emit = defineEmits(["update"]);

const localConfig = reactive({
  delay_time: props.config?.delay_time || 0,
});

// 监听配置变化并向上传递
watch(
  localConfig,
  (newVal) => {
    emit("update", { ...newVal });
  },
  { deep: true }
);

// 格式化延迟时间显示
const formatDelayTime = (minutes: number | undefined) => {
  if (minutes === undefined || minutes === null) {
    return "请设置延迟时间";
  }

  const mins = Number(minutes);
  if (mins === 0) {
    return "立即触发";
  } else if (mins < 60) {
    return `${mins}分钟后触发`;
  } else {
    const hours = Math.floor(mins / 60);
    const remainingMins = mins % 60;
    if (remainingMins === 0) {
      return `${hours}小时后触发`;
    } else {
      return `${hours}小时${remainingMins}分钟后触发`;
    }
  }
};
</script>
