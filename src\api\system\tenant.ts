import request from "@/utils/request";
import { downloadFile } from "@/utils";

// 分页查询
export class TenantPageQuery implements PageQuery {
  pageNum = 1;
  pageSize = 10;
  // 关键字
  keywords: string = "";
  // 状态
  status: string = "";
  // 部署方式
  deployWay: string = "";
}

// 系统用户分页查询
export class AppUserPageQuery implements PageQuery {
  pageNum = 1;
  pageSize = 10;
  // 关键字
  keywords: string = "";
  // 是否是助理
  isAssistant: string = "";
}

// 系统用户返回分页
export interface AppUserPageVo {
  // id
  id: number;
  // 用户账号
  username: string;
  // 用户昵称
  nickname: string;
  // 用户头像
  avatar: string;
  // 最近登录时间
  loginDate: string;
  // 是否是助理
  isAssistant: number;
  // 归公司
  tenantName: string;
  /*	ai马甲数*/
  aiVestNum: number;
  /*是否开启预警 0-否 1-是*/
  isEarlyWarning: number;
  /*主营范围*/
  businessScope: string;
}

// 分页返回对象
export interface TenantPageVO {
  id: number;
  // 公司名称
  name: string;
  // 公司logo
  logo: string;
  // 负责人id
  userId: number;
  // 负责人姓名
  nickName: string;
  // 负责人电话
  phone: string;
  // 状态 0禁用 1启用
  status: number;
  // 部署方式 0云端 1私有
  deployWay: number;
  // 主播数
  webCasterCount: number;
  // 助理数
  assistantCount: number;
  // 待开播数
  toStartCount: number;
  // 直播数
  liveCount: number;
  // 已结束直播数
  endedCount: number;
  smsSignature: number;
  // 最大房间数
  maxRoom: number;
  privateDomain: string;
  /*	ai马甲数*/
  aiVestNum: number;
  /*是否开启预警 0-否 1-是*/
  isEarlyWarning: number;
  /*主营范围*/
  businessScope: string;
}

// 分页返回对象
export interface TenantPageListVO extends TenantPageVO {
  // 操作加载中
  loading?: boolean;
}

// 保存
export class TenantSaveDto {
  id?: number;
  // 公司名称
  name: string = "";
  // 公司logo
  logo: string = "";
  // 负责人手机号
  phone: string = "";
  // 状态 0禁用 1启用
  status: number = 1;
  // 部署方式 0云端 1私有
  deployWay: string = "";
  // 负责人名称
  nickName: string = "";
  smsSignature: string = "0";
  maxRoom: number = 0;
  privateDomain: string = "";

  constructor(e?: TenantPageVO | TenantPageListVO) {
    if (e) {
      this.id = e.id;
      this.name = e.name;
      this.logo = e.logo;
      this.maxRoom = e.maxRoom;
      this.phone = e.phone;
      this.status = e.status;
      this.nickName = e.nickName;
      this.deployWay = e.deployWay.toString();
      this.smsSignature = String(e.smsSignature);
      this.privateDomain = e.privateDomain;
    }
  }
}

export class AiAuthorizeDto {
  id: number = 0;
  /*	ai马甲数*/
  aiVestNum: number = 0;
  /*是否开启预警 0-否 1-是*/
  isEarlyWarning: number = 0;
  /*主营范围*/
  businessScope: string = "";

  constructor(_e?: TenantPageVO) {
    if (_e) {
      this.id = _e.id;
      this.aiVestNum = _e.aiVestNum;
      this.isEarlyWarning = _e.isEarlyWarning;
      this.businessScope = _e.businessScope;
    }
  }
}

const TenantApi = {
  /*编辑ai授权*/
  aiAuthorize(_data: AiAuthorizeDto) {
    return request<any, null>({
      url: `/tenant/aiAuthorize`,
      method: "post",
      data: _data,
    });
  },
  /** app 用户列表*/
  appUserPage(queryParams?: AppUserPageQuery) {
    return request<any, PageResult<AppUserPageVo[]>>({
      url: `/tenant/appUserPage`,
      method: "get",
      params: queryParams,
    });
  },
  /** 分页 */
  page(queryParams?: TenantPageQuery) {
    return request<any, PageResult<TenantPageVO[]>>({
      url: `/tenant/list`,
      method: "get",
      params: queryParams,
    });
  },

  /** 导出 */
  export(queryParams: TenantPageQuery) {
    downloadFile<TenantPageQuery>("/tenant/export", queryParams);
  },
  /**
   * 删除
   *
   * @param id ID
   */
  remove(id: number) {
    return request<any, null>({
      url: `/tenant/remove/${id}`,
      method: "delete",
    });
  },

  /**
   * 保存
   * @param data 表单数据
   */
  save(data: TenantSaveDto) {
    return request({
      url: `/tenant/save`,
      method: "post",
      data: data,
    });
  },
};

export default TenantApi;
