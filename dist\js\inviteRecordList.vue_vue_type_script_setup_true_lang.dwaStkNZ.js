import{d as e,g as t,f as a,m as o,w as i,V as n,e as r,h as s,i as l,$ as p,F as d}from"./index.Dk5pbsTU.js";import{v as m}from"./el-loading.Dqi-qL7c.js";import{E as u}from"./el-card.DwLhVNHW.js";import g from"./index.Cywy93e7.js";import{a as c,E as f}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{E as v}from"./el-link.qHYW6llJ.js";/* empty css               */import{I as j,a as _}from"./liveSession.RdnykuzD.js";import{u as w}from"./commonSetup.Dm-aByKQ.js";import{_ as h}from"./inviteCountLog.vue_vue_type_script_setup_true_lang.CLIrJSbx.js";const b={class:"app-container"},y=e({__name:"inviteRecordList",props:{sessionId:{type:Number,default:-1}},setup(e){const y=e,{page:C,getPage:R}=w(new j(y.sessionId),_.inviteRecordList);return(j,_)=>{const w=f,y=v,I=c,k=g,L=u,x=m;return a(),t("div",b,[o(L,{shadow:"never"},{default:i((()=>[n((a(),r(I,{ref:"dataTableRef",data:l(C).data.records,"highlight-current-row":"",border:""},{default:i((()=>[o(w,{label:"序号",align:"center",width:"55",type:"index"}),o(w,{label:"转发人",align:"center",prop:"nickName","min-width":"120"}),o(w,{label:"转发数",align:"center",prop:"sharedCount","min-width":"120"}),o(w,{label:"引流数",align:"center",prop:"inviteCount","min-width":"120"},{default:i((({row:e})=>[o(y,{type:"primary",onClick:t=>{var a,o;return null==(o=null==(a=j.$refs)?void 0:a.inviteCountLogRef)?void 0:o.open(e)}},{default:i((()=>[p(d(e.inviteCount),1)])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[x,l(C).loading]]),l(C).data.totalRow?(a(),r(k,{key:0,total:l(C).data.totalRow,"onUpdate:total":_[0]||(_[0]=e=>l(C).data.totalRow=e),page:l(C).query.pageNum,"onUpdate:page":_[1]||(_[1]=e=>l(C).query.pageNum=e),limit:l(C).query.pageSize,"onUpdate:limit":_[2]||(_[2]=e=>l(C).query.pageSize=e),onPagination:l(R)},null,8,["total","page","limit","onPagination"])):s("",!0)])),_:1}),o(h,{ref:"inviteCountLogRef",sessionId:e.sessionId},null,8,["sessionId"])])}}});export{y as _};
