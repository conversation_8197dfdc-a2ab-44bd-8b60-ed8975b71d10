<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="name" label="标签名称">
          <el-input
            v-model="page.query.name"
            placeholder="请输入标签名称"
            clearable
            @keyup.enter="getPage"
          />
        </el-form-item>
        <el-form-item prop="status" label="状态">
          <dict v-model="page.query.status" code="disable_status" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          v-hasPerm="['basic:tags:save']"
          type="success"
          icon="plus"
          @click="editModelRef?.open()"
        >
          新增
        </el-button>
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        :row-class-name="onRowClassName"
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="标签名称" align="center" prop="name" min-width="120">
          <template #default="{ row }">
            <img
              v-if="Number(row.count) >= 1000"
              src="/src/assets/icons/hot.png"
              style="width: 10px; height: auto"
            />
            {{ row.name }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createdAt" min-width="120" />
        <el-table-column label="来源" align="center" prop="from" min-width="120">
          <template #default="{ row }">
            <dict-label v-model="row.from" :showTag="false" code="tenant_tag" />
          </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" prop="createdName" min-width="120" />
        <el-table-column label="使用数" align="center" prop="createdName" width="100">
          <template #default="{ row }">
            <el-link type="primary" @click="userListRef?.open(row.id)">
              {{ row.count }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="最后使用时间" align="center" prop="lastUseTime" min-width="120" />
        <el-table-column label="状态" align="center" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status ? 'success' : 'info'">
              {{ row.status ? "启用" : "停用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="240">
          <template #default="scope">
            <el-button
              v-hasPerm="['basic:tags:save']"
              type="primary"
              size="small"
              link
              icon="edit"
              :loading="scope.row.loading"
              @click="editModelRef?.open(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-hasPerm="['basic:tags:delete']"
              type="danger"
              size="small"
              link
              icon="delete"
              :loading="scope.row.loading"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
            <el-button
              v-hasPerm="['basic:tags:save']"
              type="warning"
              size="small"
              link
              :loading="scope.row.loading"
              @click="editStatus(scope.row)"
            >
              {{ scope.row.status ? "停用" : "启用" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
    <edit-model ref="editModelRef" @success="resetQuery" />
    <user-list ref="userListRef" />
  </div>
</template>

<script setup lang="ts">
import EditModel from "./edit.vue";
import TagApi, { TagPageQuery, TagPageVO, UpdateTagDto } from "@/api/live/tags";
import { usePage } from "@/utils/commonSetup";
import UserList from "./user-list.vue";

defineOptions({ name: "Tags" });
const userListRef = ref<InstanceType<typeof UserList>>();
const editModelRef = ref<InstanceType<typeof EditModel>>();
const { page, getPage, resetQuery } = usePage<TagPageQuery, TagPageVO>(
  new TagPageQuery(),
  TagApi.page as any
);

function onRowClassName({ row }: { row: TagPageVO }) {
  if (row.status === 0) {
    return "disable-row-item";
  }
  return "";
}

function handleDelete(_row: TagPageVO) {
  ElMessageBox.confirm(`确定删除标签《${_row.name}》吗？`, "删除", {
    confirmButtonText: "确定删除",
    cancelButtonText: "取消",
    confirmButtonClass: "el-button--danger",
    type: "error",
  })
    .then(() => {
      _row.loading = true;
      TagApi.remove(_row.id)
        .then(() => {
          ElMessage.success("删除成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}

function editStatus(_row: TagPageVO) {
  ElMessageBox.confirm(
    `确定要${_row.status ? "停用" : "启用"}标签《${_row.name}》吗？`,
    `${_row.status ? "停用" : "启用"}标签`,
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      confirmButtonClass: "el-button--danger",
      type: "error",
    }
  )
    .then(() => {
      const formData: UpdateTagDto = {
        id: _row.id,
        name: _row.name,
        status: _row.status ? 0 : 1,
      };
      _row.loading = true;
      TagApi.update(formData)
        .then(() => {
          ElMessage.success("操作成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}
</script>
<style lang="scss">
.disable-row-item {
  --el-table-tr-bg-color: var(--el-color-info-light-7) !important;
}
</style>
