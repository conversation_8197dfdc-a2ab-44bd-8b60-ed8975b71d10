var t=Object.defineProperty,s=(s,i,a)=>((s,i,a)=>i in s?t(s,i,{enumerable:!0,configurable:!0,writable:!0,value:a}):s[i]=a)(s,"symbol"!=typeof i?i+"":i,a);import{aT as i}from"./index.Dk5pbsTU.js";class a{constructor(t){s(this,"pageNum",1),s(this,"pageSize",10),s(this,"keywords",""),s(this,"status",""),s(this,"identity",""),this.identity=t}}class e{constructor(t){s(this,"id"),s(this,"mobile",""),s(this,"userName",""),s(this,"nickName",""),s(this,"avatar",""),s(this,"isAutonomous",0),s(this,"introduction",""),s(this,"account",""),s(this,"password",""),t&&(this.account=t.account,this.password=t.password,this.id=t.id,this.mobile=t.mobile,this.userName=t.userName,this.nickName=t.nickName,this.avatar=t.avatar,this.isAutonomous=t.isAutonomous,this.introduction=t.introduction)}}class o{constructor(t){s(this,"id",0),s(this,"status",0),t&&(this.id=t.id,this.status=t.status)}}const r={page:t=>i({url:"/webcaster/page",method:"get",params:t}),remove:t=>i({url:`/webcaster/remove/${t}`,method:"delete"}),save:t=>i({url:"/webcaster/save",method:"post",data:t}),editStatus:t=>i({url:"/webcaster/editStatus",method:"post",data:t})};export{o as E,e as S,a as W,r as a};
