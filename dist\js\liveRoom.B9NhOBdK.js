var t=Object.defineProperty,s=(s,e,o)=>((s,e,o)=>e in s?t(s,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):s[e]=o)(s,"symbol"!=typeof e?e+"":e,o);import{aT as e}from"./index.Dk5pbsTU.js";class o{constructor(){s(this,"pageNum",1),s(this,"pageSize",10),s(this,"keywords",""),s(this,"status","")}}class r{constructor(t){s(this,"id"),s(this,"roomName",""),s(this,"streamName",""),s(this,"coverUrl",""),s(this,"description",""),s(this,"announcement",""),s(this,"notice",""),s(this,"_anchorShowUserCount",!1),s(this,"_assistantShowUserCount",!1),s(this,"_saleShowUserCount",!1),s(this,"_userShowUserCount",!1),s(this,"fontSize",16),s(this,"stopLiveMinutes",30),s(this,"userReply",0),t&&(this.id=t.id,this.roomName=t.roomName,this.streamName=t.streamName,this.coverUrl=t.coverUrl,this.description=t.description,this.announcement=t.announcement||"",this.notice=t.notice||"",this._anchorShowUserCount=1===t.anchorShowUserCount,this._assistantShowUserCount=1===t.assistantShowUserCount,this._saleShowUserCount=1===t.saleShowUserCount,this._userShowUserCount=1===t.userShowUserCount,this.fontSize=t.fontSize||16,this.stopLiveMinutes=t.stopLiveMinutes||30,this.userReply=t.userReply||0)}get anchorShowUserCount(){return this._anchorShowUserCount}set anchorShowUserCount(t){this._anchorShowUserCount=t}get assistantShowUserCount(){return this._assistantShowUserCount}set assistantShowUserCount(t){this._assistantShowUserCount=t}get saleShowUserCount(){return this._saleShowUserCount}set saleShowUserCount(t){this._saleShowUserCount=t}get userShowUserCount(){return this._userShowUserCount}set userShowUserCount(t){this._userShowUserCount=t}toAPI(){return{id:this.id,roomName:this.roomName,streamName:this.streamName,coverUrl:this.coverUrl,description:this.description,announcement:this.announcement,notice:this.notice,anchorShowUserCount:this._anchorShowUserCount?1:0,assistantShowUserCount:this._assistantShowUserCount?1:0,saleShowUserCount:this._saleShowUserCount?1:0,userShowUserCount:this._userShowUserCount?1:0,fontSize:this.fontSize,stopLiveMinutes:this.stopLiveMinutes,userReply:this.userReply}}}class n{constructor(){s(this,"roomId",""),s(this,"selectRoomName",""),s(this,"roomName",""),s(this,"coverUrl",""),s(this,"saleShowUserCount",0),s(this,"anchorShowUserCount",0),s(this,"assistantShowUserCount",0),s(this,"userShowUserCount",0),s(this,"cloneQuickReply",1),s(this,"cloneQuickVest",1)}}const i={cloneRoom:t=>e({url:"/tenant/live/room/cloneRoom",method:"post",data:t}),page:t=>e({url:"/tenant/live/room/list",method:"get",params:t}),save:t=>e({url:"/tenant/live/room/saveOrEdit",method:"post",data:t}),remove:t=>e({url:`/tenant/live/room/remove/${t}`,method:"delete"}),saveQuickReply:t=>e({url:"/tenant/live/room/quickReply",method:"post",data:t})};export{n as C,r as L,i as a,o as b};
