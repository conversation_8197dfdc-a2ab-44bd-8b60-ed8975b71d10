<script setup lang="ts">
import GradeApi, { GradeUserVo, PointRecordVO, PointsPageQueryDto } from "@/api/system/grade";
import { usePage } from "@/utils/commonSetup";

const dialog = reactive({
  show: false,
  title: "",
});
const info = ref<GradeUserVo>({} as any);
const typeList = ref<Array<SelectVo>>([]);
const { page, getPage } = usePage<PointsPageQueryDto, PointRecordVO>(
  new PointsPageQueryDto(),
  GradeApi.pointsPage,
  false
);
defineExpose({
  open: (_row: GradeUserVo) => {
    page.query.pageNum = 1;
    page.query.id = _row.id;
    getPage();
    GradeApi.pointTypeList().then((res) => {
      typeList.value = res;
    });
    dialog.title = "积分明细";
    info.value = JSON.parse(JSON.stringify(_row));
    dialog.show = true;
  },
});
</script>

<template>
  <el-drawer size="80vw" v-model="dialog.show" :title="dialog.title">
    <el-container style="padding: 0; height: 100%">
      <el-header style="padding: 0; height: auto">
        <el-descriptions border label-width="80px">
          <el-descriptions-item label="昵称">
            {{ info.nickname }}
          </el-descriptions-item>
          <el-descriptions-item label="积分">
            {{ info.currentPoints }}
          </el-descriptions-item>
        </el-descriptions>
        <el-form style="margin-top: 10px" ref="queryFormRef" :model="page.query" :inline="true">
          <el-form-item prop="status" label="积分类型">
            <el-select v-model="page.query.type" clearable style="width: 250px">
              <el-option
                v-for="item in typeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="status" label="获取时间">
            <date-range-picker
              v-model:start="page.query.startTime"
              v-model:end="page.query.endTime"
              is-split
              type="daterange"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-header>
      <el-main style="padding: 0; height: 100%">
        <el-table
          ref="dataTableRef"
          v-loading="page.loading"
          :data="page.data.records"
          height="100%"
          highlight-current-row
          border
        >
          <el-table-column label="序号" align="center" width="55" type="index" />
          <el-table-column label="类型" align="center" prop="currentLevel" min-width="100" >
            <template #default="{ row }">
              {{ typeList.find((item) => item.value === row.type)?.label }}
            </template>
          </el-table-column>
          <el-table-column label="积分值" align="center" prop="changePoints" min-width="100" />
          <el-table-column label="入账时间" align="center" prop="completeTime" min-width="100" />
        </el-table>
      </el-main>
      <el-footer>
        <pagination
          v-if="page.data.totalRow"
          v-model:total="page.data.totalRow"
          v-model:page="page.query.pageNum"
          v-model:limit="page.query.pageSize"
          @pagination="getPage"
        />
      </el-footer>
    </el-container>
  </el-drawer>
</template>

<style scoped lang="scss"></style>
