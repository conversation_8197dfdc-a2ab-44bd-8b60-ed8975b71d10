import{u as e,a as t,E as r}from"./el-popper.Dbn4MgsT.js";import{d as o}from"./dropdown.B_OfpyL_.js";import{bb as a,t as p,_ as s,d as i,c as n,b as l,r as d,i as f,J as b,e as c,f as u,w as v,l as g,h as m,g as h,n as y,F as w,$ as x,U as S,q as k,cV as A}from"./index.Dk5pbsTU.js";const $=p({trigger:t.trigger,triggerKeys:t.triggerKeys,placement:o.placement,disabled:t.disabled,visible:e.visible,transition:e.transition,popperOptions:o.popperOptions,tabindex:o.tabindex,content:e.content,popperStyle:e.popperStyle,popperClass:e.popperClass,enterable:{...e.enterable,default:!0},effect:{...e.effect,default:"light"},teleported:e.teleported,appendTo:e.appendTo,title:String,width:{type:[String,Number],default:150},offset:{type:Number,default:void 0},showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0},showArrow:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},"onUpdate:visible":{type:Function}}),C={"update:visible":e=>a(e),"before-enter":()=>!0,"before-leave":()=>!0,"after-enter":()=>!0,"after-leave":()=>!0},N=i({name:"ElPopover"}),R=i({...N,props:$,emits:C,setup(e,{expose:t,emit:o}){const a=e,p=n((()=>a["onUpdate:visible"])),s=l("popover"),i=d(),k=n((()=>{var e;return null==(e=f(i))?void 0:e.popperRef})),A=n((()=>[{width:b(a.width)},a.popperStyle])),$=n((()=>[s.b(),a.popperClass,{[s.m("plain")]:!!a.content}])),C=n((()=>a.transition===`${s.namespace.value}-fade-in-linear`)),N=()=>{o("before-enter")},R=()=>{o("before-leave")},U=()=>{o("after-enter")},_=()=>{o("update:visible",!1),o("after-leave")};return t({popperRef:k,hide:()=>{var e;null==(e=i.value)||e.hide()}}),(e,t)=>(u(),c(f(r),S({ref_key:"tooltipRef",ref:i},e.$attrs,{trigger:e.trigger,"trigger-keys":e.triggerKeys,placement:e.placement,disabled:e.disabled,visible:e.visible,transition:e.transition,"popper-options":e.popperOptions,tabindex:e.tabindex,content:e.content,offset:e.offset,"show-after":e.showAfter,"hide-after":e.hideAfter,"auto-close":e.autoClose,"show-arrow":e.showArrow,"aria-label":e.title,effect:e.effect,enterable:e.enterable,"popper-class":f($),"popper-style":f(A),teleported:e.teleported,"append-to":e.appendTo,persistent:e.persistent,"gpu-acceleration":f(C),"onUpdate:visible":f(p),onBeforeShow:N,onBeforeHide:R,onShow:U,onHide:_}),{content:v((()=>[e.title?(u(),h("div",{key:0,class:y(f(s).e("title")),role:"title"},w(e.title),3)):m("v-if",!0),g(e.$slots,"default",{},(()=>[x(w(e.content),1)]))])),default:v((()=>[e.$slots.reference?g(e.$slots,"reference",{key:0}):m("v-if",!0)])),_:3},16,["trigger","trigger-keys","placement","disabled","visible","transition","popper-options","tabindex","content","offset","show-after","hide-after","auto-close","show-arrow","aria-label","effect","enterable","popper-class","popper-style","teleported","append-to","persistent","gpu-acceleration","onUpdate:visible"]))}});const U=(e,t)=>{const r=t.arg||t.value,o=null==r?void 0:r.popperRef;o&&(o.triggerRef=e)};const _=k(s(R,[["__file","popover.vue"]]),{directive:A({mounted(e,t){U(e,t)},updated(e,t){U(e,t)}},"popover")});export{_ as E};
