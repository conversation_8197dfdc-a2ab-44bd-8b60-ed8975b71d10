import request from "@/utils/request";
import { SaveWebmasterDto, WebmasterPageVO } from "@/api/webcasterAndAssistant/webcaster";

export interface UserVo {
  // 用户头像
  avatar: string;
  // 用户昵称
  nickname: string;
  // 用户名
  username: string;
  // 用户ID
  appUserId: number;
}

export class SaveAssistantDto {
  id?: number; // 主播ID，新增时为空
  appUserId: number = 0; // 用户id
  userName: string = ""; // 姓名
  nickName: string = ""; // 昵称
  avatar: string = ""; // 用户头像
  // 微信名称
  wechatName: string = "";

  constructor(e?: SaveAssistantDto | UserVo) {
    if (e) {
      if (e instanceof SaveAssistantDto) {
        this.id = e.id;
        this.appUserId = e.appUserId;
        this.userName = e.userName;
        this.nickName = e.nickName;
        this.avatar = e.avatar;
      } else {
        this.avatar = e.avatar;
        this.wechatName = e.nickname;
        this.appUserId = e.appUserId;
        console.log(this);
      }
    }
  }
}

// 修改状态
export class UpdateAssistantStatusDto {
  id: number = 0; // 主播ID
  status: number = 0; // 状态 0-禁用 1-启用
  constructor(data?: WebmasterPageVO) {
    if (data) {
      this.id = data.id;
      this.status = data.status;
    }
  }
}

const AssistantApi = {
  /**
   * 删除
   *
   * @param id ID
   */
  remove(id: number) {
    return request<any, null>({
      url: `/webcaster/removeAssistant?id=${id}&type=1`,
      method: "delete",
    });
  },
  /*助手变销售*/
  changeUserType(id: number) {
    return request<any, null>({
      url: `/webcaster/changeUserType/${id}`,
      method: "post",
    });
  },
  /*根据用户uid查询用户*/
  getUserByPhone(uids: string) {
    return request<any, UserVo[]>({
      url: `/webcaster/getUserByPhone?uids=${uids}&type=1`,
      method: "get",
    });
  },
  /**
   * 保存
   * @param data 表单数据
   */
  save(data: SaveAssistantDto[]) {
    return request({
      url: `/webcaster/saveAssistant`,
      method: "post",
      data: { list: data, type: 1 },
    });
  },
  /**
   * 修改状态
   * @param data 表单数据
   */
  editStatus(data: UpdateAssistantStatusDto) {
    return request({
      url: `/webcaster/editAssistantStatus`,
      method: "post",
      data: {
        ...data,
        type: 1,
      },
    });
  },
};

export default AssistantApi;
