import{d as e,S as l,r as a,e as t,f as o,i,w as s,m as n,h as r,C as d,$ as u,az as m}from"./index.Dk5pbsTU.js";import{E as p}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as c,a as v}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{E as f}from"./el-input.DiGatoux.js";/* empty css               */import{E as h}from"./el-button.CXI119n4.js";import{G as g,a as V}from"./grade.D-4Kz6mr.js";const b={class:"flex-center"},_={class:"dialog-footer"},j=e({__name:"edit",emits:["success"],setup(e,{expose:j,emit:w}){const x=l({visible:!1,title:""}),y=a([]),k=w,C=a(!1);function U(){const e=new g;y.value.length>0&&(e.startPoints=y.value[y.value.length-1].endPoints),y.value.push(e)}function P(){V.editConfig(y.value).then((()=>{m.success("保存成功"),x.visible=!1,k("success")})).finally((()=>C.value=!1))}return j({open:e=>{x.visible=!0,x.title="编辑会员等级",y.value=(e||[]).map((e=>new g(e)))}}),(e,l)=>{const a=h,m=c,g=f,V=v,j=p;return o(),t(j,{modelValue:i(x).visible,"onUpdate:modelValue":l[1]||(l[1]=e=>i(x).visible=e),title:i(x).title,width:"800px"},{footer:s((()=>[d("div",_,[n(a,{type:"primary",onClick:P},{default:s((()=>l[3]||(l[3]=[u("确 定")]))),_:1,__:[3]}),n(a,{onClick:l[0]||(l[0]=e=>i(x).visible=!1)},{default:s((()=>l[4]||(l[4]=[u("取 消")]))),_:1,__:[4]})])])),default:s((()=>[n(V,{data:i(y)||[],"highlight-current-row":"","max-height":"500px",border:""},{default:s((()=>[n(m,{label:"序号",align:"center",width:"55",type:"index"},{header:s((()=>[n(a,{type:"primary",size:"small",circle:"",icon:"plus",onClick:U})])),default:s((({$index:e})=>[i(y).length>1?(o(),t(a,{key:0,type:"danger",size:"small",circle:"",icon:"delete",onClick:l=>i(y).splice(e,1)},null,8,["onClick"])):r("",!0)])),_:1}),n(m,{label:"等级名称",align:"center",prop:"levelName","min-width":"100"},{default:s((({row:e})=>[n(g,{modelValue:e.levelName,"onUpdate:modelValue":l=>e.levelName=l},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),n(m,{label:"积分区间值",align:"center","min-width":"100"},{default:s((({row:e})=>[d("div",b,[n(g,{modelValue:e.startPoints,"onUpdate:modelValue":l=>e.startPoints=l,oninput:"value=value.replace(/[^\\d]/g,'')",placeholder:"含"},null,8,["modelValue","onUpdate:modelValue"]),l[2]||(l[2]=d("span",{class:"m-l-1 m-r-1"},"至",-1)),n(g,{modelValue:e.endPoints,"onUpdate:modelValue":l=>e.endPoints=l,oninput:"value=value.replace(/[^\\d]/g,'')",placeholder:"不含"},null,8,["modelValue","onUpdate:modelValue"])])])),_:1})])),_:1},8,["data"])])),_:1},8,["modelValue","title"])}}});export{j as _};
