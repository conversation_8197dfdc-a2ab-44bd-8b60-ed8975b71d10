import{d as e,r,aQ as t,g as o,f as i,C as a,m as s,w as n,i as l,$ as p,V as m,e as d,h as u,F as c,az as j}from"./index.Dk5pbsTU.js";import{v as f}from"./el-loading.Dqi-qL7c.js";import{E as g}from"./el-card.DwLhVNHW.js";import _ from"./index.Cywy93e7.js";import{a as h,E as w}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     *//* empty css               */import{E as y}from"./el-image-viewer.BH897zgF.js";import{a as v,E as b}from"./el-form-item.Bw6Zyv_7.js";import{E as x}from"./el-button.CXI119n4.js";import{E as k}from"./el-date-picker.B6TshyBV.js";import"./el-input.DiGatoux.js";import{_ as C,B as S,U,a as q}from"./edit.vue_vue_type_script_setup_true_lang.CoN_OMJE.js";import{u as T}from"./commonSetup.Dm-aByKQ.js";/* empty css                       */import"./el-overlay.DpVCS8zG.js";import{E}from"./index.L2DVy5yq.js";import{E as R}from"./index.BcMfjWDS.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./use-form-common-props.CQPDkY7k.js";import"./use-form-item.DzRJVC1I.js";import"./event.BwRzfsZt.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.DuiNpp1i.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./position.DfR5znly.js";import"./dayjs.min.D7B9fRnU.js";import"./arrays.CygeFE-H.js";import"./index.Cd8M2JyP.js";import"./el-dialog.Cnp8BitR.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./el-radio.w2rep3_A.js";import"./SingleImageUpload.WGBxPB_4.js";import"./el-progress.BQBUwu9o.js";import"./cloneDeep.DcCMo0F4.js";import"./index.WzKGworL.js";import"./validator.HGn2BZtD.js";const B={class:"app-container"},M={class:"search-bar"},z={class:"mb-10px"},P=e({name:"HomeBanner",__name:"index",setup(e){const P=r(null),{page:V,getPage:$,resetQuery:Y}=T(new q,S.page),I=()=>{P.value=null,Y()};function A(e){e?(V.query.startTime=e[0],V.query.endTime=e[1]):(V.query.startTime=void 0,V.query.endTime=void 0)}return(e,r)=>{const q=k,T=v,Y=x,D=b,F=w,H=y,N=E,O=h,Q=_,J=g,K=t("hasPerm"),L=f;return i(),o("div",B,[a("div",M,[s(D,{ref:"queryFormRef",model:l(V).query,inline:!0},{default:n((()=>[s(T,{prop:"keywords",label:"时间范围"},{default:n((()=>[s(q,{modelValue:P.value,"onUpdate:modelValue":r[0]||(r[0]=e=>P.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",onChange:A},null,8,["modelValue"])])),_:1}),s(T,null,{default:n((()=>[s(Y,{type:"primary",icon:"search",onClick:l($)},{default:n((()=>r[5]||(r[5]=[p("搜索")]))),_:1,__:[5]},8,["onClick"]),s(Y,{icon:"refresh",onClick:I},{default:n((()=>r[6]||(r[6]=[p("重置")]))),_:1,__:[6]})])),_:1})])),_:1},8,["model"])]),s(J,{shadow:"never"},{default:n((()=>[a("div",z,[m((i(),d(Y,{type:"success",icon:"plus",onClick:r[1]||(r[1]=r=>{var t;return null==(t=e.$refs.editModelRef)?void 0:t.open()})},{default:n((()=>r[7]||(r[7]=[p(" 新增 ")]))),_:1,__:[7]})),[[K,["basic:banner:save"]]])]),m((i(),d(O,{ref:"dataTableRef",data:l(V).data.records,"highlight-current-row":"",border:""},{default:n((()=>[s(F,{label:"序号",align:"center",width:"55",type:"index"}),s(F,{label:"轮播图",align:"center",prop:"bannerUrl","min-width":"120"},{default:n((({row:e})=>[s(H,{style:{width:"100px",height:"60px"},"preview-src-list":[e.bannerUrl],"preview-teleported":"",src:e.bannerUrl},null,8,["preview-src-list","src"])])),_:1}),s(F,{label:"跳转链接",align:"center",prop:"targetUrl","min-width":"200"}),s(F,{label:"展示状态",align:"center",width:"100"},{default:n((({row:e})=>[s(N,{type:e.isShow?"success":"info"},{default:n((()=>[p(c(e.isShow?"展示中":"已隐藏"),1)])),_:2},1032,["type"])])),_:1}),s(F,{label:"创建时间",align:"center",prop:"createdAt","min-width":"160"}),s(F,{fixed:"right",label:"操作",width:"240"},{default:n((t=>[m((i(),d(Y,{type:"primary",size:"small",link:"",icon:"edit",loading:t.row.loading,onClick:r=>{var o;return null==(o=e.$refs.editModelRef)?void 0:o.open(t.row)}},{default:n((()=>r[8]||(r[8]=[p(" 编辑 ")]))),_:2,__:[8]},1032,["loading","onClick"])),[[K,["basic:banner:save"]]]),m((i(),d(Y,{type:"danger",size:"small",link:"",icon:"delete",loading:t.row.loading,onClick:e=>{return r=t.row,void R.confirm("确定删除该轮播图吗？","删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{r.loading=!0,S.remove(r.id).then((()=>{j.success("删除成功"),I()})).finally((()=>r.loading=!1))})).catch((()=>j.info("已取消")));var r}},{default:n((()=>r[9]||(r[9]=[p(" 删除 ")]))),_:2,__:[9]},1032,["loading","onClick"])),[[K,["basic:banner:delete"]]]),m((i(),d(Y,{type:"warning",size:"small",link:"",loading:t.row.loading,onClick:e=>{return r=t.row,void R.confirm(`确定要${r.isShow?"隐藏":"展示"}该轮播图吗？`,(r.isShow?"隐藏":"展示")+"轮播图",{confirmButtonText:"确定",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{const e=new U(r);e.isShow=r.isShow?0:1,r.loading=!0,S.editStatus(e).then((()=>{j.success("操作成功"),I()})).finally((()=>r.loading=!1))})).catch((()=>j.info("已取消")));var r}},{default:n((()=>[p(c(t.row.isShow?"隐藏":"展示"),1)])),_:2},1032,["loading","onClick"])),[[K,["basic:banner:save"]]])])),_:1})])),_:1},8,["data"])),[[L,l(V).loading]]),l(V).data.totalRow?(i(),d(Q,{key:0,total:l(V).data.totalRow,"onUpdate:total":r[2]||(r[2]=e=>l(V).data.totalRow=e),page:l(V).query.pageNum,"onUpdate:page":r[3]||(r[3]=e=>l(V).query.pageNum=e),limit:l(V).query.pageSize,"onUpdate:limit":r[4]||(r[4]=e=>l(V).query.pageSize=e),onPagination:l($)},null,8,["total","page","limit","onPagination"])):u("",!0)])),_:1}),s(C,{ref:"editModelRef",onSuccess:I},null,512)])}}});export{P as default};
