import{d as e,aI as a,r as t,o as s,I as l,e as o,g as n,i as d,w as r,$ as u,F as i,f}from"./index.Dk5pbsTU.js";/* empty css               */import{E as p}from"./index.L2DVy5yq.js";const c={key:1},y=e({__name:"DictLabel",props:{code:String,modelValue:[String,Number],effect:{type:String,default:"light"},round:{type:Boolean,default:!1},addValueList:{type:Array,default:()=>[]},showTag:{type:Boolean,default:!0},size:{type:String,default:"default"}},setup(e){const y=a(),g=e,m=t(""),b=t(),v=t(g.size),V=async()=>{const e=await(async(e,a)=>{const t=y.getDictionary(e),s=(g.addValueList||[]).find((e=>e.value==a))||t.find((e=>e.value==a));return{label:s?s.label:"",tag:s?s.tagType:void 0}})(g.code,g.modelValue);m.value=e.label,b.value=e.tag};return s(V),l((()=>g.modelValue),V),(a,t)=>{const s=p;return d(b)&&e.showTag?(f(),o(s,{key:0,effect:e.effect,round:e.round,type:d(b),size:d(v)},{default:r((()=>[u(i(d(m)),1)])),_:1},8,["effect","round","type","size"])):(f(),n("span",c,i(d(m)),1))}}});export{y as _};
