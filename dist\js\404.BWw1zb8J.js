import{d as a,g as s,C as t,aN as i,j as l,ag as c,f as e}from"./index.Dk5pbsTU.js";import{_ as d}from"./_plugin-vue_export-helper.BCo6x5W8.js";const o={class:"page-container"},_={class:"bullshit"},v=d(a({name:"Page404",__name:"404",setup(a){const d=c();function v(){d.back()}return(a,c)=>(e(),s("div",o,[c[1]||(c[1]=t("div",{class:"pic-404"},[t("img",{class:"pic-404__parent",src:"/dist/img/404.BAG_oJkJ.svg",alt:"404"})],-1)),t("div",_,[c[0]||(c[0]=i('<div class="bullshit__oops" data-v-3c3007a4>OOPS！</div><div class="bullshit__info" data-v-3c3007a4> 该页面无法访问。 <a style="color:#20a0ff;" href="https://www.youlai.tech.com" target="_blank" data-v-3c3007a4> 有来开源官网 </a></div><div class="bullshit__headline" data-v-3c3007a4>抱歉，您访问的页面不存在。</div><div class="bullshit__info" data-v-3c3007a4>请确认您输入的网址是否正确，或者点击下方按钮返回首页。</div>',4)),t("a",{href:"#",class:"bullshit__return-home",onClick:l(v,["prevent"])},"返回首页")])]))}}),[["__scopeId","data-v-3c3007a4"]]);export{v as default};
