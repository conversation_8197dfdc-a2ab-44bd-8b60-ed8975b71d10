<script setup lang="ts">
import livePreview, { PageSessionUserDto } from "@/api/live/livePreview";
import IScrollList from "@/components/IScrollList/index.vue";
import useClipboard from "vue-clipboard3";
import dayjs from "dayjs";

const { toClipboard } = useClipboard();
const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
});
const userListRef = ref<any>();
const userListQuery = ref<PageSessionUserDto>(new PageSessionUserDto());
watch(
  () => props.info.id,
  (_newVal: number) => {
    if (_newVal) {
      userListQuery.value.sessionId = String(props.info.id);
      userListQuery.value.type = "0";
      userListQuery.value.pageNum = 1;
      nextTick(() => {
        userListRef.value?.reset();
      });
    }
  },
  { deep: true, immediate: true }
);

function onCopyId(_id: number) {
  toClipboard(String(_id))
    .then(() => {
      ElMessage.success("复制成功");
    })
    .catch(() => {
      ElMessage.error("复制失败");
    });
}

function convertSecondsToHMS(seconds: number) {
  const dur = dayjs.duration(seconds, "seconds");
  const hours = Math.floor(dur.asHours());
  const minutes = dur.minutes();
  const secs = dur.seconds();

  if (hours > 0) {
    return `${hours}h${minutes}min`;
  } else if (minutes > 0) {
    return `${minutes}min`;
  } else {
    return `${secs}s`;
  }
}
</script>

<template>
  <div style="width: 100%; height: 100%; background: #f8f9fa">
    <i-scroll-list
      ref="userListRef"
      v-model="userListQuery"
      class="user-scroll-list"
      style="
        --i-scroll-list-padding: 10px 10px 0 10px;
        --i-scroll-list-divider-bg: #f8f9fa;
        --i-scroll-list-item-margin-bottom: 6px;
      "
      :api="livePreview.sessionUser as any"
    >
      <template #default="{ item }">
        <div class="user-item" :class="`${(item as any).banType === 2 ? 'block-item' : ''}`">
          <div class="user-item-id">
            <div class="id-info">
              UID：{{ (item as any).userId }}
              <el-icon
                style="color: var(--el-color-primary); margin: 0 12px 0 3px; cursor: pointer"
                @click="onCopyId((item as any).userId)"
              >
                <CopyDocument />
              </el-icon>
              <div style="width: 0; flex: 1; margin-left: 12px">
                <el-scrollbar style="width: 100%" :scroll-x="true">
                  <div style="display: inline-flex">
                    <div
                      v-for="(tag, index) in (item as any).tagIds || []"
                      :key="index"
                      class="tag"
                    >
                      {{ tag.name }}
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
            <div class="status">
              <span v-if="(item as any).banType === 1" style="color: var(--el-color-danger)">
                永久禁言
              </span>
              <span v-else-if="(item as any).banType === 0" style="color: var(--el-color-danger)">
                禁言中({{ (item as any).banDuration }}min)
              </span>
              <span v-else-if="(item as any).banType === 2" style="color: #666666">已拉黑</span>
              <span v-else-if="!(item as any).isOnline" style="color: #999999">不在线</span>
            </div>
          </div>
          <div class="base-info">
            <img class="avatar" :src="(item as any).avatar" />
            <div class="user-info">
              <div class="name">{{ (item as any).nickname }}</div>
              <div class="user-info-bottom">
                <div class="user-info-bottom-item">
                  IP地址：
                  <span class="value">{{ (item as any).ipAddress }}</span>
                  <span class="value" style="margin-left: 8px">
                    {{ (item as any).province }}|{{ (item as any).city }}
                  </span>
                </div>
                <div class="user-info-bottom-item" style="margin-left: 50px">
                  累计观看：
                  <span class="value">{{ convertSecondsToHMS((item as any).duration) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </i-scroll-list>
  </div>
</template>

<style scoped lang="scss">
.user-item {
  padding: 6px 13px;
  background: #ffffff;
  border-radius: 4px;

  .base-info {
    display: flex;
    align-items: center;
    margin-top: 10px;

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 40px;
    }
  }

  .user-info {
    margin-left: 10px;
    flex: 1;

    .name {
      font-weight: 500;
      font-size: 14px;
      color: #333333;
    }

    &-bottom {
      display: flex;
      align-items: center;
      margin-top: 4px;

      &-item {
        font-weight: 400;
        font-size: 10px;
        color: #999999;

        .value {
          color: #333333;
        }
      }
    }
  }

  &-id {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .id-info {
      flex: 1;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 11px;
      color: #999999;

      .tag {
        height: 13px;
        border-radius: 9px;
        background: #4cce9c;
        margin-right: 4px;
        font-weight: 400;
        font-size: 10px;
        color: #ffffff;
        padding: 0 4px;
        line-height: 13px;
      }
    }

    .status {
      font-weight: 500;
      font-size: 10px;
      margin-left: 10px;
    }
  }
}

.block-item {
  background: #e9eaed;
}

.user-scroll-list {
  background: #f8f9fa;

  :deep(.i-scroll-list-body) {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .i-scroll-list-li {
      width: calc(50% - 5px);
    }

    @media (width <= 1550px) {
      .i-scroll-list-li {
        width: 100% !important;
      }
    }
  }
}
</style>
