import{bb as e,L as a,b6 as i,t,z as n,v as l,d as c,b as s,c as o,r as v,J as r,I as u,o as d,g as f,f as m,C as p,h as b,Z as y,i as h,n as k,e as I,w as x,D as g,E as V,F as S,l as T,m as _,bh as w,k as j,j as C,a0 as B,bi as A,_ as P,q as E}from"./index.Dk5pbsTU.js";import{i as N}from"./validator.HGn2BZtD.js";import{u as L}from"./index.C9UdVphc.js";import{I as z,C as F,U as K}from"./event.BwRzfsZt.js";import{u as U,a as $}from"./use-form-item.DzRJVC1I.js";import{u as q,a as D}from"./use-form-common-props.CQPDkY7k.js";import{d as J,t as O}from"./error.D_Dr4eZ1.js";const R=t({modelValue:{type:[Boolean,String,Number],default:!1},disabled:Boolean,loading:Boolean,size:{type:String,validator:N},width:{type:[String,Number],default:""},inlinePrompt:Boolean,inactiveActionIcon:{type:l},activeActionIcon:{type:l},activeIcon:{type:l},inactiveIcon:{type:l},activeText:{type:String,default:""},inactiveText:{type:String,default:""},activeValue:{type:[Boolean,String,Number],default:!0},inactiveValue:{type:[Boolean,String,Number],default:!1},name:{type:String,default:""},validateEvent:{type:Boolean,default:!0},beforeChange:{type:n(Function)},id:String,tabindex:{type:[String,Number]},...L(["ariaLabel"])}),Z={[K]:t=>e(t)||a(t)||i(t),[F]:t=>e(t)||a(t)||i(t),[z]:t=>e(t)||a(t)||i(t)},G="ElSwitch",H=c({name:G});const M=E(P(c({...H,props:R,emits:Z,setup(a,{expose:i,emit:t}){const n=a,{formItem:l}=U(),c=q(),P=s("switch"),{inputId:E}=$(n,{formItemContext:l}),N=D(o((()=>n.loading))),L=v(!1!==n.modelValue),R=v(),Z=v(),H=o((()=>[P.b(),P.m(c.value),P.is("disabled",N.value),P.is("checked",Y.value)])),M=o((()=>[P.e("label"),P.em("label","left"),P.is("active",!Y.value)])),Q=o((()=>[P.e("label"),P.em("label","right"),P.is("active",Y.value)])),W=o((()=>({width:r(n.width)})));u((()=>n.modelValue),(()=>{L.value=!0}));const X=o((()=>!!L.value&&n.modelValue)),Y=o((()=>X.value===n.activeValue));[n.activeValue,n.inactiveValue].includes(X.value)||(t(K,n.inactiveValue),t(F,n.inactiveValue),t(z,n.inactiveValue)),u(Y,(e=>{var a;R.value.checked=e,n.validateEvent&&(null==(a=null==l?void 0:l.validate)||a.call(l,"change").catch((e=>J())))}));const ee=()=>{const e=Y.value?n.inactiveValue:n.activeValue;t(K,e),t(F,e),t(z,e),B((()=>{R.value.checked=Y.value}))},ae=()=>{if(N.value)return;const{beforeChange:a}=n;if(!a)return void ee();const i=a();[A(i),e(i)].includes(!0)||O(G,"beforeChange must return type `Promise<boolean>` or `boolean`"),A(i)?i.then((e=>{e&&ee()})).catch((e=>{})):i&&ee()};return d((()=>{R.value.checked=Y.value})),i({focus:()=>{var e,a;null==(a=null==(e=R.value)?void 0:e.focus)||a.call(e)},checked:Y}),(e,a)=>(m(),f("div",{class:k(h(H)),onClick:C(ae,["prevent"])},[p("input",{id:h(E),ref_key:"input",ref:R,class:k(h(P).e("input")),type:"checkbox",role:"switch","aria-checked":h(Y),"aria-disabled":h(N),"aria-label":e.ariaLabel,name:e.name,"true-value":e.activeValue,"false-value":e.inactiveValue,disabled:h(N),tabindex:e.tabindex,onChange:ee,onKeydown:y(ae,["enter"])},null,42,["id","aria-checked","aria-disabled","aria-label","name","true-value","false-value","disabled","tabindex","onKeydown"]),e.inlinePrompt||!e.inactiveIcon&&!e.inactiveText?b("v-if",!0):(m(),f("span",{key:0,class:k(h(M))},[e.inactiveIcon?(m(),I(h(V),{key:0},{default:x((()=>[(m(),I(g(e.inactiveIcon)))])),_:1})):b("v-if",!0),!e.inactiveIcon&&e.inactiveText?(m(),f("span",{key:1,"aria-hidden":h(Y)},S(e.inactiveText),9,["aria-hidden"])):b("v-if",!0)],2)),p("span",{ref_key:"core",ref:Z,class:k(h(P).e("core")),style:j(h(W))},[e.inlinePrompt?(m(),f("div",{key:0,class:k(h(P).e("inner"))},[e.activeIcon||e.inactiveIcon?(m(),I(h(V),{key:0,class:k(h(P).is("icon"))},{default:x((()=>[(m(),I(g(h(Y)?e.activeIcon:e.inactiveIcon)))])),_:1},8,["class"])):e.activeText||e.inactiveText?(m(),f("span",{key:1,class:k(h(P).is("text")),"aria-hidden":!h(Y)},S(h(Y)?e.activeText:e.inactiveText),11,["aria-hidden"])):b("v-if",!0)],2)):b("v-if",!0),p("div",{class:k(h(P).e("action"))},[e.loading?(m(),I(h(V),{key:0,class:k(h(P).is("loading"))},{default:x((()=>[_(h(w))])),_:1},8,["class"])):h(Y)?T(e.$slots,"active-action",{key:1},(()=>[e.activeActionIcon?(m(),I(h(V),{key:0},{default:x((()=>[(m(),I(g(e.activeActionIcon)))])),_:1})):b("v-if",!0)])):h(Y)?b("v-if",!0):T(e.$slots,"inactive-action",{key:2},(()=>[e.inactiveActionIcon?(m(),I(h(V),{key:0},{default:x((()=>[(m(),I(g(e.inactiveActionIcon)))])),_:1})):b("v-if",!0)]))],2)],6),e.inlinePrompt||!e.activeIcon&&!e.activeText?b("v-if",!0):(m(),f("span",{key:1,class:k(h(Q))},[e.activeIcon?(m(),I(h(V),{key:0},{default:x((()=>[(m(),I(g(e.activeIcon)))])),_:1})):b("v-if",!0),!e.activeIcon&&e.activeText?(m(),f("span",{key:1,"aria-hidden":!h(Y)},S(e.activeText),9,["aria-hidden"])):b("v-if",!0)],2))],10,["onClick"]))}}),[["__file","switch.vue"]]));export{M as E};
