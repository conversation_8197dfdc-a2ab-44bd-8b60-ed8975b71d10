import{r as e,S as n,cC as t,bX as o,d as l,bU as s,a7 as a,w as i,V as r,m as u,X as c,T as d,a3 as v,H as b,a0 as m,L as g,bd as f,a1 as p,aa as x,ak as y,cD as C}from"./index.Dk5pbsTU.js";function k(b,m){let g;const f=e(!1),p=n({...b,originalPosition:"",originalOverflow:"",visible:!1});function x(){var e,n;null==(n=null==(e=L.$el)?void 0:e.parentNode)||n.removeChild(L.$el)}function y(){if(!f.value)return;const e=p.parent;f.value=!1,e.vLoadingAddClassList=void 0,function(){const e=p.parent,n=L.ns;if(!e.vLoadingAddClassList){let t=e.getAttribute("loading-number");t=Number.parseInt(t)-1,t?e.setAttribute("loading-number",t.toString()):(v(e,n.bm("parent","relative")),e.removeAttribute("loading-number")),v(e,n.bm("parent","hidden"))}x(),k.unmount()}()}const C=l({name:"ElLoading",setup(e,{expose:n}){const{ns:t,zIndex:o}=s("loading");return n({ns:t,zIndex:o}),()=>{const e=p.spinner||p.svg,n=a("svg",{class:"circular",viewBox:p.svgViewBox?p.svgViewBox:"0 0 50 50",...e?{innerHTML:e}:{}},[a("circle",{class:"path",cx:"25",cy:"25",r:"20",fill:"none"})]),o=p.text?a("p",{class:t.b("text")},[p.text]):void 0;return a(d,{name:t.b("fade"),onAfterLeave:y},{default:i((()=>[r(u("div",{style:{backgroundColor:p.background||""},class:[t.b("mask"),p.customClass,p.fullscreen?"is-fullscreen":""]},[a("div",{class:t.b("spinner")},[n,o])]),[[c,p.visible]])]))})}}}),k=t(C);Object.assign(k._context,null!=m?m:{});const L=k.mount(document.createElement("div"));return{...o(p),setText:function(e){p.text=e},removeElLoadingChild:x,close:function(){var e;b.beforeClose&&!b.beforeClose()||(f.value=!0,clearTimeout(g),g=setTimeout(y,400),p.visible=!1,null==(e=b.closed)||e.call(b))},handleAfterLeave:y,vm:L,get $el(){return L.$el}}}let L;const w=function(e={}){if(!b)return;const n=h(e);if(n.fullscreen&&L)return L;const t=k({...n,closed:()=>{var e;null==(e=n.closed)||e.call(n),n.fullscreen&&(L=void 0)}},w._context);A(n,n.parent,t),V(n,n.parent,t),n.parent.vLoadingAddClassList=()=>V(n,n.parent,t);let o=n.parent.getAttribute("loading-number");return o=o?`${Number.parseInt(o)+1}`:"1",n.parent.setAttribute("loading-number",o),n.parent.appendChild(t.$el),m((()=>t.visible.value=n.visible)),n.fullscreen&&(L=t),t},h=e=>{var n,t,o,l;let s;return s=g(e.target)?null!=(n=document.querySelector(e.target))?n:document.body:e.target||document.body,{parent:s===document.body||e.body?document.body:s,background:e.background||"",svg:e.svg||"",svgViewBox:e.svgViewBox||"",spinner:e.spinner||!1,text:e.text||"",fullscreen:s===document.body&&(null==(t=e.fullscreen)||t),lock:null!=(o=e.lock)&&o,customClass:e.customClass||"",visible:null==(l=e.visible)||l,beforeClose:e.beforeClose,closed:e.closed,target:s}},A=async(e,n,t)=>{const{nextZIndex:o}=t.vm.zIndex||t.vm._.exposed.zIndex,l={};if(e.fullscreen)t.originalPosition.value=f(document.body,"position"),t.originalOverflow.value=f(document.body,"overflow"),l.zIndex=o();else if(e.parent===document.body){t.originalPosition.value=f(document.body,"position"),await m();for(const n of["top","left"]){const t="top"===n?"scrollTop":"scrollLeft";l[n]=e.target.getBoundingClientRect()[n]+document.body[t]+document.documentElement[t]-Number.parseInt(f(document.body,`margin-${n}`),10)+"px"}for(const n of["height","width"])l[n]=`${e.target.getBoundingClientRect()[n]}px`}else t.originalPosition.value=f(n,"position");for(const[s,a]of Object.entries(l))t.$el.style[s]=a},V=(e,n,t)=>{const o=t.vm.ns||t.vm._.exposed.ns;["absolute","fixed","sticky"].includes(t.originalPosition.value)?v(n,o.bm("parent","relative")):p(n,o.bm("parent","relative")),e.fullscreen&&e.lock?p(n,o.bm("parent","hidden")):v(n,o.bm("parent","hidden"))};w._context=null;const $=Symbol("ElLoading"),B=(n,t)=>{var o,l,s,a;const i=t.instance,r=e=>x(t.value)?t.value[e]:void 0,u=t=>(n=>{const t=g(n)&&(null==i?void 0:i[n])||n;return t?e(t):t})(r(t)||n.getAttribute(`element-loading-${C(t)}`)),c=null!=(o=r("fullscreen"))?o:t.modifiers.fullscreen,d={text:u("text"),svg:u("svg"),svgViewBox:u("svgViewBox"),spinner:u("spinner"),background:u("background"),customClass:u("customClass"),fullscreen:c,target:null!=(l=r("target"))?l:c?void 0:n,body:null!=(s=r("body"))?s:t.modifiers.body,lock:null!=(a=r("lock"))?a:t.modifiers.lock},v=w(d);v._context=I._context,n[$]={options:d,instance:v}},I={mounted(e,n){n.value&&B(e,n)},updated(e,n){const t=e[$];n.oldValue!==n.value&&(n.value&&!n.oldValue?B(e,n):n.value&&n.oldValue?x(n.value)&&((e,n)=>{for(const t of Object.keys(n))y(n[t])&&(n[t].value=e[t])})(n.value,t.options):null==t||t.instance.close())},unmounted(e){var n;null==(n=e[$])||n.instance.close(),e[$]=null},_context:null};export{I as v};
