<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="keywords" label="">
          <el-input
            v-model="page.query.keywords"
            placeholder="姓名、昵称检索"
            clearable
            @keyup.enter="getPage"
          />
        </el-form-item>
        <el-form-item prop="status" label="账号状态">
          <dict v-model="page.query.status" code="disable_status" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          type="success"
          v-hasPerm="['wa:webmaster:save']"
          icon="plus"
          @click="editModelRef?.open()"
        >
          新增
        </el-button>
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="头像" align="center" prop="name" width="100">
          <template #default="{ row }">
            <el-image
              style="width: 30px; height: 30px; border-radius: 30px"
              :preview-src-list="[row.avatar]"
              preview-teleported
              :src="row.avatar"
            />
          </template>
        </el-table-column>
        <el-table-column label="主播" align="center" prop="userName" min-width="120">
          <template #default="{ row }">
            <el-link :underline="false" type="primary" @click="infoModelRef?.open(row)">
              {{ row.userName }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="昵称" align="center" prop="nickName" min-width="120" />
        <el-table-column label="是否允许自主开播" align="center" width="80">
          <template #default="{ row }">
            <dict-label v-model="row.isAutonomous" code="yes_or_no" />
          </template>
        </el-table-column>
        <el-table-column label="手机号" align="center" min-width="100" prop="mobile" />
        <el-table-column label="账号状态" align="center" width="80">
          <template #default="{ row }">
            <dict-label v-model="row.status" code="disable_status" />
          </template>
        </el-table-column>
        <el-table-column
          label="简介"
          align="center"
          min-width="100"
          prop="introduction"
          show-overflow-tooltip
        />
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button
              v-hasPerm="['wa:webmaster:save']"
              type="primary"
              size="small"
              link
              icon="edit"
              @click="editModelRef?.open(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-hasPerm="['wa:webmaster:save']"
              type="info"
              size="small"
              link
              @click="editIntroductionRef?.open(scope.row)"
            >
              主播介绍
            </el-button>
            <el-button
              v-hasPerm="['wa:webmaster:delete']"
              type="danger"
              size="small"
              link
              icon="delete"
              :loading="scope.row.loading"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
            <el-button
              v-hasPerm="['wa:webmaster:save']"
              type="warning"
              size="small"
              link
              :loading="scope.row.loading"
              @click="editStatus(scope.row)"
            >
              {{ scope.row.status ? "停用" : "启用" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
    <edit-model ref="editModelRef" @success="resetQuery" />
    <info-model ref="infoModelRef" />
    <edit-introduction ref="editIntroductionRef" @success="resetQuery" />
  </div>
</template>

<script setup lang="ts">
import EditModel from "./edit.vue";
import EditIntroduction from "./editIntroduction.vue";
import InfoModel from "./info.vue";
import WebmasterApi, {
  EditStatusWebmasterDto,
  WebmasterPageQuery,
  WebmasterPageVO,
} from "@/api/webcasterAndAssistant/webcaster";

defineOptions({ name: "Webmaster" });
import { usePage } from "@/utils/commonSetup";
const editIntroductionRef = ref<InstanceType<typeof EditIntroduction>>();
const editModelRef = ref<InstanceType<typeof EditModel>>();
const infoModelRef = ref<InstanceType<typeof InfoModel>>();
const { page, getPage, resetQuery } = usePage<WebmasterPageQuery, WebmasterPageVO>(
  new WebmasterPageQuery("0"),
  WebmasterApi.page
);

function handleDelete(_row: WebmasterPageVO) {
  ElMessageBox.confirm(`确定删除主播《${_row.nickName}》吗？`, `删除`, {
    confirmButtonText: "确定删除",
    cancelButtonText: "取消",
    confirmButtonClass: "el-button--danger",
    type: "error",
  })
    .then(() => {
      _row.loading = true;
      WebmasterApi.remove(_row.id)
        .then(() => {
          ElMessage.success("删除成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}

function editStatus(_row: WebmasterPageVO) {
  ElMessageBox.confirm(
    `确定要${_row.status ? "停用" : "启用"}主播《${_row.nickName}》吗？`,
    `${_row.status ? "停用" : "启用"}授权`,
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      confirmButtonClass: "el-button--danger",
      type: "error",
    }
  )
    .then(() => {
      const formData = new EditStatusWebmasterDto(_row);
      if (formData.status) formData.status = 0;
      else formData.status = 1;
      _row.loading = true;
      WebmasterApi.editStatus(formData)
        .then(() => {
          ElMessage.success("操作成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}
</script>
