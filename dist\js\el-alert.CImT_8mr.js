import{t as e,cj as s,bW as t,_ as a,d as l,b9 as i,b as o,r as c,c as n,e as r,f as d,w as f,V as p,C as u,n as y,i as v,h as b,E as m,l as g,D as k,g as h,$,F as _,P as x,m as S,bT as T,X as w,T as C,q as E}from"./index.Dk5pbsTU.js";const B=e({title:{type:String,default:""},description:{type:String,default:""},type:{type:String,values:s(t),default:"info"},closable:{type:Boolean,default:!0},closeText:{type:String,default:""},showIcon:Boolean,center:Boolean,effect:{type:String,values:["light","dark"],default:"light"}}),j={close:e=>e instanceof MouseEvent},I=l({name:"ElAlert"});const q=E(a(l({...I,props:B,emits:j,setup(e,{emit:s}){const a=e,{Close:l}=T,E=i(),B=o("alert"),j=c(!0),I=n((()=>t[a.type])),q=n((()=>!(!a.description&&!E.default))),A=e=>{j.value=!1,s("close",e)};return(e,s)=>(d(),r(C,{name:v(B).b("fade"),persisted:""},{default:f((()=>[p(u("div",{class:y([v(B).b(),v(B).m(e.type),v(B).is("center",e.center),v(B).is(e.effect)]),role:"alert"},[e.showIcon&&(e.$slots.icon||v(I))?(d(),r(v(m),{key:0,class:y([v(B).e("icon"),{[v(B).is("big")]:v(q)}])},{default:f((()=>[g(e.$slots,"icon",{},(()=>[(d(),r(k(v(I))))]))])),_:3},8,["class"])):b("v-if",!0),u("div",{class:y(v(B).e("content"))},[e.title||e.$slots.title?(d(),h("span",{key:0,class:y([v(B).e("title"),{"with-description":v(q)}])},[g(e.$slots,"title",{},(()=>[$(_(e.title),1)]))],2)):b("v-if",!0),v(q)?(d(),h("p",{key:1,class:y(v(B).e("description"))},[g(e.$slots,"default",{},(()=>[$(_(e.description),1)]))],2)):b("v-if",!0),e.closable?(d(),h(x,{key:2},[e.closeText?(d(),h("div",{key:0,class:y([v(B).e("close-btn"),v(B).is("customed")]),onClick:A},_(e.closeText),3)):(d(),r(v(m),{key:1,class:y(v(B).e("close-btn")),onClick:A},{default:f((()=>[S(v(l))])),_:1},8,["class"]))],64)):b("v-if",!0)],2)],2),[[w,j.value]])])),_:3},8,["name"]))}}),[["__file","alert.vue"]]));export{q as E};
