{"name": "扶摇管理后台", "version": "2.22.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit & vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint --cache \"src/**/*.{vue,ts}\" --fix", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "preinstall": "npx only-allow pnpm"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@stomp/stompjs": "^7.0.0", "@vueuse/core": "^12.5.0", "@wangeditor-next/editor": "^5.6.31", "@wangeditor-next/editor-for-vue": "^5.1.14", "axios": "^1.7.9", "codemirror": "^5.65.18", "codemirror-editor-vue3": "^2.8.0", "default-passive-events": "^2.0.0", "echarts": "^5.6.0", "element-china-area-data": "^6.1.0", "element-plus": "^2.9.3", "exceljs": "^4.4.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^2.3.1", "qs": "^6.14.0", "sortablejs": "^1.15.6", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-i18n": "^11.1.0", "vue-router": "^4.5.0"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@eslint/js": "^9.19.0", "@iconify/utils": "^2.3.0", "@types/codemirror": "^5.60.15", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.1", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.18", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "commitizen": "^4.3.1", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^9.32.0", "globals": "^15.14.0", "lint-staged": "^15.4.3", "postcss": "^8.5.1", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "prettier": "^3.4.2", "sass": "^1.84.0", "stylelint": "^16.14.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended": "^15.0.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-prettier": "^5.0.3", "terser": "^5.38.0", "typescript": "^5.7.3", "typescript-eslint": "^8.23.0", "unocss": "65.4.3", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^6.1.0", "vite-plugin-mock-dev-server": "^1.8.3", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.2.0"}, "engines": {"node": ">=18.0.0"}, "repository": "https://gitee.com/youlaiorg/vue3-element-admin.git", "author": "有来开源组织", "license": "MIT"}