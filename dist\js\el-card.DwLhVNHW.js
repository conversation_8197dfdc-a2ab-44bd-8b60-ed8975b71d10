import{t as s,z as a,_ as e,d as t,b as o,g as r,f as d,h as l,C as f,n as i,i as y,l as n,$ as h,F as c,k as v,q as p}from"./index.Dk5pbsTU.js";const u=s({header:{type:String,default:""},footer:{type:String,default:""},bodyStyle:{type:a([String,Object,Array]),default:""},headerClass:String,bodyClass:String,footerClass:String,shadow:{type:String,values:["always","hover","never"],default:"always"}}),S=t({name:"ElCard"});const b=p(e(t({...S,props:u,setup(s){const a=o("card");return(s,e)=>(d(),r("div",{class:i([y(a).b(),y(a).is(`${s.shadow}-shadow`)])},[s.$slots.header||s.header?(d(),r("div",{key:0,class:i([y(a).e("header"),s.headerClass])},[n(s.$slots,"header",{},(()=>[h(c(s.header),1)]))],2)):l("v-if",!0),f("div",{class:i([y(a).e("body"),s.bodyClass]),style:v(s.bodyStyle)},[n(s.$slots,"default")],6),s.$slots.footer||s.footer?(d(),r("div",{key:1,class:i([y(a).e("footer"),s.footerClass])},[n(s.$slots,"footer",{},(()=>[h(c(s.footer),1)]))],2)):l("v-if",!0)],2))}}),[["__file","card.vue"]]));export{b as E};
