import{d as e,S as a,I as s,r as o,g as t,f as l,m as r,w as m,C as d,h as i,$ as p,Z as u,P as n,Q as c,e as f,F as _}from"./index.Dk5pbsTU.js";import{a as j,E as y}from"./el-form-item.Bw6Zyv_7.js";/* empty css               */import{E as v}from"./el-button.CXI119n4.js";import{E as x}from"./el-input.DiGatoux.js";import{E as h,a as w}from"./el-radio.w2rep3_A.js";import{E as k}from"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./error.D_Dr4eZ1.js";import"./index.D6CER_Ot.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./index.DuiNpp1i.js";import"./use-form-item.DzRJVC1I.js";import"./index.C9UdVphc.js";import"./event.BwRzfsZt.js";import"./index.DEKElSOG.js";import"./index.Vn8pbgQR.js";const g={class:"keywords-match-config"},b={class:"mb-2 flex"},C={style:{padding:"0 6px"}},V={key:0,class:"text-gray-400 text-sm"},E=e({__name:"KeywordsMatchConfig",props:{config:{type:Object,default:()=>({keywords:[],match_mode:"partial"})}},emits:["update"],setup(e,{emit:E}){var U,O;const P=e,A=E,F=a({keywords:(null==(U=P.config)?void 0:U.keywords)||[],match_mode:(null==(O=P.config)?void 0:O.match_mode)||"partial"});s(F,(e=>{A("update",{...e})}),{deep:!0});const K=o(""),M=()=>{K.value&&!F.keywords.includes(K.value)&&(F.keywords.push(K.value),K.value="")};return(e,a)=>{const s=w,o=h,E=j,U=x,O=v,P=k,A=y;return l(),t("div",g,[r(A,{"label-width":"80px"},{default:m((()=>[r(E,{label:"匹配模式"},{default:m((()=>[r(o,{modelValue:F.match_mode,"onUpdate:modelValue":a[0]||(a[0]=e=>F.match_mode=e)},{default:m((()=>[r(s,{value:"exact"},{default:m((()=>a[2]||(a[2]=[p("精确匹配")]))),_:1,__:[2]}),r(s,{value:"partial"},{default:m((()=>a[3]||(a[3]=[p("部分匹配")]))),_:1,__:[3]})])),_:1},8,["modelValue"])])),_:1}),r(E,{label:"关键词"},{default:m((()=>[d("div",b,[r(U,{modelValue:K.value,"onUpdate:modelValue":a[1]||(a[1]=e=>K.value=e),placeholder:"请输入关键词",onKeyup:u(M,["enter"]),class:"mr-2"},null,8,["modelValue"]),r(O,{type:"primary",onClick:M},{default:m((()=>a[4]||(a[4]=[p("添加")]))),_:1,__:[4]})])])),_:1}),d("div",C,[(l(!0),t(n,null,c(F.keywords,((e,a)=>(l(),f(P,{key:a,closable:"",class:"mr-1 mb-1",onClose:e=>(e=>{F.keywords.splice(e,1)})(a)},{default:m((()=>[p(_(e),1)])),_:2},1032,["onClose"])))),128))]),0===F.keywords.length?(l(),t("div",V," 暂无关键词，请添加 ")):i("",!0)])),_:1})])}}});export{E as default};
