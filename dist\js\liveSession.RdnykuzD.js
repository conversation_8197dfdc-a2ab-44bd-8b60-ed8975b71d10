var s=Object.defineProperty,t=(t,e,i)=>((t,e,i)=>e in t?s(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i)(t,"symbol"!=typeof e?e+"":e,i);import{aT as e}from"./index.Dk5pbsTU.js";class i{constructor(){t(this,"pageNum",1),t(this,"pageSize",10),t(this,"keywords",""),t(this,"status","")}}class o{constructor(s,e,i){t(this,"pageNum",1),t(this,"pageSize",10),t(this,"sessionId"),t(this,"keywords",""),t(this,"province",[]),t(this,"userStatus",""),t(this,"userType",""),t(this,"commentCount",""),t(this,"inviterId"),t(this,"inviterType"),this.sessionId=s,this.inviterId=e,this.inviterType=i}}class r{constructor(s,e){t(this,"pageNum",1),t(this,"pageSize",10),t(this,"sessionId"),t(this,"keyWords",""),t(this,"endTime",""),t(this,"startTime",""),t(this,"userId",""),this.sessionId=s,this.userId=e||""}}class n{constructor(s){t(this,"pageNum",1),t(this,"pageSize",10),t(this,"sessionId"),this.sessionId=s}}class a{constructor(s){t(this,"id",""),t(this,"roomId"),t(this,"title",""),t(this,"anchorId",""),t(this,"startTime",""),t(this,"coverUrl",""),t(this,"description",""),t(this,"assistIdList",[]),t(this,"logo",""),t(this,"isSecret",0),t(this,"secretKey",""),s&&(this.id=s.id,this.logo=s.logo,this.roomId=s.roomId,this.title=s.title,this.anchorId=s.anchorId,this.startTime=s.startTime,this.coverUrl=s.coverUrl,this.description=s.description,this.assistIdList=s.assistIdList,this.logo=s.logo||"",this.isSecret=s.isSecret?1:0,this.secretKey=s.secretKey||"")}}const h={allowPlayback:s=>e({url:`/tenant/live/session/allowPlayback?sessionId=${s}`,method:"get"}),page:s=>e({url:"/tenant/live/session/list",method:"get",params:s}),save:s=>e({url:"/tenant/live/session/saveOrEdit",method:"post",data:s}),remove:s=>e({url:`/tenant/live/session/remove/${s}`,method:"delete"}),anchorList:()=>e({url:"/tenant/live/session/anchorList",method:"post"}),assistList:()=>e({url:"/tenant/live/session/assistList",method:"post"}),roomList:()=>e({url:"/tenant/live/session/roomList",method:"post"}),recordList:s=>e({url:"/tenant/live/watching/recordList",method:"get",params:s}),watchingUserInfo:s=>e({url:`/tenant/live/watching/userInfo?userId=${s}`,method:"get"}),commentList:s=>e({url:"/tenant/live/comment/list",method:"get",params:s}),inviteRecordList:s=>e({url:"/tenant/live/session/inviteRecord",method:"get",params:s}),sharedUrl:s=>e({url:`/tenant/live/session/sharedUrl?sessionId=${s}`,method:"get"}),baseInfo:s=>e({url:`/tenant/live/session/baseInfo?sessionId=${s}`,method:"get"})};export{r as C,n as I,a as L,o as R,h as a,i as b};
