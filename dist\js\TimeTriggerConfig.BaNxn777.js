import{d as e,S as t,I as r,g as i,f as s,m,w as o,C as a,F as l}from"./index.Dk5pbsTU.js";import{a as n,E as p}from"./el-form-item.Bw6Zyv_7.js";import"./el-input.DiGatoux.js";import{E as d}from"./el-input-number.C02ig7uT.js";import"./use-form-common-props.CQPDkY7k.js";import"./castArray.C4RhTg2c.js";import"./error.D_Dr4eZ1.js";import"./index.D6CER_Ot.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./_initCloneObject.BN1anLuC.js";import"./index.C9UdVphc.js";import"./event.BwRzfsZt.js";import"./index.DEKElSOG.js";import"./use-form-item.DzRJVC1I.js";import"./index.Vn8pbgQR.js";import"./index.Cd8M2JyP.js";const u={class:"time-trigger-config"},f={class:"flex items-center"},c={class:"ml-2 text-sm text-gray-500"},j=e({__name:"TimeTriggerConfig",props:{config:{type:Object,default:()=>({delay_time:0})}},emits:["update"],setup(e,{emit:j}){var x;const y=j,_=t({delay_time:(null==(x=e.config)?void 0:x.delay_time)||0});r(_,(e=>{y("update",{...e})}),{deep:!0});const g=e=>{if(null==e)return"请设置延迟时间";const t=Number(e);if(0===t)return"立即触发";if(t<60)return`${t}分钟后触发`;{const e=Math.floor(t/60),r=t%60;return 0===r?`${e}小时后触发`:`${e}小时${r}分钟后触发`}};return(e,t)=>{const r=d,j=n,x=p;return s(),i("div",u,[m(x,{"label-width":"120px"},{default:o((()=>[m(j,{label:"开播后触发时间"},{default:o((()=>[a("div",f,[m(r,{modelValue:_.delay_time,"onUpdate:modelValue":t[0]||(t[0]=e=>_.delay_time=e),placeholder:"输入延迟分钟数",min:0,max:1440,step:1,"step-strictly":"",style:{width:"180px"}},null,8,["modelValue"]),a("span",c,l(g(_.delay_time)),1)])])),_:1})])),_:1})])}}});export{j as default};
