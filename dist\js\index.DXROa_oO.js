import{C as e}from"./config.DWdYQVGn.js";import{d as n,ah as a,r as i,g as s,C as t,i as o,f as r}from"./index.Dk5pbsTU.js";const d={style:{padding:"0 10px"}},u=["innerHTML"],l=n({__name:"index",setup(n){const l=String(a().query.code||""),c=i({});return l&&e.getValueByKey(l).then((e=>{c.value=e,document.title=e.configName||""})),(e,n)=>(r(),s("div",d,[t("div",{innerHTML:o(c).configValue},null,8,u)]))}});export{l as default};
