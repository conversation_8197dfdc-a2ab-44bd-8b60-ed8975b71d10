var a=Object.defineProperty,t=(t,s,e)=>((t,s,e)=>s in t?a(t,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[s]=e)(t,"symbol"!=typeof s?s+"":s,e);import{aT as s}from"./index.Dk5pbsTU.js";class e{constructor(a="10"){t(this,"pageNum",1),t(this,"pageSize",10),t(this,"name"),t(this,"status"),this.pageSize=parseInt(a)}}class i{constructor(a="10"){t(this,"pageNum",1),t(this,"pageSize",10),t(this,"keyword",""),t(this,"startPoints",""),t(this,"endPoints",""),t(this,"tagId",""),this.pageSize=parseInt(a)}}const p={page:a=>s.get("/api/basic/tag/list",{params:a}),userPage:a=>s.get("/api/basic/tag/user",{params:a}),batchAdd:a=>s.post("/api/basic/tag/batch",a),remove:a=>s.delete(`/api/basic/tag/${a}`),update:a=>s.put("/api/basic/tag",a)};export{p as T,e as a,i as b};
