import{d as e,r as t,aQ as a,g as o,f as r,C as s,m as i,w as l,Z as n,i as p,$ as m,V as d,e as u,h as c,P as f,F as j,az as g}from"./index.Dk5pbsTU.js";import{v as _}from"./el-loading.Dqi-qL7c.js";import{E as y}from"./el-card.DwLhVNHW.js";import w from"./index.Cywy93e7.js";import{a as h,E as b}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{_ as v}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as x}from"./el-image-viewer.BH897zgF.js";/* empty css               */import{a as k,E as C}from"./el-form-item.Bw6Zyv_7.js";import{E as U}from"./el-button.CXI119n4.js";import{_ as q}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as V}from"./el-input.DiGatoux.js";import B from"./edit.C4UHxgmb.js";import{W as N,a as T}from"./webcaster.CYqw_lYq.js";import{u as E}from"./commonSetup.Dm-aByKQ.js";import{A as P,U as R}from"./assistant.DzsSf80o.js";/* empty css                       */import"./el-overlay.DpVCS8zG.js";import{E as S}from"./index.BcMfjWDS.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./event.BwRzfsZt.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.DuiNpp1i.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./_initCloneObject.BN1anLuC.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./position.DfR5znly.js";import"./index.DEKElSOG.js";import"./el-radio.w2rep3_A.js";import"./el-dialog.Cnp8BitR.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./validator.HGn2BZtD.js";const z={class:"app-container"},$={class:"search-bar"},I={class:"mb-10px"},A=e({name:"Assistant",__name:"index",setup(e){const A=t(),{page:K,getPage:F,resetQuery:L}=E(new N("1"),T.page);return(e,t)=>{const N=V,T=k,E=q,M=U,O=C,Q=b,D=x,H=v,J=h,Z=w,G=y,W=a("hasPerm"),Y=_;return r(),o("div",z,[s("div",$,[i(O,{ref:"queryFormRef",model:p(K).query,inline:!0},{default:l((()=>[i(T,{prop:"keywords",label:""},{default:l((()=>[i(N,{modelValue:p(K).query.keywords,"onUpdate:modelValue":t[0]||(t[0]=e=>p(K).query.keywords=e),placeholder:"姓名、昵称检索",clearable:"",onKeyup:n(p(F),["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),i(T,{prop:"status",label:"账号状态"},{default:l((()=>[i(E,{modelValue:p(K).query.status,"onUpdate:modelValue":t[1]||(t[1]=e=>p(K).query.status=e),code:"disable_status"},null,8,["modelValue"])])),_:1}),i(T,null,{default:l((()=>[i(M,{type:"primary",icon:"search",onClick:p(F)},{default:l((()=>t[6]||(t[6]=[m("搜索")]))),_:1,__:[6]},8,["onClick"]),i(M,{icon:"refresh",onClick:p(L)},{default:l((()=>t[7]||(t[7]=[m("重置")]))),_:1,__:[7]},8,["onClick"])])),_:1})])),_:1},8,["model"])]),i(G,{shadow:"never"},{default:l((()=>[s("div",I,[d((r(),u(M,{type:"success",icon:"plus",onClick:t[2]||(t[2]=e=>{var t;return null==(t=p(A))?void 0:t.open()})},{default:l((()=>t[8]||(t[8]=[m(" 新增 ")]))),_:1,__:[8]})),[[W,["wa:assistant:save"]]])]),d((r(),u(J,{ref:"dataTableRef",data:p(K).data.records,"highlight-current-row":"",border:""},{default:l((()=>[i(Q,{label:"序号",align:"center",width:"55",type:"index"}),i(Q,{label:"头像",align:"center",prop:"name",width:"100"},{default:l((({row:e})=>[i(D,{style:{width:"30px",height:"30px","border-radius":"30px"},"preview-src-list":[e.avatar],"preview-teleported":"",src:e.avatar},null,8,["preview-src-list","src"])])),_:1}),i(Q,{label:"用户ID",align:"center",prop:"appUserId","min-width":"120"}),i(Q,{label:"微信昵称",align:"center",prop:"sysUserName","min-width":"120"}),i(Q,{label:"助理姓名",align:"center",prop:"userName","min-width":"120"}),i(Q,{label:"助理昵称",align:"center",prop:"nickName","min-width":"120"}),i(Q,{label:"账号状态",align:"center",width:"80"},{default:l((({row:e})=>[i(H,{modelValue:e.status,"onUpdate:modelValue":t=>e.status=t,code:"disable_status"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),i(Q,{fixed:"right",label:"操作",width:"210"},{default:l((e=>[0==e.row.status?(r(),o(f,{key:0},[d((r(),u(M,{type:"danger",size:"small",link:"",icon:"delete",loading:e.row.loading,onClick:t=>{return a=e.row,void S.confirm(`确定删除助理《${a.nickName}》吗？`,"删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{a.loading=!0,P.remove(a.id).then((()=>{g.success("删除成功"),L()})).finally((()=>a.loading=!1))})).catch((()=>g.info("已取消")));var a}},{default:l((()=>t[9]||(t[9]=[m(" 删除 ")]))),_:2,__:[9]},1032,["loading","onClick"])),[[W,["wa:assistant:delete"]]]),d((r(),u(M,{type:"primary",size:"small",link:"",loading:e.row.loading,onClick:t=>{return a=e.row,void S.confirm(`确定要将助理《${a.nickName}》切换为销售吗？`,"切换为销售",{confirmButtonText:"确定",cancelButtonText:"取消",confirmButtonClass:"el-button--primary"}).then((()=>{a.loading=!0,P.changeUserType(a.id).then((()=>{g.success("操作成功"),L()})).finally((()=>a.loading=!1))}));var a}},{default:l((()=>t[10]||(t[10]=[m(" 切换为销售 ")]))),_:2,__:[10]},1032,["loading","onClick"])),[[W,["wa:assistant:save"]]])],64)):c("",!0),d((r(),u(M,{type:"warning",size:"small",link:"",loading:e.row.loading,onClick:t=>{return a=e.row,void S.confirm(`确定要${a.status?"停用":"启用"}助理《${a.nickName}》吗？`,(a.status?"停用":"启用")+"授权",{confirmButtonText:"确定",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{const e=new R(a);e.status?e.status=0:e.status=1,a.loading=!0,P.editStatus(e).then((()=>{g.success("操作成功"),L()})).finally((()=>a.loading=!1))})).catch((()=>g.info("已取消")));var a}},{default:l((()=>[m(j(e.row.status?"停用":"启用"),1)])),_:2},1032,["loading","onClick"])),[[W,["wa:assistant:save"]]])])),_:1})])),_:1},8,["data"])),[[Y,p(K).loading]]),p(K).data.totalRow?(r(),u(Z,{key:0,total:p(K).data.totalRow,"onUpdate:total":t[3]||(t[3]=e=>p(K).data.totalRow=e),page:p(K).query.pageNum,"onUpdate:page":t[4]||(t[4]=e=>p(K).query.pageNum=e),limit:p(K).query.pageSize,"onUpdate:limit":t[5]||(t[5]=e=>p(K).query.pageSize=e),onPagination:p(F)},null,8,["total","page","limit","onPagination"])):c("",!0)])),_:1}),i(B,{ref_key:"editModelRef",ref:A,onSuccess:p(L)},null,8,["onSuccess"])])}}});export{A as default};
