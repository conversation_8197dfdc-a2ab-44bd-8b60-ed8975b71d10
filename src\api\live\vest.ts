import request from '@/utils/request'

// Types
export interface VestGroupPageDto {
  roomId?: number
  pageNum: number
  pageSize: number
  groupName?: string
}

export interface VestGroupVo {
  id: number
  roomId: number
  groupName: string
  createdAt: string
}

export interface VestGroupDto {
  id?: number
  groupName: string
  roomId: number
}

export interface VestPageDto {
  groupId?: number
  pageNum: number
  pageSize: number
  nickname?: string
}

export interface VestVo {
  id: number
  tenantId: number
  groupId: number
  webcasterId: number
  nickname: string
  avatar: string
  status: number
  createdAt: string
  updatedAt: string
}

export interface VestDto {
  id?: number
  nickname: string
  avatar: string
  status: number
  roomId: number
  groupId: number
}

// 直播间马甲分组VO（用于下拉选择）
export interface LiveVestGroupVo {
  id: number
  roomId: number
  groupName: string
  createdAt: string
}

// 直播间马甲VO（用于下拉选择）
export interface LiveVestVo {
  id: number
  roomId: number
  groupId: number
  nickname: string
  avatar: string
  status: number
  isAi: number
  createdAt: string
}

// API endpoints
export default {
  // Vest Group APIs
  groupPage(params: VestGroupPageDto) {
    return request.get('/system/vest/group/list', { params })
  },
  saveGroup(data: VestGroupDto) {
    return request.post('/system/vest/group', data)
  },
  removeGroup(id: number) {
    return request.delete(`/system/vest/group/${id}`)
  },

  // Vest APIs
  page(params: VestPageDto) {
    return request.get('/system/vest/list', { params })
  },
  save(data: VestDto) {
    return request.post('/system/vest', data)
  },
  remove(id: number) {
    return request({ url: `/live/vest/${id}`, method: 'delete' })
  },
  
  // 获取所有马甲分组
  getAllGroups(roomId: number) {
    return request<LiveVestGroupVo[]>({
      url: '/system/vest/group/all',
      method: 'get',
      params: { roomId }
    })
  },
  
  // 根据分组ID获取所有马甲
  getVestsByGroupId(groupId: number) {
    return request<LiveVestVo[]>({
      url: `/system/vest/byGroup/${groupId}`,
      method: 'get'
    })
  }
} 