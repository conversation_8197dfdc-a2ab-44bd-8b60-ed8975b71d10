import{d as e,r as l,S as a,e as t,f as o,w as i,m as r,ak as u,i as d,h as s,C as n,V as m,Z as p,$ as c,az as f}from"./index.Dk5pbsTU.js";import{v}from"./el-loading.Dqi-qL7c.js";import{E as _}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as b,a as V}from"./el-tab-pane.CXnN_Izo.js";import{E as h}from"./el-input.DiGatoux.js";import{E as w}from"./el-input-number.C02ig7uT.js";/* empty css               */import{E as x,a as g}from"./el-select.CRWkm-it.js";/* empty css                     */import"./el-popper.Dbn4MgsT.js";import{E as j}from"./el-checkbox.DDYarIkn.js";import{E as U,a as y}from"./el-col.Cfu8vZQ4.js";import{E as S}from"./el-switch.kQ5v4arH.js";import{_ as C}from"./SingleImageUpload.WGBxPB_4.js";import{E as k}from"./el-alert.CImT_8mr.js";import{E as R}from"./el-popover.Bo2lPKkO.js";import N from"./index.Cywy93e7.js";import{a as E,E as q}from"./el-table-column.DRgE6Qqc.js";import"./el-tooltip.l0sNRNKZ.js";import{_ as z}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as I,a as L}from"./el-form-item.Bw6Zyv_7.js";import{E as P}from"./el-button.CXI119n4.js";import{C as M,L as Q,a as A,b as K}from"./liveRoom.B9NhOBdK.js";import{u as F}from"./commonSetup.Dm-aByKQ.js";import{_ as O}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./index.DuiNpp1i.js";import"./vnode.Cbclzz8S.js";import"./event.BwRzfsZt.js";import"./error.D_Dr4eZ1.js";import"./scroll.CVc-P3_z.js";import"./index.D6CER_Ot.js";import"./isUndefined.DgmxjSXK.js";import"./strings.MqEQKtyI.js";import"./index.C_BbqFDa.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.DEKElSOG.js";import"./use-form-item.DzRJVC1I.js";import"./use-form-common-props.CQPDkY7k.js";import"./index.Vn8pbgQR.js";import"./index.Cd8M2JyP.js";import"./index.ybpLT-bz.js";import"./index.L2DVy5yq.js";import"./token.DWNpOE8r.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./validator.HGn2BZtD.js";import"./el-progress.BQBUwu9o.js";import"./cloneDeep.DcCMo0F4.js";import"./el-image-viewer.BH897zgF.js";import"./position.DfR5znly.js";import"./index.WzKGworL.js";import"./dropdown.B_OfpyL_.js";import"./el-pagination.C5FHY27u.js";import"./_initCloneObject.BN1anLuC.js";const D={class:"new-body"},H={class:"new-body"},J={style:{"padding-left":"50px"}},T={style:{"padding-left":"50px"}},W={class:"dialog-footer"},Z=O(e({__name:"cloneRoom",emits:["success"],setup(e,{expose:O,emit:Z}){const $=l("clone"),G=a({visible:!1,title:""}),{page:Y,getPage:B}=F(new K,A.page,!1),X=l(new M),ee=l(new Q),le=l(),ae=Z,te=l(!1),oe=l(!1),ie={selectRoomName:[{required:!0,message:"请选择复用直播间",trigger:"change"}],coverUrl:[{required:!0,message:"请上传封面图",trigger:"blur"}],roomName:[{required:!0,message:"请输入直播间名称",trigger:"blur"}]};function re(e){X.value.selectRoomName=e.roomName,X.value.roomId=String(e.id),oe.value=!1}function ue(){le.value.validate((e=>{e&&(te.value=!0,"clone"===$.value?A.cloneRoom(X.value).then((()=>{f.success("保存成功"),G.visible=!1,ae("success")})).finally((()=>te.value=!1)):A.save(ee.value.toAPI()).then((()=>{f.success("保存成功"),G.visible=!1,ae("success")})).finally((()=>te.value=!1)))}))}return O({open:()=>{oe.value=!1,ee.value=new Q,X.value=new M,B(),G.title="新增直播间",G.visible=!0}}),(e,l)=>{const a=h,f=L,M=P,Q=I,A=q,K=z,F=E,O=N,Z=R,ae=k,te=C,de=S,se=y,ne=U,me=j,pe=V,ce=g,fe=x,ve=w,_e=b,be=_,Ve=v;return o(),t(be,{modelValue:d(G).visible,"onUpdate:modelValue":l[28]||(l[28]=e=>d(G).visible=e),title:d(G).title,width:"700px"},{footer:i((()=>[n("div",W,[r(M,{type:"primary",onClick:ue},{default:i((()=>l[36]||(l[36]=[c("确 定")]))),_:1,__:[36]}),r(M,{onClick:l[27]||(l[27]=e=>d(G).visible=!1)},{default:i((()=>l[37]||(l[37]=[c("取 消")]))),_:1,__:[37]})])])),default:i((()=>[r(_e,{modelValue:d($),"onUpdate:modelValue":l[26]||(l[26]=e=>u($)?$.value=e:null)},{default:i((()=>[r(pe,{name:"clone",label:"快捷克隆"},{default:i((()=>["clone"===d($)?(o(),t(Q,{key:0,ref_key:"editFormRef",ref:le,model:d(X),rules:ie,"label-width":"140px"},{default:i((()=>[r(f,{label:"选择复用的直播间",prop:"selectRoomName"},{default:i((()=>[r(Z,{visible:d(oe),"onUpdate:visible":l[5]||(l[5]=e=>u(oe)?oe.value=e:null),placement:"bottom",trigger:"click",width:"500px"},{reference:i((()=>[r(a,{modelValue:d(X).selectRoomName,"onUpdate:modelValue":l[0]||(l[0]=e=>d(X).selectRoomName=e),"show-word-limit":"",readonly:"",placeholder:"选择复用的直播间"},null,8,["modelValue"])])),default:i((()=>[r(Q,{ref:"queryFormRef",model:d(Y).query,inline:!0},{default:i((()=>[r(f,{prop:"keywords",label:""},{default:i((()=>[r(a,{modelValue:d(Y).query.keywords,"onUpdate:modelValue":l[1]||(l[1]=e=>d(Y).query.keywords=e),placeholder:"直播间名称检索",clearable:"",onKeyup:p(d(B),["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),r(f,null,{default:i((()=>[r(M,{type:"primary",icon:"search",onClick:d(B)},{default:i((()=>l[29]||(l[29]=[c("搜索")]))),_:1,__:[29]},8,["onClick"])])),_:1})])),_:1},8,["model"]),m((o(),t(F,{ref:"dataTableRef",height:"400px",data:d(Y).data.records,"highlight-current-row":"",border:"",onCurrentChange:re},{default:i((()=>[r(A,{label:"序号",align:"center",width:"55",type:"index"}),r(A,{label:"直播间名称",align:"center",prop:"roomName","min-width":"120"}),r(A,{label:"累计开播次数",align:"center",prop:"liveCount","min-width":"100"}),r(A,{label:"直播间状态",align:"center",width:"120"},{default:i((({row:e})=>[r(K,{modelValue:e.status,"onUpdate:modelValue":l=>e.status=l,code:"live_status"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["data"])),[[Ve,d(Y).loading]]),d(Y).data.totalRow?(o(),t(O,{key:0,total:d(Y).data.totalRow,"onUpdate:total":l[2]||(l[2]=e=>d(Y).data.totalRow=e),page:d(Y).query.pageNum,"onUpdate:page":l[3]||(l[3]=e=>d(Y).query.pageNum=e),limit:d(Y).query.pageSize,"onUpdate:limit":l[4]||(l[4]=e=>d(Y).query.pageSize=e),onPagination:d(B)},null,8,["total","page","limit","onPagination"])):s("",!0)])),_:1},8,["visible"])])),_:1}),r(ae,{title:"您可以在这里快捷创建直播，同步复刻该直播间的快捷语、马甲",closable:!1,type:"primary"}),n("div",D,[l[30]||(l[30]=n("div",{class:"new-body-title"},"请设置直播间基础数据",-1)),r(f,{label:"直播间名称",prop:"roomName"},{default:i((()=>[r(a,{modelValue:d(X).roomName,"onUpdate:modelValue":l[6]||(l[6]=e=>d(X).roomName=e),maxlength:50,"show-word-limit":"",placeholder:"请输入直播间名称"},null,8,["modelValue"])])),_:1}),r(f,{label:"封面图",prop:"coverUrl"},{default:i((()=>[r(te,{modelValue:d(X).coverUrl,"onUpdate:modelValue":l[7]||(l[7]=e=>d(X).coverUrl=e),style:{width:"100px",height:"100px"}},null,8,["modelValue"])])),_:1}),r(f,{label:"在线用户显示设置"},{default:i((()=>[r(ne,{gutter:20},{default:i((()=>[r(se,{span:12},{default:i((()=>[r(f,{label:"主播","label-width":"80px"},{default:i((()=>[r(de,{modelValue:d(X).anchorShowUserCount,"onUpdate:modelValue":l[8]||(l[8]=e=>d(X).anchorShowUserCount=e),"inline-prompt":"","active-value":1,"inactive-value":0,"active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1}),r(se,{span:12},{default:i((()=>[r(f,{label:"助理","label-width":"80px"},{default:i((()=>[r(de,{modelValue:d(X).assistantShowUserCount,"onUpdate:modelValue":l[9]||(l[9]=e=>d(X).assistantShowUserCount=e),"inline-prompt":"","active-value":1,"inactive-value":0,"active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(ne,{gutter:20},{default:i((()=>[r(se,{span:12},{default:i((()=>[r(f,{label:"销售","label-width":"80px"},{default:i((()=>[r(de,{modelValue:d(X).saleShowUserCount,"onUpdate:modelValue":l[10]||(l[10]=e=>d(X).saleShowUserCount=e),"inline-prompt":"","active-value":1,"inactive-value":0,"active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1}),r(se,{span:12},{default:i((()=>[r(f,{label:"用户","label-width":"80px"},{default:i((()=>[r(de,{modelValue:d(X).userShowUserCount,"onUpdate:modelValue":l[11]||(l[11]=e=>d(X).userShowUserCount=e),"inline-prompt":"","active-text":"开","active-value":1,"inactive-value":0,"inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1})]),n("div",H,[l[33]||(l[33]=n("div",{class:"new-body-title"},"同步克隆：",-1)),n("div",J,[r(me,{modelValue:d(X).cloneQuickReply,"onUpdate:modelValue":l[12]||(l[12]=e=>d(X).cloneQuickReply=e),"false-label":0,"true-label":1},{default:i((()=>l[31]||(l[31]=[c(" 快捷语 ")]))),_:1,__:[31]},8,["modelValue"])]),n("div",T,[r(me,{modelValue:d(X).cloneQuickVest,"onUpdate:modelValue":l[13]||(l[13]=e=>d(X).cloneQuickVest=e),"false-label":0,"true-label":1},{default:i((()=>l[32]||(l[32]=[c(" 马甲 ")]))),_:1,__:[32]},8,["modelValue"])])])])),_:1},8,["model"])):s("",!0)])),_:1}),r(pe,{name:"new",label:"手动新增"},{default:i((()=>["new"===d($)?(o(),t(Q,{key:0,ref_key:"editFormRef",ref:le,model:d(ee),rules:ie,"label-width":"140px"},{default:i((()=>[r(f,{label:"直播间名称",prop:"roomName"},{default:i((()=>[r(a,{modelValue:d(ee).roomName,"onUpdate:modelValue":l[14]||(l[14]=e=>d(ee).roomName=e),maxlength:50,"show-word-limit":"",placeholder:"请输入直播间名称"},null,8,["modelValue"])])),_:1}),r(f,{label:"封面图",prop:"coverUrl"},{default:i((()=>[r(te,{modelValue:d(ee).coverUrl,"onUpdate:modelValue":l[15]||(l[15]=e=>d(ee).coverUrl=e),style:{width:"100px",height:"100px"}},null,8,["modelValue"])])),_:1}),r(f,{label:"直播间介绍",prop:"description"},{default:i((()=>[r(a,{modelValue:d(ee).description,"onUpdate:modelValue":l[16]||(l[16]=e=>d(ee).description=e),maxlength:200,"show-word-limit":"",type:"textarea",rows:5,placeholder:"请输入直播间介绍"},null,8,["modelValue"])])),_:1}),r(f,{label:"直播间公告",prop:"announcement"},{default:i((()=>[r(a,{modelValue:d(ee).announcement,"onUpdate:modelValue":l[17]||(l[17]=e=>d(ee).announcement=e),maxlength:200,"show-word-limit":"",type:"textarea",rows:4,placeholder:"请输入直播间公告"},null,8,["modelValue"])])),_:1}),r(f,{label:"温馨提示",prop:"notice"},{default:i((()=>[r(a,{modelValue:d(ee).notice,"onUpdate:modelValue":l[18]||(l[18]=e=>d(ee).notice=e),maxlength:80,"show-word-limit":"",type:"textarea",rows:4,placeholder:"请输入温馨提示"},null,8,["modelValue"])])),_:1}),r(f,{label:"在线用户显示设置"},{default:i((()=>[r(ne,{gutter:20},{default:i((()=>[r(se,{span:12},{default:i((()=>[r(f,{label:"主播","label-width":"80px"},{default:i((()=>[r(de,{modelValue:d(ee).anchorShowUserCount,"onUpdate:modelValue":l[19]||(l[19]=e=>d(ee).anchorShowUserCount=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1}),r(se,{span:12},{default:i((()=>[r(f,{label:"助理","label-width":"80px"},{default:i((()=>[r(de,{modelValue:d(ee).assistantShowUserCount,"onUpdate:modelValue":l[20]||(l[20]=e=>d(ee).assistantShowUserCount=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(ne,{gutter:20},{default:i((()=>[r(se,{span:12},{default:i((()=>[r(f,{label:"销售","label-width":"80px"},{default:i((()=>[r(de,{modelValue:d(ee).saleShowUserCount,"onUpdate:modelValue":l[21]||(l[21]=e=>d(ee).saleShowUserCount=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1}),r(se,{span:12},{default:i((()=>[r(f,{label:"用户","label-width":"80px"},{default:i((()=>[r(de,{modelValue:d(ee).userShowUserCount,"onUpdate:modelValue":l[22]||(l[22]=e=>d(ee).userShowUserCount=e),"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),r(ne,{gutter:20,style:{margin:"0"}},{default:i((()=>[r(se,{span:12},{default:i((()=>[r(f,{label:"直播间默认字号",prop:"fontSize"},{default:i((()=>[r(fe,{modelValue:d(ee).fontSize,"onUpdate:modelValue":l[23]||(l[23]=e=>d(ee).fontSize=e),placeholder:"请选择字号"},{default:i((()=>[r(ce,{label:"14px",value:"14"}),r(ce,{label:"15px",value:"15"}),r(ce,{label:"16px",value:"16"}),r(ce,{label:"17px",value:"17"}),r(ce,{label:"18px",value:"18"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),r(se,{span:12},{default:i((()=>[r(f,{label:"是否允许普通用户回复","label-width":"160px",prop:"fontSize"},{default:i((()=>[r(de,{modelValue:d(ee).userReply,"onUpdate:modelValue":l[24]||(l[24]=e=>d(ee).userReply=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(f,{label:"直播暂停自动结束",prop:"stopLiveMinutes"},{default:i((()=>[r(ve,{modelValue:d(ee).stopLiveMinutes,"onUpdate:modelValue":l[25]||(l[25]=e=>d(ee).stopLiveMinutes=e),min:1,max:999,placeholder:"请输入分钟数"},null,8,["modelValue"]),l[34]||(l[34]=n("span",{style:{"margin-left":"10px"}},"分钟",-1))])),_:1,__:[34]}),l[35]||(l[35]=n("div",{class:"el-form-item-msg"},"当直播间暂停时长等于该值，则自动结束",-1))])),_:1,__:[35]},8,["model"])):s("",!0)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-a5642d7b"]]);export{Z as default};
