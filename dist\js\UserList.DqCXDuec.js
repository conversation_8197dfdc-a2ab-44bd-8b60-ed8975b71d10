import{P as s,I as t,L as i}from"./index.CgJfYQHq.js";import{d as e}from"./dayjs.min.Bj2GcQlV.js";import{ar as n,d as r,r as o,I as a,g as d,f as u,m as c,i as h,ak as l,w as m,C as f,$ as p,F as $,az as v,a0 as y}from"./index.Dk5pbsTU.js";import{u as g}from"./index.CcWFbuMM.js";import{_ as M}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./el-divider.2VNIZioN.js";import"./el-empty.Dee0wMKK.js";import"./index.D6CER_Ot.js";import"./commonSetup.Dm-aByKQ.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";var b,S,j,x,k,Y,I,w,D,H,_,O,P,N,U,C,F,L,T,V,z,A={exports:{}};const W=n(b?A.exports:(b=1,A.exports=(w=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,_=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/,O={years:D=31536e6,months:H=2628e6,days:I=864e5,hours:Y=36e5,minutes:k=6e4,seconds:x=1e3,milliseconds:1,weeks:6048e5},P=function(s){return s instanceof V},N=function(s,t,i){return new V(s,i,t.$l)},U=function(s){return j.p(s)+"s"},C=function(s){return s<0},F=function(s){return C(s)?Math.ceil(s):Math.floor(s)},L=function(s){return Math.abs(s)},T=function(s,t){return s?C(s)?{negative:!0,format:""+L(s)+t}:{negative:!1,format:""+s+t}:{negative:!1,format:""}},V=function(){function s(s,t,i){var e=this;if(this.$d={},this.$l=i,void 0===s&&(this.$ms=0,this.parseFromMilliseconds()),t)return N(s*O[U(t)],this);if("number"==typeof s)return this.$ms=s,this.parseFromMilliseconds(),this;if("object"==typeof s)return Object.keys(s).forEach((function(t){e.$d[U(t)]=s[t]})),this.calMilliseconds(),this;if("string"==typeof s){var n=s.match(_);if(n){var r=n.slice(2).map((function(s){return null!=s?Number(s):0}));return this.$d.years=r[0],this.$d.months=r[1],this.$d.weeks=r[2],this.$d.days=r[3],this.$d.hours=r[4],this.$d.minutes=r[5],this.$d.seconds=r[6],this.calMilliseconds(),this}}return this}var t=s.prototype;return t.calMilliseconds=function(){var s=this;this.$ms=Object.keys(this.$d).reduce((function(t,i){return t+(s.$d[i]||0)*O[i]}),0)},t.parseFromMilliseconds=function(){var s=this.$ms;this.$d.years=F(s/D),s%=D,this.$d.months=F(s/H),s%=H,this.$d.days=F(s/I),s%=I,this.$d.hours=F(s/Y),s%=Y,this.$d.minutes=F(s/k),s%=k,this.$d.seconds=F(s/x),s%=x,this.$d.milliseconds=s},t.toISOString=function(){var s=T(this.$d.years,"Y"),t=T(this.$d.months,"M"),i=+this.$d.days||0;this.$d.weeks&&(i+=7*this.$d.weeks);var e=T(i,"D"),n=T(this.$d.hours,"H"),r=T(this.$d.minutes,"M"),o=this.$d.seconds||0;this.$d.milliseconds&&(o+=this.$d.milliseconds/1e3,o=Math.round(1e3*o)/1e3);var a=T(o,"S"),d=s.negative||t.negative||e.negative||n.negative||r.negative||a.negative,u=n.format||r.format||a.format?"T":"",c=(d?"-":"")+"P"+s.format+t.format+e.format+u+n.format+r.format+a.format;return"P"===c||"-P"===c?"P0D":c},t.toJSON=function(){return this.toISOString()},t.format=function(s){var t=s||"YYYY-MM-DDTHH:mm:ss",i={Y:this.$d.years,YY:j.s(this.$d.years,2,"0"),YYYY:j.s(this.$d.years,4,"0"),M:this.$d.months,MM:j.s(this.$d.months,2,"0"),D:this.$d.days,DD:j.s(this.$d.days,2,"0"),H:this.$d.hours,HH:j.s(this.$d.hours,2,"0"),m:this.$d.minutes,mm:j.s(this.$d.minutes,2,"0"),s:this.$d.seconds,ss:j.s(this.$d.seconds,2,"0"),SSS:j.s(this.$d.milliseconds,3,"0")};return t.replace(w,(function(s,t){return t||String(i[s])}))},t.as=function(s){return this.$ms/O[U(s)]},t.get=function(s){var t=this.$ms,i=U(s);return"milliseconds"===i?t%=1e3:t="weeks"===i?F(t/O[i]):this.$d[i],t||0},t.add=function(s,t,i){var e;return e=t?s*O[U(t)]:P(s)?s.$ms:N(s,this).$ms,N(this.$ms+e*(i?-1:1),this)},t.subtract=function(s,t){return this.add(s,t,!0)},t.locale=function(s){var t=this.clone();return t.$l=s,t},t.clone=function(){return N(this.$ms,this)},t.humanize=function(s){return S().add(this.$ms,"ms").locale(this.$l).fromNow(!s)},t.valueOf=function(){return this.asMilliseconds()},t.milliseconds=function(){return this.get("milliseconds")},t.asMilliseconds=function(){return this.as("milliseconds")},t.seconds=function(){return this.get("seconds")},t.asSeconds=function(){return this.as("seconds")},t.minutes=function(){return this.get("minutes")},t.asMinutes=function(){return this.as("minutes")},t.hours=function(){return this.get("hours")},t.asHours=function(){return this.as("hours")},t.days=function(){return this.get("days")},t.asDays=function(){return this.as("days")},t.weeks=function(){return this.get("weeks")},t.asWeeks=function(){return this.as("weeks")},t.months=function(){return this.get("months")},t.asMonths=function(){return this.as("months")},t.years=function(){return this.get("years")},t.asYears=function(){return this.as("years")},s}(),z=function(s,t,i){return s.add(t.years()*i,"y").add(t.months()*i,"M").add(t.days()*i,"d").add(t.hours()*i,"h").add(t.minutes()*i,"m").add(t.seconds()*i,"s").add(t.milliseconds()*i,"ms")},function(s,t,i){S=i,j=i().$utils(),i.duration=function(s,t){var e=i.locale();return N(s,{$l:e},t)},i.isDuration=P;var e=t.prototype.add,n=t.prototype.subtract;t.prototype.add=function(s,t){return P(s)?z(this,s,1):e.bind(this)(s,t)},t.prototype.subtract=function(s,t){return P(s)?z(this,s,-1):n.bind(this)(s,t)}}))),Z={class:"user-list"},E={class:"user-item"},J={class:"user-item-id"},K={class:"id-info"},R=["onClick"],X={class:"time"},q={class:"time-info"},B={class:"user-info"},G=["src"],Q={class:"base-info"},ss={class:"name"},ts={class:"address"},is={class:"address-info"},es={class:"address-info",style:{"margin-left":"8px"}},ns=M(r({__name:"UserList",props:{sessionId:Number,info:{type:Object,default:()=>({})}},setup(n){const{toClipboard:r}=g();e.extend(W);const M=n,b=o(),S=o(new s);function j(s){const t=e.duration(s,"seconds"),i=Math.floor(t.asHours()),n=t.minutes(),r=t.seconds();return i>0?`${i}h${n}min`:n>0?`${n}min`:`${r}s`}return a((()=>M.info.inviterId),(s=>{s&&(S.value.sessionId=String(M.sessionId),S.value.inviterId=String(s),S.value.type="0",S.value.pageNum=1,y((()=>{var s;null==(s=b.value)||s.reset()})))}),{deep:!0,immediate:!0}),(s,e)=>(u(),d("div",Z,[c(t,{ref_key:"userListRef",ref:b,modelValue:h(S),"onUpdate:modelValue":e[0]||(e[0]=s=>l(S)?S.value=s:null),class:"user-scroll-list",style:{"--i-scroll-list-padding":"10px 10px 0 10px","--i-scroll-list-divider-bg":"#f8f9fa","--i-scroll-list-item-margin-bottom":"10px"},api:h(i).sessionUser},{default:m((({item:s})=>[f("div",E,[f("div",J,[f("div",K,[p(" UID："+$(s.userId)+" ",1),f("span",{class:"copy",onClick:t=>{return i=s.userId,void r(String(i)).then((()=>{v.success("复制成功")})).catch((()=>{v.error("复制失败")}));var i}},"复制",8,R)]),f("div",X,[e[1]||(e[1]=p(" 累计观看： ")),f("span",q,$(j(s.duration)),1)])]),f("div",B,[f("img",{class:"avatar",src:s.avatar},null,8,G),f("div",Q,[f("div",ss,$(s.nickname),1),f("div",ts,[e[2]||(e[2]=p(" IP地址： ")),f("span",is,$(s.ipAddress),1),f("span",es,$(s.province)+"|"+$(s.city),1)])])])])])),_:1},8,["modelValue","api"])]))}}),[["__scopeId","data-v-bc543eba"]]);export{ns as default};
