<script setup lang="ts">
import { usePage } from "@/utils/commonSetup";
import TagApi, { PageTagUserVO, TagUserPageQuery } from "@/api/live/tags";

const dialog = reactive({
  show: false,
  title: "同标签用户",
});
const { page, getPage } = usePage<TagUserPageQuery, PageTagUserVO>(
  new TagUserPageQuery(),
  TagApi.userPage as any,
  false
);
defineExpose({
  open: (_id: string) => {
    page.query = new TagUserPageQuery();
    page.query.tagId = _id;
    page.query.pageNum = 1;
    dialog.show = true;
    getPage();
  },
});
</script>

<template>
  <el-drawer v-model="dialog.show" size="80vw" :title="dialog.title">
    <el-container v-if="dialog.show" style="height: 100%">
      <el-header style="padding: 0">
        <el-form ref="queryFormRef" :model="page.query" :inline="true">
          <el-form-item prop="name" label-width="0">
            <el-input
              v-model="page.query.keyword"
              placeholder="客户昵称检索"
              clearable
              @keyup.enter="getPage"
            />
          </el-form-item>
          <el-form-item prop="status" label="积分值检索">
            <div style="display: flex; align-items: center">
              <el-input
                v-model="page.query.startPoints"
                placeholder="开始积分"
                clearable
                oninput="value=value.replace(/[^\d]/g,'')"
                style="width: 120px"
              />
              <span style="margin: 0 8px">~</span>
              <el-input
                v-model="page.query.endPoints"
                placeholder="截止积分"
                clearable
                oninput="value=value.replace(/[^\d]/g,'')"
                style="width: 120px"
              />
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-header>
      <el-main style="padding: 0; height: 100%">
        <el-table v-loading="page.loading" height="100%" :data="page.data.records" border>
          <el-table-column label="序号" align="center" width="55" type="index" />
          <el-table-column label="客户" align="center" prop="nickName" width="120" />
          <el-table-column label="当前积分" align="center" prop="currentPoints" width="120" />
          <el-table-column label="当前用户等级" align="center" prop="currentLevel" width="120" />
          <el-table-column label="标签" show-overflow-tooltip align="center" min-width="100">
            <template #default="{ row }">
              {{ row.tagList.length ? row.tagList.join("、") : "-" }}
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <pagination
          v-if="page.data.totalRow"
          v-model:total="page.data.totalRow"
          v-model:page="page.query.pageNum"
          v-model:limit="page.query.pageSize"
          @pagination="getPage"
        />
      </el-footer>
    </el-container>
  </el-drawer>
</template>

<style scoped lang="scss"></style>
