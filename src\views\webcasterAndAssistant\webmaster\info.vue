<template>
  <el-drawer v-model="dialog.visible" :title="dialog.title" size="80vw">
    <el-descriptions :column="1" border label-width="90px">
      <el-descriptions-item label="姓名">
        {{ info.userName }}
      </el-descriptions-item>
      <el-descriptions-item label="昵称">
        {{ info.nickName }}
      </el-descriptions-item>
      <el-descriptions-item label="手机号">
        {{ info.mobile }}
      </el-descriptions-item>
      <el-descriptions-item label="头像">
        <el-image :src="info.avatar" style="width: 100px; height: 100px" />
      </el-descriptions-item>
      <el-descriptions-item label="自主开播">
        <dict-label v-model="info.isAutonomous" code="yes_or_no" />
      </el-descriptions-item>
      <el-descriptions-item label="主播介绍">
        <div v-html="info.introduction" />
      </el-descriptions-item>
    </el-descriptions>
  </el-drawer>
</template>
<script setup lang="ts">
import { WebmasterPageVO } from "@/api/webcasterAndAssistant/webcaster";

const dialog = reactive({
  visible: false,
  title: "",
});
const info = ref<WebmasterPageVO>({} as WebmasterPageVO);

defineExpose({
  open: (_row: WebmasterPageVO) => {
    dialog.visible = true;
    dialog.title = "主播详情";
    info.value = _row;
  },
});
</script>
