import{d as e,r as t,g as r,f as i,C as a,m as o,w as s,i as l,P as p,Q as n,e as m,$ as d,V as u,h as j,F as c}from"./index.Dk5pbsTU.js";import{v as f}from"./el-loading.Dqi-qL7c.js";import{E as _}from"./el-card.DwLhVNHW.js";import g from"./index.Cywy93e7.js";import{a as y,E as v}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{E as w}from"./el-link.qHYW6llJ.js";import{E as b}from"./el-image-viewer.BH897zgF.js";/* empty css               */import{a as h,E as x}from"./el-form-item.Bw6Zyv_7.js";import{E as k}from"./el-button.CXI119n4.js";import{E as q,a as C}from"./el-select.CRWkm-it.js";import{_ as U}from"./index.vue_vue_type_script_setup_true_lang.VmedQQUp.js";import{_ as E}from"./pointsDetails.vue_vue_type_script_setup_true_lang.CqbxAxEf.js";import{u as P,a as L}from"./commonSetup.Dm-aByKQ.js";import{b as R,a as T}from"./grade.D-4Kz6mr.js";import"./el-pagination.C5FHY27u.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./_arrayPush.DSBJLlac.js";import"./el-input.DiGatoux.js";import"./index.C9UdVphc.js";import"./event.BwRzfsZt.js";import"./index.DEKElSOG.js";import"./use-form-item.DzRJVC1I.js";import"./use-form-common-props.CQPDkY7k.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./error.D_Dr4eZ1.js";import"./index.DuiNpp1i.js";import"./index.ybpLT-bz.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.L2DVy5yq.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./_initCloneObject.BN1anLuC.js";import"./position.DfR5znly.js";import"./el-date-picker.B6TshyBV.js";import"./dayjs.min.D7B9fRnU.js";import"./arrays.CygeFE-H.js";import"./index.Cd8M2JyP.js";import"./el-drawer.Df_TzNjH.js";import"./el-overlay.DpVCS8zG.js";import"./el-main.CclDHmVj.js";import"./el-descriptions-item.BlvmJIy_.js";const V={class:"app-container"},S={class:"search-bar"},I=e({name:"VipLevelOverview",__name:"index",setup(e){const{page:I,getPage:N,resetQuery:O}=P(new R,T.userList),z=t(),{sendInfo:A,onSend:Q}=L(T.configList);return Q(),(e,t)=>{const P=U,L=h,R=C,T=q,Q=k,$=x,D=v,F=b,G=w,H=y,J=g,K=_,M=f;return i(),r(p,null,[a("div",V,[a("div",S,[o($,{ref:"queryFormRef",model:l(I).query,inline:!0},{default:s((()=>[o(L,{prop:"status",label:"最近登录时间"},{default:s((()=>[o(P,{start:l(I).query.startTime,"onUpdate:start":t[0]||(t[0]=e=>l(I).query.startTime=e),end:l(I).query.endTime,"onUpdate:end":t[1]||(t[1]=e=>l(I).query.endTime=e),"is-split":"",type:"daterange"},null,8,["start","end"])])),_:1}),o(L,{prop:"status",label:"会员等级"},{default:s((()=>[o(T,{modelValue:l(I).query.level,"onUpdate:modelValue":t[2]||(t[2]=e=>l(I).query.level=e),style:{width:"250px"}},{default:s((()=>[(i(!0),r(p,null,n(l(A).data,(e=>(i(),m(R,{key:e.id,label:e.levelName,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),o(L,null,{default:s((()=>[o(Q,{type:"primary",icon:"search",onClick:l(N)},{default:s((()=>t[6]||(t[6]=[d("搜索")]))),_:1,__:[6]},8,["onClick"]),o(Q,{icon:"refresh",onClick:l(O)},{default:s((()=>t[7]||(t[7]=[d("重置")]))),_:1,__:[7]},8,["onClick"])])),_:1})])),_:1},8,["model"])]),o(K,{shadow:"never"},{default:s((()=>[u((i(),m(H,{ref:"dataTableRef",data:l(I).data.records,"highlight-current-row":"",border:""},{default:s((()=>[o(D,{label:"序号",align:"center",width:"55",type:"index"}),o(D,{label:"等级名称",align:"center",prop:"currentLevel","min-width":"100"}),o(D,{label:"头像",align:"center",width:"100"},{default:s((({row:e})=>[o(F,{style:{width:"30px",height:"30px","border-radius":"30px"},"preview-src-list":[e.avatar],"preview-teleported":"",src:e.avatar},null,8,["preview-src-list","src"])])),_:1}),o(D,{label:"昵称",align:"center",prop:"nickname","min-width":"100"},{default:s((({row:e})=>[o(G,{type:"primary",onClick:t=>{var r;return null==(r=l(z))?void 0:r.open(e)}},{default:s((()=>[d(c(e.nickname),1)])),_:2},1032,["onClick"])])),_:1}),o(D,{label:"平台积分",align:"center",prop:"currentPoints","min-width":"100"}),o(D,{label:"标签",align:"center",prop:"currentPoints","min-width":"100"},{default:s((({row:e})=>[d(c((e.tagList||[]).join("、")),1)])),_:1}),o(D,{label:"最近登录时间",align:"center",prop:"updatedAt","min-width":"100"})])),_:1},8,["data"])),[[M,l(I).loading]]),l(I).data.totalRow?(i(),m(J,{key:0,total:l(I).data.totalRow,"onUpdate:total":t[3]||(t[3]=e=>l(I).data.totalRow=e),page:l(I).query.pageNum,"onUpdate:page":t[4]||(t[4]=e=>l(I).query.pageNum=e),limit:l(I).query.pageSize,"onUpdate:limit":t[5]||(t[5]=e=>l(I).query.pageSize=e),onPagination:l(N)},null,8,["total","page","limit","onPagination"])):j("",!0)])),_:1})]),o(E,{ref_key:"pointsDetailsRef",ref:z},null,512)],64)}}});export{I as default};
