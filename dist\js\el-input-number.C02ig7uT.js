import{b6 as e,M as a,t,N as l,_ as r,d as n,x as u,b as s,r as i,S as o,c as m,a5 as d,I as c,o as p,b8 as v,g as b,f,V as N,h as y,m as x,i as V,Z as I,n as g,l as h,w as S,e as E,W as w,cP as F,E as A,cF as j,c1 as $,aL as _,j as k,L as B,q as K}from"./index.Dk5pbsTU.js";import{E as L,i as O}from"./el-input.DiGatoux.js";import{u as M}from"./index.C9UdVphc.js";import{U as P,I as C,C as T}from"./event.BwRzfsZt.js";import{v as z}from"./index.Cd8M2JyP.js";import{u as D}from"./use-form-item.DzRJVC1I.js";import{d as U,t as Y}from"./error.D_Dr4eZ1.js";import{u as q,a as G}from"./use-form-common-props.CQPDkY7k.js";const R=t({id:{type:String,default:void 0},step:{type:Number,default:1},stepStrictly:Boolean,max:{type:Number,default:Number.POSITIVE_INFINITY},min:{type:Number,default:Number.NEGATIVE_INFINITY},modelValue:{type:[Number,null]},readonly:Boolean,disabled:Boolean,size:l,controls:{type:Boolean,default:!0},controlsPosition:{type:String,default:"",values:["","right"]},valueOnClear:{type:[String,Number,null],validator:a=>null===a||e(a)||["min","max"].includes(a),default:null},name:String,placeholder:String,precision:{type:Number,validator:e=>e>=0&&e===Number.parseInt(`${e}`,10)},validateEvent:{type:Boolean,default:!0},...M(["ariaLabel"])}),W={[T]:(e,a)=>a!==e,blur:e=>e instanceof FocusEvent,focus:e=>e instanceof FocusEvent,[C]:t=>e(t)||a(t),[P]:t=>e(t)||a(t)},Z=n({name:"ElInputNumber"});const H=K(r(n({...Z,props:R,emits:W,setup(t,{expose:l,emit:r}){const n=t,{t:K}=u(),M=s("input-number"),R=i(),W=o({currentValue:n.modelValue,userInput:null}),{formItem:Z}=D(),H=m((()=>e(n.modelValue)&&n.modelValue<=n.min)),J=m((()=>e(n.modelValue)&&n.modelValue>=n.max)),Q=m((()=>{const e=re(n.step);return d(n.precision)?Math.max(re(n.modelValue),e):(n.precision,n.precision)})),X=m((()=>n.controls&&"right"===n.controlsPosition)),ee=q(),ae=G(),te=m((()=>{if(null!==W.userInput)return W.userInput;let t=W.currentValue;if(a(t))return"";if(e(t)){if(Number.isNaN(t))return"";d(n.precision)||(t=t.toFixed(n.precision))}return t})),le=(e,a)=>{if(d(a)&&(a=Q.value),0===a)return Math.round(e);let t=String(e);const l=t.indexOf(".");if(-1===l)return e;if(!t.replace(".","").split("")[l+a])return e;const r=t.length;return"5"===t.charAt(r-1)&&(t=`${t.slice(0,Math.max(0,r-1))}6`),Number.parseFloat(Number(t).toFixed(a))},re=e=>{if(a(e))return 0;const t=e.toString(),l=t.indexOf(".");let r=0;return-1!==l&&(r=t.length-l-1),r},ne=(a,t=1)=>e(a)?le(a+n.step*t):W.currentValue,ue=()=>{if(n.readonly||ae.value||J.value)return;const e=Number(te.value)||0,a=ne(e);oe(a),r(C,W.currentValue),ve()},se=()=>{if(n.readonly||ae.value||H.value)return;const e=Number(te.value)||0,a=ne(e,-1);oe(a),r(C,W.currentValue),ve()},ie=(e,t)=>{const{max:l,min:u,step:s,precision:i,stepStrictly:o,valueOnClear:m}=n;l<u&&Y("InputNumber","min should not be greater than max.");let c=Number(e);if(a(e)||Number.isNaN(c))return null;if(""===e){if(null===m)return null;c=B(m)?{min:u,max:l}[m]:m}return o&&(c=le(Math.round(c/s)*s,i),c!==e&&t&&r(P,c)),d(i)||(c=le(c,i)),(c>l||c<u)&&(c=c>l?l:u,t&&r(P,c)),c},oe=(e,a=!0)=>{var t;const l=W.currentValue,u=ie(e);a?l===u&&e||(W.userInput=null,r(P,u),l!==u&&r(T,u,l),n.validateEvent&&(null==(t=null==Z?void 0:Z.validate)||t.call(Z,"change").catch((e=>U()))),W.currentValue=u):r(P,u)},me=e=>{W.userInput=e;const a=""===e?null:Number(e);r(C,a),oe(a,!1)},de=a=>{const t=""!==a?Number(a):"";(e(t)&&!Number.isNaN(t)||""===a)&&oe(t),ve(),W.userInput=null},ce=e=>{r("focus",e)},pe=e=>{var a,t;W.userInput=null,O()&&null===W.currentValue&&(null==(a=R.value)?void 0:a.input)&&(R.value.input.value=""),r("blur",e),n.validateEvent&&(null==(t=null==Z?void 0:Z.validate)||t.call(Z,"blur").catch((e=>U())))},ve=()=>{W.currentValue!==n.modelValue&&(W.currentValue=n.modelValue)},be=e=>{document.activeElement===e.target&&e.preventDefault()};return c((()=>n.modelValue),((e,a)=>{const t=ie(e,!0);null===W.userInput&&t!==a&&(W.currentValue=t)}),{immediate:!0}),p((()=>{var a;const{min:t,max:l,modelValue:u}=n,s=null==(a=R.value)?void 0:a.input;if(s.setAttribute("role","spinbutton"),Number.isFinite(l)?s.setAttribute("aria-valuemax",String(l)):s.removeAttribute("aria-valuemax"),Number.isFinite(t)?s.setAttribute("aria-valuemin",String(t)):s.removeAttribute("aria-valuemin"),s.setAttribute("aria-valuenow",W.currentValue||0===W.currentValue?String(W.currentValue):""),s.setAttribute("aria-disabled",String(ae.value)),!e(u)&&null!=u){let e=Number(u);Number.isNaN(e)&&(e=null),r(P,e)}s.addEventListener("wheel",be,{passive:!1})})),v((()=>{var e,a;const t=null==(e=R.value)?void 0:e.input;null==t||t.setAttribute("aria-valuenow",`${null!=(a=W.currentValue)?a:""}`)})),l({focus:()=>{var e,a;null==(a=null==(e=R.value)?void 0:e.focus)||a.call(e)},blur:()=>{var e,a;null==(a=null==(e=R.value)?void 0:e.blur)||a.call(e)}}),(e,a)=>(f(),b("div",{class:g([V(M).b(),V(M).m(V(ee)),V(M).is("disabled",V(ae)),V(M).is("without-controls",!e.controls),V(M).is("controls-right",V(X))]),onDragstart:k((()=>{}),["prevent"])},[e.controls?N((f(),b("span",{key:0,role:"button","aria-label":V(K)("el.inputNumber.decrease"),class:g([V(M).e("decrease"),V(M).is("disabled",V(H))]),onKeydown:I(se,["enter"])},[h(e.$slots,"decrease-icon",{},(()=>[x(V(A),null,{default:S((()=>[V(X)?(f(),E(V(w),{key:0})):(f(),E(V(F),{key:1}))])),_:1})]))],42,["aria-label","onKeydown"])),[[V(z),se]]):y("v-if",!0),e.controls?N((f(),b("span",{key:1,role:"button","aria-label":V(K)("el.inputNumber.increase"),class:g([V(M).e("increase"),V(M).is("disabled",V(J))]),onKeydown:I(ue,["enter"])},[h(e.$slots,"increase-icon",{},(()=>[x(V(A),null,{default:S((()=>[V(X)?(f(),E(V(j),{key:0})):(f(),E(V($),{key:1}))])),_:1})]))],42,["aria-label","onKeydown"])),[[V(z),ue]]):y("v-if",!0),x(V(L),{id:e.id,ref_key:"input",ref:R,type:"number",step:e.step,"model-value":V(te),placeholder:e.placeholder,readonly:e.readonly,disabled:V(ae),size:V(ee),max:e.max,min:e.min,name:e.name,"aria-label":e.ariaLabel,"validate-event":!1,onKeydown:[I(k(ue,["prevent"]),["up"]),I(k(se,["prevent"]),["down"])],onBlur:pe,onFocus:ce,onInput:me,onChange:de},_({_:2},[e.$slots.prefix?{name:"prefix",fn:S((()=>[h(e.$slots,"prefix")]))}:void 0,e.$slots.suffix?{name:"suffix",fn:S((()=>[h(e.$slots,"suffix")]))}:void 0]),1032,["id","step","model-value","placeholder","readonly","disabled","size","max","min","name","aria-label","onKeydown"])],42,["onDragstart"]))}}),[["__file","input-number.vue"]]));export{H as E};
