<script setup lang="ts">
import IScrollList from "@/components/IScrollList/index.vue";
import livePreview, { PagePreviewSessionVO } from "@/api/live/livePreview";

const listQuery = ref<PageQuery>({
  pageNum: 1,
  pageSize: 10,
});
const modelValue = defineModel("modelValue", {
  type: Object,
  required: false,
  default: {} as PagePreviewSessionVO,
});

function onListUpdate(e: PagePreviewSessionVO[]) {
  if (!modelValue.value.id) {
    modelValue.value = e[0];
  }
}
</script>

<template>
  <i-scroll-list
    ref="sessionListRef"
    v-model="listQuery"
    style="
      --i-scroll-list-padding: 10px 10px 0 10px;
      --i-scroll-list-item-margin-bottom: 10px;
      --i-scroll-list-divider-bg: #f8f9fa;
    "
    auto
    :api="livePreview.list as any"
    @update:list="onListUpdate"
  >
    <template #default="{ item }">
      <div
        class="session-item"
        :class="{ 'select-session-item': (item as any).id === modelValue.id }"
        @click="modelValue = item"
      >
        <div class="session-item-name">{{ (item as any).title }}</div>
        <div class="session-item-user">
          <el-icon
            size="12"
            style="color: var(--el-color-primary); margin-right: 2px; transform: translateY(2px)"
          >
            <Avatar />
          </el-icon>
          {{ (item as any).anchorName }}
        </div>
      </div>
    </template>
  </i-scroll-list>
</template>

<style scoped lang="scss">
.session-item {
  width: 175px;
  background: #ffffff;
  border-radius: 6px;
  padding: 12px 14px 18px 12px;
  cursor: pointer;
  margin-bottom: 10px;
  overflow: hidden;
  border: 1px solid #ffffff;
  &-name {
    font-weight: 500;
    height: 37px;
    font-size: 12px;
    color: #666666;
    line-height: 17px;
  }

  &-user {
    font-size: 12px;
    color: #8d9caf;
    margin-top: 8px;
  }
}

.select-session-item {
  background: #ffffff;
  position: relative;
  border: 1px solid var(--el-color-primary);
  &:after {
    position: absolute;
    content: " ";
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #e7f3ff 0%, rgba(231, 243, 255, 0) 100%);
    left: 0;
    top: 0;
    z-index: 1;
  }

  .session-item-name {
    color: #304156;
    position: relative;
    z-index: 2;
  }

  .session-item-user {
    color: var(--el-color-primary);
    position: relative;
    z-index: 2;
  }
}
</style>
