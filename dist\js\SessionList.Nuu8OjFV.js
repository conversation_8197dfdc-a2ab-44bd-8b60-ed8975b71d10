import{d as e,r as s,ai as i,ap as a,e as t,f as o,i as l,ak as r,w as m,C as d,F as p,m as n,$ as u,E as c,n as f}from"./index.Dk5pbsTU.js";import{I as v,L as x}from"./index.CgJfYQHq.js";import{_ as j}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./el-divider.2VNIZioN.js";import"./el-empty.Dee0wMKK.js";import"./index.D6CER_Ot.js";import"./commonSetup.Dm-aByKQ.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";const _=["onClick"],g={class:"session-item-name"},y={class:"session-item-user"},V=j(e({__name:"SessionList",props:{modelValue:{type:Object,required:!1,default:{}},modelModifiers:{}},emits:["update:modelValue"],setup(e){const j=s({pageNum:1,pageSize:10}),V=i(e,"modelValue");function h(e){V.value.id||(V.value=e[0])}return(e,s)=>{const i=a("Avatar"),b=c;return o(),t(v,{ref:"sessionListRef",modelValue:l(j),"onUpdate:modelValue":s[0]||(s[0]=e=>r(j)?j.value=e:null),style:{"--i-scroll-list-padding":"10px 10px 0 10px","--i-scroll-list-item-margin-bottom":"10px","--i-scroll-list-divider-bg":"#f8f9fa"},auto:"",api:l(x).list,"onUpdate:list":h},{default:m((({item:e})=>[d("div",{class:f(["session-item",{"select-session-item":e.id===V.value.id}]),onClick:s=>V.value=e},[d("div",g,p(e.title),1),d("div",y,[n(b,{size:"12",style:{color:"var(--el-color-primary)","margin-right":"2px",transform:"translateY(2px)"}},{default:m((()=>[n(i)])),_:1}),u(" "+p(e.anchorName),1)])],10,_)])),_:1},8,["modelValue","api"])}}}),[["__scopeId","data-v-d77d91de"]]);export{V as default};
