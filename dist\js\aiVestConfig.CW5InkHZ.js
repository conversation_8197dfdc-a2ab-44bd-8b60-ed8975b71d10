import{aT as t}from"./index.Dk5pbsTU.js";function e(e){return t({url:"/system/aiVestConfig/list",method:"get",params:e})}function s(e){return t({url:"/system/aiVestConfig",method:"post",data:e})}function r(e,s){return t({url:`/system/aiVestConfig/${e}/status`,method:"put",params:{status:s}})}function n(e){return t({url:`/system/aiVestConfig/${e}`,method:"delete"})}function u(e){return t({url:`/system/aiVestConfig/vest/${e}`,method:"get"})}function a(){return t({url:"/system/aiVestConfig/enabled-trigger-rules",method:"get"})}function i(e){return t({url:"/ai-vest/trigger-rules/list",method:"get",params:e})}function o(e){return t({url:"/ai-vest/trigger-rules/saveOrUpdate",method:"post",data:e})}function g(e,s){return t({url:"/ai-vest/trigger-rules/status",method:"post",data:{id:e,enabled:s}})}function l(e){return t({url:`/ai-vest/trigger-rules/${e}`,method:"delete"})}function d(e){return t({url:`/ai-vest/trigger-rules/${e}/detail`,method:"get"})}function m(){return t({url:"/ai-vest/trigger-rules/condition-types",method:"get"})}function f(){return t({url:"/system/aiVestConfig/getVestUserType",method:"get"})}export{a,e as b,u as c,n as d,m as e,i as f,f as g,d as h,o as i,l as j,g as k,s,r as u};
