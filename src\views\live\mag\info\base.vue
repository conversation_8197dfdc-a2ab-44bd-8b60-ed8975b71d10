<template>
  <el-descriptions border label-width="100px" :column="1">
    <el-descriptions-item label="直播名称">{{ info?.title }}</el-descriptions-item>
    <el-descriptions-item label="主播">{{ info?.anchorName }}</el-descriptions-item>
    <!--    <el-descriptions-item label="助理">{{ info?.assistList.join("、") }}</el-descriptions-item>-->
    <el-descriptions-item label="开播时间">{{ info?.actualStartTime }}</el-descriptions-item>
    <el-descriptions-item label="开播时长">{{ info?.durationStr }}</el-descriptions-item>
    <el-descriptions-item label="评论数">{{ info?.commentCount }}</el-descriptions-item>
    <el-descriptions-item label="观看用户">{{ info?.watchCount }}</el-descriptions-item>
    <el-descriptions-item v-if="info?.status === 2" label="直播回放">
      <video
        :controls="true"
        :poster="info?.coverUrl"
        :src="info?.replayUrl"
        style="width: 200px; height: 200px"
      />
    </el-descriptions-item>
  </el-descriptions>
</template>
<script setup lang="ts">
import LiveSessionApi, { LiveSessionBaseInfoVo } from "@/api/live/liveSession";

const props = defineProps({
  sessionId: {
    type: Number,
    default: -1,
  },
});
const info = ref<LiveSessionBaseInfoVo>();
LiveSessionApi.baseInfo(props.sessionId).then((res) => {
  info.value = res;
});
</script>
