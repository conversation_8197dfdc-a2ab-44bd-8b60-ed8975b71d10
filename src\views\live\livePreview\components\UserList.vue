<script setup lang="ts">
import livePreview, { PageSessionUserDto } from "@/api/live/livePreview";
import IScrollList from "@/components/IScrollList/index.vue";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import useClipboard from "vue-clipboard3";

const { toClipboard } = useClipboard();
dayjs.extend(duration);

const props = defineProps({
  sessionId: Number,
  info: {
    type: Object,
    default: () => ({}),
  },
});
const userListRef = ref<any>();
const userListQuery = ref<PageSessionUserDto>(new PageSessionUserDto());
watch(
  () => props.info.inviterId,
  (_newVal: number) => {
    if (_newVal) {
      userListQuery.value.sessionId = String(props.sessionId);
      userListQuery.value.inviterId = String(_newVal);
      userListQuery.value.type = "0";
      userListQuery.value.pageNum = 1;
      nextTick(() => {
        userListRef.value?.reset();
      });
    }
  },
  { deep: true, immediate: true }
);

function convertSecondsToHMS(seconds: number) {
  const dur = dayjs.duration(seconds, "seconds");
  const hours = Math.floor(dur.asHours());
  const minutes = dur.minutes();
  const secs = dur.seconds();

  if (hours > 0) {
    return `${hours}h${minutes}min`;
  } else if (minutes > 0) {
    return `${minutes}min`;
  } else {
    return `${secs}s`;
  }
}

function onCopyId(_id: number) {
  toClipboard(String(_id))
    .then(() => {
      ElMessage.success("复制成功");
    })
    .catch(() => {
      ElMessage.error("复制失败");
    });
}
</script>

<template>
  <div class="user-list">
    <i-scroll-list
      ref="userListRef"
      v-model="userListQuery"
      class="user-scroll-list"
      style="
        --i-scroll-list-padding: 10px 10px 0 10px;
        --i-scroll-list-divider-bg: #f8f9fa;
        --i-scroll-list-item-margin-bottom: 10px;
      "
      :api="livePreview.sessionUser as any"
    >
      <template #default="{ item }">
        <div class="user-item">
          <div class="user-item-id">
            <div class="id-info">
              UID：{{ (item as any).userId }}
              <span class="copy" @click="onCopyId((item as any).userId)">复制</span>
            </div>
            <div class="time">
              累计观看：
              <span class="time-info">{{ convertSecondsToHMS((item as any).duration) }}</span>
            </div>
          </div>
          <div class="user-info">
            <img class="avatar" :src="(item as any).avatar" />
            <div class="base-info">
              <div class="name">{{ (item as any).nickname }}</div>
              <div class="address">
                IP地址：
                <span class="address-info">{{ (item as any).ipAddress }}</span>
                <span class="address-info" style="margin-left: 8px">
                  {{ (item as any).province }}|{{ (item as any).city }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </i-scroll-list>
  </div>
</template>

<style scoped lang="scss">
.user-scroll-list {
  background: #f8f9fa;

  :deep(.i-scroll-list-body) {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .i-scroll-list-li {
      width: calc(50% - 5px);
    }

    @media (width <= 1550px) {
      .i-scroll-list-li {
        width: 100% !important;
      }
    }
  }
}

.user-list {
  background: #f8f9fa;
  width: 100%;
  height: 100%;

  .user-item {
    width: 100%;
    background: #ffffff;
    padding: 8px 13px 15px 13px;
    border-radius: 4px;

    &-id {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .id-info {
        font-weight: 400;
        font-size: 11px;
        color: #999999;
      }

      .copy {
        font-weight: 400;
        font-size: 11px;
        color: #666666;
        margin-left: 5px;
        cursor: pointer;
      }

      .time {
        font-weight: 400;
        font-size: 10px;
        color: #999999;

        &-info {
          color: var(--el-color-primary);
        }
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      margin-top: 11px;

      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 40px;
      }

      .base-info {
        flex: 1;
        margin-left: 10px;

        .name {
          font-weight: 500;
          font-size: 12px;
          color: #333333;
        }

        .address {
          margin-top: 6px;
          font-weight: 400;
          font-size: 10px;
          color: #999999;

          &-info {
            font-weight: 400;
            font-size: 10px;
            color: #333333;
          }
        }
      }
    }
  }
}
</style>
