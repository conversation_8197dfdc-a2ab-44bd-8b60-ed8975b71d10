import{o as e,R as t,a6 as n,J as o}from"./index.Dk5pbsTU.js";const s=(s,a,i,u)=>{const m={offsetX:0,offsetY:0},f=(e,t)=>{if(s.value){const{offsetX:n,offsetY:a}=m,i=s.value.getBoundingClientRect(),f=i.left,l=i.top,v=i.width,d=i.height,r=-f+n,c=-l+a,E=document.documentElement.clientWidth-f-v+n,h=document.documentElement.clientHeight-l-d+a;(null==u?void 0:u.value)||(e=Math.min(Math.max(e,r),E),t=Math.min(Math.max(t,c),h)),m.offsetX=e,m.offsetY=t,s.value.style.transform=`translate(${o(e)}, ${o(t)})`}},l=e=>{const t=e.clientX,n=e.clientY,{offsetX:o,offsetY:s}=m,a=e=>{const a=o+e.clientX-t,i=s+e.clientY-n;f(a,i)},i=()=>{document.removeEventListener("mousemove",a),document.removeEventListener("mouseup",i)};document.addEventListener("mousemove",a),document.addEventListener("mouseup",i)},v=()=>{a.value&&s.value&&(a.value.removeEventListener("mousedown",l),window.removeEventListener("resize",d))},d=()=>{const{offsetX:e,offsetY:t}=m;f(e,t)};return e((()=>{t((()=>{i.value?a.value&&s.value&&(a.value.addEventListener("mousedown",l),window.addEventListener("resize",d)):v()}))})),n((()=>{v()})),{resetPosition:()=>{m.offsetX=0,m.offsetY=0,s.value&&(s.value.style.transform="")},updatePosition:d}};export{s as u};
