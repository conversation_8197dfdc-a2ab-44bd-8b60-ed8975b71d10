<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px">
    <el-form ref="editFormRef" :model="formData" :rules="rules" label-width="110px">
      <el-form-item label="直播名称" prop="title">
        <el-input
          v-model="formData.title"
          :maxlength="50"
          show-word-limit
          placeholder="请输入直播名称"
        />
      </el-form-item>
      <el-form-item label="直播间" prop="roomId">
        <el-select v-model="formData.roomId" style="width: 100%">
          <el-option
            v-for="(item, index) in roomList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="主播" prop="anchorId">
        <el-select v-model="formData.anchorId" style="width: 100%">
          <el-option
            v-for="(item, index) in anchorList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="助理">-->
      <!--        <el-select v-model="formData.assistIdList" multiple style="width: 100%">-->
      <!--          <el-option-->
      <!--            v-for="(item, index) in assistList"-->
      <!--            :key="index"-->
      <!--            :label="item.label"-->
      <!--            :value="item.value"-->
      <!--          />-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <el-form-item label="封面图" prop="coverUrl">
        <SingleImageUpload
          v-model="formData.coverUrl"
          :style="{ width: '100px', height: '100px' }"
        />
      </el-form-item>
      <el-form-item label="是否私密直播" prop="isSecret">
        <el-switch v-model="isSecretSwitch" />
      </el-form-item>
      <el-form-item v-if="isSecretSwitch" label="口令密码" prop="secretKey">
        <div class="flex items-center">
          <el-input v-model="formData.secretKey" placeholder="请输入口令密码" />
          <el-button type="primary" class="ml-2" @click="generateSecretKey">生成口令</el-button>
        </div>
        <div class="el-form-item-msg">可手动编辑或点击生成按钮随机生成</div>
      </el-form-item>
      <el-form-item label="预计开播时间" prop="startTime">
        <el-date-picker
          v-model="formData.startTime"
          style="width: 100%"
          type="datetime"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm"
          placeholder="选择日期时间"
        />
      </el-form-item>
      <el-form-item label="本场直播介绍" prop="description">
        <el-input
          v-model="formData.description"
          :maxlength="200"
          show-word-limit
          type="textarea"
          :rows="5"
          placeholder="请输入直播间介绍"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="dialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import LiveSessionApi, { LiveSessionDto, LiveSessionPageVo } from "@/api/live/liveSession";

const dialog = reactive({
  visible: false,
  title: "",
});
const formData = ref(new LiveSessionDto());
const editFormRef = ref();
const emits = defineEmits(["success"]);
const loading = ref(false);
// eslint-disable-next-line no-undef
const anchorList = ref<SelectVo[]>([]);
// eslint-disable-next-line no-undef
const roomList = ref<SelectVo[]>([]);
// eslint-disable-next-line no-undef
const assistList = ref<SelectVo[]>([]);

function getSelectList() {
  LiveSessionApi.anchorList().then((res) => {
    anchorList.value = res;
  });
  LiveSessionApi.roomList().then((res) => {
    roomList.value = res;
  });
  LiveSessionApi.assistList().then((res) => {
    assistList.value = res;
  });
}

const isSecretSwitch = computed({
  get: () => formData.value.isSecret === 1,
  set: (val) => {
    formData.value.isSecret = val ? 1 : 0;
    if (!val) {
      formData.value.secretKey = "";
    }
  },
});

const rules = {
  title: [{ required: true, message: "请输入直播名称", trigger: "blur" }],
  coverUrl: [{ required: true, message: "请上传封面图", trigger: "blur" }],
  anchorId: [{ required: true, message: "请选择主播", trigger: "blur" }],
  roomId: [{ required: true, message: "请选择直播间", trigger: "blur" }],
  startTime: [{ required: true, message: "请选择预计开播时间", trigger: "blur" }],
  logo: [{ required: true, message: "请上传logo", trigger: "blur" }],
  secretKey: [
    {
      required: true,
      message: "请输入口令密码",
      trigger: "blur",
      validator: (_: any, value: any, callback: any) => {
        if (formData.value.isSecret === 1 && !value) {
          return callback(new Error("请输入口令密码"));
        }
        callback();
      },
    },
  ],
  assistIdList: [
    {
      required: true,
      message: "请选择助理",
      trigger: "blur",
      validator: (_: any, value: any, callback: any) => {
        if (!value?.length) {
          return callback(new Error("请选择助理"));
        } else {
          callback();
        }
      },
    },
  ],
};

function handleSubmit() {
  editFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      LiveSessionApi.save(formData.value)
        .then(() => {
          ElMessage.success("保存成功");
          dialog.visible = false;
          emits("success");
        })
        .finally(() => (loading.value = false));
    }
  });
}

function generateSecretKey() {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < 7; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  formData.value.secretKey = result;
}

defineExpose({
  open: (_row?: LiveSessionPageVo, _isCopy: boolean = false) => {
    getSelectList();
    formData.value = new LiveSessionDto(_row);
    if (_isCopy) {
      formData.value.id = "";
      formData.value.anchorId = "";
    }
    dialog.title = formData.value?.id ? "编辑直播场次" : "新增直播间场次";
    dialog.visible = true;
  },
});
</script>
