<template>
  <div class="app-container" style="padding: 0">
    <div v-if="!hideHead" class="search-bar" style="box-shadow: none">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="keywords" label="评论内容">
          <el-input v-model="page.query.keyWords" />
        </el-form-item>
        <el-form-item prop="status" label="发言时间">
          <date-range-picker
            v-model:start="page.query.startTime"
            v-model:end="page.query.endTime"
            is-split
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        :row-class-name="
          (row: any) =>
            row.userType === 1 ? 'webcaster-row' : row.userType === 2 ? 'assistant-row' : ''
        "
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="发送人" align="center" width="150">
          <template #default="{ row }">
            <span v-if="row.userType === 1">【主播】</span>
            <span v-else-if="row.userType === 2">【助理】</span>
            <span v-else-if="row.userType === 3">【销售】</span>
            {{ row.commentUsername }}
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" width="100">
          <template #default="{ row }">
            <dict-label v-model="row.sendType" code="msg_send_type" />
          </template>
        </el-table-column>
        <el-table-column label="发言信息" align="center" prop="content" min-width="120" />
        <el-table-column label="发言时间" align="center" prop="createdAt" min-width="120" />
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
  </div>
</template>
<script setup lang="ts">
import LiveSessionApi, { CommentListQueryPageDto, PageLiveCommentVo } from "@/api/live/liveSession";
import { usePage } from "@/utils/commonSetup";

const props = defineProps({
  hideHead: {
    type: Boolean,
    default: false,
  },
  sessionId: {
    type: Number,
    default: -1,
  },
  userId: {
    type: String,
    default: "",
  },
});
const { page, getPage, resetQuery } = usePage<CommentListQueryPageDto, PageLiveCommentVo>(
  new CommentListQueryPageDto(props.sessionId, props.userId),
  LiveSessionApi.commentList
);
</script>

<style scoped lang="scss">
:deep(.webcaster-row) {
  background: var(--el-color-warning-light-8) !important;

  td {
    background: var(--el-color-warning-light-8) !important;
  }
}

:deep(.assistant-row) {
  background: var(--el-color-warning-light-8) !important;

  td {
    background: var(--el-color-warning-light-8) !important;
  }
}
</style>
