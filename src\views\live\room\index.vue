<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="keywords" label="">
          <el-input
            v-model="page.query.keywords"
            placeholder="直播间名称检索"
            clearable
            @keyup.enter="getPage"
          />
        </el-form-item>
        <el-form-item prop="status" label="直播间状态">
          <dict v-model="page.query.status" code="live_status" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          v-hasPerm="['live:room:save']"
          type="success"
          icon="plus"
          @click="cloneRoomRef?.open()"
        >
          新增
        </el-button>
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="封面图" align="center" prop="name" width="100">
          <template #default="{ row }">
            <el-image
              style="width: 30px; height: 30px"
              :preview-src-list="[row.coverUrl]"
              preview-teleported
              :src="row.coverUrl"
            />
          </template>
        </el-table-column>
        <el-table-column label="直播间名称" align="center" prop="roomName" min-width="120" />
        <el-table-column label="累计开播次数" align="center" prop="liveCount" min-width="100" />
        <el-table-column label="直播间状态" align="center" width="120">
          <template #default="{ row }">
            <dict-label v-model="row.status" code="live_status" />
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="280">
          <template #default="scope">
            <el-button
              v-hasPerm="['live:room:save']"
              type="primary"
              size="small"
              link
              icon="edit"
              @click="editModelRef?.open(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-hasPerm="['live:quick-reply']"
              type="primary"
              size="small"
              link
              icon="chat-line-round"
              @click="quickReplyRef?.open(scope.row)"
            >
              快捷语设置
            </el-button>
            <el-button
              v-hasPerm="['live:vest']"
              type="warning"
              size="small"
              link
              icon="user"
              @click="handleVestManage(scope.row)"
            >
              马甲设置
            </el-button>
            <el-button
              v-if="
                scope.row.status === 0 && scope.row.liveCount <= 0 && hasAuth('live:room:delete')
              "
              type="danger"
              size="small"
              link
              icon="delete"
              :loading="scope.row.loading"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
    <edit-model ref="editModelRef" @success="resetQuery" />
    <quick-reply ref="quickReplyRef" @success="getPage" />
    <vest-model ref="vestModelRef" :room-id="selectedRoomId" @success="getPage" />
    <clone-room ref="cloneRoomRef" @success="resetQuery" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import EditModel from "./edit.vue";
import QuickReply from "./quick-reply.vue";
import VestModel from "./vest.vue";

defineOptions({ name: "LiveRoom" });
import { usePage } from "@/utils/commonSetup";
import LiveRoomApi, { LiveRoomPageDto, LiveRoomPageVo } from "@/api/live/liveRoom";
import { hasAuth } from "@/plugins/permission";
import CloneRoom from "./cloneRoom.vue";
const cloneRoomRef = ref<InstanceType<typeof CloneRoom>>();
// Extend LiveRoomPageVo to include loading property
interface ExtendedLiveRoomPageVo extends LiveRoomPageVo {
  loading?: boolean;
}

const { page, getPage, resetQuery } = usePage<LiveRoomPageDto, ExtendedLiveRoomPageVo>(
  new LiveRoomPageDto(),
  LiveRoomApi.page
);

// Component refs
const editModelRef = ref<InstanceType<typeof EditModel>>();
const quickReplyRef = ref<InstanceType<typeof QuickReply>>();
const vestModelRef = ref<InstanceType<typeof VestModel>>();

// Selected room ID for vest management
const selectedRoomId = ref(0);

function handleVestManage(row: ExtendedLiveRoomPageVo) {
  selectedRoomId.value = row.id;
  vestModelRef.value?.open();
}

function handleDelete(_row: ExtendedLiveRoomPageVo) {
  ElMessageBox.confirm(`确定删除直播间《${_row.roomName}》吗？`, `删除`, {
    confirmButtonText: "确定删除",
    cancelButtonText: "取消",
    confirmButtonClass: "el-button--danger",
    type: "error",
  })
    .then(() => {
      _row.loading = true;
      LiveRoomApi.remove(_row.id)
        .then(() => {
          ElMessage.success("删除成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}
</script>
