import{d as e,S as l,r as a,e as t,f as s,w as o,m as i,i as r,g as d,P as m,Q as u,C as p,$ as n,az as f}from"./index.Dk5pbsTU.js";import{E as _}from"./el-drawer.Df_TzNjH.js";import"./el-overlay.DpVCS8zG.js";import{E as c}from"./el-button.CXI119n4.js";import{E as v,a as g}from"./el-form-item.Bw6Zyv_7.js";import{E as b,a as j}from"./el-col.Cfu8vZQ4.js";import{_ as y}from"./index.vue_vue_type_script_setup_true_lang.BOVTspW2.js";/* empty css               */import{E as w,a as V}from"./el-select.CRWkm-it.js";/* empty css                     */import"./el-popper.Dbn4MgsT.js";import{E as x}from"./el-input.DiGatoux.js";import{A as h,K as E,a as k}from"./knowledgeType.DrndLWHa.js";import{a as I}from"./commonSetup.Dm-aByKQ.js";const S={class:"dialog-footer"},N=e({__name:"edit",emits:["success"],setup(e,{expose:N,emit:U}){const C=l({visible:!1,title:""}),{sendInfo:K,onSend:q}=I(k.allKnowledgeType,new E);K.params.status="1";const z=a(new h),A=a(),G=U,T=a(!1),F={fileName:[{required:!0,message:"请输入文件名称",trigger:"blur"}],typeId:[{required:!0,message:"请选择文件类型",trigger:"blur"}]};function H(){A.value.validate((e=>{e&&(T.value=!0,k.aiKnowledgeSave(z.value).then((()=>{f.success("保存成功"),C.visible=!1,G("success")})).finally((()=>T.value=!1)))}))}return N({open:e=>{C.visible=!0,C.title=(null==e?void 0:e.id)?"编辑知识库文件":"新增知识库文件",z.value=new h(e),q()}}),(e,l)=>{const a=x,f=g,h=j,E=V,k=w,I=y,N=b,U=v,q=c,G=_;return s(),t(G,{modelValue:r(C).visible,"onUpdate:modelValue":l[4]||(l[4]=e=>r(C).visible=e),title:r(C).title,size:"80vw"},{footer:o((()=>[p("div",S,[i(q,{type:"primary",onClick:H},{default:o((()=>l[5]||(l[5]=[n("确 定")]))),_:1,__:[5]}),i(q,{onClick:l[3]||(l[3]=e=>r(C).visible=!1)},{default:o((()=>l[6]||(l[6]=[n("取 消")]))),_:1,__:[6]})])])),default:o((()=>[i(U,{ref_key:"editFormRef",ref:A,model:r(z),rules:F,"label-width":"80px"},{default:o((()=>[i(N,{gutter:20},{default:o((()=>[i(h,{span:12},{default:o((()=>[i(f,{label:"文件名",prop:"fileName"},{default:o((()=>[i(a,{modelValue:r(z).fileName,"onUpdate:modelValue":l[0]||(l[0]=e=>r(z).fileName=e),maxlength:50,"show-word-limit":"",placeholder:"请输入文件名"},null,8,["modelValue"])])),_:1})])),_:1}),i(h,{span:12},{default:o((()=>[i(f,{label:"文件类型",prop:"typeId"},{default:o((()=>[i(k,{modelValue:r(z).typeId,"onUpdate:modelValue":l[1]||(l[1]=e=>r(z).typeId=e),style:{width:"200px"}},{default:o((()=>[(s(!0),d(m,null,u(r(K).data,(e=>(s(),t(E,{key:e.id,label:e.name,value:String(e.id)},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),i(h,{span:24},{default:o((()=>[i(I,{modelValue:r(z).content,"onUpdate:modelValue":l[2]||(l[2]=e=>r(z).content=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}});export{N as _};
