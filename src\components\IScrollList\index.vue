<template>
  <div class="i-scroll-list">
    <el-scrollbar ref="iScrollBodyRef" height="100%" @scroll="onScroll">
      <div ref="iScrollListBody">
        <slot name="head" />
        <div v-if="!hideItem" class="i-scroll-list-body">
          <div v-for="(item, index) in allList" :key="index" class="i-scroll-list-li">
            <slot :item="item" :itemIndex="index" />
          </div>
        </div>
        <slot :data="allList" name="allList" />
        <template v-if="!sendInfo.loading && !sendInfo?.data?.totalRow">
          <slot name="empty">
            <el-empty :image-size="30" :title="emptyTitle" />
          </slot>
        </template>
        <el-divider
          v-else-if="!((sendInfo?.data?.totalRow || 0) <= allList.length)"
          class="i-scroll-list-divider"
          style="max-width: 220px; margin: 50px auto; background: none; font-size: 12px"
        >
          <template v-if="sendInfo.loading">加载中...</template>
          <template v-else-if="(sendInfo?.data?.totalRow || 0) <= allList.length">
            已经到底了
          </template>
          <template v-else>加载更多</template>
        </el-divider>
      </div>
    </el-scrollbar>
  </div>
</template>

<script generic="R, Q extends PageQuery" lang="ts" setup>
import { ElScrollbar } from "element-plus";
import { ref, computed, shallowRef, onActivated, nextTick } from "vue";
import { SendApiReactive, useApi } from "@/utils/commonSetup";

interface DefineListProps {
  item: R;
  itemIndex: number;
}

defineSlots<{
  /* 默认列表 */
  default(_props: DefineListProps): any;
  /* 头部 */
  head(): any;
  /* 空提示 */
  empty(): any;
  /* 全部列表 */
  allList(_props: { data: Array<R> }): any;
}>();
const props = withDefaults(
  defineProps<{
    /** 无数据提示 */
    emptyTitle?: string;
    /* 请求信息 */
    modelValue?: Q;
    /** 列表数据 */
    list?: Array<R>;
    hideItem?: boolean;
    auto?: boolean;
    // 请求api
    api: SendApiReactive<Q, PageResult<R[]>>;
  }>(),
  { emptyTitle: "暂无数据", hideItem: false, auto: false }
);
const iScrollBodyRef = ref<InstanceType<typeof ElScrollbar>>();
const emits = defineEmits<{
  (_res: "update:modelValue", _query: Q): void;
  (_res: "update:list", _list: Array<R>): void;
}>();
const query = computed<Q>({
  get: () => props.modelValue as Q,
  set: (_val: Q) => emits("update:modelValue", _val),
});
const allList = shallowRef<Array<R>>([]);
const { sendInfo, onSend } = useApi<Q, PageResult<R[]>>(props.api, props.modelValue, (res: any) => {
  allList.value = allList.value.concat(allList.value, res!.records);
  emits("update:list", allList.value as Array<R>);
});

/** 滚动条高度 */
const nowScrollTop = ref<number>(0);
/** 是否可以请求 */
const canRequest = computed<boolean>(
  () => !sendInfo.loading && (sendInfo.data?.totalRow || 0) > allList.value.length
);
/** 下一页 */
const nextPage = () => {
  if (canRequest.value) {
    query.value.pageNum += 1;
    onSend();
  }
};
const onScroll = ({ scrollTop }: any) => {
  if (sendInfo.loading) return;
  const scrollHeight = iScrollBodyRef.value?.wrapRef?.scrollHeight || 0;
  const clientHeight = iScrollBodyRef.value?.wrapRef?.clientHeight || 0;
  nowScrollTop.value = scrollTop;
  // 滚动到底部时
  if (scrollHeight - clientHeight + 100 >= scrollTop) {
    nextPage();
  }
};
onActivated(() => {
  if (nowScrollTop.value > 0) {
    nextTick(() => {
      iScrollBodyRef.value?.setScrollTop(nowScrollTop.value);
    });
  }
});
if (props.auto) onSend();
// 重置列表
const reset = () => {
  allList.value = [];
  emits("update:list", []);
  query.value.pageNum = 1;
  onSend();
};
defineExpose({
  // 重置列表
  reset: () => reset(),
});
</script>

<style lang="scss">
.i-scroll-list-divider {
  & > .el-divider__text {
    background-color: var(--i-scroll-list-divider-bg, #ffffff) !important;
  }
}
</style>
<style lang="scss" scoped>
.i-scroll-list {
  padding: var(--i-scroll-list-padding, 0 16px);
  margin: 0;
  list-style: none;
  height: 100%;

  &-li {
    margin-bottom: var(--i-scroll-list-item-margin-bottom, 15px);
  }
}
</style>
