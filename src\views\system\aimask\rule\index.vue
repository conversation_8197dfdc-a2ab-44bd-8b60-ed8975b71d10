<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item prop="ruleName" label="规则名称">
          <el-input
            v-model="queryParams.ruleName"
            placeholder="请输入规则名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="description" label="规则描述">
          <el-input
            v-model="queryParams.description"
            placeholder="请输入规则描述"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="responseType" label="响应类型">
          <el-select
            v-model="queryParams.responseType"
            placeholder="请选择"
            clearable
            style="width: 150px"
          >
            <el-option label="静态回复" value="static" />
            <el-option label="动态生成" value="dynamic" />
          </el-select>
        </el-form-item>
        <el-form-item prop="enabled" label="状态">
          <el-select
            v-model="queryParams.enabled"
            placeholder="请选择"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleQuery">搜索</el-button>
          <el-button :icon="Refresh" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          v-hasPerm="['sys:aimask:config:saveOrUpdate']"
          type="success"
          :icon="Plus"
          @click="handleOpenDialog()"
        >
          添加新规则
        </el-button>
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="ruleList"
        highlight-current-row
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="规则名称" prop="ruleName" min-width="120" show-overflow-tooltip />
        <el-table-column
          label="规则描述"
          prop="description"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column label="响应类型" prop="responseTypeName" min-width="100" />
        <el-table-column label="优先级" prop="priority" min-width="80" align="center" />
        <el-table-column
          label="冷却时间(秒)"
          prop="cooldownSeconds"
          min-width="100"
          align="center"
        />
        <el-table-column label="状态" prop="enabled" min-width="80" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.enabled"
              :disabled="!checkPerms(['sys:aimask:config:saveOrUpdate']) || statusLoading"
              :loading="statusLoading && scope.row.id === currentStatusId"
              :active-value="1"
              :inactive-value="0"
              @change="(val) => handleStatusChange(scope.row, val)"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createdAt" min-width="160" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button
              v-hasPerm="['sys:aimask:config:saveOrUpdate']"
              type="primary"
              size="small"
              link
              :icon="Edit"
              @click="handleOpenDialog(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-hasPerm="['sys:aimask:config:delete']"
              type="danger"
              size="small"
              link
              :icon="Delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- AI马甲触发规则表单弹窗 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="700px"
      top="5vh"
      @close="handleCloseDialog"
    >
      <RuleForm
        ref="ruleFormRef"
        v-model:formData="formData"
        :condition-types="conditionTypes"
        :submit-loading="submitLoading"
        @submit="handleSubmit"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, ref } from "vue";
import type { FormInstance } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";
import { Delete, Edit, Plus, Refresh, Search } from "@element-plus/icons-vue";
import {
  type AiVestTriggerRuleDto,
  type AiVestTriggerRuleVo,
  type AiVestConditionTypeVo,
  deleteAiVestTriggerRule,
  getAiVestTriggerRuleDetail,
  getAiVestTriggerRuleList,
  saveOrUpdateAiVestTriggerRule,
  updateAiVestTriggerRuleStatus,
  getAiVestConditionTypes,
} from "@/api/system/aiVestConfig";
import { checkPerms } from "@/directive/permission";

// 导入分页组件和规则表单组件
const Pagination = defineAsyncComponent(() => import("@/components/Pagination/index.vue"));
const RuleForm = defineAsyncComponent(() => import("./components/RuleForm.vue"));

// 查询参数
const queryParams = reactive({
  ruleName: "",
  description: "",
  responseType: "",
  priority: "",
  enabled: "",
  pageNum: 1,
  pageSize: 20,
});

const loading = ref(false);
const ruleList = ref<AiVestTriggerRuleVo[]>([]);
const total = ref(0);

// 状态更新
const statusLoading = ref(false);
const currentStatusId = ref<number | null>(null);

// 提交状态
const submitLoading = ref(false);

// 对话框
const dialog = reactive({
  visible: false,
  title: "添加AI马甲触发规则",
});

// 表单引用
const ruleFormRef = ref();

// 引用
const queryFormRef = ref<FormInstance>();
const dataTableRef = ref();

// 表单数据
const defaultFormData: AiVestTriggerRuleDto = {
  ruleName: "",
  description: "",
  responseType: "static",
  staticResponse: "",
  dynamicPrompt: "",
  priority: 1,
  cooldownSeconds: 60,
  enabled: 1,
  conditions: [],
};

const formData = reactive<AiVestTriggerRuleDto>({ ...defaultFormData });

// 条件类型列表
const conditionTypes = ref<AiVestConditionTypeVo[]>([]);

// 获取条件类型列表
const fetchConditionTypes = async () => {
  try {
    const response: any = await getAiVestConditionTypes();
    conditionTypes.value = response || [];
  } catch (error) {
    console.error("获取条件类型列表失败", error);
    ElMessage.error("获取条件类型列表失败");
  }
};

// 查询列表
const handleQuery = () => {
  loading.value = true;
  getAiVestTriggerRuleList(queryParams)
    .then((res: any) => {
      const { list, total: totalCount } = res;
      ruleList.value = list;
      total.value = totalCount;
    })
    .finally(() => {
      loading.value = false;
    });
};

// 重置查询
const handleResetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
};

// 打开对话框
const handleOpenDialog = async (row?: AiVestTriggerRuleVo) => {
  dialog.visible = true;
  ruleFormRef.value?.resetSteps();

  if (row) {
    dialog.title = "编辑AI马甲触发规则";
    loading.value = true;

    try {
      // 通过API获取详情
      const detail = (await getAiVestTriggerRuleDetail(row.id)) as any;
      console.log("获取到的规则详情数据:", JSON.stringify(detail));

      // 将详情数据填充到表单
      Object.keys(formData).forEach((key) => {
        delete (formData as any)[key];
      });

      // 确保静态回复内容和动态生成提示词有默认值
      const detailData = {
        id: detail.id,
        ruleName: detail.ruleName,
        description: detail.description,
        responseType: detail.responseType,
        staticResponse: detail.staticResponse || "",
        dynamicPrompt: detail.dynamicPrompt || "",
        priority: detail.priority,
        cooldownSeconds: detail.cooldownSeconds,
        enabled: detail.enabled,
        conditions:
          detail.conditions?.map((item: any) => ({
            conditionTypeId: item.conditionTypeId,
            conditionConfig: item.conditionConfig,
            logicalOperator: item.logicalOperator,
          })) || [],
      };

      Object.assign(formData, JSON.parse(JSON.stringify(detailData)));

      console.log("编辑模式 - 初始化表单数据:", JSON.stringify(formData));
    } catch (error) {
      console.error("获取规则详情失败", error);
      ElMessage.error("获取规则详情失败");
      dialog.visible = false;
    } finally {
      loading.value = false;
    }
  } else {
    dialog.title = "添加AI马甲触发规则";
    // 清空现有数据
    Object.keys(formData).forEach((key) => {
      delete (formData as any)[key];
    });
    // 重新初始化默认值
    Object.assign(formData, JSON.parse(JSON.stringify(defaultFormData)));
    console.log("新增模式 - 初始化表单数据:", JSON.stringify(formData));
  }
};

// 关闭对话框
const handleCloseDialog = () => {
  dialog.visible = false;
};

// 提交表单
const handleSubmit = async (submitData: AiVestTriggerRuleDto) => {
  // 提交表单
  submitLoading.value = true;
  try {
    await saveOrUpdateAiVestTriggerRule(submitData);
    ElMessage.success(submitData.id ? "修改成功" : "添加成功");
    dialog.visible = false;
    handleQuery();
  } catch (error) {
    console.error("提交失败", error);
  } finally {
    submitLoading.value = false;
  }
};

// 删除规则
const handleDelete = (row: AiVestTriggerRuleVo) => {
  ElMessageBox.confirm(`确认删除规则"${row.ruleName}"吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    loading.value = true;
    try {
      await deleteAiVestTriggerRule(row.id);
      ElMessage.success("删除成功");
      handleQuery();
    } catch (error) {
      console.error("删除失败", error);
    } finally {
      loading.value = false;
    }
  });
};

// 修改状态
const handleStatusChange = async (row: AiVestTriggerRuleVo, enabled: any | number) => {
  statusLoading.value = true;
  currentStatusId.value = row.id;
  try {
    await updateAiVestTriggerRuleStatus(row.id, enabled);
    ElMessage.success("状态更新成功");
  } catch (error) {
    console.error("状态更新失败", error);
    row.enabled = enabled === 1 ? 0 : 1; // 恢复原状态
  } finally {
    statusLoading.value = false;
    currentStatusId.value = null;
  }
};

// 初始化数据
fetchConditionTypes();
handleQuery();
</script>
