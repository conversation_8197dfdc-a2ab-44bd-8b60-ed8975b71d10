import{d as e,r as t,aQ as a,g as o,f as s,C as r,m as i,w as l,Z as n,i as d,$ as m,V as p,e as u,h as c,a_ as g,az as j}from"./index.Dk5pbsTU.js";import{v as f}from"./el-loading.Dqi-qL7c.js";import{E as _}from"./el-card.DwLhVNHW.js";import y from"./index.Cywy93e7.js";import{a as k,E as v}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{_ as h}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as w}from"./el-switch.kQ5v4arH.js";/* empty css               */import{a as x,E as b}from"./el-form-item.Bw6Zyv_7.js";import{E as V}from"./el-button.CXI119n4.js";import{_ as C}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as q}from"./el-input.DiGatoux.js";import{_ as E}from"./edit.vue_vue_type_script_setup_true_lang.CwgBZ0bl.js";import{u as U}from"./commonSetup.Dm-aByKQ.js";import{a as T,S as O,K as P}from"./knowledgeType.DrndLWHa.js";/* empty css                       */import"./el-overlay.DpVCS8zG.js";import{E as R}from"./index.BcMfjWDS.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./event.BwRzfsZt.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.DuiNpp1i.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./validator.HGn2BZtD.js";import"./el-radio.w2rep3_A.js";import"./el-dialog.Cnp8BitR.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";const S={class:"app-container"},B={class:"search-bar"},K={class:"mb-10px"},L=e({name:"MaskKnowledgeType",__name:"index",setup(e){const L=t(),{page:z,getPage:$,resetQuery:M}=U(new P,T.page);return(e,t)=>{const U=q,P=x,I=C,Q=V,W=b,A=v,F=w,H=h,J=k,N=y,Z=_,D=a("hasPerm"),G=f;return s(),o("div",S,[r("div",B,[i(W,{ref:"queryFormRef",model:d(z).query,inline:!0},{default:l((()=>[i(P,{prop:"keywords",label:""},{default:l((()=>[i(U,{modelValue:d(z).query.keyWord,"onUpdate:modelValue":t[0]||(t[0]=e=>d(z).query.keyWord=e),placeholder:"类型名称检索",clearable:"",onKeyup:n(d($),["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),i(P,{prop:"status",label:"启用状态"},{default:l((()=>[i(I,{modelValue:d(z).query.status,"onUpdate:modelValue":t[1]||(t[1]=e=>d(z).query.status=e),code:"disable_status"},null,8,["modelValue"])])),_:1}),i(P,null,{default:l((()=>[i(Q,{type:"primary",icon:"search",onClick:d($)},{default:l((()=>t[6]||(t[6]=[m("搜索")]))),_:1,__:[6]},8,["onClick"]),i(Q,{icon:"refresh",onClick:d(M)},{default:l((()=>t[7]||(t[7]=[m("重置")]))),_:1,__:[7]},8,["onClick"])])),_:1})])),_:1},8,["model"])]),i(Z,{shadow:"never"},{default:l((()=>[r("div",K,[p((s(),u(Q,{type:"success",icon:"plus",onClick:t[2]||(t[2]=e=>{var t;return null==(t=d(L))?void 0:t.open()})},{default:l((()=>t[8]||(t[8]=[m(" 新增 ")]))),_:1,__:[8]})),[[D,["sys:aimask:knowledge:type:addOrEdit"]]])]),p((s(),u(J,{ref:"dataTableRef",data:d(z).data.records,"highlight-current-row":"",border:""},{default:l((()=>[i(A,{label:"序号",align:"center",width:"55",type:"index"}),i(A,{label:"类型名称",align:"center",prop:"name","min-width":"120"}),i(A,{label:"启用状态",align:"center",width:"120"},{default:l((({row:e})=>[d(g)("sys:aimask:knowledge:type:addOrEdit")?(s(),u(F,{key:0,modelValue:e.status,"onUpdate:modelValue":t=>e.status=t,"inline-prompt":"","active-text":"是","inactive-text":"否","inactive-value":0,"active-value":1,loading:e.statusLoading,"before-change":()=>{return t=e,new Promise(((e,a)=>{R.confirm(`确定要${t.status?"停用":"启用"}知识库类型《${t.name}》吗？`,(t.status?"停用":"启用")+"知识库类型",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((()=>{const o=new O(t);o.status?o.status=0:o.status=1,t.statusLoading=!0,T.saveOrEdit(o).then((()=>{j.success("操作成功"),e(!1),M()})).finally((()=>{t.statusLoading=!1,a(!1)}))})).catch((()=>{j.info("已取消"),a(!1)}))}));var t}},null,8,["modelValue","onUpdate:modelValue","loading","before-change"])):(s(),u(H,{key:1,modelValue:e.status,"onUpdate:modelValue":t=>e.status=t,code:"disable_status"},null,8,["modelValue","onUpdate:modelValue"]))])),_:1}),i(A,{fixed:"right",align:"center",label:"操作",width:"180"},{default:l((e=>[p((s(),u(Q,{type:"warning",size:"small",link:"",loading:e.row.loading,onClick:t=>{var a;return null==(a=d(L))?void 0:a.open(e.row)}},{default:l((()=>t[9]||(t[9]=[m(" 编辑 ")]))),_:2,__:[9]},1032,["loading","onClick"])),[[D,["sys:aimask:knowledge:type:addOrEdit"]]]),!e.row.isChoose&&d(g)("sys:aimask:knowledge:type:delete")?(s(),u(Q,{key:0,type:"danger",size:"small",link:"",loading:e.row.loading,onClick:t=>{return a=e.row,void R.confirm(`确定删除知识库类型《${a.name}》吗？`,"删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{a.loading=!0,T.remove(a.id).then((()=>{j.success("删除成功"),M()})).finally((()=>a.loading=!1))})).catch((()=>j.info("已取消")));var a}},{default:l((()=>t[10]||(t[10]=[m(" 删除 ")]))),_:2,__:[10]},1032,["loading","onClick"])):c("",!0)])),_:1})])),_:1},8,["data"])),[[G,d(z).loading]]),d(z).data.totalRow?(s(),u(N,{key:0,total:d(z).data.totalRow,"onUpdate:total":t[3]||(t[3]=e=>d(z).data.totalRow=e),page:d(z).query.pageNum,"onUpdate:page":t[4]||(t[4]=e=>d(z).query.pageNum=e),limit:d(z).query.pageSize,"onUpdate:limit":t[5]||(t[5]=e=>d(z).query.pageSize=e),onPagination:d($)},null,8,["total","page","limit","onPagination"])):c("",!0)])),_:1}),i(E,{ref_key:"editModelRef",ref:L,onSuccess:d(M)},null,8,["onSuccess"])])}}});export{L as default};
