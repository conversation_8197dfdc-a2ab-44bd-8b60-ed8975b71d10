import{d as s,r as e,I as a,ap as i,g as r,f as l,m as t,i as o,ak as n,w as d,C as p,$ as m,F as c,E as u,P as f,Q as v,h as y,n as x,az as g,a0 as b}from"./index.Dk5pbsTU.js";/* empty css                     */import{P as j,I as h,L as k}from"./index.CgJfYQHq.js";import{u as _}from"./index.CcWFbuMM.js";import{d as I}from"./dayjs.min.Bj2GcQlV.js";import{E as w}from"./index.ybpLT-bz.js";import{_ as C}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./el-divider.2VNIZioN.js";import"./el-empty.Dee0wMKK.js";import"./index.D6CER_Ot.js";import"./commonSetup.Dm-aByKQ.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";const U={style:{width:"100%",height:"100%",background:"#f8f9fa"}},$={class:"user-item-id"},O={class:"id-info"},P={style:{width:"0",flex:"1","margin-left":"12px"}},T={style:{display:"inline-flex"}},D={class:"status"},L={key:0,style:{color:"var(--el-color-danger)"}},S={key:1,style:{color:"var(--el-color-danger)"}},V={key:2,style:{color:"#666666"}},A={key:3,style:{color:"#999999"}},E={class:"base-info"},z=["src"],F={class:"user-info"},H={class:"name"},K={class:"user-info-bottom"},M={class:"user-info-bottom-item"},N={class:"value"},Q={class:"value",style:{"margin-left":"8px"}},R={class:"user-info-bottom-item",style:{"margin-left":"50px"}},X={class:"value"},Y=C(s({__name:"AllUserList",props:{info:{type:Object,default:()=>({})}},setup(s){const{toClipboard:C}=_(),Y=s,Z=e(),q=e(new j);function B(s){const e=I.duration(s,"seconds"),a=Math.floor(e.asHours()),i=e.minutes(),r=e.seconds();return a>0?`${a}h${i}min`:i>0?`${i}min`:`${r}s`}return a((()=>Y.info.id),(s=>{s&&(q.value.sessionId=String(Y.info.id),q.value.type="0",q.value.pageNum=1,b((()=>{var s;null==(s=Z.value)||s.reset()})))}),{deep:!0,immediate:!0}),(s,e)=>{const a=i("CopyDocument"),b=u,j=w;return l(),r("div",U,[t(h,{ref_key:"userListRef",ref:Z,modelValue:o(q),"onUpdate:modelValue":e[0]||(e[0]=s=>n(q)?q.value=s:null),class:"user-scroll-list",style:{"--i-scroll-list-padding":"10px 10px 0 10px","--i-scroll-list-divider-bg":"#f8f9fa","--i-scroll-list-item-margin-bottom":"6px"},api:o(k).sessionUser},{default:d((({item:s})=>[p("div",{class:x(["user-item",""+(2===s.banType?"block-item":"")])},[p("div",$,[p("div",O,[m(" UID："+c(s.userId)+" ",1),t(b,{style:{color:"var(--el-color-primary)",margin:"0 12px 0 3px",cursor:"pointer"},onClick:e=>{return a=s.userId,void C(String(a)).then((()=>{g.success("复制成功")})).catch((()=>{g.error("复制失败")}));var a}},{default:d((()=>[t(a)])),_:2},1032,["onClick"]),p("div",P,[t(j,{style:{width:"100%"},"scroll-x":!0},{default:d((()=>[p("div",T,[(l(!0),r(f,null,v(s.tagIds||[],((s,e)=>(l(),r("div",{key:e,class:"tag"},c(s.name),1)))),128))])])),_:2},1024)])]),p("div",D,[1===s.banType?(l(),r("span",L," 永久禁言 ")):0===s.banType?(l(),r("span",S," 禁言中("+c(s.banDuration)+"min) ",1)):2===s.banType?(l(),r("span",V,"已拉黑")):s.isOnline?y("",!0):(l(),r("span",A,"不在线"))])]),p("div",E,[p("img",{class:"avatar",src:s.avatar},null,8,z),p("div",F,[p("div",H,c(s.nickname),1),p("div",K,[p("div",M,[e[1]||(e[1]=m(" IP地址： ")),p("span",N,c(s.ipAddress),1),p("span",Q,c(s.province)+"|"+c(s.city),1)]),p("div",R,[e[2]||(e[2]=m(" 累计观看： ")),p("span",X,c(B(s.duration)),1)])])])])],2)])),_:1},8,["modelValue","api"])])}}}),[["__scopeId","data-v-48c70531"]]);export{Y as default};
