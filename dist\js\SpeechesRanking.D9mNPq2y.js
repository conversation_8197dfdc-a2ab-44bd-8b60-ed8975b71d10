import{d as s,r as e,I as i,ap as a,g as r,f as t,m as n,i as l,ak as o,w as d,C as m,h as c,F as p,$ as u,E as f,P as v,Q as y,n as x,az as g,a0 as h}from"./index.Dk5pbsTU.js";/* empty css                     */import{_ as j,a as k,b as _}from"./ph3.gbMn8L6h.js";import{P as I,I as b,L as w}from"./index.CgJfYQHq.js";import{u as C}from"./index.CcWFbuMM.js";import{d as $}from"./dayjs.min.Bj2GcQlV.js";import{E as P}from"./index.ybpLT-bz.js";import{_ as S}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./el-divider.2VNIZioN.js";import"./el-empty.Dee0wMKK.js";import"./index.D6CER_Ot.js";import"./commonSetup.Dm-aByKQ.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";const U={style:{width:"100%",height:"100%",background:"#f8f9fa"}},N={class:"user-item-index"},O={key:0},V={key:1,class:"user-item-index-icon",src:j},D={key:2,class:"user-item-index-icon",src:k},E={key:3,class:"user-item-index-icon",src:_},L={class:"user-item-info"},R=["src"],z={class:"user-info"},A={class:"id-info"},F={style:{width:"0",flex:"1"}},H={style:{width:"100%"}},K={style:{display:"inline-flex"}},M={class:"other-info"},Q={class:"name"},X={key:0,class:"inviter-name"},Y={class:"address",style:{"min-width":"120px"}},Z={class:"address-info"},q={class:"address-info",style:{"margin-left":"8px"}},B={class:"address",style:{"min-width":"120px"}},G={class:"address-info"},J={class:"address",style:{"min-width":"120px"}},T={class:"address-info"},W=S(s({__name:"SpeechesRanking",props:{info:{type:Object,default:()=>({})}},setup(s){const{toClipboard:j}=C(),k=s,_=e(),S=e(new I);function W(s){const e=$.duration(s,"seconds"),i=Math.floor(e.asHours()),a=e.minutes(),r=e.seconds();return i>0?`${i}h${a}min`:a>0?`${a}min`:`${r}s`}return i((()=>k.info.id),(s=>{s&&(S.value.sessionId=String(k.info.id),S.value.type="1",S.value.pageNum=1,h((()=>{var s;null==(s=_.value)||s.reset()})))}),{deep:!0,immediate:!0}),(s,e)=>{const i=a("CopyDocument"),h=f,k=P;return t(),r("div",U,[n(b,{ref_key:"userListRef",ref:_,modelValue:l(S),"onUpdate:modelValue":e[0]||(e[0]=s=>o(S)?S.value=s:null),class:"user-scroll-list",style:{"--i-scroll-list-padding":"10px 10px 0 10px","--i-scroll-list-divider-bg":"#f8f9fa","--i-scroll-list-item-margin-bottom":"6px"},api:l(w).sessionUser},{default:d((({item:s,itemIndex:a})=>[m("div",{class:x(["user-item",`user-item-${a+1}`])},[m("div",N,[a>2?(t(),r("span",O,p(a+1),1)):0===a?(t(),r("img",V)):1===a?(t(),r("img",D)):2===a?(t(),r("img",E)):c("",!0)]),m("div",L,[m("img",{class:"avatar",src:s.avatar},null,8,R),m("div",z,[m("div",A,[u(" UID："+p(s.userId)+" ",1),n(h,{style:{color:"var(--el-color-primary)",margin:"0 12px 0 3px",cursor:"pointer"},onClick:e=>{return i=s.userId,void j(String(i)).then((()=>{g.success("复制成功")})).catch((()=>{g.error("复制失败")}));var i}},{default:d((()=>[n(i)])),_:2},1032,["onClick"]),m("div",F,[m("div",H,[n(k,{style:{width:"100%"},"scroll-x":!0},{default:d((()=>[m("div",K,[(t(!0),r(v,null,y(s.tagIds||[],((s,e)=>(t(),r("div",{key:e,class:"tag"},p(s.name),1)))),128))])])),_:2},1024)])])]),m("div",M,[m("div",Q,[u(p(s.nickname)+" ",1),s.inviterName?(t(),r("div",X,p(s.inviterName),1)):c("",!0)]),m("div",Y,[e[1]||(e[1]=u(" IP地址： ")),m("span",Z,p(s.ipAddress),1),m("span",q,p(s.province)+"|"+p(s.city),1)]),m("div",B,[e[2]||(e[2]=u(" 累计观看： ")),m("span",G,p(W(s.duration)),1)]),m("div",J,[e[3]||(e[3]=u(" 弹幕数量： ")),m("span",T,p(s.commentCount),1)])])])])],2)])),_:1},8,["modelValue","api"])])}}}),[["__scopeId","data-v-b6bf791a"]]);export{W as default};
