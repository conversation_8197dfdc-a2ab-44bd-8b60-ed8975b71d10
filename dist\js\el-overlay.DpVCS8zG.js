import{ba as e,d as o,t,b as l,m as n,a7 as a,l as s,z as u,v as r,bb as d,ak as c,c as i,H as p,a2 as f,I as y,bc as m,a1 as v,bd as b,a3 as g,B as C,be as x,r as S,bf as k,J as B,o as h,bg as M,a8 as w,a0 as I}from"./index.Dk5pbsTU.js";import{P as E}from"./vnode.Cbclzz8S.js";import{b as z}from"./index.C6NthMtN.js";import{U as D}from"./event.BwRzfsZt.js";import{t as O}from"./error.D_Dr4eZ1.js";import{g as j}from"./scroll.CVc-P3_z.js";import{u as A}from"./index.D6CER_Ot.js";import{i as F}from"./isUndefined.DgmxjSXK.js";const L=o=>{if(!o)return{onClick:e,onMousedown:e,onMouseup:e};let t=!1,l=!1;return{onClick:e=>{t&&l&&o(e),t=l=!1},onMousedown:e=>{t=e.target===e.currentTarget},onMouseup:e=>{l=e.target===e.currentTarget}}},T=t({mask:{type:Boolean,default:!0},customMaskEvent:Boolean,overlayClass:{type:u([String,Array,Object])},zIndex:{type:u([String,Number])}});const P=o({name:"ElOverlay",props:T,emits:{click:e=>e instanceof MouseEvent},setup(e,{slots:o,emit:t}){const u=l("overlay"),{onClick:r,onMousedown:d,onMouseup:c}=L(e.customMaskEvent?void 0:e=>{t("click",e)});return()=>e.mask?n("div",{class:[u.b(),e.overlayClass],style:{zIndex:e.zIndex},onClick:r,onMousedown:d,onMouseup:c},[s(o,"default")],E.STYLE|E.CLASS|E.PROPS,["onClick","onMouseup","onMousedown"]):a("div",{class:e.overlayClass,style:{zIndex:e.zIndex,position:"fixed",top:"0px",right:"0px",bottom:"0px",left:"0px"}},[s(o,"default")])}}),N=t({center:Boolean,alignCenter:Boolean,closeIcon:{type:r},draggable:Boolean,overflow:Boolean,fullscreen:Boolean,headerClass:String,bodyClass:String,footerClass:String,showClose:{type:Boolean,default:!0},title:{type:String,default:""},ariaLevel:{type:String,default:"2"}}),$={close:()=>!0},H=t({...N,appendToBody:Boolean,appendTo:{type:z.to.type,default:"body"},beforeClose:{type:u(Function)},destroyOnClose:Boolean,closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:Boolean,modalClass:String,headerClass:String,bodyClass:String,footerClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:Boolean,headerAriaLevel:{type:String,default:"2"}}),R={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[D]:e=>d(e),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},U=(e,o={})=>{c(e)||O("[useLockscreen]","You need to pass a ref param to this function");const t=o.ns||l("popup"),n=i((()=>t.bm("parent","hidden")));if(!p||f(document.body,n.value))return;let a=0,s=!1,u="0";const r=()=>{setTimeout((()=>{"undefined"!=typeof document&&s&&document&&(document.body.style.width=u,g(document.body,n.value))}),200)};y(e,(e=>{if(!e)return void r();s=!f(document.body,n.value),s&&(u=document.body.style.width,v(document.body,n.value)),a=j(t.namespace.value);const o=document.documentElement.clientHeight<document.body.scrollHeight,l=b(document.body,"overflowY");a>0&&(o||"scroll"===l)&&s&&(document.body.style.width=`calc(100% - ${a}px)`)})),m((()=>r()))},V=(e,o)=>{var t;const l=C().emit,{nextZIndex:n}=x();let a="";const s=A(),u=A(),r=S(!1),d=S(!1),c=S(!1),f=S(null!=(t=e.zIndex)?t:n());let m,v;const b=k("namespace",M),g=i((()=>{const o={},t=`--${b.value}-dialog`;return e.fullscreen||(e.top&&(o[`${t}-margin-top`]=e.top),e.width&&(o[`${t}-width`]=B(e.width))),o})),E=i((()=>e.alignCenter?{display:"flex"}:{}));function z(){null==v||v(),null==m||m(),e.openDelay&&e.openDelay>0?({stop:m}=w((()=>L()),e.openDelay)):L()}function O(){null==m||m(),null==v||v(),e.closeDelay&&e.closeDelay>0?({stop:v}=w((()=>T()),e.closeDelay)):T()}function j(){e.beforeClose?e.beforeClose((function(e){e||(d.value=!0,r.value=!1)})):O()}function L(){p&&(r.value=!0)}function T(){r.value=!1}return e.lockScroll&&U(r),y((()=>e.modelValue),(t=>{t?(d.value=!1,z(),c.value=!0,f.value=F(e.zIndex)?n():f.value++,I((()=>{l("open"),o.value&&(o.value.parentElement.scrollTop=0,o.value.parentElement.scrollLeft=0,o.value.scrollTop=0)}))):r.value&&O()})),y((()=>e.fullscreen),(e=>{o.value&&(e?(a=o.value.style.transform,o.value.style.transform=""):o.value.style.transform=a)})),h((()=>{e.modelValue&&(r.value=!0,c.value=!0,z())})),{afterEnter:function(){l("opened")},afterLeave:function(){l("closed"),l(D,!1),e.destroyOnClose&&(c.value=!1)},beforeLeave:function(){l("close")},handleClose:j,onModalClick:function(){e.closeOnClickModal&&j()},close:O,doClose:T,onOpenAutoFocus:function(){l("openAutoFocus")},onCloseAutoFocus:function(){l("closeAutoFocus")},onCloseRequested:function(){e.closeOnPressEscape&&j()},onFocusoutPrevented:function(e){var o;"pointer"===(null==(o=e.detail)?void 0:o.focusReason)&&e.preventDefault()},titleId:s,bodyId:u,closed:d,style:g,overlayDialogStyle:E,rendered:c,visible:r,zIndex:f}};export{P as E,H as a,U as b,L as c,R as d,$ as e,N as f,V as u};
