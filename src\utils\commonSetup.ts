export type PageApiType<Q, R> = (_query: Q) => Promise<PageResult<R[]>>;

export interface PageDto<Q, R> {
  // 加载中状态
  loading: boolean;
  // 返回数据
  data: PageReturnVo<R>;
  // 查询参数
  query: Q;
}

export interface PageReturnVo<D> {
  // 列表
  records: D[];
  // 页码
  pageNumber: number;
  // 每页条数
  pageSize: number;
  // 总页数
  totalPage: number;
  // 总条数
  totalRow: number;
}

/**
 * 分页查询Hook
 * @template Q 查询参数类型，需继承PageQuery
 * @template R 返回数据类型
 * @param {Q} query 初始查询参数
 * @param {PageApiType<Q, R>} api 分页查询API函数
 * @returns {Object} 包含分页状态、数据和操作方法的对象
 */
export function usePage<Q extends PageQuery, R>(
  query: Q,
  api: PageApiType<Q, R>,
  _isInit: boolean = true
) {
  // 响应式分页状态对象
  const page = reactive<PageDto<Q, R>>({
    loading: false, // 加载状态
    data: {
      records: [], // 当前页数据列表
      pageNumber: 0, // 当前页码
      pageSize: 20, // 每页条数
      totalPage: 0, // 总页数
      totalRow: 0, // 总记录数
    },
    query: JSON.parse(JSON.stringify(query)), // 当前查询参数(深拷贝初始值)
  });

  /**
   * 执行分页查询
   * @async
   * @function getPage
   */
  function getPage() {
    page.loading = true;
    api(page.query as Q)
      .then((res) => {
        page.data = res as any;
      })
      .finally(() => {
        page.loading = false;
      });
  }

  // 组件挂载时自动执行分页查询
  onMounted(() => {
    if (_isInit) {
      getPage();
    }
  });

  /**
   * 重置查询条件并重新查询
   * @function resetQuery
   page, // 分页状态对象
   getPage, // 查询方法
   resetQuery, // 重置查询方法
   page.query = JSON.parse(JSON.stringify(query)); // 重置为初始查询参数
   getPage(); // 重新查询
   */
  function resetQuery() {
    page.query = JSON.parse(JSON.stringify(query));
    getPage();
  }

  return {
    page,
    getPage,
    resetQuery,
  };
}

interface ApiReactiveInterface<R, Q> {
  // 加载中
  loading: boolean;
  // 返回数据
  data?: R;
  // 请求数据
  params?: Q;
}

export type SendApiReactive<Q, R> = (_query?: Q) => Promise<R>;
type ApiSuccessCb<D> = (res: D) => void;

/**
 * 通用API请求Hook
 * @template Q 请求参数类型
 * @template R 返回数据类型
 * @param {SendApiReactive<Q, R>} api API请求函数
 * @param {Q} [data] 可选初始请求参数
 * @param successCb
 * @returns {Object} 包含请求状态和触发方法的对象
 */
export function useApi<Q, R>(api: SendApiReactive<Q, R>, data?: Q, successCb?: ApiSuccessCb<R>) {
  // 响应式请求状态对象
  const sendInfo = reactive<ApiReactiveInterface<R, Q>>({
    loading: false, // 请求加载状态
    data: undefined, // 请求返回的数据
    params: data, // 请求参数
  });

  /**
   * 执行API请求
   * @function onSend
   */
  function onSend() {
    sendInfo.loading = true; // 开始加载
    return new Promise((resolve, reject) => {
      api(sendInfo.params as Q)
        .then((res) => {
          sendInfo.data = res as any;
          if (successCb) successCb(res);
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          sendInfo.loading = false; // 加载完成
        });
    });
  }

  return {
    sendInfo,
    onSend,
  };
}
