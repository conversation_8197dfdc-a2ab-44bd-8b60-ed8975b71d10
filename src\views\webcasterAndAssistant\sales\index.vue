<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="page.query" :inline="true">
        <el-form-item prop="keywords" label="">
          <el-input
            v-model="page.query.keywords"
            placeholder="姓名、昵称检索"
            clearable
            @keyup.enter="getPage"
          />
        </el-form-item>
        <el-form-item prop="status" label="账号状态">
          <dict v-model="page.query.status" code="disable_status" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="getPage">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          type="success"
          v-hasPerm="['wa:sales:save']"
          icon="plus"
          @click="editModelRef?.open()"
        >
          新增
        </el-button>
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="page.loading"
        :data="page.data.records"
        highlight-current-row
        border
      >
        <el-table-column label="序号" align="center" width="55" type="index" />
        <el-table-column label="头像" align="center" prop="name" width="100">
          <template #default="{ row }">
            <el-image
              style="width: 30px; height: 30px; border-radius: 30px"
              :preview-src-list="[row.avatar]"
              preview-teleported
              :src="row.avatar"
            />
          </template>
        </el-table-column>
        <el-table-column label="微信昵称" align="center" prop="sysUserName" min-width="120" />
        <el-table-column label="销售姓名" align="center" prop="userName" min-width="120" />
        <el-table-column label="销售昵称" align="center" prop="nickName" min-width="120" />
        <el-table-column label="账号状态" align="center" width="80">
          <template #default="{ row }">
            <dict-label v-model="row.status" code="disable_status" />
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button
              type="danger"
              size="small"
              v-hasPerm="['wa:sales:delete']"
              link
              icon="delete"
              :loading="scope.row.loading"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
            <el-button
              type="warning"
              size="small"
              link
              :loading="scope.row.loading"
              v-hasPerm="['wa:sales:save']"
              @click="editStatus(scope.row)"
            >
              {{ scope.row.status ? "停用" : "启用" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="page.data.totalRow"
        v-model:total="page.data.totalRow"
        v-model:page="page.query.pageNum"
        v-model:limit="page.query.pageSize"
        @pagination="getPage"
      />
    </el-card>
    <edit-model ref="editModelRef" @success="resetQuery" />
  </div>
</template>

<script setup lang="ts">
import EditModel from "./edit.vue";
import WebmasterApi, {
  WebmasterPageQuery,
  WebmasterPageVO,
} from "@/api/webcasterAndAssistant/webcaster";

defineOptions({ name: "Sales" });
import { usePage } from "@/utils/commonSetup";
import SalesApi, { UpdateSalesStatusDto } from "@/api/webcasterAndAssistant/sales";
const editModelRef = ref<InstanceType<typeof EditModel>>();
const { page, getPage, resetQuery } = usePage<WebmasterPageQuery, WebmasterPageVO>(
  new WebmasterPageQuery("2"),
  WebmasterApi.page
);

function handleDelete(_row: WebmasterPageVO) {
  ElMessageBox.confirm(`确定删除销售《${_row.nickName}》吗？`, `删除`, {
    confirmButtonText: "确定删除",
    cancelButtonText: "取消",
    confirmButtonClass: "el-button--danger",
    type: "error",
  })
    .then(() => {
      _row.loading = true;
      SalesApi.remove(_row.id)
        .then(() => {
          ElMessage.success("删除成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}

function editStatus(_row: WebmasterPageVO) {
  ElMessageBox.confirm(
    `确定要${_row.status ? "停用" : "启用"}销售《${_row.nickName}》吗？`,
    `${_row.status ? "停用" : "启用"}授权`,
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      confirmButtonClass: "el-button--danger",
      type: "error",
    }
  )
    .then(() => {
      const formData = new UpdateSalesStatusDto(_row);
      if (formData.status) formData.status = 0;
      else formData.status = 1;
      _row.loading = true;
      SalesApi.editStatus(formData)
        .then(() => {
          ElMessage.success("操作成功");
          resetQuery();
        })
        .finally(() => (_row.loading = false));
    })
    .catch(() => ElMessage.info("已取消"));
}
</script>
