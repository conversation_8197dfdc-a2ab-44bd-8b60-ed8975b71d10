import{L as e,b6 as l,bb as a,N as n,A as o,c as u,a5 as t,B as i,I as s,a0 as d,r,a9 as c,c9 as v,aa as b,ca as m,_ as p,d as h,b9 as f,b as k,e as x,f as g,w as C,C as y,g as L,h as V,n as B,i as S,V as F,j as I,ak as E,cb as z,l as U,P as N,$ as j,F as w,D,k as O,t as _,z as G,y as $,bX as R,G as A,q as P}from"./index.Dk5pbsTU.js";import{u as q,p as K}from"./index.C9UdVphc.js";import{U as M,C as X}from"./event.BwRzfsZt.js";import{a as H,u as J}from"./use-form-common-props.CQPDkY7k.js";import{u as Q,a as T}from"./use-form-item.DzRJVC1I.js";import{d as W}from"./error.D_Dr4eZ1.js";import{i as Y}from"./isEqual.C0S6DIiJ.js";import{u as Z}from"./index.DuiNpp1i.js";const ee={modelValue:{type:[Number,String,Boolean],default:void 0},label:{type:[String,Boolean,Number,Object],default:void 0},value:{type:[String,Boolean,Number,Object],default:void 0},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueValue:{type:[String,Number],default:void 0},falseValue:{type:[String,Number],default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},border:Boolean,size:n,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0},...q(["ariaControls"])},le={[M]:n=>e(n)||l(n)||a(n),change:n=>e(n)||l(n)||a(n)},ae=Symbol("checkboxGroupContextKey"),ne=(e,{model:l,isLimitExceeded:a,hasOwnLabel:n,isDisabled:t,isLabeledByFormItem:r})=>{const c=o(ae,void 0),{formItem:v}=Q(),{emit:b}=i();function m(l){var a,n,o,u;return[!0,e.trueValue,e.trueLabel].includes(l)?null==(n=null!=(a=e.trueValue)?a:e.trueLabel)||n:null!=(u=null!=(o=e.falseValue)?o:e.falseLabel)&&u}const p=u((()=>(null==c?void 0:c.validateEvent)||e.validateEvent));return s((()=>e.modelValue),(()=>{p.value&&(null==v||v.validate("change").catch((e=>W())))})),{handleChange:function(e){if(a.value)return;const l=e.target;b(X,m(l.checked),e)},onClickRoot:async function(o){if(!a.value&&!n.value&&!t.value&&r.value){o.composedPath().some((e=>"LABEL"===e.tagName))||(l.value=m([!1,e.falseValue,e.falseLabel].includes(l.value)),await d(),function(e,l){b(X,m(e),l)}(l.value,o))}}}},oe=(e,l)=>{const{formItem:n}=Q(),{model:s,isGroup:d,isLimitExceeded:p}=(e=>{const l=r(!1),{emit:a}=i(),n=o(ae,void 0),s=u((()=>!1===t(n))),d=r(!1),v=u({get(){var a,o;return s.value?null==(a=null==n?void 0:n.modelValue)?void 0:a.value:null!=(o=e.modelValue)?o:l.value},set(e){var o,u;s.value&&c(e)?(d.value=void 0!==(null==(o=null==n?void 0:n.max)?void 0:o.value)&&e.length>(null==n?void 0:n.max.value)&&e.length>v.value.length,!1===d.value&&(null==(u=null==n?void 0:n.changeEvent)||u.call(n,e))):(a(M,e),l.value=e)}});return{model:v,isGroup:s,isLimitExceeded:d}})(e),{isFocused:h,isChecked:f,checkboxButtonSize:k,checkboxSize:x,hasOwnLabel:g,actualValue:C}=((e,l,{model:n})=>{const t=o(ae,void 0),i=r(!1),s=u((()=>v(e.value)?e.label:e.value)),d=u((()=>{const l=n.value;return a(l)?l:c(l)?b(s.value)?l.map(m).some((e=>Y(e,s.value))):l.map(m).includes(s.value):null!=l?l===e.trueValue||l===e.trueLabel:!!l}));return{checkboxButtonSize:J(u((()=>{var e;return null==(e=null==t?void 0:t.size)?void 0:e.value})),{prop:!0}),isChecked:d,isFocused:i,checkboxSize:J(u((()=>{var e;return null==(e=null==t?void 0:t.size)?void 0:e.value}))),hasOwnLabel:u((()=>!!l.default||!v(s.value))),actualValue:s}})(e,l,{model:s}),{isDisabled:y}=(({model:e,isChecked:l})=>{const a=o(ae,void 0),n=u((()=>{var n,o;const u=null==(n=null==a?void 0:a.max)?void 0:n.value,i=null==(o=null==a?void 0:a.min)?void 0:o.value;return!t(u)&&e.value.length>=u&&!l.value||!t(i)&&e.value.length<=i&&l.value}));return{isDisabled:H(u((()=>(null==a?void 0:a.disabled.value)||n.value))),isLimitDisabled:n}})({model:s,isChecked:f}),{inputId:L,isLabeledByFormItem:V}=T(e,{formItemContext:n,disableIdGeneration:g,disableIdManagement:d}),{handleChange:B,onClickRoot:S}=ne(e,{model:s,isLimitExceeded:p,hasOwnLabel:g,isDisabled:y,isLabeledByFormItem:V});var F,I;return e.checked&&(c(s.value)&&!s.value.includes(C.value)?s.value.push(C.value):s.value=null==(I=null!=(F=e.trueValue)?F:e.trueLabel)||I),Z({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},u((()=>d.value&&v(e.value)))),Z({from:"true-label",replacement:"true-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},u((()=>!!e.trueLabel))),Z({from:"false-label",replacement:"false-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},u((()=>!!e.falseLabel))),{inputId:L,isLabeledByFormItem:V,isChecked:f,isDisabled:y,isFocused:h,checkboxButtonSize:k,checkboxSize:x,hasOwnLabel:g,model:s,actualValue:C,handleChange:B,onClickRoot:S}},ue=h({name:"ElCheckbox"});var te=p(h({...ue,props:ee,emits:le,setup(e){const l=e,a=f(),{inputId:n,isLabeledByFormItem:o,isChecked:t,isDisabled:i,isFocused:s,checkboxSize:d,hasOwnLabel:r,model:c,actualValue:v,handleChange:b,onClickRoot:m}=oe(l,a),p=k("checkbox"),h=u((()=>[p.b(),p.m(d.value),p.is("disabled",i.value),p.is("bordered",l.border),p.is("checked",t.value)])),O=u((()=>[p.e("input"),p.is("disabled",i.value),p.is("checked",t.value),p.is("indeterminate",l.indeterminate),p.is("focus",s.value)]));return(e,l)=>(g(),x(D(!S(r)&&S(o)?"span":"label"),{class:B(S(h)),"aria-controls":e.indeterminate?e.ariaControls:null,onClick:S(m)},{default:C((()=>{var l,a,o,u;return[y("span",{class:B(S(O))},[e.trueValue||e.falseValue||e.trueLabel||e.falseLabel?F((g(),L("input",{key:0,id:S(n),"onUpdate:modelValue":e=>E(c)?c.value=e:null,class:B(S(p).e("original")),type:"checkbox",indeterminate:e.indeterminate,name:e.name,tabindex:e.tabindex,disabled:S(i),"true-value":null==(a=null!=(l=e.trueValue)?l:e.trueLabel)||a,"false-value":null!=(u=null!=(o=e.falseValue)?o:e.falseLabel)&&u,onChange:S(b),onFocus:e=>s.value=!0,onBlur:e=>s.value=!1,onClick:I((()=>{}),["stop"])},null,42,["id","onUpdate:modelValue","indeterminate","name","tabindex","disabled","true-value","false-value","onChange","onFocus","onBlur","onClick"])),[[z,S(c)]]):F((g(),L("input",{key:1,id:S(n),"onUpdate:modelValue":e=>E(c)?c.value=e:null,class:B(S(p).e("original")),type:"checkbox",indeterminate:e.indeterminate,disabled:S(i),value:S(v),name:e.name,tabindex:e.tabindex,onChange:S(b),onFocus:e=>s.value=!0,onBlur:e=>s.value=!1,onClick:I((()=>{}),["stop"])},null,42,["id","onUpdate:modelValue","indeterminate","disabled","value","name","tabindex","onChange","onFocus","onBlur","onClick"])),[[z,S(c)]]),y("span",{class:B(S(p).e("inner"))},null,2)],2),S(r)?(g(),L("span",{key:0,class:B(S(p).e("label"))},[U(e.$slots,"default"),e.$slots.default?V("v-if",!0):(g(),L(N,{key:0},[j(w(e.label),1)],64))],2)):V("v-if",!0)]})),_:3},8,["class","aria-controls","onClick"]))}}),[["__file","checkbox.vue"]]);const ie=h({name:"ElCheckboxButton"});var se=p(h({...ie,props:ee,emits:le,setup(e){const l=e,a=f(),{isFocused:n,isChecked:t,isDisabled:i,checkboxButtonSize:s,model:d,actualValue:r,handleChange:c}=oe(l,a),v=o(ae,void 0),b=k("checkbox"),m=u((()=>{var e,l,a,n;const o=null!=(l=null==(e=null==v?void 0:v.fill)?void 0:e.value)?l:"";return{backgroundColor:o,borderColor:o,color:null!=(n=null==(a=null==v?void 0:v.textColor)?void 0:a.value)?n:"",boxShadow:o?`-1px 0 0 0 ${o}`:void 0}})),p=u((()=>[b.b("button"),b.bm("button",s.value),b.is("disabled",i.value),b.is("checked",t.value),b.is("focus",n.value)]));return(e,l)=>{var a,o,u,s;return g(),L("label",{class:B(S(p))},[e.trueValue||e.falseValue||e.trueLabel||e.falseLabel?F((g(),L("input",{key:0,"onUpdate:modelValue":e=>E(d)?d.value=e:null,class:B(S(b).be("button","original")),type:"checkbox",name:e.name,tabindex:e.tabindex,disabled:S(i),"true-value":null==(o=null!=(a=e.trueValue)?a:e.trueLabel)||o,"false-value":null!=(s=null!=(u=e.falseValue)?u:e.falseLabel)&&s,onChange:S(c),onFocus:e=>n.value=!0,onBlur:e=>n.value=!1,onClick:I((()=>{}),["stop"])},null,42,["onUpdate:modelValue","name","tabindex","disabled","true-value","false-value","onChange","onFocus","onBlur","onClick"])),[[z,S(d)]]):F((g(),L("input",{key:1,"onUpdate:modelValue":e=>E(d)?d.value=e:null,class:B(S(b).be("button","original")),type:"checkbox",name:e.name,tabindex:e.tabindex,disabled:S(i),value:S(r),onChange:S(c),onFocus:e=>n.value=!0,onBlur:e=>n.value=!1,onClick:I((()=>{}),["stop"])},null,42,["onUpdate:modelValue","name","tabindex","disabled","value","onChange","onFocus","onBlur","onClick"])),[[z,S(d)]]),e.$slots.default||e.label?(g(),L("span",{key:2,class:B(S(b).be("button","inner")),style:O(S(t)?S(m):void 0)},[U(e.$slots,"default",{},(()=>[j(w(e.label),1)]))],6)):V("v-if",!0)],2)}}}),[["__file","checkbox-button.vue"]]);const de=_({modelValue:{type:G(Array),default:()=>[]},disabled:Boolean,min:Number,max:Number,size:n,fill:String,textColor:String,tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0},...q(["ariaLabel"])}),re={[M]:e=>c(e),change:e=>c(e)},ce=h({name:"ElCheckboxGroup"});var ve=p(h({...ce,props:de,emits:re,setup(e,{emit:l}){const a=e,n=k("checkbox"),{formItem:o}=Q(),{inputId:t,isLabeledByFormItem:i}=T(a,{formItemContext:o}),r=async e=>{l(M,e),await d(),l(X,e)},c=u({get:()=>a.modelValue,set(e){r(e)}});return $(ae,{...K(R(a),["size","min","max","disabled","validateEvent","fill","textColor"]),modelValue:c,changeEvent:r}),s((()=>a.modelValue),(()=>{a.validateEvent&&(null==o||o.validate("change").catch((e=>W())))})),(e,l)=>{var a;return g(),x(D(e.tag),{id:S(t),class:B(S(n).b("group")),role:"group","aria-label":S(i)?void 0:e.ariaLabel||"checkbox-group","aria-labelledby":S(i)?null==(a=S(o))?void 0:a.labelId:void 0},{default:C((()=>[U(e.$slots,"default")])),_:3},8,["id","class","aria-label","aria-labelledby"])}}}),[["__file","checkbox-group.vue"]]);const be=P(te,{CheckboxButton:se,CheckboxGroup:ve});A(se);const me=A(ve);export{be as E,me as a};
