import{d as e,b2 as a,ai as l,r as t,I as s,ap as i,g as n,f as o,m as r,k as u,i as d,ak as p,w as m,$ as c,F as f,C as y,E as _,az as v}from"./index.Dk5pbsTU.js";import{E as g,a as x}from"./el-progress.BQBUwu9o.js";import{E as b}from"./el-button.CXI119n4.js";import{F as h}from"./index.WzKGworL.js";import{_ as j}from"./_plugin-vue_export-helper.BCo6x5W8.js";const k={class:"el-upload-list__item-info"},w=["onClick"],F={class:"el-upload-list__item-file-name"},C=["onClick"],S=j(e({__name:"FileUpload",props:a({data:{type:Object,default:()=>({})},name:{type:String,default:"file"},limit:{type:Number,default:10},maxFileSize:{type:Number,default:10},accept:{type:String,default:"*"},uploadBtnText:{type:String,default:"上传文件"},style:{type:Object,default:()=>({width:"300px"})}},{modelValue:{type:[Array],required:!0,default:()=>[]},modelModifiers:{}}),emits:["update:modelValue"],setup(e){const a=e,j=l(e,"modelValue"),S=t([]),z=t(!1),E=t(0);function O(e){return!(e.size>1024*a.maxFileSize*1024)||(v.warning("上传文件不能大于"+a.maxFileSize+"M"),!1)}function V(e){return new Promise(((l,t)=>{const s=e.file,i=new FormData;i.append(a.name,s),Object.keys(a.data).forEach((e=>{i.append(e,a.data[e])})),h.upload(i).then((e=>{l(e)})).catch((e=>{t(e)}))}))}s(j,(e=>{(null==e?void 0:e.length)?S.value=e.map((e=>({name:e.substring(e.lastIndexOf("/")+1),url:e}))):S.value=[]}),{immediate:!0});const I=e=>{E.value=e.percent},M=e=>{v.success("上传成功"),j.value=[...j.value,e.url]},q=e=>{v.error("上传失败")};return(e,l)=>{const t=b,s=i("Document"),v=_,B=i("Close"),D=g,N=x;return o(),n("div",null,[r(D,{"file-list":d(S),"onUpdate:fileList":l[0]||(l[0]=e=>p(S)?S.value=e:null),style:u(a.style),"before-upload":O,"http-request":V,"on-progress":I,"on-success":M,"on-error":q,accept:a.accept,limit:a.limit,multiple:""},{file:m((({file:e})=>[y("div",k,[y("a",{class:"el-upload-list__item-name",onClick:a=>function(e){const{url:a,name:l}=e;a&&h.download(a,l)}(e)},[r(v,null,{default:m((()=>[r(s)])),_:1}),y("span",F,f(e.name),1),y("span",{class:"el-icon--close",onClick:a=>{return l=e.url,void h.delete(l).then((()=>{j.value=j.value.filter((e=>e!==l))}));var l}},[r(v,null,{default:m((()=>[r(B)])),_:1})],8,C)],8,w)])])),default:m((()=>[r(t,{type:"primary",disabled:d(S).length>=a.limit},{default:m((()=>[c(f(a.uploadBtnText),1)])),_:1},8,["disabled"])])),_:1},8,["file-list","style","accept","limit"]),r(N,{style:u({display:d(z)?"inline-flex":"none",width:"100%"}),percentage:d(E)},null,8,["style","percentage"])])}}}),[["__scopeId","data-v-d785ffec"]]);export{S as _};
