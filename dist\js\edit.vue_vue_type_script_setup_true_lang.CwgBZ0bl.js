import{d as e,S as l,r as a,e as s,f as t,w as i,m as o,i as r,C as m,$ as d,az as u}from"./index.Dk5pbsTU.js";import{E as n}from"./el-dialog.Cnp8BitR.js";import"./el-overlay.DpVCS8zG.js";import{E as p}from"./el-button.CXI119n4.js";import{E as f,a as v}from"./el-form-item.Bw6Zyv_7.js";import{E as c}from"./el-input.DiGatoux.js";import{S as _,a as b}from"./knowledgeType.DrndLWHa.js";const w={class:"dialog-footer"},g=e({__name:"edit",emits:["success"],setup(e,{expose:g,emit:j}){const h=l({visible:!1,title:""}),x=a(new _),y=a(),V=j,E=a(!1),k={name:[{required:!0,message:"请输入类型名称",trigger:"blur"}]};function C(){y.value.validate((e=>{e&&(E.value=!0,b.saveOrEdit(x.value).then((()=>{u.success("保存成功"),h.visible=!1,V("success")})).finally((()=>E.value=!1)))}))}return g({open:e=>{h.visible=!0,h.title=(null==e?void 0:e.id)?"编辑知识库类型":"新增知识库类型",x.value=new _(e)}}),(e,l)=>{const a=c,u=v,_=f,b=p,g=n;return t(),s(g,{modelValue:r(h).visible,"onUpdate:modelValue":l[2]||(l[2]=e=>r(h).visible=e),title:r(h).title,width:"500px"},{footer:i((()=>[m("div",w,[o(b,{type:"primary",onClick:C},{default:i((()=>l[3]||(l[3]=[d("确 定")]))),_:1,__:[3]}),o(b,{onClick:l[1]||(l[1]=e=>r(h).visible=!1)},{default:i((()=>l[4]||(l[4]=[d("取 消")]))),_:1,__:[4]})])])),default:i((()=>[o(_,{ref_key:"editFormRef",ref:y,model:r(x),"label-position":"top",rules:k,"label-width":"140px"},{default:i((()=>[o(u,{label:"类型名称",prop:"name"},{default:i((()=>[o(a,{modelValue:r(x).name,"onUpdate:modelValue":l[0]||(l[0]=e=>r(x).name=e),maxlength:50,"show-word-limit":"",placeholder:"请输入类型名称"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}});export{g as _};
