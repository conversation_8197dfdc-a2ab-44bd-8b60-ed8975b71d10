import{d as e,r as t,aQ as o,g as a,f as r,C as l,m as s,w as i,Z as n,i as p,$ as m,V as d,e as u,h as c,F as _,az as j}from"./index.Dk5pbsTU.js";import{v as y}from"./el-loading.Dqi-qL7c.js";import{E as f}from"./el-card.DwLhVNHW.js";import g from"./index.Cywy93e7.js";import{a as w,E as v}from"./el-table-column.DRgE6Qqc.js";import"./el-checkbox.DDYarIkn.js";import"./el-tooltip.l0sNRNKZ.js";import"./el-popper.Dbn4MgsT.js";/* empty css                     */import{_ as x}from"./DictLabel.vue_vue_type_script_setup_true_lang.Dlr7VMO2.js";import{E as b}from"./el-image-viewer.BH897zgF.js";/* empty css               */import{a as h,E as k}from"./el-form-item.Bw6Zyv_7.js";import{E as V}from"./el-button.CXI119n4.js";import{_ as C}from"./index.vue_vue_type_script_setup_true_lang.iltE5kcu.js";import{E as q}from"./el-input.DiGatoux.js";import{_ as U}from"./edit.vue_vue_type_script_setup_true_lang.CK44RVM3.js";import{T as E,c as S,a as R}from"./tenant.BHgTweuE.js";import{u as z}from"./commonSetup.Dm-aByKQ.js";import{_ as P}from"./editAi.vue_vue_type_script_setup_true_lang.8r5L3EZy.js";/* empty css                       */import"./el-overlay.DpVCS8zG.js";import{E as T}from"./index.BcMfjWDS.js";import"./el-pagination.C5FHY27u.js";import"./el-select.CRWkm-it.js";import"./index.ybpLT-bz.js";import"./error.D_Dr4eZ1.js";import"./index.C9UdVphc.js";import"./_arrayPush.DSBJLlac.js";import"./index.L2DVy5yq.js";import"./use-form-common-props.CQPDkY7k.js";import"./token.DWNpOE8r.js";import"./strings.MqEQKtyI.js";import"./castArray.C4RhTg2c.js";import"./isEqual.C0S6DIiJ.js";import"./_Uint8Array.n_j8oILW.js";import"./index.D6CER_Ot.js";import"./index.Vn8pbgQR.js";import"./use-form-item.DzRJVC1I.js";import"./event.BwRzfsZt.js";import"./scroll.CVc-P3_z.js";import"./debounce.DJJTSR8O.js";import"./_baseIteratee.CPRpgrLu.js";import"./index.wZTqlYZ6.js";import"./vnode.Cbclzz8S.js";import"./index.DuiNpp1i.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.C6NthMtN.js";import"./isUndefined.DgmxjSXK.js";import"./index.DEKElSOG.js";import"./_initCloneObject.BN1anLuC.js";import"./position.DfR5znly.js";import"./el-radio.w2rep3_A.js";import"./el-dialog.Cnp8BitR.js";import"./index.DFyomGhz.js";import"./refs.BzVvuxps.js";import"./el-input-number.C02ig7uT.js";import"./index.Cd8M2JyP.js";import"./SingleImageUpload.WGBxPB_4.js";import"./el-progress.BQBUwu9o.js";import"./cloneDeep.DcCMo0F4.js";import"./index.WzKGworL.js";import"./validate.Bicq6Mu8.js";import"./index.C-k5aYA-.js";import"./el-switch.kQ5v4arH.js";import"./validator.HGn2BZtD.js";const A={class:"app-container"},I={class:"search-bar"},W={class:"mb-10px"},B=e({name:"Tenant",__name:"index",setup(e){const B=t(),K=t(),{page:N,getPage:$,resetQuery:F}=z(new S,E.page);return(e,t)=>{const S=q,z=h,L=C,M=V,O=k,Q=v,D=b,H=x,J=w,Z=g,G=f,Y=o("hasPerm"),X=y;return r(),a("div",A,[l("div",I,[s(O,{ref:"queryFormRef",model:p(N).query,inline:!0},{default:i((()=>[s(z,{prop:"keywords",label:""},{default:i((()=>[s(S,{modelValue:p(N).query.keywords,"onUpdate:modelValue":t[0]||(t[0]=e=>p(N).query.keywords=e),placeholder:"公司名称、负责人名称检索",clearable:"",onKeyup:n(p($),["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),s(z,{prop:"status",label:"公司状态"},{default:i((()=>[s(L,{modelValue:p(N).query.status,"onUpdate:modelValue":t[1]||(t[1]=e=>p(N).query.status=e),code:"enable_status"},null,8,["modelValue"])])),_:1}),s(z,{prop:"keywords",label:"部署方式"},{default:i((()=>[s(L,{modelValue:p(N).query.deployWay,"onUpdate:modelValue":t[2]||(t[2]=e=>p(N).query.deployWay=e),code:"tenant_deploy_way"},null,8,["modelValue"])])),_:1}),s(z,null,{default:i((()=>[s(M,{type:"primary",icon:"search",onClick:p($)},{default:i((()=>t[8]||(t[8]=[m("搜索")]))),_:1,__:[8]},8,["onClick"]),s(M,{icon:"refresh",onClick:p(F)},{default:i((()=>t[9]||(t[9]=[m("重置")]))),_:1,__:[9]},8,["onClick"])])),_:1})])),_:1},8,["model"])]),s(G,{shadow:"never"},{default:i((()=>[l("div",W,[d((r(),u(M,{type:"success",icon:"plus",onClick:t[3]||(t[3]=e=>{var t;return null==(t=p(B))?void 0:t.open()})},{default:i((()=>t[10]||(t[10]=[m(" 新增 ")]))),_:1,__:[10]})),[[Y,["sys:tenant:save"]]]),d((r(),u(M,{type:"primary",icon:"download",onClick:t[4]||(t[4]=e=>p(E).export(p(N).query))},{default:i((()=>t[11]||(t[11]=[m(" 导出 ")]))),_:1,__:[11]})),[[Y,["sys:tenant:export"]]])]),d((r(),u(J,{ref:"dataTableRef",data:p(N).data.records,"highlight-current-row":"",border:""},{default:i((()=>[s(Q,{label:"序号",align:"center",width:"55",type:"index"}),s(Q,{label:"公司名称",align:"center",prop:"name","min-width":"120"}),s(Q,{label:"公司logo",align:"center",prop:"name",width:"100"},{default:i((({row:e})=>[s(D,{style:{width:"30px",height:"30px","border-radius":"30px"},"preview-src-list":[e.logo],"preview-teleported":"",src:e.logo},null,8,["preview-src-list","src"])])),_:1}),s(Q,{label:"公司负责人",align:"center",prop:"nickName","min-width":"100"}),s(Q,{label:"负责人电话",align:"center",prop:"phone","min-width":"100"}),s(Q,{label:"状态",align:"center",width:"80"},{default:i((({row:e})=>[s(H,{modelValue:e.status,"onUpdate:modelValue":t=>e.status=t,code:"enable_status"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(Q,{label:"部署方式",align:"center",width:"100"},{default:i((({row:e})=>[s(H,{modelValue:e.deployWay,"onUpdate:modelValue":t=>e.deployWay=t,code:"tenant_deploy_way"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(Q,{fixed:"right",label:"操作",width:"160"},{default:i((e=>[d((r(),u(M,{type:"primary",size:"small",link:"",onClick:t=>{var o;return null==(o=p(B))?void 0:o.open(e.row)}},{default:i((()=>t[12]||(t[12]=[m(" 编辑 ")]))),_:2,__:[12]},1032,["onClick"])),[[Y,["sys:tenant:save"]]]),d((r(),u(M,{type:"warning",size:"small",link:"",loading:e.row.loading,onClick:t=>{return o=e.row,void T.confirm(`确定要${o.status?"停用":"启用"}租户《${o.name}》吗？`,(o.status?"停用":"启用")+"授权",{confirmButtonText:"确定",cancelButtonText:"取消",confirmButtonClass:"el-button--danger",type:"error"}).then((()=>{const e=new R(o);e.status?e.status=0:e.status=1,o.loading=!0,E.save(e).then((()=>{j.success("操作成功"),F()})).finally((()=>o.loading=!1))})).catch((()=>j.info("已取消")));var o}},{default:i((()=>[m(_(e.row.status?"停用":"启用"),1)])),_:2},1032,["loading","onClick"])),[[Y,["sys:tenant:save"]]]),d((r(),u(M,{type:"info",size:"small",link:"",loading:e.row.loading,onClick:t=>p(K).open(e.row)},{default:i((()=>t[13]||(t[13]=[m(" AI授权 ")]))),_:2,__:[13]},1032,["loading","onClick"])),[[Y,["sys:tenant:save"]]])])),_:1})])),_:1},8,["data"])),[[X,p(N).loading]]),p(N).data.totalRow?(r(),u(Z,{key:0,total:p(N).data.totalRow,"onUpdate:total":t[5]||(t[5]=e=>p(N).data.totalRow=e),page:p(N).query.pageNum,"onUpdate:page":t[6]||(t[6]=e=>p(N).query.pageNum=e),limit:p(N).query.pageSize,"onUpdate:limit":t[7]||(t[7]=e=>p(N).query.pageSize=e),onPagination:p($)},null,8,["total","page","limit","onPagination"])):c("",!0)])),_:1}),s(U,{ref_key:"editModelRef",ref:B,onSuccess:p(F)},null,8,["onSuccess"]),s(P,{ref_key:"editAiRef",ref:K,onSuccess:p(F)},null,8,["onSuccess"])])}}});export{B as default};
