import{t as e,bP as t,_ as a,d as l,r as s,b as n,c as i,a5 as u,bw as r,o,b8 as p,e as f,f as c,w as d,l as m,k as v,n as g,i as y,D as b,q as x}from"./index.Dk5pbsTU.js";import{u as C}from"./use-form-common-props.CQPDkY7k.js";const S=e({type:{type:String,values:["primary","success","info","warning","danger",""],default:""},size:{type:String,values:t,default:""},truncated:Boolean,lineClamp:{type:[String,Number]},tag:{type:String,default:"span"}}),_=l({name:"ElText"});const h=x(a(l({..._,props:S,setup(e){const t=e,a=s(),l=C(),x=n("text"),S=i((()=>[x.b(),x.m(t.type),x.m(l.value),x.is("truncated",t.truncated),x.is("line-clamp",!u(t.lineClamp))])),_=r().title,h=()=>{var e,l,s,n,i,r,o;if(_)return;let p=!1;const f=(null==(e=a.value)?void 0:e.textContent)||"";if(t.truncated){const e=null==(l=a.value)?void 0:l.offsetWidth,t=null==(s=a.value)?void 0:s.scrollWidth;e&&t&&t>e&&(p=!0)}else if(!u(t.lineClamp)){const e=null==(n=a.value)?void 0:n.offsetHeight,t=null==(i=a.value)?void 0:i.scrollHeight;e&&t&&t>e&&(p=!0)}p?null==(r=a.value)||r.setAttribute("title",f):null==(o=a.value)||o.removeAttribute("title")};return o(h),p(h),(e,t)=>(c(),f(b(e.tag),{ref_key:"textRef",ref:a,class:g(y(S)),style:v({"-webkit-line-clamp":e.lineClamp})},{default:d((()=>[m(e.$slots,"default")])),_:3},8,["class","style"]))}}),[["__file","text.vue"]]));export{h as E};
